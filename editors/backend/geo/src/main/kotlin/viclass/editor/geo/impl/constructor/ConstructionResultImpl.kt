package viclass.editor.geo.impl.constructor

import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ConstructionResultException

class ConstructionResultImpl<R : Element> constructor() : ConstructionResult<R> {

    override var ctIdx: Int = -1

    override var newly: Boolean = true

    private var result: R? = null

    /**
     * The direct dependencies of the result itself
     */
    private val resultDeps: MutableSet<Int> = mutableSetOf()
    private val nodes: MutableList<Element> = mutableListOf()
    private val edges: MutableList<Set<Int>> = mutableListOf()

    /**
     * Given the element, find its index
     */
    private val indexes: MutableMap<Element, Int> = mutableMapOf()

    override fun result(): R? {
        return result
    }

    fun setResult(result: R) {
        result.validate()
        this.result = result
    }

    override fun elements(): List<Element?> {
        val allEls = mutableListOf<Element?>()
        allEls.addAll(nodes)
        allEls.add(result)
        return allEls
    }

    /**
     * Add elements as the DIRECT dependency FOR THE MAIN ELEMENT. Note that
     * any element that this element depends on should have been added into the construction results
     * beforehand or an exception will be thrown
     * @param els the dependency elements for the main element
     * @param isResultDep element e is a direct dependency for the main element
     */
    fun addDependencies(els: List<Element>, isResultDep : Boolean) {
        els.forEach { el -> addDependency(el, emptyList(), isResultDep)}
    }

    /**
     * Add an element as a DIRECT dependency FOR THE MAIN ELEMENT. Note that
     * any element that this element depends on should have been added into the construction results
     * beforehand or an exception will be thrown
     * @param e the dependency element for the main element
     * @param eDeps the direct dependencies of the element e.
     * @param isResultDep element e is a direct dependency for the main element
     */
    override fun addDependency(e: Element, eDeps: List<Element>, isResultDep : Boolean) {

        // the invariant that the dependencies elements must contain the full inference graph meaning eDeps
        // must contain ALL inferred element of e, hence if this element already exists in this construction result
        // we don't need to re-add it again.
        if (indexes.containsKey(e)) return
        nodes.add(e)
        indexes[e] = nodes.size - 1
        val ds: MutableSet<Int> = HashSet()
        edges.add(ds)

        if (isResultDep) resultDeps.add(nodes.size - 1) // element e is a direct dependency for the main element

        for (e1 in eDeps) {  // constructing the edges for element e
            val i = indexes[e1]
                    ?: throw ConstructionResultException("A dependency of an inferred element has not been added before.")
            ds.add(i)
        }
    }

    /**
     * Merge a construction result as the dependency for the MAIN ELEMENT.
     * Because the last element within construction result's list of element
     * is the constructed result itself, only that element is the direct dependency
     * for the result of this construction result.
     */
    override fun mergeAsDependency(c: ConstructionResult<out Element>) {

        val nodes = c.elements().filterNotNull()
        val edges = c.deps()
        for (i in nodes.indices) {
            val deps = edges[i].map { j -> nodes[j] }   // direct dependency of node i
            // if this is the last element, it is the direct dependency of the main element.
            // otherwise, we're just adding a transitive dependency to ensure the completeness
            // of the dependency graph
            addDependency(nodes[i], deps, i == nodes.size-1)
        }

        // because nodes array already contains the result element as the last element,
        // we don't need to add it separately

    }

    override fun deps(): List<Set<Int>> {
        val allEdges = mutableListOf<Set<Int>>()
        allEdges.addAll(edges)
        allEdges.add(resultDeps)
        return allEdges
    }

    override fun resultDeps(): List<Int> {
        return resultDeps.toList()
    }

    override fun edges(): List<Set<Int>> {
        return edges
    }

    override fun nodes(): List<Element> {
        return nodes.toList()
    }

    fun initData(
        ctIdx: Int,
        result: R,
        resultDeps: List<Int>,
        nodes: List<Element>,
        edges: List<List<Int>>
    ): ConstructionResultImpl<R> {
        this.resultDeps.clear()
        this.nodes.clear()
        this.edges.clear()

        this.ctIdx = ctIdx
        this.result = result
        this.resultDeps.addAll(resultDeps)
        this.nodes.addAll(nodes)
        this.edges.addAll(edges.map { it.toSet() })

        nodes.indices.forEach {
            this.indexes[nodes[it]] = it
        }

        return this
    }
}
