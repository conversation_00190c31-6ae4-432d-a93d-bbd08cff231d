package viclass.editor.geo.impl.constructor

import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.exceptions.IncompatibleConstraintException

object Validations {
    /**
     * Validate that a construction has a correct number of constraint
     * @param c
     * @param numConstraint number of constraint
     */
    fun validateNumConstraints(c: Construction, numConstraint: Int, type: String = "equal") {
        val paramSize = c.params.count { !it.specs.optional }
        when (type) {
            "equal" -> {
                if (paramSize != numConstraint)
                    throw IncompatibleConstraintException(
                        "Number of constraints is not correct. Expect $numConstraint but has $paramSize constraints"
                    )
            }

            "min" -> {
                if (paramSize < numConstraint)
                    throw IncompatibleConstraintException(
                        "Number of constraints is less than required minimum. Expect at least $numConstraint but has $paramSize constraints"
                    )
            }

            "max" -> {
                if (paramSize > numConstraint)
                    throw IncompatibleConstraintException(
                        "Number of constraints exceeds maximum allowed. Expect at most $numConstraint but has $paramSize constraints"
                    )
            }

            else -> throw IllegalArgumentException("Unknown constraint type: $type")
        }
    }
}
