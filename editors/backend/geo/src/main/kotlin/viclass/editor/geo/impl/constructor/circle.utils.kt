package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.*


/**
 * <AUTHOR>
 */
object Circles {
    fun calculatePointOnCircleWithRadian(doc: GeoDoc, name: String?, circle: Circle, alpha: Double): Point {
        val r = circle.radius
        val x0 = circle.centerPoint.x
        val y0 = circle.centerPoint.y
        val x = x0 + r * cos(alpha)
        val y = y0 + r * sin(alpha)
        return PointImpl(doc, name, x, y)
    }

    fun angleOfPoint(center: Vector3D, radius: Double, v: Vector3D): Double {
        val vX = v.x - center.x
        val vY = v.y - center.y
//        val vZ = v.z-center.z
        val cosAlpha = vX / radius
        if (vY >= 0) return acos(cosAlpha)
        else return 2 * PI - acos(cosAlpha)
    }

    fun isOnCircle(circle: Circle, v: Vector3D): Boolean {
        return abs(Distances.of(circle.centerPoint.coordinates(), v) - circle.radius) < DEFAULT_TOLERANCE
    }

    fun tangentAt(circle: Circle, at: Point): LineVi? {
        if (!isOnCircle(circle, at.coordinates())) return null
        val center = circle.centerPoint.coordinates()
        val v = at.coordinates()
        val vtpt = center.vectorTo(v)
        val parallelVector = Vector3D.of(vtpt.y, -vtpt.x, vtpt.z)
        return LineImpl(circle.doc, null, at, parallelVector)
    }

    /**
     * Computes tangents from a point to a circle,
     * supporting both the point on the circle and outside the circle.
     */
    fun tangentThroughPoint(circle: Circle, through: Point): List<LineVi>? {
        val I = circle.centerPoint.coordinates()
        val R = circle.radius
        val M = through.coordinates()

        val dist = Distances.of(I, M)

        // If the point is inside the circle, no tangent exists
        if (dist < R - DEFAULT_TOLERANCE) return null

        // If the point is exactly on the circle, only one tangent exists: the tangent at that point
        if (abs(dist - R) < DEFAULT_TOLERANCE) {
            // The radius vector IM: direction (M.x - I.x, M.y - I.y)
            // The tangent's direction is perpendicular to this vector
            val dx = M.x - I.x
            val dy = M.y - I.y
            // Perpendicular direction:
            val v1 = Vector3D.of(-dy, dx, 0.0)
            val line = LineImpl(circle.doc, null, through, v1)
            return listOf(line)
        }

        // Point outside the circle: two tangents exist
        val X = I.x - M.x
        val Y = I.y - M.y

        val Y_R = Y * Y - R * R
        val X_R = X * X - R * R

        val a: Double = 1.0 // chọn a = 1 rồi tìm b
        val delta = Y * Y + X * X - R * R
        if (delta < 0.0) return null

        val sqrt_delta = sqrt(delta)
        val b1: Double
        val b2: Double

        if (Y == R) {
            b1 = X_R / (2 * R * X)
            b2 = -X_R / (2 * R * X)
        } else {
            b1 = -(R * sqrt_delta + X * Y) / Y_R
            b2 = (R * sqrt_delta - X * Y) / Y_R
        }

        return listOf(b1, b2).map { Vector3D.of(it, -a, 0.0) }.map {
            LineImpl(circle.doc, null, through, it)
        }
    }

    fun intersecBetween2Circles(c1: Vector3D, r1: Double, c2: Vector3D, r2: Double): List<Vector3D> {
        val dx = c2.x - c1.x
        val dy = c2.y - c1.y
        val d = hypot(dx, dy)

        // Không giao nhau hoặc đồng tâm
        if (d > r1 + r2 || d < abs(r1 - r2) || d == 0.0) return emptyList()

        val a = (r1 * r1 - r2 * r2 + d * d) / (2 * d)
        val h = sqrt(r1 * r1 - a * a)

        val xm = c1.x + a * dx / d
        val ym = c1.y + a * dy / d

        val rx = -dy * (h / d)
        val ry = dx * (h / d)

        val p1 = Vector3D.of(xm + rx, ym + ry, 0.0)
        val p2 = Vector3D.of(xm - rx, ym - ry, 0.0)

        return if (p1 == p2) listOf(p1) else listOf(p1, p2)
    }
}

fun Circle.intersect(other: Circle): List<Vector3D> {
    val (c1, c2) = this.centerPoint.coordinates() to other.centerPoint.coordinates()
    val (r1, r2) = this.radius to other.radius
    return Circles.intersecBetween2Circles(c1, r1, c2, r2)
}
