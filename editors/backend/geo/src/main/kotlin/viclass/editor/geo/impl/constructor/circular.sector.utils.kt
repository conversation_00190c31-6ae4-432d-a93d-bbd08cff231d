package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 */
object CircularSectors {
    fun calculatePointOnCircularSectorWithRadian(
        doc: GeoDoc,
        name: String?,
        sector: CircularSector,
        alpha: Double
    ): Point? {
        val pC = sector.centerPoint.coordinates()
        val pS = sector.startPoint.coordinates()
        val pM = pS.rotate(alpha, pC)
        if (isOnCircularSector(sector, pM)) return PointImpl(doc, name, pM)
        else return null
    }

    fun isOnCircularSector(sector: CircularSector, p: Vector3D): Boolean {
        val pC = sector.centerPoint.coordinates()
        val vecCS = pC.vectorTo(sector.startPoint.coordinates())
        val vecCE = pC.vectorTo(sector.endPoint.coordinates())
        val vecCP = pC.vectorTo(p)

        val angleSE = vecCS.angleTo(vecCE)
        val angleSP = vecCS.angleTo(vecCP)
        if (angleSP - angleSE > DEFAULT_TOLERANCE) return false
        return abs(Distances.of(sector.centerPoint.coordinates(), p) - sector.radius) < DEFAULT_TOLERANCE
    }

    fun tangentAt(sector: CircularSector, at: Point): LineVi? {
        if (!isOnCircularSector(sector, at.coordinates())) return null
        val center = sector.centerPoint.coordinates()
        val v = at.coordinates()
        val vtpt = center.vectorTo(v)
        val parallelVector = Vector3D.of(vtpt.y, -vtpt.x, vtpt.z)
        return LineImpl(sector.doc, null, at, parallelVector)
    }
}

fun CircularSector.contain(p: Point): Boolean {
    return CircularSectors.isOnCircularSector(this, p.coordinates())
}
