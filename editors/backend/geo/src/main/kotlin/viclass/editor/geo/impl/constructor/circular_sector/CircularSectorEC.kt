package viclass.editor.geo.impl.constructor.circular_sector

import kotlin.reflect.KClass
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementCirclePath
import viclass.editor.geo.dbentity.transformdata.PointOnArcWithAngleTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aCircle
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.CircularSector
import viclass.editor.geo.elements.Point
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.CircularSectorImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnArcWithAngleTransformer
import viclass.editor.geo.impl.transformer.TransformMapping

/** <AUTHOR> */
@Singleton
class CircularSectorEC : ElementConstructor<CircularSector> {
    override fun outputType(): KClass<CircularSector> {
        return CircularSector::class
    }

    private enum class CGS {
        PointsOnCircle,
        WithCenterAndStartPointAngleRadian,
        WithCenterAndStartEndPoint
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.WithCenterAndStartEndPoint.name)
                    .hints()
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfCircle"),
                        "tpl-CenterCircle"
                    )
                    .constraintDepends(
                        1,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf(0),
                        listOf("NameOfPoint", "NameOfPoint"),
                        "tpl-StartEndPoint"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.PointsOnCircle.name)
                    .hints()
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aCircle]!!,
                        listOf("NameOfCircle"),
                        "tpl-OnCircle"
                    )
                    .constraintDepends(
                        1,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf(0),
                        listOf("NameOfPoint", "NameOfPoint"),
                        "tpl-StartEndPoint"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.WithCenterAndStartPointAngleRadian.name)
                    .hints()
                    .constraint(
                        0,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("NameOfPoint"),
                        "tpl-CenterCircle"
                    )
                    .constraintDepends(
                        1,
                        ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf(0),
                        listOf("NameOfPoint"),
                        "tpl-Point"
                    )
                    .constraintDepends(
                        2,
                        ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Radian"),
                        "tpl-AngleRadian"
                    )
                    .build(),
            )
            .elTypes(CircularSector::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<CircularSector> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.WithCenterAndStartEndPoint -> {
                Validations.validateNumConstraints(c, 2)
                constructWithCenterAndStartEndPoint(doc, inputName, c)
            }
            CGS.PointsOnCircle -> {
                Validations.validateNumConstraints(c, 2)
                constructFromPointsOnCircle(doc, inputName!!, c)
            }
            CGS.WithCenterAndStartPointAngleRadian -> {
                Validations.validateNumConstraints(c, 3)
                constructFromPointsWithCenter(doc, inputName, c)
            }
        }
    }

    private fun constructWithCenterAndStartEndPoint(
        doc: GeoDoc,
        inputName: String?,
        c: Construction,
    ): ConstructionResult<CircularSector> {
        val exr0: ElementExtraction<Point> = extractFirstPossible(doc, PK_Name, c.params[0], c.ctIdx)
        val exr1: ElementListExtraction<Point> = extractFirstPossible(doc, PK_Name, c.params[1], c.ctIdx)

        val pCenter = exr0.result.result() ?: throw ConstructionException("not found center point")
        val pStart = exr1.result.getOrNull(0)?.result() ?: throw ConstructionException("not found start point")
        val pEnd = exr1.result.getOrNull(1)?.result() ?: throw ConstructionException("not found end point")


        val circularSector = CircularSectorImpl(doc,  generateLowercaseName(doc,arrayListOf()), pCenter, pStart, pEnd)

        val cr = ConstructionResultImpl<CircularSector>()
        cr.setResult(circularSector)
        cr.addDependency(pCenter, emptyList(), true)
        cr.addDependency(pStart, emptyList(), true)
        cr.addDependency(pEnd, listOf(pCenter, pStart), true)

        return cr
    }

    private fun constructFromPointsOnCircle(
        doc: GeoDoc,
        inputName: String,
        c: Construction,
    ): ConstructionResult<CircularSector> {
        var circle: Circle? = null
        var pS: Point? = null
        var pE: Point? = null

        c.params.forEach { p ->
            when (p.paramDef.id) {
                aCircle -> {
                    val extraction = extractFirstPossible<ElementExtraction<Circle>>(doc, PK_Name, p, c.ctIdx)
                    val cR = extraction.result
                    circle = cR.result()
                }
                aPoint -> {
                    val extraction = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, p, c.ctIdx)
                    val pRs = extraction.result
                    pS = pRs[0].result()
                    pE = pRs[1].result()
                }
            }
        }

        circle ?: throw ElementNotExistInDocumentException("not found circle")
        pS ?: throw ElementNotExistInDocumentException("not found point start")
        pE ?: throw ElementNotExistInDocumentException("not found point end")

        val cr = ConstructionResultImpl<CircularSector>()
        val circularSector =
            CircularSectorImpl(
                doc,
                generateLowercaseName(doc,arrayListOf()),
                circle!!.centerPoint,
                pS!!,
                pE!!
            )
        cr.setResult(circularSector)
        cr.addDependency(circle!!, emptyList(), true)
        cr.addDependency(pS!!, emptyList(), true)
        cr.addDependency(pE!!, emptyList(), true)

        return cr
    }

    private fun constructFromPointsWithCenter(
        doc: GeoDoc,
        inputName: String?,
        c: Construction,
    ): ConstructionResult<CircularSector> {
        val exr0: ElementExtraction<Point> = extractFirstPossible(doc, PK_Name, c.params[0], c.ctIdx)
        val exr1: ElementExtraction<Point> = extractFirstPossible(doc, PK_Name, c.params[1], c.ctIdx)
        val exr2: NumberExtraction<Double> = extractFirstPossible(doc, ParamKind.PK_Value, c.params[2], c.ctIdx)

        val pCenter = exr0.result.result() ?: throw ConstructionException("not found center point")
        val pStart = exr1.result.result() ?: throw ConstructionException("not found start point")
        val angle: Double = exr2.result

        // TODO naming circular name
        val pointName: String = generatePointName(doc)
        val vEnd = pStart.coordinates().rotate(angle, pCenter.coordinates())
        val pEnd = PointImpl(doc, pointName, vEnd)

        var name = inputName?: generateLowercaseName(doc,arrayListOf())

        val circularSector =
            if (angle < 0) CircularSectorImpl(doc, name, pCenter, pEnd, pStart)
            else CircularSectorImpl(doc, name, pCenter, pStart, pEnd)

        pEnd.transformData =
            PointOnArcWithAngleTransformData(
                2,
                ParamKind.PK_Value,
                pCenter.coordinates().toArray(),
                pStart.coordinates().toArray()
            )
        pEnd.transformer = TransformMapping.fromClazz(PointOnArcWithAngleTransformer::class)
        pEnd.movementPath = MovementCirclePath(pCenter.coordinates().toArray(), Distances.of(pCenter, pStart))

        val cr = ConstructionResultImpl<CircularSector>()
        cr.setResult(circularSector)
        cr.addDependency(pCenter, emptyList(), true)
        cr.addDependency(pStart, emptyList(), true)
        cr.addDependency(pEnd, listOf(pCenter, pStart), true)

        return cr
    }
}
