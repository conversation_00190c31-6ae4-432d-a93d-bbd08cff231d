package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.CircleImpl
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.PI


/**
 *
 * <AUTHOR>
 */

object Orders {

    fun directionOfPointOnParallelVector(parallel: Vector3D, root: Vector3D, p: Vector3D) : Int {
        val vecUnit = parallel.normalize()
        val k1 = root.dot(vecUnit)
        val k2 = p.dot(vecUnit)
        return if (k1 < k2) -1 else 1
    }

    /**
     * Sort points on a line by parallel vector.
     * Make sure these points are all on the line, this method will not check them.
     */
    fun pointsOnParallelVector(parallel: Vector3D, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points

        val vecUnit = parallel.normalize()
        return points.sortedBy { it.dot(vecUnit) }
    }
    fun pointsOnParallelVector(parallel: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsOnParallelVector(parallel, points.toList())
    }

    /**
     * Sort points by angle base on reference line
     * You have to make sure these points are all on circle, this method will not check them.
     */
    fun pointsOnCircle(refCircle: Circle, refPoint: Vector3D, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points

        val doc = refCircle.doc

        val center = refCircle.centerPoint.coordinates()
        val p = PointImpl(doc, null, refPoint)
        val line = LineSegmentImpl(doc, null, refCircle.centerPoint, p)
        val ref = Intersections.of(line, refCircle)!!.first { Points.isBetweenTwoPoints(it, center, refPoint) }

        val angleRef = Circles.angleOfPoint(center, refCircle.radius, ref)

        return points.sortedBy {
            var angle = Circles.angleOfPoint(center, refCircle.radius, it)
            if (angle >= angleRef) angle = angle - angleRef
            else angle = 2*PI - angleRef + angle
            angle
        }
    }
    fun pointsOnCircle(refCircle: Circle, refPoint: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsOnCircle(refCircle, refPoint, points.toList())
    }

    fun pointsOnEllipse(refEllipse: Ellipse, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points
        val center = refEllipse.center
        val f2 = refEllipse.f2.coordinates()
        val vecCF2 = center.vectorTo(f2)
        return points.sortedBy {
            val vec = center.vectorTo(it)
            vecCF2.angleTo(vec)
        }
    }

    fun pointsOnEllipse(refEllipse: Ellipse, vararg points: Vector3D): List<Vector3D> {
        return pointsOnEllipse(refEllipse, points.toList())
    }

    fun pointsByRotation(vec: Vector3D, role: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsByRotation(vec, role, points.toList())
    }

    fun pointsByRotation(vec: Vector3D, role: Vector3D, points: List<Vector3D>): List<Vector3D> {
        return points.sortedBy { vec.angleTo(role.vectorTo(it)) }
    }

    fun pointByParallelVector(parallel: Vector3D, role: Vector3D, v1: Vector3D, v2: Vector3D): List<Vector3D> {
        if (v1 == v2) return listOf(v1, v2)
        val angle1 = parallel.angleTo(role.vectorTo(v1))
        val angle2 = parallel.angleTo(role.vectorTo(v2))
        if (angle1 < PI && angle2 > PI) return listOf(v1, v2)
        if (angle2 < PI && angle1 > PI) return listOf(v2, v1)
        return emptyList()
    }

    fun pointByCreateTime(doc: GeoDoc, vararg points: Point): List<Point> {
        return points.sortedBy { doc.getIndex(it) }
    }

    fun pointsBaseLineReference(refLine: LineVi, v1: Vector3D, v2: Vector3D): List<Vector3D> {
        if (v1 == v2) return listOf(v1, v2)

        val doc = refLine.doc

        val p1 = PointImpl(doc, null, v1)
        val p2 = PointImpl(doc, null, v2)

        val l12 = LineSegmentImpl(doc, null, p1, p2)

        val vO = Intersections.of(refLine, l12)!!
        val pO = PointImpl(doc, null, vO)

        val lpOp1 = LineSegmentImpl(doc, null, pO, p1)
        val lpOp2 = LineSegmentImpl(doc, null, pO, p2)

        val circle = CircleImpl(doc, null, pO, minOf(lpOp1.length(), lpOp2.length()) - 0.01)

        val inters1 = Intersections.of(lpOp1, circle)!!.first { Points.isBetweenTwoPoints(it, vO, v1) }
        val inters2 = Intersections.of(lpOp2, circle)!!.first { Points.isBetweenTwoPoints(it, vO, v2) }
        val intersRefPoints = Intersections.of(refLine, circle)!!
        val intersRef = pointsOnParallelVector(refLine.parallelVector, intersRefPoints)[0]

        val v = pointsOnCircle(circle, intersRef, inters1, inters2)

        if (v[0] == inters1) return listOf(v1, v2)
        if (v[0] == inters2) return listOf(v2, v1)

        return emptyList()
    }

    /**
     * Order points in document from left -> right, up -> down
     */
    @JvmName("pointsInDocumentVector3D")
    fun pointsInDocument(points: List<Vector3D>): List<Vector3D> {
        return points.sortedWith { o1, o2 ->
            if (o1.x < o2.x) 1
            else if (o1.x > o2.x) -1
            else if (o1.y < o2.y) 1
            else if (o1.y > o2.y) -1
            else 0
        }
    }

    /**
     * Order points in document from left -> right, up -> down
     */
    @JvmName("pointsInDocumentPoint")
    fun pointsInDocument(points: List<Point>): List<Point> {
        return points.sortedWith { o1, o2 ->
            val p1 = o1.coordinates()
            val p2 = o2.coordinates()
            if (p1.x < p2.x) 1
            else if (p1.x > p2.x) -1
            else if (p1.y < p2.y) 1
            else if (p1.y > p2.y) -1
            else 0
        }
    }
}