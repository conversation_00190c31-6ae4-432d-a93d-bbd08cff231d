package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Plane
import org.apache.commons.geometry.euclidean.threed.Planes
import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.numbers.core.Precision
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Polygon
import viclass.editor.geo.impl.elements.PointImpl

/**
 * <AUTHOR>
 */
object Polygons {
    val nextIndex: (Int, List<Any>) -> Int = { idx, lst -> if (idx == lst.size - 1) 0 else idx + 1 }
    val previousIndex: (Int, List<Any>) -> Int = { idx, lst -> if (idx == 0) lst.size - 1 else idx - 1 }

    private fun arePointsCoplanar(points: List<Point>): Boolean {
        if (points.size < 4) return true
        val p1Coord = points[0].coordinates()
        val normal = points[1].coordinates().subtract(p1Coord).cross(points[2].coordinates().subtract(p1Coord))
        return points.drop(3).all {
            Precision.equals(normal.dot(it.coordinates().subtract(p1Coord)), 0.0, DEFAULT_TOLERANCE)
        }
    }

    private fun projectTo2D(points: List<Point>): List<Point> {
        val normal = findDominantPlaneNormal()
        val projX =
            if (normal.dot(Vector3D.of(1.0, 0.0, 0.0)) > 0.99) normal.orthogonal() else Vector3D.of(1.0, 0.0, 0.0)
        val projY =
            if (normal.dot(Vector3D.of(0.0, 1.0, 0.0)) > 0.99) normal.orthogonal() else Vector3D.of(0.0, 1.0, 0.0)
        return points.map { PointImpl(it.doc, it.name, it.coordinates().dot(projX), it.coordinates().dot(projY), 0.0) }
    }

    private fun findDominantPlaneNormal(): Vector3D = Vector3D.of(0.0, 0.0, 1.0)

    /**
     * Checks if a 2D point is inside a 2D polygon using the ray-casting algorithm.
     *
     * @param point The 2D point to check.
     * @param polygon The list of 2D vertices defining the polygon.
     * @return True if the point is inside the polygon, false otherwise.
     */
    fun isPointInPolygon2D(point: Vector3D, polygon: List<Vector3D>): Boolean {
        val x = point.x
        val y = point.y
        var inside = false

        for (i in polygon.indices) {
            val a = polygon[i]
            val b = polygon[(i + 1) % polygon.size]

            // Check if the edge crosses the horizontal ray to the right of the point
            if ((a.y > y) != (b.y > y)) {
                val intersectX = (b.x - a.x) * (y - a.y) / (b.y - a.y + DEFAULT_TOLERANCE) + a.x
                if (x < intersectX) {
                    inside = !inside
                }
            }
        }

        return inside
    }

    /**
     * Finds the center of the inscribed circle (incenter) of a polygon.
     *
     * The incenter is the unique point that is equidistant to all sides of a convex polygon.
     * This method finds that point and ensures the radius is exactly the distance from the incenter to each side,
     * and also ensures the incenter is strictly inside the polygon, and that this distance is the same for all sides
     * within a given tolerance.
     *
     * @param vertices The vertices of the polygon for which to find the incenter.
     * @return The coordinates of the incenter (Vector3D), or null if not found (e.g., polygon is not convex, degenerate, or less than 3 vertices).
     */
    fun findInscribedCircleCenter(vertices: List<Vector3D>): Vector3D? {
        if (vertices.size < 3) return null

        val n = vertices.size

        // Select 3 consecutive points to compute 2 angle bisectors: ∠v0, ∠v1
        val v0 = vertices[0]
        val v1 = vertices[1]
        val v2 = vertices[2]

        // Compute the angle bisector at v0 (between the last, v0, and v1)
        val bisector1 = Lines.angleBisectorLine2D(vertices[(n - 1).boundIndex(n - 1)], v0, v1)
        // Compute the angle bisector at v1 (between v0, v1, and v2)
        val bisector2 = Lines.angleBisectorLine2D(v0, v1, v2)

        // The intersection of the two bisectors is the incenter
        val incenter = bisector1.intersection(bisector2) ?: return null

        // Check if the incenter is inside the polygon (important for non-convex polygons)
        if (!isPointInPolygon2D(incenter, vertices)) return null

        // Calculate the distance from the incenter to each edge
        val distances = (0 until n).map { i ->
            val p1 = vertices[i]
            val p2 = vertices[(i + 1) % n]
            incenter.distancePointToLineSegment(p1, p2)
        }

        val minDist = distances.minOrNull() ?: return null
        val maxDist = distances.maxOrNull() ?: return null

        // The incenter must not be too close to any edge (avoid degenerate cases)
        if (minDist <= DEFAULT_TOLERANCE) return null
        // The distances to all edges must be nearly equal (within tolerance)
        if (maxDist - minDist > DEFAULT_TOLERANCE) return null

        return incenter
    }

    fun findCircumscribedCircleCenter(doc: GeoDoc, centerName: String?, polygon: Polygon): Point? {
        val vertices = polygon.vertices()
        if (vertices.size < 3) return null

        // Use the first three vertices to define the circumscribed circle
        val plane = Planes.fromPoints(
            vertices[0].coordinates(), vertices[1].coordinates(), vertices[2].coordinates(), DEFAULT_PRECISION
        )
        val center = Plane.intersection(
            perpendicularBisectingPlane(vertices[0].coordinates(), vertices[1].coordinates()),
            perpendicularBisectingPlane(vertices[0].coordinates(), vertices[2].coordinates()),
            plane
        )?.toPoint(doc, centerName) ?: return null

        val radius = calculateDistance(center, vertices[0])
        for (v in vertices) {
            if (!Precision.equals(calculateDistance(center, v), radius, DEFAULT_TOLERANCE)) {
                return null
            }
        }
        return center
    }

    private fun perpendicularBisectingPlane(p1: Vector3D, p2: Vector3D): Plane {
        val midpoint = Vector3D.of((p1.x + p2.x) / 2, (p1.y + p2.y) / 2, (p1.z + p2.z) / 2)
        return Planes.fromPointAndNormal(midpoint, p2.subtract(p1).normalize(), DEFAULT_PRECISION)
    }

    fun calculateDistance(p1: Point, p2: Point): Double {
        val deltaX = p2.x - p1.x
        val deltaY = p2.y - p1.y
        return kotlin.math.sqrt(deltaX * deltaX + deltaY * deltaY)
    }
}
