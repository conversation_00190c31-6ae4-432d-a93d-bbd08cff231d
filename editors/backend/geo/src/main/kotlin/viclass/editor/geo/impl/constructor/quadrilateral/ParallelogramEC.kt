package viclass.editor.geo.impl.constructor.quadrilateral

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.constructor.ElementConstructor
import viclass.editor.geo.constructor.ElementListExtraction
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Parallelogram
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Quadrilateral
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.constructor.Intersections
import viclass.editor.geo.impl.constructor.Polygons.nextIndex
import viclass.editor.geo.impl.constructor.Polygons.previousIndex
import viclass.editor.geo.impl.constructor.Validations
import viclass.editor.geo.impl.constructor.extractFirstPossible
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.ParallelogramImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class ParallelogramEC : ElementConstructor<Parallelogram> {

    override fun outputType(): KClass<Parallelogram> {
        return Parallelogram::class
    }

    private enum class CGS {
        ByPointsName, FromPoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.ByPointsName.name)
                    .constraintDefault()
                    .build(),
            )
            .elTypes(Parallelogram::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Parallelogram> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }
            CGS.ByPointsName -> {
                Validations.validateNumConstraints(c, 0)
                constructFromName(doc, inputName!!, c)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Parallelogram> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Parallelogram::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }.sortedBy { it } != pointNames) throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val parallelogram = ParallelogramImpl(doc, inputName ?: points.map { it.name }.joinToString(""), points[0], points[1], points[2], points[3])
        val cr = ConstructionResultImpl<Parallelogram>()
        cr.addDependencies(points, true)
        cr.setResult(parallelogram)
        return cr;
    }

    private fun constructFromName(doc: GeoDoc, inputName: String, c: Construction): ConstructionResult<Parallelogram> {
        val p1: Point?
        val p2: Point?
        val p3: Point?

        val vertexGroup = ArrayList<List<Point>>()
        val names = NamePattern.extractPointName(Quadrilateral::class, inputName)

        val vertexes = names.map { it to doc.findElementByName(it, Point::class, c.ctIdx) }
        val numVertex = vertexes.count { it.second != null }
        val curIdx = vertexes.indexOfFirst { it.second != null }
        val prevIdx = previousIndex(curIdx, names)
        val nextIdx = nextIndex(curIdx, names)
        val next2Idx = nextIndex(nextIdx, names)

        if (numVertex == 3) {
            if (vertexes[nextIdx].second == null) {
                p1 = vertexes[next2Idx].second
                p2 = vertexes[prevIdx].second
                p3 = vertexes[curIdx].second
            } else if (vertexes[prevIdx].second == null) {
                p1 = vertexes[curIdx].second
                p2 = vertexes[nextIdx].second
                p3 = vertexes[next2Idx].second
            } else if (vertexes[curIdx].second == null) {
                p1 = vertexes[nextIdx].second
                p2 = vertexes[next2Idx].second
                p3 = vertexes[prevIdx].second
            } else {
                p1 = vertexes[prevIdx].second
                p2 = vertexes[curIdx].second
                p3 = vertexes[nextIdx].second
            }
            val v1 = p1!!.coordinates()
            val v2 = p2!!.coordinates()
            val v3 = p3!!.coordinates()

            val l14 = LineImpl(doc, null, p1, v2.vectorTo(v3))
            val l34 = LineImpl(doc, null, p3, v2.vectorTo(v1))

            val v4 = Intersections.of(l14, l34) ?: throw ConstructionException("Cannot construct parallelogram")
            val p4 = PointImpl(doc, names[prevIdx], v4)
            vertexGroup.add(listOf(p1, p2, p3, p4))

        } else {
            throw ConstructionException("Cannot construct parallelogram from 4 point")
        }

        val points = vertexGroup.firstOrNull()
            ?: throw ConstructionException("Cannot construct parallelogram")

        val cr = ConstructionResultImpl<Parallelogram>()
        cr.setResult(ParallelogramImpl(doc, inputName, points[0], points[1], points[2], points[3]))
        cr.addDependency(points[0], listOf(), true)
        cr.addDependency(points[1], listOf(), true)
        cr.addDependency(points[2], listOf(), true)
        cr.addDependency(points[3], listOf(), true)

        return cr
    }
}
