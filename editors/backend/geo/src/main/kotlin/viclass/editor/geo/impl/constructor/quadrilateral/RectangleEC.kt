package viclass.editor.geo.impl.constructor.quadrilateral

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.NamePattern.extractPointName
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Quadrilateral
import viclass.editor.geo.elements.Rectangle
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.entity.ParamKind.PK_Value
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.RectangleImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import viclass.editor.geo.impl.transformer.PointOnLineWithLengthTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class RectangleEC : ElementConstructor<Rectangle> {

    override fun outputType(): KClass<Rectangle> {
        return Rectangle::class
    }

    private enum class CGS {
        LineSegmentAndLength, FromPoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndLength.name)
                    .invisible()
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value"),
                        "tpl-LengthValue"
                    )
                    .constraintOptional(
                        2, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf(0, 1),
                        listOf("Value"),
                        "tpl-thShape"
                    )
                    .build(),
            )
            .elTypes(Rectangle::class)
            .build()
    }

    override fun construct(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Rectangle> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }

            CGS.LineSegmentAndLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLineSegmentAndLength(doc, inputName!!, c, c.params)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Rectangle> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Rectangle::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }
                    .sortedBy { it } != pointNames) throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val rectangle = RectangleImpl(
            doc,
            inputName ?: points.joinToString { it.name.toString() },
            points[0],
            points[1],
            points[2],
            points[3]
        )
        val cr = ConstructionResultImpl<Rectangle>()
        cr.addDependencies(points, true)
        cr.setResult(rectangle)
        return cr;
    }

    private fun constructFromLineSegmentAndLength(
        doc: GeoDoc,
        rectangleName: String,
        construction: Construction,
        parameters: List<ConstructionParams>
    ): ConstructionResult<Rectangle> {
        // Variables to hold extracted parameter values
        var selectedVertexIndex: Int? = null
        var startPoint: Point? = null
        var endPoint: Point? = null
        var sideLength: Double? = null

        // Extract parameters from the input list
        parameters.forEach { param ->
            when (param.paramDef.id) {
                aLineSegment -> {
                    // Extract the line segment's name and find its endpoints
                    val store = (param.specs.getParam(PK_Name)
                        ?: throw InvalidElementNameException("Parameter with name ${PK_Name} doesn't have any value")
                            ) as ParamStoreValue
                    val lineSegmentName = store.value
                    val endpointNames = NamePattern.extractPointName(LineSegment::class, lineSegmentName)
                    val endpoints = endpointNames.map { name ->
                        doc.findElementByName(name, Point::class, construction.ctIdx)
                    }
                    startPoint = endpoints[0]!!
                    endPoint = endpoints[1]!!
                }

                aValue -> when (param.specs.indexInCG) {
                    1 -> {
                        // Extract the side length value
                        val lengthExtraction =
                            extractFirstPossible<NumberExtraction<Double>>(
                                doc,
                                ParamKind.PK_Value,
                                param,
                                construction.ctIdx
                            )
                        sideLength = lengthExtraction.result
                    }

                    2 -> {
                        // Extract the index for the selected rectangle orientation (if provided)
                        val indexExtraction =
                            extractFirstPossible<NumberExtraction<Int>>(
                                doc,
                                ParamKind.PK_Value,
                                param,
                                construction.ctIdx
                            )
                        selectedVertexIndex = indexExtraction.result - 1
                    }
                }
            }
        }

        // Generate the names for the rectangle's vertices
        val vertexNames = extractPointName(Quadrilateral::class, rectangleName)
        val possibleRectangles = ArrayList<List<Point>>()

        // Compute possible third vertices for the rectangle
        val possibleThirdVertices =
            RightTriangles.computeRightTriangleAdjacentVertices(endPoint!!, startPoint!!, sideLength!!)

        if (selectedVertexIndex == null) {
            // If no orientation index is provided, try all possible rectangles
            possibleThirdVertices
                .map { PointImpl(doc, vertexNames[2], it) }
                .map { findVertexes(doc, vertexNames, startPoint, endPoint, it) }
                .filter { it.isNotEmpty() }
                .forEach { possibleRectangles.add(it) }
        } else {
            // If an orientation index is provided, select the corresponding rectangle
            val baseVector = createVectorByEl(doc, startPoint, endPoint)
            val chosenVertex = Orders.pointByParallelVector(
                baseVector,
                startPoint.coordinates(),
                possibleThirdVertices[0],
                possibleThirdVertices[1]
            )[selectedVertexIndex]
            val rectanglePoints =
                findVertexes(doc, vertexNames, startPoint, endPoint, PointImpl(doc, vertexNames[2], chosenVertex))
            possibleRectangles.add(rectanglePoints)
        }

        // Get the first valid rectangle or throw an exception if none found
        val rectanglePoints = possibleRectangles.firstOrNull()
            ?: throw ConstructionException("Cannot construct rectangle")

        // Helper function to set up transformation data for a point
        val setupTransform: (point: Point, root: Vector3D) -> Unit = { point, rootVector ->
            val rootArray = rootVector.toArray()
            val directionArray = rootVector.vectorTo(point.coordinates()).toArray()
            point.transformer = TransformMapping.fromClazz(PointOnLineWithLengthTransformer::class)
            point.transformData = PointOnLineWithLengthTransformData(
                lengthParamIdx = 1,
                lengthParamKind = PK_Value,
                nthParamIdx = 2,
                root = rootArray,
                unitVector = directionArray
            )
            point.movementPath = MovementLinePath(rootArray, directionArray)
        }

        // Set up transformation for the third and fourth vertices
        setupTransform(rectanglePoints[2], endPoint.coordinates())
        setupTransform(rectanglePoints[3], startPoint.coordinates())

        // Build the construction result and add dependencies
        val result = ConstructionResultImpl<Rectangle>()
        result.setResult(
            RectangleImpl(
                doc,
                rectangleName,
                rectanglePoints[0],
                rectanglePoints[1],
                rectanglePoints[2],
                rectanglePoints[3]
            )
        )
        result.addDependency(rectanglePoints[0], listOf(), true)
        result.addDependency(rectanglePoints[1], listOf(), true)
        result.addDependency(rectanglePoints[2], listOf(), true)
        result.addDependency(rectanglePoints[3], listOf(), true)

        return result
    }

    private fun findVertexes(doc: GeoDoc, names: List<String>, p1: Point, p2: Point, p3: Point): List<Point> {
        val vM = Points.calculateCenterPoint(p1, p3)
        val pM = PointImpl(doc, null, vM)
        val line_p2M = LineSegmentImpl(doc, null, p2, pM)

        val v4s = Lines.findPointOnlineWithLength(ConstraintParamDefManager.aLine, line_p2M, pM, line_p2M.length())
            ?.filter { it.distance(p2.coordinates()) > DEFAULT_TOLERANCE }

        v4s ?: return emptyList()

        val v4 = v4s.first()

        val p4 = PointImpl(doc, (names - setOf(p1.name!!, p2.name!!, p3.name!!)).first(), v4)

        return listOf(p1, p2, p3, p4)
    }
}
