package viclass.editor.geo.impl.constructor.quadrilateral

import org.koin.core.annotation.Singleton
import viclass.editor.geo.NamePattern
import viclass.editor.geo.constructor.*
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLine
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aLineSegment
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aPoint
import viclass.editor.geo.doc.ConstraintParamDefManager.Companion.aValue
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.*
import viclass.editor.geo.entity.ConstructorTemplate
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.entity.ParamKind.PK_Name
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.ExtractionFailedException
import viclass.editor.geo.exceptions.InvalidElementNameException
import viclass.editor.geo.impl.constructor.*
import viclass.editor.geo.impl.doc.ConstraintGroupBuilder
import viclass.editor.geo.impl.doc.ConstructorTemplateBuilder
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.TrapezoidImpl
import kotlin.reflect.KClass


/**
 *
 * <AUTHOR>
 */
@Singleton
class TrapezoidEC : ElementConstructor<Trapezoid> {

    override fun outputType(): KClass<Trapezoid> {
        return Trapezoid::class
    }

    private enum class CGS {
        LineSegmentAndOppositeSideLength, FromPoints
    }

    override fun template(): ConstructorTemplate {
        return ConstructorTemplateBuilder.create(this)
            .cgs(
                ConstraintGroupBuilder.create()
                    .name(CGS.FromPoints.name)
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aPoint]!!,
                        listOf("Points"),
                        "tpl-Points"
                    )
                    .build(),
                ConstraintGroupBuilder.create()
                    .name(CGS.LineSegmentAndOppositeSideLength.name)
                    .invisible()
                    .constraint(
                        0, ConstraintParamDefManager.instance()[aLineSegment]!!,
                        listOf("NameOfLineSegment"),
                        "tpl-FromLineSegment"
                    )
                    .constraint(
                        1, ConstraintParamDefManager.instance()[aValue]!!,
                        listOf("Value"),
                        "tpl-OppositeSideLength"
                    )
                    .build(),
            )
            .elTypes(Rhombus::class)
            .build()
    }

    override fun construct(doc: GeoDoc, inputName: String?, c: Construction): ConstructionResult<Trapezoid> {
        return when (CGS.valueOf(c.cgName)) {
            CGS.FromPoints -> {
                Validations.validateNumConstraints(c, 1)
                constructFromPoints(doc, inputName, c)
            }

            CGS.LineSegmentAndOppositeSideLength -> {
                Validations.validateNumConstraints(c, 2)
                constructFromLineSegmentAndOppositeSideLength(doc, inputName!!, c, c.params)
            }
        }
    }

    private fun constructFromPoints(
        doc: GeoDoc,
        inputName: String?,
        c: Construction
    ): ConstructionResult<Trapezoid> {
        val ext = extractFirstPossible<ElementListExtraction<Point>>(doc, PK_Name, c.params[0], c.ctIdx)
        inputName?.let {
            val pointNames = NamePattern.extractPointName(Trapezoid::class, it).sortedBy { it }
            if (ext.result.map { it.result()?.name }
                    .sortedBy { it } != pointNames) throw InvalidElementNameException("input name is not match with input points")
        }
        val points = ext.result.map { it.result()!! }
        val trapezoid = TrapezoidImpl(
            doc,
            inputName ?: points.map { it.name }.joinToString(""),
            points[0],
            points[1],
            points[2],
            points[3]
        )
        val cr = ConstructionResultImpl<Trapezoid>()
        cr.addDependencies(points, true)
        cr.setResult(trapezoid)
        return cr;
    }

    private fun constructFromLineSegmentAndOppositeSideLength(
        doc: GeoDoc, inputName: String, c: Construction, params: List<ConstructionParams>
    ): ConstructionResult<Trapezoid> {
        var p1: Point? = null
        var p2: Point? = null
        var length: Double? = null

        params.forEach { p ->
            when (p.paramDef.id) {
                aLineSegment -> {
                    val store: ParamStoreValue = ((p.specs.getParam(ParamKind.PK_Name)
                        ?: throw ExtractionFailedException("Parameter with name ${ParamKind.PK_Name} doesn't have any value"))
                            as ParamStoreValue)
                    val lineNameSubmit = store.value
                    val pointsName = NamePattern.extractPointName(LineSegment::class, lineNameSubmit)
                    val points = pointsName.map { n -> doc.findElementByName(n, Point::class, c.ctIdx) }
                    p1 = points[0]!!
                    p2 = points[1]!!
                }

                aValue -> if (p.specs.indexInCG == 1) {
                    val extractedResult =
                        extractFirstPossible<NumberExtraction<Double>>(doc, ParamKind.PK_Value, p, c.ctIdx)
                    length = extractedResult.result
                }
            }
        }

        val names = NamePattern.extractPointName(Quadrilateral::class, inputName)

        val p3 = doc.findElementByName(names[2], Point::class, c.ctIdx)
            ?: throw ElementNotExistInDocumentException("Cannot construct Trapezoid")

        val v1 = p1!!.coordinates()
        val v2 = p2!!.coordinates()

        val line34 = LineImpl(doc, null, p3, v2.vectorTo(v1))

        val v4s = Lines.findPointOnlineWithLength(aLine, line34, p3, length!!)!!

        val v4 = Orders.pointsOnParallelVector(line34.parallelVector, v4s[0], v4s[1])[1]

        val p4 = PointImpl(doc, names[3], v4)

        val cr = ConstructionResultImpl<Trapezoid>()
        cr.setResult(TrapezoidImpl(doc, inputName, p1!!, p2!!, p3, p4))
        cr.addDependency(p1!!, listOf(), true)
        cr.addDependency(p2!!, listOf(), true)
        cr.addDependency(p3, listOf(), true)
        cr.addDependency(p4, listOf(), true)

        return cr
    }
}
