package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.ConstraintParamDefManager
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dim
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.elements.Triangle
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.elements.createVectorByEl
import kotlin.math.*


object RightTriangles {

    /**
     * Computes the two potential third vertices of a right triangle, given the hypotenuse and a leg's length.
     *
     * This function takes the endpoints of the hypotenuse and the length of one leg, and calculates
     * the coordinates of the two possible right-angle vertices that would complete the triangle.
     *
     * It first validates the leg's length against the hypotenuse, then uses geometric principles
     * and the quadratic formula to derive the solutions.
     *
     * @param adjacentSideName Identifies the leg adjacent to the hypotenuse's starting point, used to orient the calculations.
     * @param hypotenuseStart The starting point of the hypotenuse.
     * @param hypotenuseEnd The ending point of the hypotenuse.
     * @param legLength The length of the leg adjacent to the hypotenuse's starting point.
     * @return A list of two Vector3D objects representing the potential third vertices, or null if no solution exists.
     */
    fun computeRightTriangleThirdVertices(
        adjacentSideName: String,
        hypotenuseStart: Point,
        hypotenuseEnd: Point,
        legLength: Double
    ): List<Vector3D>? {
        // Ensure the leg's length is valid.
        val hypotenuseDistance = Distances.of(hypotenuseStart, hypotenuseEnd)
        if (legLength > hypotenuseDistance) return null

        // Correctly orient the hypotenuse based on the adjacent leg.
        var start = hypotenuseStart
        var end = hypotenuseEnd
        if (adjacentSideName.contains(end.name!!)) {
            start = hypotenuseEnd
            end = hypotenuseStart
        }

        // Extract coordinates for clarity.
        val x1 = start.x
        val y1 = start.y
        val x2 = end.x
        val y2 = end.y

        // Pre-calculate values for the quadratic equation.
        val deltaX = x2 - x1
        val deltaY = y2 - y1
        val legSquared = legLength * legLength
        val hypotenuseSquared = hypotenuseDistance * hypotenuseDistance
        val constant = x1.pow(2) - x2.pow(2) + y1.pow(2) - y2.pow(2)

        // Calculate the discriminant to determine the nature of the solutions.
        val discriminant = sqrt(
            (-4 * deltaY.pow(2) * y1.pow(2)) +
                    ((-8 * deltaX * deltaY * x1) - 4 * deltaY * (hypotenuseSquared - legSquared) + 4 * deltaY * legSquared - 4 * deltaY * constant) * y1 -
                    4 * deltaX.pow(2) * x1.pow(2) +
                    ((-4 * deltaX * (hypotenuseSquared - legSquared)) + 4 * deltaX * legSquared - 4 * deltaX * constant) * x1 -
                    (hypotenuseSquared - legSquared).pow(2) +
                    (2 * legSquared - 2 * constant) * (hypotenuseSquared - legSquared) -
                    legSquared.pow(2) +
                    (2 * constant + 4 * deltaY.pow(2) + 4 * deltaX.pow(2)) * legSquared -
                    constant.pow(2)
        )

        // Calculate the two possible third vertex coordinates.
        val vertex1X =
            -(deltaY * discriminant + 2 * deltaX * deltaY * y1 - 2 * deltaY.pow(2) * x1 + deltaX * (hypotenuseSquared - legSquared) - deltaX * legSquared + deltaX * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))
        val vertex1Y =
            +(deltaX * discriminant + 2 * deltaX.pow(2) * y1 - 2 * deltaX * deltaY * x1 - deltaY * (hypotenuseSquared - legSquared) + deltaY * legSquared - deltaY * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))
        val vertex2X =
            +(deltaY * discriminant - 2 * deltaX * deltaY * y1 + 2 * deltaY.pow(2) * x1 - deltaX * (hypotenuseSquared - legSquared) + deltaX * legSquared - deltaX * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))
        val vertex2Y =
            -(deltaX * discriminant - 2 * deltaX.pow(2) * y1 + 2 * deltaX * deltaY * x1 + deltaY * (hypotenuseSquared - legSquared) + deltaY * legSquared - deltaY * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))

        // Return the potential vertices as Vector3D objects.
        return listOf(Vector3D.of(vertex1X, vertex1Y, 0.0), Vector3D.of(vertex2X, vertex2Y, 0.0))
    }

    fun findAdjacentVertexOfRightTriangleWhenKnowARightSideAndHypotenuseLength(
        rightVertex: Point, adjacentVertex: Point, length: Double
    ): List<Vector3D> {
        val sideLength1 = Distances.of(rightVertex, adjacentVertex)
        val sideLength2 = sqrt(length.pow(2) - sideLength1.pow(2))
        return computeRightTriangleAdjacentVertices(
            rightVertex,
            adjacentVertex,
            sideLength2
        )
    }

    /**
     * Computes the two possible adjacent vertices of a right triangle, given the right vertex,
     * an adjacent vertex, and the length of the adjacent side.
     *
     * This function determines the coordinates of the two potential adjacent vertices that complete
     * a right triangle, given the right-angle vertex, one known adjacent vertex, and the length
     * of the side connecting the right vertex to the desired adjacent vertex.
     *
     * @param rightVertex The vertex at the right angle.
     * @param knownAdjacentVertex The known adjacent vertex.
     * @param adjacentSideLength The length of the side adjacent to the right vertex.
     * @return A list of two Vector3D objects representing the potential adjacent vertices.
     */
    fun computeRightTriangleAdjacentVertices(
        rightVertex: Point,
        knownAdjacentVertex: Point,
        adjacentSideLength: Double
    ): List<Vector3D> {
        // Extract coordinates for clarity.
        val rightVertexX = rightVertex.x
        val rightVertexY = rightVertex.y

        // Calculate the vector from the right vertex to the known adjacent vertex.
        val deltaX = knownAdjacentVertex.x - rightVertexX
        val deltaY = knownAdjacentVertex.y - rightVertexY

        // Calculate the components of the vector representing the adjacent side.
        val adjacentSideX1: Double
        val adjacentSideX2: Double
        val adjacentSideY1: Double
        val adjacentSideY2: Double

        // Handle the case where deltaX is close to zero (vertical line).
        if (abs(deltaX) < DEFAULT_TOLERANCE) {
            adjacentSideY1 = 0.0
            adjacentSideY2 = 0.0
            adjacentSideX1 = adjacentSideLength
            adjacentSideX2 = -adjacentSideLength
        } else {
            // Calculate the Y component of the adjacent side vector.
            val adjacentSideY =
                sqrt((adjacentSideLength * adjacentSideLength * deltaX * deltaX) / (deltaY * deltaY + deltaX * deltaX))
            adjacentSideY1 = adjacentSideY
            adjacentSideY2 = -adjacentSideY

            // Calculate the X component of the adjacent side vector.
            adjacentSideX1 = -adjacentSideY1 * deltaY / deltaX
            adjacentSideX2 = -adjacentSideY2 * deltaY / deltaX
        }

        // Calculate the coordinates of the two possible adjacent vertices.
        val firstAdjacentX = rightVertexX + adjacentSideX1
        val firstAdjacentY = rightVertexY + adjacentSideY1

        val secondAdjacentX = rightVertexX + adjacentSideX2
        val secondAdjacentY = rightVertexY + adjacentSideY2

        // Return the two possible adjacent vertices as Vector3D objects.
        return listOf(
            Vector3D.of(firstAdjacentX, firstAdjacentY, 0.0),
            Vector3D.of(secondAdjacentX, secondAdjacentY, 0.0)
        )
    }

    /**
     * Computes the two possible right vertices of a right triangle given the hypotenuse (as a LineSegment) and an adjacent angle.
     *
     * This function is a convenience wrapper around `computeRightTriangleRightVertices`, which calculates
     * the coordinates of the two possible right vertices of a right triangle, given the hypotenuse
     * (represented as a LineSegment) and the angle adjacent to the hypotenuse's starting point.
     *
     * @param adjacentVertexName The name of the adjacent vertex, used to determine the order of the line segment's points.
     * @param segment The LineSegment representing the hypotenuse of the right triangle.
     * @param adjacentAngle The angle adjacent to the hypotenuse's starting point (in radians).
     * @return A list containing the two possible right vertices as Vector3D objects, or null if no solution exists.
     */
    fun findRightVertexWithHypotenuseAndAdjacentAngle(
        adjacentVertexName: String,
        segment: LineSegment,
        adjacentAngle: Double
    ): List<Vector3D>? {
        // Delegate the calculation to the computeRightTriangleRightVertices function.
        return computeRightTriangleRightVertices(
            adjacentVertexName, segment.p1, segment.p2, adjacentAngle
        )
    }

    /**
     * Computes the two possible right vertices of a right triangle given the hypotenuse and an adjacent angle.
     *
     * This function calculates the coordinates of the two possible right vertices of a right triangle,
     * given the two endpoints of the hypotenuse (hypotenuseStart and hypotenuseEnd) and the angle
     * adjacent to the hypotenuse's starting point (adjacentAngle).
     *
     * The function first validates the adjacent angle. Then, it sets up a system of equations based
     * on trigonometric relationships and the distance formula. The system of equations is then solved
     * to find the coordinates of the two possible right vertices.
     *
     * @param adjacentVertexName The name of the adjacent vertex, used to determine the order of hypotenuseStart and hypotenuseEnd.
     * @param hypotenuseStart The first endpoint of the hypotenuse.
     * @param hypotenuseEnd The second endpoint of the hypotenuse.
     * @param adjacentAngle The angle adjacent to the hypotenuse's starting point (in radians).
     * @return A list containing the two possible right vertices as Vector3D objects, or null if no solution exists.
     */
    fun computeRightTriangleRightVertices(
        adjacentVertexName: String,
        hypotenuseStart: Point,
        hypotenuseEnd: Point,
        adjacentAngle: Double
    ): List<Vector3D>? {
        // Validate the adjacent angle.
        if (adjacentAngle <= 0.0 || adjacentAngle >= PI / 2.0) return null

        // Ensure the correct hypotenuse orientation.
        var start = hypotenuseStart
        var end = hypotenuseEnd
        if (adjacentVertexName == end.name!!) {
            start = hypotenuseEnd
            end = hypotenuseStart
        }

        // Extract coordinates for clarity.
        val startX = start.x
        val startY = start.y
        val endX = end.x
        val endY = end.y

        // Pre-calculate values for the quadratic equation.
        val deltaX = endX - startX
        val deltaY = endY - startY
        val hypotenuseSquared = deltaX.pow(2) + deltaY.pow(2)
        val legSquared = hypotenuseSquared * sin(adjacentAngle).pow(2)
        val adjacentSquared = hypotenuseSquared * cos(adjacentAngle).pow(2)
        val constant = startX.pow(2) - endX.pow(2) + startY.pow(2) - endY.pow(2)

        // Calculate the discriminant of the quadratic equation.
        val discriminant = sqrt(
            (-4 * deltaY.pow(2) * startY.pow(2)) +
                    ((-8 * deltaX * deltaY * startX) - 4 * deltaY * adjacentSquared + 4 * deltaY * legSquared - 4 * deltaY * constant) * startY -
                    4 * deltaX.pow(2) * startX.pow(2) +
                    ((-4 * deltaX * adjacentSquared) + 4 * deltaX * legSquared - 4 * deltaX * constant) * startX -
                    adjacentSquared.pow(2) +
                    (2 * legSquared - 2 * constant) * adjacentSquared -
                    legSquared.pow(2) +
                    (2 * constant + 4 * deltaY.pow(2) + 4 * deltaX.pow(2)) * legSquared -
                    constant.pow(2)
        )

        // Calculate the coordinates of the two possible right vertices.
        val firstVertexX =
            -(deltaY * discriminant + 2 * deltaX * deltaY * startY - 2 * deltaY.pow(2) * startX + deltaX * adjacentSquared - deltaX * legSquared + deltaX * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))
        val firstVertexY =
            +(deltaX * discriminant + 2 * deltaX.pow(2) * startY - 2 * deltaX * deltaY * startX - deltaY * adjacentSquared + deltaY * legSquared - deltaY * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))
        val secondVertexX =
            +(deltaY * discriminant - 2 * deltaX * deltaY * startY + 2 * deltaY.pow(2) * startX - deltaX * adjacentSquared + deltaX * legSquared - deltaX * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))
        val secondVertexY =
            -(deltaX * discriminant - 2 * deltaX.pow(2) * startY + 2 * deltaX * deltaY * startX + deltaY * adjacentSquared + deltaY * legSquared - deltaY * constant) / (2 * deltaY.pow(
                2
            ) + 2 * deltaX.pow(2))

        // Return the two possible right vertices as Vector3D objects.
        return listOf(Vector3D.of(firstVertexX, firstVertexY, 0.0), Vector3D.of(secondVertexX, secondVertexY, 0.0))
    }
}


/**
 *
 * <AUTHOR>
 */
object Triangles {

    /**
     * Finds the intersection point of the angle bisectors of a given Triangle.
     *
     * This function calculates the incenter of a Triangle, which is the point where the
     * three angle bisectors intersect. It uses the weighted average of the triangle's
     * vertices, where the weights are the lengths of the opposite sides.
     *
     * @param doc The GeoDoc to which the resulting Point belongs.
     * @param centerName The name to assign to the resulting Point, or null if no name is needed.
     * @param triangle The Triangle for which to find the incenter.
     * @return The Point representing the intersection of the angle bisectors (incenter).
     */
    fun findBisectorsIntersection(doc: GeoDoc, centerName: String?, triangle: Triangle): Point {
        val vertices = triangle.vertices()

        // Calculate the lengths of the sides opposite each vertex.
        val lengths = (0..2).map { i ->
            Distances.of(
                vertices[(i + 1) % 3],
                vertices[(i + 2) % 3]
            ) // Calculate distance between the next two vertices.
        }

        // Calculate the total length of the sides.
        val totalLength = lengths.sum()

        /**
         * Calculates the coordinate of the incenter along a given dimension.
         *
         * @param dim The dimension (X, Y, or Z) for which to calculate the coordinate.
         * @return The coordinate of the incenter along the given dimension.
         */
        fun coord(dim: Dim): Double {
            // Calculate the weighted average of the vertex coordinates along the given dimension.
            return (0..2).sumOf { i -> lengths[i] * vertices[i].coord(dim) } / totalLength
        }

        // Create a new PointImpl at the calculated incenter coordinates.
        return PointImpl(doc, centerName, coord(Dim.X), coord(Dim.Y), coord(Dim.Z))
    }

    /**
     * Computes the possible third vertices of a triangle given two vertices, a side length, and an angle.
     *
     * This function calculates the coordinates of the possible third vertices of a triangle,
     * given two known vertices (point1 and point2), the length of the side adjacent to point2,
     * and the angle at the vertex specified by angleVertexName.
     *
     * The function first determines the order of the points based on the angle vertex name.
     * It then adjusts the angle to ensure it's within the range of (0, PI/2) for right triangle calculations.
     *
     * It then uses `computeRightTriangleRightVertices` to find the possible right vertices based on
     * the hypotenuse and adjusted angle. Subsequently, it constructs lines from point2 to these
     * right vertices. Finally, it uses `findPointOnlineWithLength` to find points along these lines
     * that satisfy the given side length and angle criteria.
     *
     * @param doc The GeoDoc context.
     * @param point1 The first known vertex.
     * @param point2 The second known vertex.
     * @param sideLength The length of the side adjacent to point2.
     * @param angleVertexName The name of the vertex where the angle is located.
     * @param angle The angle at the specified vertex (in radians).
     * @return A list of Vector3D objects representing the possible third vertices, or null if no solution exists.
     */
    fun findVertexWhenKnownASideAAngleALength(
        doc: GeoDoc,
        point1: Point,
        point2: Point,
        sideLength: Double,
        angleVertexName: String,
        angle: Double
    ): List<Vector3D>? {
        // Determine the order of points based on the angle vertex name.
        val (startPoint, hypotenuseStartPoint) = if (point1.name == angleVertexName) point2 to point1 else point1 to point2

        // Adjust the angle to be acute for right triangle calculations.
        val adjustedAngle = if (angle < PI / 2.0) angle else PI - angle

        // Find the possible right vertices using the adjusted angle.
        val possibleRightVertices = RightTriangles.computeRightTriangleRightVertices(
            hypotenuseStartPoint.name!!, startPoint, hypotenuseStartPoint, adjustedAngle
        ) ?: return null

        // Create lines from point2 to the possible right vertices.
        val lines = possibleRightVertices.map { vertex ->
            LineImpl(startPoint.doc, null, hypotenuseStartPoint, vertex.vectorTo(hypotenuseStartPoint.coordinates()))
        }

        // Find points along the lines that satisfy the side length and angle criteria.
        return lines.mapNotNull { line ->
            Lines.findPointOnlineWithLength(ConstraintParamDefManager.aLine, line, hypotenuseStartPoint, sideLength)
                ?.firstOrNull { potentialVertex ->
                    // Check if the angle between the vectors matches the given angle.
                    createVectorByEl(doc, startPoint, hypotenuseStartPoint).angleTo(
                        potentialVertex.vectorTo(
                            hypotenuseStartPoint.coordinates()
                        )
                    ).toFloat() == angle.toFloat()
                }
        }
    }

    /**
     * Computes the excenter of a triangle opposite a given point.
     *
     * This function calculates the excenter of a triangle, which is the intersection point of
     * the external angle bisectors of two angles and the internal angle bisector of the third angle.
     *
     * It takes a triangle and a point opposite to the desired excenter as input.
     * It first validates that the opposite point is one of the triangle's vertices.
     * Then, it calculates the incenter of the triangle and constructs the internal angle bisector
     * from the opposite vertex to the incenter.
     *
     * It then constructs an external angle bisector. It does this by creating a line parallel to one of the sides, rotating it 90 degrees, and then finding a line that intersects with the opposite vertex.
     *
     * Finally, it finds the intersection of these two bisectors, which is the excenter.
     *
     * @param doc The GeoDoc context.
     * @param centerName The name to assign to the excenter point, or null if no name is needed.
     * @param triangle The triangle.
     * @param oppositePoint The point opposite to the desired excenter.
     * @return The excenter as a Point object.
     * @throws ConstructionException if the opposite point is not a vertex of the triangle or if lines are parallel.
     */
    fun findInscribedCenter(doc: GeoDoc, centerName: String?, triangle: Triangle, oppositePoint: Point): Point {
        // Get the vertices of the triangle.
        val vertices = triangle.vertices()

        // Find the vertex opposite to the given point.
        val oppositeVertex = vertices.find { it.name == oppositePoint.name }
            ?: throw ConstructionException("Điểm đối diện phải là một trong các đỉnh của tam giác") // "Opposite point must be one of the triangle's vertices"

        // Get the other two vertices.
        val otherVertices = vertices.filter { it != oppositeVertex }
        val p1 = otherVertices[0]

        // Find the incenter of the triangle.
        val inscribedCenterCoord = Polygons.findInscribedCircleCenter(triangle.vertices().map { it.coordinates() })
            ?: throw ConstructionException("Hai cạnh song song") // "Two sides are parallel"
        val inscribedCenter = PointImpl(doc, centerName, inscribedCenterCoord)

        // Construct the internal angle bisector from the opposite vertex to the incenter.
        val internalBisector = Lines.getLineViFrom2Points(doc, null, oppositeVertex, inscribedCenter)

        // Construct the external angle bisector.
        val rotatedParallelVector = Lines.getLineViFrom2Points(doc, null, p1, inscribedCenter)
            .parallelVector.rotate90Degrees()
        val externalBisector = rotatedParallelVector.toLineVi(doc, null, p1, inscribedCenter)

        // Find the intersection of the two bisectors (excenter).
        val intersection = Lines.findIntersection(internalBisector, externalBisector)
            ?: throw ConstructionException("Có hai cạnh song song") // "Two sides are parallel"
        return PointImpl(doc, centerName, intersection.x, intersection.y, intersection.z)
    }

    fun findHypotenusePointsOfRightTriangle(a: Vector3D, b: Vector3D, c: Vector3D): Pair<Vector3D, Vector3D> {
        val ab = b - a
        val ac = c - a
        val bc = c - b

        return when {
            ab.dot(ac).absoluteValue < 1e-8 -> Pair(b, c) // Góc vuông tại A → cạnh huyền là BC
            ab.dot(bc).absoluteValue < 1e-8 -> Pair(a, c) // Góc vuông tại B → cạnh huyền là AC
            ac.dot(bc).absoluteValue < 1e-8 -> Pair(a, b) // Góc vuông tại C → cạnh huyền là AB
            else -> throw IllegalArgumentException("Ba điểm không tạo thành tam giác vuông")
        }
    }

    fun isRightIsoscelesTriangle(
        pointA: Vector3D,
        pointB: Vector3D,
        pointC: Vector3D,
    ): Boolean {
        val ab2 = pointA.vectorTo(pointB).normSq()
        val bc2 = pointB.vectorTo(pointC).normSq()
        val ca2 = pointC.vectorTo(pointA).normSq()

        val squaredSides = listOf(ab2, bc2, ca2).sorted()

        // Right triangle check: a^2 + b^2 ≈ c^2
        // Isosceles check: two sides must be equal (with tolerance)
        val isRight = abs(squaredSides[2] - (squaredSides[0] + squaredSides[1])) < DEFAULT_TOLERANCE
        val isIsosceles = abs(squaredSides[0] - squaredSides[1]) < DEFAULT_TOLERANCE ||
                abs(squaredSides[1] - squaredSides[2]) < DEFAULT_TOLERANCE ||
                abs(squaredSides[0] - squaredSides[2]) < DEFAULT_TOLERANCE

        return isRight && isIsosceles
    }
}
