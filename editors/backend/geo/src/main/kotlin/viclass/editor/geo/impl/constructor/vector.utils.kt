package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin

operator fun Vector3D.plus(other: Vector3D): Vector3D = Vector3D.of(x + other.x, y + other.y, z + other.z)

operator fun Vector3D.minus(other: Vector3D): Vector3D = Vector3D.of(x - other.x, y - other.y, z - other.z)

operator fun Vector3D.times(scalar: Double): Vector3D = Vector3D.of(x * scalar, y * scalar, z * scalar)

operator fun Vector3D.times(other: Vector3D): Vector3D {
    return Vector3D.of(
        this.x * other.x,
        this.y * other.y,
        this.z * other.z
    )
}

operator fun Vector3D.div(scalar: Double): Vector3D = Vector3D.of(x / scalar, y / scalar, z / scalar)

/**
 * Returns new point rotated by given angle around given center point.
 * If center point is omitted, rotates around zero point (0,0).
 * Positive value of angle defines rotation in counterclockwise direction,
 * negative angle defines rotation in clockwise direction
 * @param {number} angle - angle in radians
 * @param {Point} [center=(0,0)] center
 * @returns {Point}
 */
fun Vector3D.rotate(angleRadian: Double, center: Vector3D = Vector3D.ZERO): Vector3D {
    val cosTheta = cos(angleRadian)
    val sinTheta = sin(angleRadian)
    val x_rot = center.x + (this.x - center.x) * cosTheta - (this.y - center.y) * sinTheta
    val y_rot = center.y + (this.x - center.x) * sinTheta + (this.y - center.y) * cosTheta

    return Vector3D.of(x_rot, y_rot, 0.0)
}

fun Vector3D.rotateAroundZ(theta: Double): Vector3D {
    val cosTheta = cos(theta)
    val sinTheta = sin(theta)
    return Vector3D.of(x * cosTheta - y * sinTheta, x * sinTheta + y * cosTheta, z)
}

fun Vector3D.rotateRight90(): Vector3D {
    return Vector3D.of(this.y, -this.x, this.z)
}

fun Vector3D.rotateLeft90(): Vector3D {
    return Vector3D.of(-this.y, this.x, this.z)
}

fun Vector3D.rotateRight45(): Vector3D {
    return this.rotateAroundZ(-Math.PI / 4)
}

fun Vector3D.rotateLeft45(): Vector3D {
    return this.rotateAroundZ(Math.PI / 4)
}

fun Vector3D.rotateRight(radian: Double): Vector3D {
    return this.rotateAroundZ(-radian)
}

fun Vector3D.rotateLeft(radian: Double): Vector3D {
    return this.rotateAroundZ(radian)
}

/**
 * Return angle between this vector and other vector. <br/>
 * Angle is measured from 0 to 2*PI in the counterclockwise direction
 * from current vector to another.
 * @param {Vector} v Another vector
 * @returns {number}
 */
fun Vector3D.angleTo(v: Vector3D): Double {
    val norm1 = this.normalize()
    val norm2 = v.normalize()
    var angle = atan2(norm1.crossProduct(norm2), norm1.dot(norm2))
    if (angle < 0) angle += 2 * Math.PI;
    return angle
}

/**
 * Returns vector product (cross product) of two vectors <br/>
 * <code>cross_product = (this x v)</code>
 * @param {Vector} v Other vector
 * @returns {number}
 */
fun Vector3D.crossProduct(v: Vector3D): Double {
    return (this.x * v.y - this.y * v.x);
}

fun Vector3D.toLineVi(doc: GeoDoc, name: String?, p1: Point, p2: Point? = null): LineVi {
    return LineImpl(doc, name, p1, this, p2)
}

fun Vector3D.toPoint(doc: GeoDoc, name: String?): Point {
    return PointImpl(doc, name, this.x, this.y, this.z)
}

fun Vector3D.rotate90Degrees(): Vector3D {
    return Vector3D.of(-this.y, this.x, this.z)
}
