package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.DistanceTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.transformer.Transformer

class DistanceTransformer constructor(): Transformer<DistanceTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: DistanceTransformData, pos: Vector3D) {
        val distance = Distances.of(pos, Vector3D.of(transformData.root))
        val ps = c.params[transformData.targetParamIdx].specs
            .getParam(ParamKind.PK_Value) as ParamStoreValue
        ps.value = distance.toString()
    }
}