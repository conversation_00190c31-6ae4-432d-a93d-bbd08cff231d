package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.MoveAngleTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.transformer.Transformer

/**
 * param store is an angle
 */
class MoveAngleTransformer constructor(): Transformer<MoveAngleTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: MoveAngleTransformData, pos: Vector3D) {
        val center = Vector3D.of(transformData.root)
        val source = Vector3D.of(transformData.source)
        val vectorC0 = center.vectorTo(Vector3D.of(center.x+10, center.y, center.z))
        val vectorCS = center.vectorTo(source)
        val vectorCPos = center.vectorTo(pos)
        val angleP = vectorC0.angleTo(vectorCS)
        val anglePos = vectorC0.angleTo(vectorCPos)
        val angleDelta = anglePos - angleP
        val ps = c.params[transformData.targetParamIdx].specs.getParam(ParamKind.PK_Value) as ParamStoreValue
        val oldAngle = ps.value.toDouble()
        ps.value = (oldAngle + angleDelta).toString()
    }
}