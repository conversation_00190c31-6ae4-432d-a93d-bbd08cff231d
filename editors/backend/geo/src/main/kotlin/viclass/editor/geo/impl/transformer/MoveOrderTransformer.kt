package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreArray
import viclass.editor.geo.dbentity.transformdata.MoveOrderTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.impl.constructor.rotate
import viclass.editor.geo.transformer.Transformer

/**
 * param store is a position = [x, y, z]
 */
class MoveOrderTransformer constructor(): Transformer<MoveOrderTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: MoveOrderTransformData, pos: Vector3D) {
        val center = Vector3D.of(transformData.center)
        val source = Vector3D.of(transformData.source)
        val vectorC0 = center.vectorTo(Vector3D.of(center.x+10, center.y, center.z))
        val vectorCS = center.vectorTo(source)
        val vectorCPos = center.vectorTo(pos)
        val angleP = vectorC0.angleTo(vectorCS)
        val anglePos = vectorC0.angleTo(vectorCPos)
        val angleDelta = anglePos - angleP
        val unitVectorPos = vectorCPos.normalize()
        val k = vectorCPos.x / unitVectorPos.x

        transformData.targetParamIdx.forEach { paramIdx ->
            val ps = c.params[paramIdx].specs.getParam(ParamKind.PK_Value) as ParamStoreArray
            val p = Vector3D.of(ps.values[0].toDouble(), ps.values[1].toDouble(), ps.values.getOrNull(2)?.toDouble() ?: .0)
            val rotateP1 = p.rotate(angleDelta, center)
            val unitVectorNewP1 = center.vectorTo(rotateP1).normalize()
            val p1New = unitVectorNewP1.multiply(k).add(center)
            ps.values = p1New.toArray().map { it.toString() }
        }
    }
}