package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnArcWithAngleTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.impl.constructor.angleTo
import viclass.editor.geo.impl.constructor.degree
import viclass.editor.geo.transformer.Transformer

/**
 * calculate angle: (AB = k * vectorUnit)
 */
class PointOnArcWithAngleTransformer constructor(): Transformer<PointOnArcWithAngleTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnArcWithAngleTransformData, pos: Vector3D) {
        val vC = Vector3D.of(transformData.center)
        val vS = transformData.pS?.let { Vector3D.of(it) } ?: Vector3D.of(vC.x + 10, vC.y, vC.z)
        val vec = vC.vectorTo(pos)
        val vecS = vC.vectorTo(vS)
        val angle = vecS.angleTo(vec).let { if (transformData.degree == true) degree(it) else it }

        val ps = c.params[transformData.targetParamIdx].specs
            .getParam(transformData.paramKind) as ParamStoreValue
        ps.value = angle.toString()
    }
}