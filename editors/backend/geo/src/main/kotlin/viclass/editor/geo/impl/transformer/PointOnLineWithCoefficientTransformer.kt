package viclass.editor.geo.impl.transformer

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.dbentity.paramstore.ParamStoreValue
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithCoefficientTransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.impl.constructor.DEFAULT_PRECISION
import viclass.editor.geo.impl.constructor.rotate90Degrees
import viclass.editor.geo.transformer.Transformer

/**
 * calculate k: (AB = k * vectorUnit)
 */
class PointOnLineWithCoefficientTransformer constructor(): Transformer<PointOnLineWithCoefficientTransformData> {
    override fun apply(doc: GeoDoc, c: Construction, transformData: PointOnLineWithCoefficientTransformData, pos: Vector3D) {
        val root = Vector3D.of(transformData.rootPoint)
        val unitVector = Vector3D.of(transformData.unitVector)
        val line = Lines3D.fromPointAndDirection(root, unitVector, DEFAULT_PRECISION)
        val line2 = Lines3D.fromPointAndDirection(pos, unitVector.rotate90Degrees(), DEFAULT_PRECISION)
        val i = line.intersection(line2)
        val newVector = root.vectorTo(i)
        val coefficient = newVector.x / unitVector.x
        val ps = c.params[transformData.targetParamIdx].specs
            .getParam(transformData.paramKind) as ParamStoreValue
        ps.value = coefficient.toString()
    }
}