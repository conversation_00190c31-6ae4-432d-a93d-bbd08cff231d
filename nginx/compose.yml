# Only use this file if you are using docker-compose from local machine
services:
  viclass:
    container_name: viclass
    image: nginx:latest
    # command: [nginx-debug, '-g', 'daemon off;']
    volumes:
      - ./dist:$NGINX_CONF_PATH
      - ./dist/nginx.conf:/etc/nginx/conf.d/viclass.conf
      - ./ssl:$SSL_PATH
      - ../viclass/dist/viclass:$DEPLOYMENT_ROOT/ui/viclass
    env_file:
      - local.env
    ports:
      - $NGINX_DOCKER_CONNECT_PORT:$NGINX_CONNECT_PORT
      - $NGINX_DOCKER_CONNECT_PORT_SSL:$NGINX_CONNECT_PORT_SSL
