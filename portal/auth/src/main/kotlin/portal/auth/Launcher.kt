package portal.auth

import common.libs.logger.Logging
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import org.koin.core.context.GlobalContext
import org.koin.dsl.koinApplication
import org.koin.ksp.generated.module
import portal.auth.koin.KoinApplicationModule
import portal.auth.server.Server

/**
 *
 * <AUTHOR>
 */
object Launcher : Logging {
    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching auth service")

        RxJavaPlugins.setErrorHandler {
            logger.error("Uncaught error... ", it)
        }

        val koinApplication = koinApplication {
            modules(KoinApplicationModule().module)
        }
        GlobalContext.startKoin(koinApplication)

        val server = koinApplication.koin.get<Server>()

        // start account manager server
        server.start()

        //Add a shutdown hook so that if the JVM is stopped the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            logger.info("Shutting down auth service")
            server.shutdown()
            logger.info("Shutdown auth service")
        })

        server.blockUntilShutdown()
    }
}
