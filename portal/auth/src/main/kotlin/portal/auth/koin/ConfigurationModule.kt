package portal.auth.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import portal.auth.configuration.Configuration
import portal.auth.configuration.DatabaseConfig
import portal.auth.configuration.ServerConfig
import java.io.File
import kotlin.system.exitProcess


/**
 *
 * <AUTHOR>
 */
@Module
class ConfigurationModule : Logging {
    @Singleton
    fun provideConfiguration(): Configuration {
        val config: Configuration
        try {
            val path = "conf/config.json"
            val mapper = jacksonObjectMapper()
            val jsonString: String = File(path).readText(Charsets.UTF_8)
            config = mapper.readValue(jsonString, Configuration::class.java)
        } catch (t: Throwable) {
            logger.error("Exception when load configurations... ", t)
            exitProcess(1)
        }
        return config
    }

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig {
        return config.serverConf
    }

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig {
        return config.dbConf
    }
}
