package portal.auth.koin

import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import portal.auth.configuration.DatabaseConfig
import portal.auth.pojo.AuthPojo


/**
 *
 * <AUTHOR>
 */
@Module
class DatabaseModule {
    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        val pojoCodecRegistry: CodecRegistry = CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build())
        )
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(pojoCodecRegistry)
    }

    @Singleton
    fun provideAuthCollection(db: MongoDatabase): MongoCollection<AuthPojo> {
        return db.getCollection("auth", AuthPojo::class.java)
    }
}
