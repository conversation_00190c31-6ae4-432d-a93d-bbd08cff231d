package portal.backend

import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import common.libs.logger.Logging
import freemarker.cache.ClassTemplateLoader
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.freemarker.*
import io.ktor.server.logging.*
import io.ktor.server.plugins.callloging.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.sessions.*
import io.ktor.util.*
import jayeson.lib.access.ktor.auth.configureAccessLibCookie
import jayeson.lib.access.ktor.koin.AccessManagerModule
import jayeson.lib.session.dagger.DaggerSessionComponent
import jayeson.lib.session.dagger.SessionComponent
import org.koin.ktor.ext.inject
import org.koin.ktor.plugin.Koin
import org.slf4j.event.Level
import portal.backend.gateways.internalJobRunr.InternalJobRunrScheduler
import portal.backend.koin.koinApplicationModule
import portal.backend.plugins.configureCORS
import portal.backend.plugins.configureRouting

val logger = (object : Logging {
    override val loggerName = "APPLICATION"
}).logger

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

fun Application.module() {
    install(ContentNegotiation) {
        jackson {
            setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                indentObjectsWith(DefaultIndenter("  ", "\n"))
            })
            registerModule(JavaTimeModule())  // support java.time.* types
        }
    }
    install(Sessions) {
        val secretSignKey = hex("6819b57a326945c1968f45236589")
        configureAccessLibCookie(secretSignKey, "viclass_sess", "/")
    }

    install(Koin) {
        val sessionModelFactory: SessionComponent = DaggerSessionComponent.create()
        modules(
            koinApplicationModule,
            AccessManagerModule(sessionComponent = sessionModelFactory).createModule(),
        )
    }

    install(FreeMarker) {
        templateLoader = ClassTemplateLoader(this::class.java.classLoader, "templates")
    }

    configureRouting()
    configureCORS()

    install(StatusPages) {
//        status(HttpStatusCode.NotFound) { call, status ->
//            logger.error("404: Page Not Found ${call.request.toLogString()}")
//            call.respondText(text = "404: Page Not Found", status = status)
//        }

        exception<Throwable> { call, cause ->
            logger.error("unknown exception... ${call.request.toLogString()}", cause)
            call.respondText(text = "500: InternalServerError", status = HttpStatusCode.InternalServerError)
        }
    }

    install(CallLogging) {
        level = Level.DEBUG
        format { call ->
            val status = call.response.status()
            val httpMethod = call.request.httpMethod.value
            val userAgent = call.request.headers["User-Agent"]
            val params = call.request.queryParameters.formUrlEncode()
            val path = call.request.path()
            "$status, $httpMethod $path, $userAgent, $params"
        }
    }

    // Start Internal Job Runr Scheduler
    val internalJobRunrScheduler: InternalJobRunrScheduler by inject()
    internalJobRunrScheduler.startSchedulers()
}
