package portal.backend.controllers

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.util.*
import jayeson.lib.access.ktor.router.requireLogin
import org.bson.BsonDocument
import org.bson.BsonDocumentWriter
import org.bson.BsonInt64
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistry
import org.bson.json.JsonWriterSettings
import org.koin.ktor.ext.inject
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.ClassroomServiceGateway
import portal.backend.gateways.ILSessionServiceGateway
import portal.backend.logger
import portal.backend.models.GetClassroomActivityModel
import portal.backend.utility.ObjectIdConverter
import portal.backend.utility.defaultMapper
import portal.datastructures.lsession.LSRegStatus
import portal.lsession.pojo.activity.ClassroomActivity
import portal.lsession.pojo.activity.RequestPresentationAD
import portal.lsession.pojo.toPojo
import proto.portal.user.UserMessage

fun Application.classroomController() {
    val classroomSG: ClassroomServiceGateway by inject()
    val lsessionSG: ILSessionServiceGateway by inject()
    val codecRegistry: CodecRegistry by inject()

    fun serializeClassroomActivity(activity: ClassroomActivity): String {
        val document = BsonDocument()

        val clrActivityCodec = codecRegistry.get(ClassroomActivity::class.java)
        clrActivityCodec.encode(BsonDocumentWriter(document), activity, EncoderContext.builder().build())

        // replace _id by id
        val id = document.getObjectId("_id")
        document.remove("_id")
        document["id"] = id

        // replace time in ISO 8601 spec (e.g 2021-07-10T16:01:18.227Z) by long
        val createdTime = document.getDateTime(ClassroomActivity::createdTime.name)
        document[ClassroomActivity::createdTime.name] = BsonInt64(createdTime.value)

        return document.toJson(JsonWriterSettings.builder().objectIdConverter(ObjectIdConverter()).build())
    }

    routing {
        route("/api/classroom") {
            requireLogin {
                retrieveProfile {
                    get("/activity/fetch/ls/{lsId}") {
                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val lsId: String = call.parameters.getOrFail("lsId")

                            val result = lsessionSG.getLSession(lsId)
                            val activities = classroomSG.loadClassroomActivities(lsId)

                            val jsonList = activities.filter {
                                when (val activityData = it.data) {
                                    is RequestPresentationAD -> profile.user.id == result.lsessionDetails.creatorId || activityData.requestedTo == profile.user.id

                                    else -> true
                                }
                            }.map { activity -> defaultMapper.readTree(serializeClassroomActivity(activity)) }

                            return@get call.respond(HttpStatusCode.OK, jsonList)
                        } catch (e: Throwable) {
                            logger.error("loading activities exception... ", e)
                            val errMessage: String? = e.message
                            call.respond(
                                HttpStatusCode.InternalServerError, mapOf(
                                    "backendErrorCode" to Status.INTERNAL.code.value(),
                                    "message" to (errMessage ?: "failed")
                                )
                            )
                        }
                    }
                }
            }
        }
        route("/api/classroom") {
            requireLogin {
                retrieveProfile {
                    post("/activity/fetch") {
                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val model = call.receiveNullable<GetClassroomActivityModel>()

                            if (model == null) return@post call.respond(HttpStatusCode.BadRequest)

                            val res = lsessionSG.getLSession(model.lsId)

                            if (res.lsessionDetails.creatorId != profile.user.id) {
                                val sess = lsessionSG.getSessionRegistrationByLsId(model.lsId, profile.user.id)
                                val registration = sess.registration.toPojo()

                                if (registration.regStatus != LSRegStatus.REGISTERED) {
                                    val json = jacksonObjectMapper().createObjectNode()
                                        .put("backendErrorCode", Status.INVALID_ARGUMENT.code.value())
                                        .put("message", "You haven't registered yet")

                                    return@post call.respond(HttpStatusCode.InternalServerError, json)
                                } else {
                                    val result = classroomSG.getClassroomActivityById(model.activityId)

                                    return@post call.respond(
                                        HttpStatusCode.OK,
                                        defaultMapper.readTree(serializeClassroomActivity(result))
                                    )
                                }
                            } else return@post call.respond(HttpStatusCode.OK)
                        } catch (e: Throwable) {
                            logger.error("loading activity exception... ", e)
                            val errMessage: String? = e.message
                            call.respond(
                                HttpStatusCode.InternalServerError, mapOf(
                                    "backendErrorCode" to Status.INTERNAL.code.value(),
                                    "message" to (errMessage ?: "failed")
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}
