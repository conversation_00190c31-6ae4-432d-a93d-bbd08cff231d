package portal.backend.controllers

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import jayeson.lib.access.ktor.router.requireLogin
import org.bson.BsonType
import org.koin.ktor.ext.inject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import portal.backend.configs.ProfileDocMetadataConfig
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.FileStoreServiceGateway
import portal.backend.gateways.LoadMetadataOptions
import portal.backend.gateways.MetadataServiceGateway
import portal.backend.gateways.internalJobRunr.jobs.DeleteDocShareWithMeJob
import portal.backend.models.*
import portal.backend.utility.defaultMapper
import portal.backend.utility.respondWithErrorStatus
import portal.backend.utility.respondWithException
import proto.portal.user.UserMessage
import vi.metadata.doc.proto.MetadataDocMessage
import java.util.*

const val CLASSROOM_DOC_TYPE = vinet.ccs.metatadata.model.CLASSROOM_METADATA_DOC_TYPE

fun Application.docInProfileController() {
    val logger: Logger = LoggerFactory.getLogger(javaClass.name)

    val jackson = jacksonObjectMapper()
    val metadataServiceGateway: MetadataServiceGateway by inject()
    val fileStoreServiceGateway: FileStoreServiceGateway by inject()
    val profileDocConfig: ProfileDocMetadataConfig by inject()
    val deleteDocShareWithMeJob: DeleteDocShareWithMeJob by inject()

    routing {
        route("/api/doc-in-profile") {
            requireLogin {
                retrieveProfile {
                    post("/save-doc-to-profile") {
                        val request = call.receive<CreateDocInProfileInfoRequest>()

                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            // find doc metadata
                            val metadata = metadataServiceGateway.loadMetadataDoc(
                                request.details.source, emptyList(), null, request.docGlobalId, LoadMetadataOptions(
                                    limit = 1
                                )
                            ).await().metadataList.firstOrNull() ?: return@post call.respondText(
                                "Document Not Found", status = HttpStatusCode.NotFound
                            )

                            val ownerUserId: String? = when (metadata.metadataType) {
                                CLASSROOM_DOC_TYPE -> defaultMapper.readValue(
                                    metadata.metadataDetails,
                                    vinet.ccs.metatadata.model.DocumentInfoDetail::class.java
                                ).ownerUserId

                                DOC_OWNERSHIP_TYPE -> defaultMapper.readValue(
                                    metadata.metadataDetails, DocOwnershipInfoDetail::class.java
                                ).ownerUserId

                                else -> null
                            }

                            if (ownerUserId != profile.user.id) return@post call.respondText(
                                "Document Not Owned By User", status = HttpStatusCode.Forbidden
                            )

                            val grpcRes = metadataServiceGateway.createMetadataDoc(
                                request.docGlobalId, request.editorType,
                                DOC_IN_PROFILE_TYPE, request.details.copy(
                                    savedToProfile = true,
                                    savedDate = Date(),
                                )
                            ).await()

                            val docInfo = grpcRes.metadata.asDocInProfileInfo()
                            call.respond(HttpStatusCode.OK, docInfo)
                        } catch (e: Exception) {
                            logger.error("create doc info failed", e)
                            call.respondWithException(e)
                        }
                    }

                    post("/load-doc-info") {
                        val profile: UserMessage.GetUserResponse = try {
                            call.attributes[RetrieveProfile.PROFILE_ATTR]
                        } catch (e: Exception) {
                            return@post call.respondWithException(e)
                        }

                        val request = call.receive<LoadDocumentInfoRequest>()

                        try {
                            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                                DOC_IN_PROFILE_TYPE, listOfNotNull(
                                    Triple("docGlobalId", BsonType.STRING.name, request.docGlobalId),
                                ), request.editorType, request.docGlobalId
                            ).await()

                            val res = grpcRes.metadataList.firstOrNull() ?: return@post call.respondText(
                                "Document Not Found",
                                status = HttpStatusCode.NotFound
                            )

                            val docDetails =
                                defaultMapper.readValue(res.metadataDetails, DocInProfileInfoDetail::class.java)
                            if (docDetails.ownerUserId != profile.user.id) {
                                return@post call.respondText(
                                    "Document Not Owned By User", status = HttpStatusCode.Forbidden
                                )
                            }

                            val docInfo = DocumentInfoResponse(
                                docGlobalId = res.docId,
                                editorType = res.editorType,
                                docType = res.metadataType,
                                details = docDetails
                            )
                            call.respond(HttpStatusCode.OK, docInfo)
                        } catch (e: Exception) {
                            logger.error("load doc info failed", e)
                            return@post call.respondWithException(e)
                        }
                    }

                    post("/docs-by-user") {
                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val request = call.receive<DocumentsByUserRequest>()

                            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                                DOC_IN_PROFILE_TYPE, listOfNotNull(
                                    Triple("ownerUserId", BsonType.STRING.name, profile.user.id),
                                ), request.editorType, options = LoadMetadataOptions(
                                    limit = request.limit
                                )
                            ).await()

                            val res = grpcRes.metadataList.map { it.asDocInProfileInfo() }
                            call.respond(HttpStatusCode.OK, res)
                        } catch (e: Exception) {
                            logger.error("load docs by user failed", e)
                            call.respondWithException(e)
                        }
                    }

                    post("/doc-preview-upload-token") {
                        val dto = call.receive<DocPreviewUploadTokenDto>()

                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            // find doc metadata
                            val metadata = metadataServiceGateway.loadMetadataDoc(
                                dto.metadataDocType, emptyList(), null, dto.docGlobalId, LoadMetadataOptions(
                                    limit = 1
                                )
                            ).await().metadataList.firstOrNull() ?: return@post call.respondText(
                                "Document Not Found", status = HttpStatusCode.NotFound
                            )

                            val ownerUserId: String? = when (metadata.metadataType) {
                                CLASSROOM_DOC_TYPE -> defaultMapper.readValue(
                                    metadata.metadataDetails,
                                    vinet.ccs.metatadata.model.DocumentInfoDetail::class.java
                                ).ownerUserId

                                DOC_OWNERSHIP_TYPE -> defaultMapper.readValue(
                                    metadata.metadataDetails, DocOwnershipInfoDetail::class.java
                                ).ownerUserId

                                else -> null
                            }

                            if (ownerUserId != profile.user.id) return@post call.respondText(
                                "Document Not Owned By User", status = HttpStatusCode.Forbidden
                            )

                            val uploadTokenDto = GetUploadTokenDto(
                                allowFileTypes = dto.allowFileTypes, maxFileSize = dto.maxFileSize
                            )

                            // retrieve upload token
                            val response = fileStoreServiceGateway.getUploadToken(profile.user.id, uploadTokenDto)
                            if (response.status.code != Status.Code.OK.value()) {
                                return@post call.respondWithErrorStatus(response.status)
                            }
                            if (response.uploadToken.isEmpty()) {
                                throw RuntimeException("Upload token is empty")
                            }

                            call.respond(HttpStatusCode.OK, mapOf("uploadToken" to response.uploadToken))
                        } catch (e: Exception) {
                            logger.error("get doc preview upload token failed", e)
                            call.respondWithException(e)
                        }
                    }

                    post("/toggle-sharing") {
                        val request = try {
                            call.receive<LoadDocumentInfoRequest>()
                        } catch (e: Exception) {
                            return@post call.respondWithException(e, HttpStatusCode.BadRequest)
                        }

                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            val metadata = metadataServiceGateway.loadMetadataDoc(
                                DOC_IN_PROFILE_TYPE, listOfNotNull(
                                    Triple("docGlobalId", BsonType.STRING.name, request.docGlobalId),
                                ), request.editorType, request.docGlobalId
                            ).await().metadataList.firstOrNull() ?: return@post call.respondText(
                                "Document Not Found", status = HttpStatusCode.NotFound
                            )

                            val docDetails = defaultMapper.readValue(
                                metadata.metadataDetails,
                                DocInProfileInfoDetail::class.java
                            )

                            // check owner doc
                            if (docDetails.ownerUserId != profile.user.id) return@post call.respondText(
                                "Document Not Owned By User", status = HttpStatusCode.Forbidden
                            )

                            // update doc
                            val resUpdate = metadataServiceGateway.updateMetadataDocById(
                                metadata.id, docDetails.copy(
                                    shared = !docDetails.shared
                                )
                            ).await()

                            val shareStatusUpdated = defaultMapper.readValue(
                                resUpdate.metadata.metadataDetails,
                                DocInProfileInfoDetail::class.java
                            ).shared


                            // check doc.shared after update equal false for delete doc-share-with-me job
                            if (!shareStatusUpdated) {
                                deleteDocShareWithMeJob.enqueueShareWithMeDelete(metadata.docId, DOC_SHARE_WITH_ME_TYPE)
                            }

                            return@post when (resUpdate.status.code) {
                                Status.Code.OK.value() -> call.respond(
                                    HttpStatusCode.OK, resUpdate.metadata.asDocInProfileInfo()
                                )

                                Status.Code.NOT_FOUND.value() -> call.respondWithErrorStatus(
                                    resUpdate.status, HttpStatusCode.NotFound
                                )

                                else -> call.respondWithErrorStatus(resUpdate.status)
                            }
                        } catch (e: Exception) {
                            call.respondWithException(e)
                        }
                    }

                    post("/toggle-embedding") {
                        val request = try {
                            call.receive<LoadDocumentInfoRequest>()
                        } catch (e: Exception) {
                            return@post call.respondWithException(e, HttpStatusCode.BadRequest)
                        }

                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            val metadata = metadataServiceGateway.loadMetadataDoc(
                                DOC_IN_PROFILE_TYPE, listOfNotNull(
                                    Triple("docGlobalId", BsonType.STRING.name, request.docGlobalId),
                                ), request.editorType, request.docGlobalId
                            ).await().metadataList.firstOrNull() ?: return@post call.respondText(
                                "Document Not Found", status = HttpStatusCode.NotFound
                            )

                            val docDetails =
                                defaultMapper.readValue(metadata.metadataDetails, DocInProfileInfoDetail::class.java)
                            if (docDetails.ownerUserId != profile.user.id) return@post call.respondText(
                                "Document Not Owned By User", status = HttpStatusCode.Forbidden
                            )

                            val res = metadataServiceGateway.updateMetadataDocById(
                                metadata.id, docDetails.copy(
                                    embedded = !docDetails.embedded
                                )
                            ).await()

                            return@post when (res.status.code) {
                                Status.Code.OK.value() -> call.respond(
                                    HttpStatusCode.OK, res.metadata.asDocInProfileInfo()
                                )

                                Status.Code.NOT_FOUND.value() -> call.respondWithErrorStatus(
                                    res.status, HttpStatusCode.NotFound
                                )

                                else -> call.respondWithErrorStatus(res.status)
                            }
                        } catch (e: Exception) {
                            call.respondWithException(e)
                        }
                    }

                    post("/share-with-me-docs") {
                        val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                        val dto = call.receive<GetSavedDocsDto>()
                        val size = dto.size.coerceIn(1, profileDocConfig.maxPageSize)
                        val page = dto.page.coerceAtLeast(1)
                        val offset = (page - 1) * size
                        val editorType = if (dto.editorType.isNullOrEmpty()) null else dto.editorType

                        try {
                            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                                DOC_SHARE_WITH_ME_TYPE,
                                listOfNotNull(Triple("recipient", BsonType.STRING.name, profile.user.id),
                                    Triple("savedToProfile", BsonType.BOOLEAN.name, true.toString()),
                                    dto.startDate?.let {
                                        Triple(
                                            "gte:savedDate", BsonType.INT64.name, it.time.toString()
                                        )
                                    },
                                    dto.endDate?.let {
                                        Triple(
                                            "lte:savedDate", BsonType.INT64.name, it.time.toString()
                                        )
                                    }),
                                editorType = editorType,
                                options = LoadMetadataOptions(
                                    limit = size, offset = offset, textSearch = dto.textSearch, shouldCount = true
                                )
                            ).await()

                            val documents = grpcRes.metadataList.map {
                                DocumentInfoResponse(
                                    docGlobalId = it.docId,
                                    editorType = it.editorType,
                                    docType = it.metadataType,
                                    details = defaultMapper.readValue(
                                        it.metadataDetails, DocShareWithMeInfoDetail::class.java
                                    )
                                )
                            }
                            val count = if (grpcRes.hasTotalCount()) grpcRes.totalCount.value else documents.size
                            val res = GetSavedDocsResponse(
                                documents = documents,
                                totalCount = count,
                                page = page,
                                pageSize = size,
                                hasPrevious = page > 1,
                                hasNext = count > page * size,
                            )
                            call.respond(HttpStatusCode.OK, res)
                        } catch (e: Exception) {
                            logger.error("get saved docs failed", e)
                            call.respondWithException(e)
                        }
                    }

                    post("/saved-docs") {
                        val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                        val dto = call.receive<GetSavedDocsDto>()
                        val size = dto.size.coerceIn(1, profileDocConfig.maxPageSize)
                        val page = dto.page.coerceAtLeast(1)
                        val offset = (page - 1) * size
                        val editorType = if (dto.editorType.isNullOrEmpty()) null else dto.editorType

                        try {
                            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                                DOC_IN_PROFILE_TYPE,
                                listOfNotNull(Triple("ownerUserId", BsonType.STRING.name, profile.user.id),
                                    Triple("savedToProfile", BsonType.BOOLEAN.name, true.toString()),
                                    dto.startDate?.let {
                                        Triple(
                                            "gte:savedDate", BsonType.INT64.name, it.time.toString()
                                        )
                                    },
                                    dto.endDate?.let {
                                        Triple(
                                            "lte:savedDate", BsonType.INT64.name, it.time.toString()
                                        )
                                    }),
                                editorType = editorType,
                                options = LoadMetadataOptions(
                                    limit = size, offset = offset, textSearch = dto.textSearch, shouldCount = true
                                )
                            ).await()

                            val documents = grpcRes.metadataList.map { it.asDocInProfileInfo() }
                            val count = if (grpcRes.hasTotalCount()) grpcRes.totalCount.value else documents.size
                            val res = GetSavedDocsResponse(
                                documents = documents,
                                totalCount = count,
                                page = page,
                                pageSize = size,
                                hasPrevious = page > 1,
                                hasNext = count > page * size,
                            )
                            call.respond(HttpStatusCode.OK, res)
                        } catch (e: Exception) {
                            logger.error("get saved docs failed", e)
                            call.respondWithException(e)
                        }
                    }

                    delete("/saved-docs/{edType}/{docGlobalId}") {
                        val edType = call.parameters["edType"] ?: return@delete call.respondText(
                            "EditorType must be specified", status = HttpStatusCode.BadRequest
                        )
                        val docGlobalId = call.parameters["docGlobalId"] ?: return@delete call.respondText(
                            "DocGlobalId must be specified", status = HttpStatusCode.BadRequest
                        )

                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            val metadata = metadataServiceGateway.loadMetadataDoc(
                                DOC_IN_PROFILE_TYPE, listOf(
                                    Triple("ownerUserId", BsonType.STRING.name, profile.user.id),
                                    Triple("savedToProfile", BsonType.BOOLEAN.name, true.toString()),
                                ), edType, docGlobalId, LoadMetadataOptions(limit = 1)
                            ).await().metadataList.firstOrNull() ?: return@delete call.respondText(
                                "Document Not Found", status = HttpStatusCode.NotFound
                            )

                            val docDetails =
                                defaultMapper.readValue(metadata.metadataDetails, DocInProfileInfoDetail::class.java)
                            val deleteRes = metadataServiceGateway.updateMetadataDocById(
                                metadata.id, docDetails.copy(
                                    savedToProfile = false, savedDate = null
                                )
                            ).await()

                            if (deleteRes.status.code != Status.Code.OK.value()) return@delete call.respondWithErrorStatus(
                                deleteRes.status
                            )

                            call.respond(HttpStatusCode.OK)
                        } catch (e: Exception) {
                            logger.error("delete saved doc failed", e)
                            call.respondWithException(e)
                        }
                    }

                    delete("/share-with-me-docs/{edType}/{docGlobalId}") {
                        val edType = call.parameters["edType"] ?: return@delete call.respondText(
                            "EditorType must be specified", status = HttpStatusCode.BadRequest
                        )
                        val docGlobalId = call.parameters["docGlobalId"] ?: return@delete call.respondText(
                            "DocGlobalId must be specified", status = HttpStatusCode.BadRequest
                        )

                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            val deleteRes = metadataServiceGateway.deleteManyMetadataDocs(
                                docGlobalId,
                                DOC_SHARE_WITH_ME_TYPE,
                                edType,
                                listOf(Triple("recipient", BsonType.STRING.name, profile.user.id))
                            ).await()

                            if (deleteRes.status.code != Status.Code.OK.value()) return@delete call.respondWithErrorStatus(
                                deleteRes.status
                            )

                            call.respond(HttpStatusCode.OK)
                        } catch (e: Exception) {
                            logger.error("delete doc in profile failed", e)
                            call.respondWithException(e)
                        }
                    }
                }
            }

            post("/is-embedded") {
                val request = try {
                    call.receive<LoadDocumentInfoRequest>()
                } catch (e: Exception) {
                    return@post call.respondWithException(e, HttpStatusCode.BadRequest)
                }

                try {
                    val metadata = metadataServiceGateway.loadMetadataDoc(
                        DOC_IN_PROFILE_TYPE, listOfNotNull(
                            Triple("docGlobalId", BsonType.STRING.name, request.docGlobalId),
                        ), request.editorType, request.docGlobalId
                    ).await().metadataList.firstOrNull() ?: return@post call.respondText(
                        "Document Not Found", status = HttpStatusCode.NotFound
                    )

                    val docDetails =
                        defaultMapper.readValue(metadata.metadataDetails, DocInProfileInfoDetail::class.java)

                    return@post if (docDetails.embedded) {
                        call.respond(HttpStatusCode.OK)
                    } else {
                        call.respond(HttpStatusCode.Forbidden)
                    }
                } catch (e: Exception) {
                    call.respondWithException(e)
                }
            }
        }


        route("api/lession") {
            post("/docs/fetch") {
                return@post call.respond(HttpStatusCode.OK, jackson.createArrayNode())
            }
        }
    }
}


fun MetadataDocMessage.MetadataDocProto.asDocInProfileInfo(): DocumentInfoResponse {
    return DocumentInfoResponse(
        docGlobalId = this.docId,
        editorType = this.editorType,
        docType = this.metadataType,
        details = defaultMapper.readValue(
            this.metadataDetails, DocInProfileInfoDetail::class.java
        )
    )
}