package portal.backend.controllers

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import jayeson.lib.access.ktor.router.requireLogin
import org.bson.BsonType
import org.koin.ktor.ext.inject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.LoadMetadataOptions
import portal.backend.gateways.MetadataServiceGateway
import portal.backend.models.*
import portal.backend.utility.defaultMapper
import portal.backend.utility.respondWithException
import proto.portal.user.UserMessage

/**
 * Register routes to handle document metadata related requests
 */
fun Application.documentController() {
    val logger: Logger = LoggerFactory.getLogger(javaClass.name)
    val jackson = jacksonObjectMapper()
    val metadataServiceGateway: MetadataServiceGateway by inject()

    routing {
        route("/api/document") {
            /**
             * Check if the doc is public doc, which means it is not saved in metadata and not owned by any one (i.e: created by guest).
             * To be used in the viewer in homepage to check if current user is allowed to view the owner-less doc.
             */
            post("/is-public-doc") {
                val request = call.receive<CheckPublicDocumentRequest>()

                try {
                    // docGlobalId is not unique between editor types, so we have to check all types
                    val grpcRes = metadataServiceGateway.loadMetadataDoc(
                        DOC_OWNERSHIP_TYPE, listOfNotNull(
                            Triple("docGlobalId", BsonType.STRING.name, request.docGlobalId),
                        ), docGlobalId = request.docGlobalId
                    ).await()

                    val isNotSaved = grpcRes.metadataList.isEmpty()
                    val docMatchEdType = grpcRes.metadataList.firstOrNull { it.editorType == request.editorType }

                    val respond = CheckPublicDocumentResponse(
                        isPublic = isNotSaved, // Not saved in metadata -> not owned by any one
                        validEditorType = isNotSaved || docMatchEdType != null
                    )
                    call.respond(HttpStatusCode.OK, respond)
                } catch (e: Exception) {
                    logger.error("check public doc failed", e)
                    call.respondWithException(e)
                }
            }

            /**
             * Load shared doc info for guest (non-login user).
             * Will only return doc info if the doc is shared.
             */
            post("/load-shared-doc-info-guest") {
                val request: LoadSharedDocumentInfoRequest
                try {
                    request = call.receive<LoadSharedDocumentInfoRequest>()
                } catch (e: Exception) {
                    return@post call.respond(HttpStatusCode.BadRequest)
                }

                try {
                    val grpcRes = metadataServiceGateway.loadMetadataDoc(
                        docGlobalId = request.docGlobalId,
                        metadataType = DOC_IN_PROFILE_TYPE,
                        editorType = request.editorType,
                        filterFields = emptyList(),
                    ).await()

                    val res = grpcRes.metadataList.firstOrNull() ?: return@post call.respondText(
                        "Document Not Found",
                        status = HttpStatusCode.NotFound
                    )

                    val docDetails = defaultMapper.readValue(res.metadataDetails, DocInProfileInfoDetail::class.java)

                    // check doc access
                    if (!docDetails.shared) {
                        return@post call.respondText(
                            "Document not shared", status = HttpStatusCode.Forbidden
                        )
                    }

                    val docInfo = DocumentInfoResponse(
                        docGlobalId = res.docId,
                        editorType = res.editorType,
                        docType = res.metadataType,
                        details = docDetails
                    )
                    call.respond(HttpStatusCode.OK, docInfo)
                } catch (e: Exception) {
                    logger.error("load shared doc info failed", e)
                    return@post call.respondWithException(e)
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Save basic doc metadata include docGlobalId, editorType and doc owner info.
                     */
                    post("/create-doc-info") {
                        val profile: UserMessage.GetUserResponse = try {
                            call.attributes[RetrieveProfile.PROFILE_ATTR]
                        } catch (e: Exception) {
                            return@post call.respondWithException(e)
                        }

                        val request = call.receive<CreateDocOwnershipInfoRequest>()
                        if (profile.user.id != request.details.ownerUserId) {
                            return@post call.respondText("Can only create doc info owned by the user", status = HttpStatusCode.Forbidden)
                        }

                        try {
                            val grpcRes = metadataServiceGateway.createMetadataDoc(
                                request.docGlobalId, request.editorType, DOC_OWNERSHIP_TYPE, request.details
                            ).await()
                            val docInfo = DocumentInfoResponse(
                                docGlobalId = grpcRes.metadata.docId,
                                editorType = grpcRes.metadata.editorType,
                                docType = grpcRes.metadata.metadataType,
                                details = defaultMapper.readValue(
                                    grpcRes.metadata.metadataDetails, DocOwnershipInfoDetail::class.java
                                )
                            )

                            call.respond(HttpStatusCode.OK, docInfo)
                        } catch (e: Exception) {
                            logger.error("create doc info failed", e)
                            call.respondWithException(e)
                        }
                    }

                    /**
                     * Load shared doc info for logged-in user.
                     * Will only return doc info if the doc is shared or the doc is owned by the user.
                     */
                    post("/load-shared-doc-info") {
                        val profile: UserMessage.GetUserResponse = try {
                            call.attributes[RetrieveProfile.PROFILE_ATTR]
                        } catch (e: Exception) {
                            return@post call.respondWithException(e)
                        }

                        val request: LoadSharedDocumentInfoRequest
                        try {
                            request = call.receive<LoadSharedDocumentInfoRequest>()
                        } catch (e: Exception) {
                            return@post call.respond(HttpStatusCode.BadRequest)
                        }

                        try {
                            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                                docGlobalId = request.docGlobalId,
                                metadataType = DOC_IN_PROFILE_TYPE,
                                editorType = request.editorType,
                                filterFields = emptyList(),
                            ).await()

                            val res = grpcRes.metadataList.firstOrNull() ?: return@post call.respondText(
                                "Document not found",
                                status = HttpStatusCode.NotFound
                            )

                            val docDetails =
                                defaultMapper.readValue(res.metadataDetails, DocInProfileInfoDetail::class.java)

                            // check doc access
                            if (!docDetails.shared && docDetails.ownerUserId != profile.user.id) {
                                return@post call.respondText(
                                    "Document not shared", status = HttpStatusCode.Forbidden
                                )
                            }

                            // check doc owner
                            if (docDetails.ownerUserId == profile.user.id) {
                                val docInfo = DocumentInfoResponse(
                                    docGlobalId = res.docId,
                                    editorType = res.editorType,
                                    docType = res.metadataType,
                                    details = docDetails
                                )
                                return@post call.respond(HttpStatusCode.OK, docInfo)
                            }

                            // check and create doc share with me
                            val docShareWithMe = metadataServiceGateway.loadMetadataDoc(
                                docGlobalId = request.docGlobalId,
                                metadataType = DOC_SHARE_WITH_ME_TYPE,
                                editorType = request.editorType,
                                filterFields = emptyList(),
                            ).await().metadataList.firstOrNull()

                            if (docShareWithMe == null) {
                                metadataServiceGateway.createMetadataDoc(
                                    res.docId, res.editorType, DOC_SHARE_WITH_ME_TYPE, DocShareWithMeInfoDetail(
                                        recipient = profile.user.id,
                                        ownerUserId = docDetails.ownerUserId,
                                        shared = docDetails.shared,
                                        docGlobalId = docDetails.docGlobalId,
                                        ownerUserName = docDetails.ownerUserName,
                                        docLocalId = docDetails.docLocalId,
                                        docName = docDetails.docName,
                                        previewUrl = docDetails.previewUrl,
                                        source = docDetails.source,
                                        savedDate = docDetails.savedDate,
                                        savedToProfile = docDetails.savedToProfile,
                                    )
                                ).await()
                            }

                            val docInfo = DocumentInfoResponse(
                                docGlobalId = res.docId,
                                editorType = res.editorType,
                                docType = res.metadataType,
                                details = docDetails
                            )
                            call.respond(HttpStatusCode.OK, docInfo)
                        } catch (e: Exception) {
                            logger.error("load shared doc info failed", e)
                            return@post call.respondWithException(e)
                        }
                    }

                    /**
                     * Load document info metadata. It will find the doc from doc-ownership and doc-in-profile metadata type,
                     * and will only return doc info if the doc is owned by the user.
                     */
                    post("/load-doc-info") {
                        val profile: UserMessage.GetUserResponse = try {
                            call.attributes[RetrieveProfile.PROFILE_ATTR]
                        } catch (e: Exception) {
                            return@post call.respondWithException(e)
                        }

                        val request = call.receive<LoadDocumentInfoRequest>()

                        try {
                            var docDetailOwnerUserId: String? = null
                            var docInfo: DocumentInfoResponse? = null

                            val (docOwnershipInfo, docOwnershipDetail) = metadataServiceGateway.loadDocInfo(
                                request,
                                DOC_OWNERSHIP_TYPE,
                                DocOwnershipInfoDetail::class.java
                            )
                            if (docOwnershipInfo != null) {
                                docInfo = docOwnershipInfo
                                docDetailOwnerUserId = docOwnershipDetail?.ownerUserId
                            }

                            if (docInfo == null) {
                                val (docInProfileInfo, docInProfileDetail) = metadataServiceGateway.loadDocInfo(
                                    request,
                                    DOC_IN_PROFILE_TYPE,
                                    DocInProfileInfoDetail::class.java
                                )
                                if (docInProfileInfo != null) {
                                    docInfo = docInProfileInfo
                                    docDetailOwnerUserId = docInProfileDetail?.ownerUserId
                                }
                            }

                            if (docInfo == null) {
                                return@post call.respondText(
                                    "Document Not Found",
                                    status = HttpStatusCode.NotFound
                                )
                            }

                            if (docDetailOwnerUserId != profile.user.id) {
                                return@post call.respondText(
                                    "Document Not Owned By User", status = HttpStatusCode.Forbidden
                                )
                            }

                            call.respond(HttpStatusCode.OK, docInfo)
                        } catch (e: Exception) {
                            logger.error("load doc info failed", e)
                            return@post call.respondWithException(e)
                        }
                    }

                    /**
                     * Get the latest docs of a specific editor type that is created by the user.
                     * Use in homepage when there is no user document ID is cached in the browser,
                     * to show the user the latest docs instead of creating a new doc
                     */
                    post("/docs-by-user") {
                        try {
                            val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val request = call.receive<DocumentsByUserRequest>()

                            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                                DOC_OWNERSHIP_TYPE, listOfNotNull(
                                    Triple("ownerUserId", BsonType.STRING.name, profile.user.id),
                                ), request.editorType, options = LoadMetadataOptions(
                                    limit = request.limit
                                )
                            ).await()

                            val res = grpcRes.metadataList.map {
                                DocumentInfoResponse(
                                    docGlobalId = it.docId,
                                    editorType = it.editorType,
                                    docType = it.metadataType,
                                    details = defaultMapper.readValue(
                                        it.metadataDetails, DocOwnershipInfoDetail::class.java
                                    )
                                )
                            }
                            call.respond(HttpStatusCode.OK, res)
                        } catch (e: Exception) {
                            logger.error("load docs by user failed", e)
                            call.respondWithException(e)
                        }
                    }
                }
            }
        }


        /**
         * @deprecated
         */
        route("api/lession") {
            post("/docs/fetch") {
                return@post call.respond(HttpStatusCode.OK, jackson.createArrayNode())
            }
        }
    }
}
