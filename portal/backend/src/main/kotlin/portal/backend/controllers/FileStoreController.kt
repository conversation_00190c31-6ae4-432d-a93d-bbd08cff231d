package portal.backend.controllers

import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import jayeson.lib.access.ktor.annotation.RequireLoginConfiguration
import jayeson.lib.access.ktor.annotation.UnauthorizedAccessHandler
import jayeson.lib.access.ktor.router.requireLogin
import org.koin.ktor.ext.inject
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.FileStoreServiceGateway
import portal.backend.gateways.IFileStoreServiceGateway
import portal.backend.models.GetUploadTokenDto
import portal.backend.utility.respondWithErrorStatus
import portal.backend.utility.respondWithException
import proto.portal.user.UserMessage

/**
 * Custom handler to allow unauthorized access. We need this with requireLogin because that's required for support `retrieveProfile`
 * but still allow Guest user to use the API with limited access (ex: limited upload size).
 */
class AllowLimitedUnauthorizedAccessHandler : UnauthorizedAccessHandler {
    override suspend fun handleUnauthorizedAccess(
        req: ApplicationCall,
        configuration: RequireLoginConfiguration
    ): ApplicationCall? {
        return req
    }
}

fun Application.fileStoreController() {
    val fileStoreServiceGateway: IFileStoreServiceGateway by inject<FileStoreServiceGateway>()

    routing {
        route("/api/filestore") {
            requireLogin(RequireLoginConfiguration(unauthAccessHandler = AllowLimitedUnauthorizedAccessHandler::class.java)) {
                retrieveProfile {
                    post("/upload-token") {
                        val dto = call.receive<GetUploadTokenDto>()
                        try {
                            val profile: UserMessage.GetUserResponse? =
                                call.attributes.getOrNull(RetrieveProfile.PROFILE_ATTR)

                            val includeDownloadUrl = call.request.queryParameters["withUrl"] == "true"
                            val response =
                                fileStoreServiceGateway.getUploadToken(profile?.user?.id, dto, includeDownloadUrl)
                            if (response.status.code != Status.Code.OK.value()) {
                                return@post call.respondWithErrorStatus(response.status)
                            }
                            if (response.uploadToken.isEmpty()) {
                                throw RuntimeException("Upload token is empty")
                            }

                            val responseBody = mutableMapOf("uploadToken" to response.uploadToken)
                            if (includeDownloadUrl) {
                                responseBody["downloadUrl"] = response.downloadUrl
                            }
                            call.respond(HttpStatusCode.OK, responseBody)
                        } catch (e: Exception) {
                            call.respondWithException(e)
                        }
                    }
                }
            }
        }
    }
}