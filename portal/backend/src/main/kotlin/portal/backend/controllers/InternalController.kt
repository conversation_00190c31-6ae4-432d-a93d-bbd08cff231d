package portal.backend.controllers

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.koin.ktor.ext.inject
import portal.backend.gateways.internalJobRunr.InternalJobRunrService
import portal.jobrunr.datastructure.JobNextAction
import portal.jobrunr.datastructure.JobStatus
import portal.jobrunr.execution.JobExecutionResponse
import portal.jobrunr.request.JobRequest


fun Application.internalController() {
    val internalJobRunrService: InternalJobRunrService by inject()

    routing {
        route("/internal/backend") {
            post("/jobrunr/execute") {
                val req = call.receive<JobRequest>()
                try {
                    // add job for enqueue retry
                    val job = internalJobRunrService.getJobInChannel(req.jobId.toString())

                    // delete job if job is completed
                    if (job != null && job.status == JobStatus.Completed) {
                        return@post call.respond(
                            HttpStatusCode.OK,
                            JobExecutionResponse(req.jobId, nextAction = JobNextAction.Stop)
                        )
                    }

                    // retry job if job in channel is null
                    if (job == null) {
                        internalJobRunrService.enqueueJobRetry(req)
                    }

                    // continue job if job in channel is null or in process
                    return@post call.respond(
                        HttpStatusCode.OK,
                        JobExecutionResponse(req.jobId, nextAction = JobNextAction.Continue)
                    )
                } catch (t: Throwable) {
                    // if delete doc-share-with-me error retry job
                    return@post call.respond(
                        HttpStatusCode.OK,
                        JobExecutionResponse(req.jobId, nextAction = JobNextAction.Continue)
                    )
                }
            }
        }
    }
}


