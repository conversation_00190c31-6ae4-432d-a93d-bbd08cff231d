package portal.backend.controllers

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.util.*
import jayeson.lib.access.ktor.router.requireLogin
import org.bson.types.ObjectId
import org.koin.ktor.ext.inject
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.ILSessionServiceGateway
import portal.backend.gateways.IUserServiceGateway
import portal.backend.gateways.NotificationServiceGateway
import portal.backend.logger
import portal.backend.models.Profile
import portal.backend.models.fromGetUserProfileResponseProto
import portal.datastructures.lsession.LSRegStatus
import portal.datastructures.lsession.RaiseHandStatus
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.ClassroomUserState
import portal.lsession.pojo.LSessionRegistration
import portal.lsession.pojo.toPojo
import portal.notification.pojo.ClassroomTarget
import portal.notification.pojo.MultiTarget
import portal.notification.pojo.Notification
import portal.notification.pojo.UserTarget
import portal.notification.pojo.notidata.RegisterND
import portal.notification.pojo.notidata.RegistrationCancelledND
import proto.portal.lsession.LsessionMessage
import proto.portal.user.UserMessage
import java.time.Instant
import java.util.stream.Collectors

fun Application.LSessionRegistrationController() {
    val lsService: ILSessionServiceGateway by inject()
    val userService: IUserServiceGateway by inject()
    val notificationSG: NotificationServiceGateway by inject()
    val mapper = jacksonObjectMapper()

    /**
     * Saves a notification to the notification service. Handles the response and
     * logs any error that occurs during saving.
     *
     * @param notification the notification to save
     */
    fun saveNotification(notification: Notification) {
        notificationSG.saveNotification(notification).handle { _, t ->
            if (t != null) {
                logger.error("failed to save notification... ", t)
            }
        }
    }

    /**
     * Endpoint to re-register for a session. It is allowed to re-register for a session when the current status of the
     * registration is either [LSRegStatus.WAITING_CONFIRMED] or [LSRegStatus.CANCELLED].
     *
     * @param call the application call
     * @param profileResponse the user information
     * @param id the id of the registration
     * @param lsessionDetails the details of the session
     * @return a response with the id of the registration, or an error response if the registration failed
     */
    suspend fun reRegister(
        call: ApplicationCall,
        profileResponse: UserMessage.GetUserResponse,
        id: String,
        lsessionDetails: LsessionMessage.LSessionDetailsProto,
    ) {
        val mbIdRes = lsService.getLSessionRegistrationById(id)

        if (!mbIdRes.hasRegistration()) return call.respond(
            HttpStatusCode.BadRequest, message = "The user has not sent registration to this session"
        )

        val registration = mbIdRes.registration
        val oldStatus = LSRegStatus.valueOf(registration.regStatus)
        val newStatus = LSRegStatus.WAITING_CONFIRMED

        if (oldStatus == newStatus) return call.respond(HttpStatusCode.OK, mapper.createObjectNode())

        var error = false

        if (profileResponse.user.id.equals(registration.userId)) {
            if (!listOf(LSRegStatus.WAITING_CONFIRMED, LSRegStatus.CANCELLED).contains(newStatus)) {
                error = true
            }
            if (!listOf(LSRegStatus.REJECTED, LSRegStatus.CANCELLED).contains(oldStatus)) {
                error = true
            }
        } else {
            return call.respond(HttpStatusCode.BadRequest, message = "Đăng ký buổi học không thành công")
        }

        if (error) return call.respond(
            HttpStatusCode.BadRequest,
            message = "Trang thái yêu cầu đăng ký buổi học đã thay đổi, tải lại trang để cập nhật"
        )

        val regTime = System.currentTimeMillis()
        val updateRes = lsService.updateRegistrationRegStatus(id, newStatus, regTime)

        if (updateRes.hasStatus() && updateRes.status.code == Status.Code.OK.value()) {
            // init and save notification
            val extendedData = RegisterND(lsessionDetails.id, updateRes.regId)
            val notification = Notification(
                profileResponse.user.id,
                ClassroomTarget(lsessionDetails.id),
                "Học viên ${profileResponse.user.username} đã gởi yêu cầu tham gia buổi học",
                Instant.now().plusSeconds(300),
                extendedData
            )

            saveNotification(notification)

            return call.respond(HttpStatusCode.OK, mapOf("lsmId" to id))
        } else return call.respond(
            HttpStatusCode.InternalServerError, mapOf("backendErrorCode" to updateRes.status.code)
        )
    }


    routing {
        route("/api/lsession") {
            requireLogin {
                /**
                 * Fetch all registrations of a classroom, include user profile info and the registration status.
                 * Used to show the list of members on the classroom page
                 */
                get("{lsId}/registration/fetch") {
                    try {
                        val lsId: String = call.parameters.getOrFail("lsId")
                        val res = lsService.getLSessionRegistrationByLsId(lsId)

                        if (res.status.code != Status.Code.OK.value()) {
                            return@get call.respond(
                                HttpStatusCode.InternalServerError, mapOf("backendErrorCode" to res.status.code)
                            )
                        }

                        val ids = res.registrationsMap.values.map { it.userId }
                        val resp = userService.getUserProfileByIds(ids)
                        // convert the result into a map of json with key is id
                        val profileJsonMapByUserId = resp.userProfileList.stream().collect(
                            Collectors.toMap({ it.id }, {
                                mapper.valueToTree<JsonNode>(Profile.fromGetUserProfileResponseProto(it))
                            })
                        )
                        val arr = res.registrationsMap.entries.map {
                            val json: ObjectNode = mapper.createObjectNode()
                            json.put("id", it.key)
                            json.set<JsonNode>("profile", profileJsonMapByUserId[it.value.userId])
                            json.put("regStatus", it.value.regStatus)
                            json.put("regTime", it.value.regTime)
                            if (it.value.hasState()) {
                                json.set<JsonNode>(
                                    "userState", mapper.valueToTree(it.value.state.toPojo())
                                )
                            }
                            json
                        }.foldRight(mapper.createArrayNode()) { node, array ->
                            array.add(node)
                        }

                        return@get call.respond(HttpStatusCode.OK, arr)
                    } catch (t: Throwable) {
                        return@get call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                    }
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Fetch the registration status of the current user
                     */
                    post("{lsId}/registration/current/fetch") {
                        try {
                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val lsId = call.parameters.getOrFail("lsId")
                            val regRes = lsService.getSessionRegistrationByLsId(lsId, profileResponse.user.id)
                            val registration: ObjectNode = mapper.createObjectNode()

                            registration.put("id", regRes.regId)
                            registration.put("regStatus", regRes.registration.regStatus)
                            registration.put("regTime", regRes.registration.regTime)

                            if (regRes.registration.hasState()) {
                                registration.set<JsonNode>(
                                    "userState", mapper.valueToTree(regRes.registration.state.toPojo())
                                )
                            }

                            val upRes = userService.getUserProfileById(regRes.registration.userId)
                            val profile =
                                mapper.valueToTree<JsonNode>(Profile.fromGetUserProfileResponseProto(upRes.userProfile))

                            registration.set<JsonNode>("profile", profile)

                            return@post call.respond(HttpStatusCode.OK, registration)
                        } catch (t: Throwable) {
                            return@post call.respond(
                                HttpStatusCode.InternalServerError, message = t.message.toString()
                            )
                        }
                    }

                    post("{lsId}/registration/registered-users") {
                        try {
                            val lsId = call.parameters.getOrFail("lsId")
                            val data: ObjectNode = mapper.createObjectNode()

                            val registeredRes =
                                lsService.getLSessionRegistrationByStatus(lsId, listOf(LSRegStatus.REGISTERED))
                            val registeredUserIds = registeredRes.registrationsMap.values.map { it.userId }
                            val registeredProfiles = userService.getUserProfileByIds(registeredUserIds)
                            val registeredUsersArray = mapper.createArrayNode()
                            registeredProfiles.userProfileList.forEach { profile ->
                                val userJson = mapper.createObjectNode()
                                userJson.put("userId", profile.id)
                                userJson.put("username", profile.username)
                                userJson.put("email", profile.email)
                                userJson.put("avatarUrl", profile.avatarUrl)
                                registeredUsersArray.add(userJson)
                            }
                            data.set<JsonNode>("registeredUsers", registeredUsersArray)

                            return@post call.respond(HttpStatusCode.OK, data)
                        } catch (t: Throwable) {
                            return@post call.respond(
                                HttpStatusCode.InternalServerError, message = t.message.toString()
                            )
                        }
                    }
                }
            }

            /**
             * Get information of a registration by its id. Use to fetch the registration status of a new user.
             * i.g. classroom is already loaded and a new user is registered -> fetch the registration status of that user only
             */
            requireLogin {
                get("registration/{regId}/fetch") {

                    try {
                        val regId = call.parameters.getOrFail("regId");
                        val mbRes = lsService.getLSessionRegistrationById(regId)
                        val registration: ObjectNode = mapper.createObjectNode()

                        registration.put("id", mbRes.regId)
                        registration.put("regStatus", mbRes.registration.regStatus)
                        registration.put("regTime", mbRes.registration.regTime)
                        if (mbRes.registration.hasState()) {
                            registration.set<JsonNode>(
                                "userState", mapper.valueToTree(mbRes.registration.state.toPojo())
                            )
                        }

                        val upRes = userService.getUserProfileById(mbRes.registration.userId)
                        val profile =
                            mapper.valueToTree<JsonNode>(Profile.fromGetUserProfileResponseProto(upRes.userProfile))

                        registration.set<JsonNode>("profile", profile)

                        return@get call.respond(HttpStatusCode.OK, registration)
                    } catch (t: Throwable) {
                        return@get call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                    }
                }
            }

            /**
             * This allow user to register to a learning session
             * Verification done:
             * - The user have to logged in
             *  registerLSession
             */
            requireLogin {
                retrieveProfile {
                    post("{lsId}/registration/register") {
                        try {
                            val lsId = call.parameters.getOrFail("lsId")
                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val lsRes = lsService.getSessionOnAllById(lsId)

                            if (lsRes.lsessionDetails.creatorId == profileResponse.user.id) return@post call.respond(
                                HttpStatusCode.BadRequest, message = "Đăng ký buổi học không thành công"
                            )
                            if (lsRes.classroomSettings.maxRegistration > 0 && lsRes.classroomSettings.maxRegistration <= lsRes.lsessionDetails.state.registered) return@post call.respond(
                                HttpStatusCode.BadRequest, message = "Đăng ký buổi học không thành công"
                            )

                            val registrations =
                                lsRes.registrationsMap.values.filter { it.userId == profileResponse.user.id }
                            if (registrations.isNotEmpty()) {
                                val registration = registrations.first()
                                return@post reRegister(
                                    call, profileResponse, registration.id, lsRes.lsessionDetails
                                )
                            }

                            val clrUserState = ClassroomUserState(
                                ShareScreenStatus.NONE,
                                RaiseHandStatus.NONE,
                                UserAvailableStatus.OFFLINE
                            )
                            val pojo = LSessionRegistration(
                                ObjectId().toHexString(),
                                lsId,
                                lsRes.lsessionDetails.creatorId,
                                profileResponse.user.id,
                                LSRegStatus.WAITING_CONFIRMED,
                                System.currentTimeMillis(),
                                clrUserState
                            )

                            val mbRes = lsService.createSessionRegistration(pojo)

                            when (mbRes.status.code) {
                                Status.Code.OK.value() -> {
                                    // init and save notification
                                    val extendedData = RegisterND(lsId, mbRes.regId)
                                    val notification = Notification(
                                        profileResponse.user.id,
                                        //UserTarget(lsRes.lsessionDetails.creatorId),
                                        ClassroomTarget(lsRes.lsessionDetails.id),
                                        "Học viên ${profileResponse.user.username} đã gởi yêu cầu tham gia buổi học",
                                        Instant.now().plusSeconds(300),
                                        extendedData
                                    )

                                    saveNotification(notification)

                                    return@post call.respond(HttpStatusCode.OK, mapOf("lsmId" to mbRes.regId))
                                }

                                Status.Code.ALREADY_EXISTS.value() -> {
                                    return@post call.respond(HttpStatusCode.Conflict, message = mbRes.status.message)
                                }

                                else -> {
                                    return@post call.respond(
                                        HttpStatusCode.InternalServerError,
                                        mapOf("backendErrorCode" to mbRes.status.code)
                                    )
                                }
                            }

                        } catch (t: Throwable) {
                            return@post call.respond(
                                HttpStatusCode.InternalServerError, message = t.message.toString()
                            )
                        }
                    }
                }
            }

            /**
             * This allows user to unregister to a learning session
             * Verification done:
             * - The logged-in user must be the registration represented by id
             * unregisterLSession
             */
            requireLogin {
                retrieveProfile {
                    post("/registration/{regId}/unregister") {
                        try {
                            val regId = call.parameters.getOrFail("regId")
                            val mbRes = lsService.getLSessionRegistrationById(regId)

                            if (!listOf(
                                    LSRegStatus.WAITING_CONFIRMED, LSRegStatus.REGISTERED, LSRegStatus.REJECTED
                                ).contains(LSRegStatus.valueOf(mbRes.registration.regStatus))
                            ) {
                                return@post call.respond(
                                    HttpStatusCode.BadRequest, message = "Hủy đăng ký buổi học không thành công"
                                )
                            }

                            val profileResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                            if (mbRes.registration.lsOwner == profileResponse.user.id) call.respond(
                                HttpStatusCode.OK, message = "Hủy đăng ký buổi học không thành công"
                            )

                            if (profileResponse.user.id.equals(mbRes.registration.userId)) {
                                val updateRes = lsService.updateRegistrationRegStatus(regId, LSRegStatus.CANCELLED)

                                if (updateRes.hasStatus() && updateRes.status.code == Status.Code.OK.value()) {
                                    // create a notification for unregister of the user
                                    val extendedData = RegistrationCancelledND(
                                        mbRes.registration.lsId, profileResponse.user.id
                                    )
                                    val notification = Notification(
                                        profileResponse.user.id,
                                        MultiTarget(
                                            listOf(
                                                UserTarget(profileResponse.user.id),
                                                ClassroomTarget(mbRes.registration.lsId)
                                            )
                                        ),
                                        "Học viên ${profileResponse.user.username} hủy yêu cầu tham gia buổi học",
                                        Instant.now().plusSeconds(300),
                                        extendedData
                                    )

                                    saveNotification(notification)

                                    return@post call.respond(HttpStatusCode.OK, mapOf("lsmId" to mbRes.regId))
                                } else {
                                    return@post call.respond(
                                        HttpStatusCode.InternalServerError,
                                        mapOf("backendErrorCode" to updateRes.status.code)
                                    )
                                }
                            } else {
                                return@post call.respond(
                                    HttpStatusCode.BadRequest,
                                    message = "Only registered user of the learning session can cancel a registration"
                                )
                            }
                        } catch (t: Throwable) {
                            return@post call.respond(HttpStatusCode.InternalServerError, message = t.message.toString())
                        }
                    }
                }
            }
        }
    }
}
