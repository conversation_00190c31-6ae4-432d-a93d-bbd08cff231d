package portal.backend.controllers

object MimeMap {

    val mimeToExt = mapOf(
            "image/jpeg" to "jpg",
            "image/jpg" to "jpg",
            "image/gif" to "gif",
            "image/vnd.microsoft.icon" to "ico",
            "image/png" to "png"
    )

    val extToMime = mapOf(
            "jpg" to "image/jpg",
            "gif" to "image/gif",
            "png" to "image/png"
    )

}