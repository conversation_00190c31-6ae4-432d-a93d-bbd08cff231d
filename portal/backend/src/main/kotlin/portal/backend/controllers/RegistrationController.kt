package portal.backend.controllers

import common.libs.captcha.CaptchaUtil
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.util.*
import jayeson.lib.access.ktor.ActionDoLogin
import jayeson.lib.access.ktor.action.AccessChecker
import jayeson.lib.access.ktor.router.requireLogin
import org.koin.ktor.ext.inject
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.IUserServiceGateway
import portal.backend.gateways.UserLoginCache
import portal.backend.gateways.UserServiceGatewayImpl
import portal.backend.models.*
import portal.backend.utility.LoginUtils
import proto.portal.user.UserMessage

fun Application.registrationController() {
    val userService: IUserServiceGateway by inject<UserServiceGatewayImpl>()
    val userLoginCache: UserLoginCache by inject<UserLoginCache>()
    val actionDoLogin by inject<ActionDoLogin>()
    val captcha: CaptchaUtil by inject()

    routing {
        route("/api/user") {
            /**
             * Register user by email-password
             */
            post("/register") {
                try {
                    val body: RegistrationUser = call.receiveNullable<RegistrationUser>() ?: return@post call.respond(
                        HttpStatusCode.BadRequest, mapOf("backendErrorCode" to Status.Code.INVALID_ARGUMENT.value())
                    )

                    val isVerifiedCaptcha: Boolean = captcha.isVerified(body.captcha)

                    if (!isVerifiedCaptcha) return@post call.respond(HttpStatusCode.BadRequest, "captcha error")

                    if (!isValidEmail(body.email)) {
                        return@post call.respond(HttpStatusCode.BadRequest, "invalid email")
                    } else if (!isValidUsername(body.username)) {
                        return@post call.respond(HttpStatusCode.BadRequest, "invalid username")
                    }

                    body.password = LoginUtils.doHashMD5(body.password)
                    val createUserResponse: UserMessage.EmailRegistrationResponse = userService.registerByEmail(body)

                    if (createUserResponse.status.code == Status.Code.OK.value()) {
                        return@post call.respond(mapOf("userId" to createUserResponse.userId))
                    } else {
                        return@post call.respond(
                            HttpStatusCode.InternalServerError, mapOf(
                                "message" to createUserResponse.status.message,
                                "backendErrorCode" to createUserResponse.status.code
                            )
                        )
                    }
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                }
            }

            /**
             * Get sitekey for hcaptcha
             */
            get("/captcha/sitekey") {
                try {
                    return@get call.respond(HttpStatusCode.OK, mapOf("sitekey" to captcha.getSiteKey()))
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                }
            }

            /**
             * Check if a username is available
             * Used in the register form
             */
            get("/name/{username}/exist") {
                try {
                    val username: String = call.parameters.getOrFail("username")
                    val response = userService.getUserByUsername(username)

                    if (response.status.code != Status.OK.code.value()) return@get call.respond(HttpStatusCode.NotFound)
                    else return@get call.respond(HttpStatusCode.OK)
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Get all linked registrations, include email-password and social accounts
                     */
                    get("/linked-registrations") {
                        val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                        try {
                            val response = userService.getLinkedRegistrations(profile.user.email)

                            if (response.status.code == Status.Code.OK.value()) {
                                return@get call.respond(HttpStatusCode.OK, response.registrationNameList)
                            }

                            return@get call.respond(
                                HttpStatusCode.InternalServerError, mapOf(
                                    "message" to response.status.message, "backendErrorCode" to response.status.code
                                )
                            )
                        } catch (e: Exception) {
                            call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                        }
                    }
                }
            }

            /**
             * Verify the email using the verification code, and create or merge user profile if verify success
             */
            route("/verify-email") {
                post {
                    val dto = call.receive<EmailVerificationDto>()
                    try {
                        val response = userService.verifyEmail(dto, false)
                        if (response.status.code != Status.Code.OK.value()) {
                            return@post call.respond(
                                HttpStatusCode.InternalServerError, mapOf(
                                    "message" to response.status.message, "backendErrorCode" to response.status.code
                                )
                            )
                        }

                        val profileResponse = userService.createOrMergeUserProfile(response.registrationId)
                        if (profileResponse.status.code != Status.Code.OK.value()) {
                            return@post call.respond(
                                HttpStatusCode.InternalServerError, mapOf(
                                    "message" to profileResponse.status.message, "backendErrorCode" to profileResponse.status.code
                                )
                            )
                        }

                        val userData: UserMessage.UserProto = profileResponse.user
                        // Generate temp password for login and cache for check in UserDataAccessor
                        val password = LoginUtils.generateRandomPassword()
                        userLoginCache.set(
                            userData.email, BasicUserData(
                                userData.id,
                                userData.email,
                                LoginUtils.doHashMD5(password),
                                userData.verified,
                                userData.registrationId
                            )
                        )

                        val call: ApplicationCall =
                            LoginUtils.createInternalLoginCall(call, actionDoLogin, userData.email, password)

                        //  check access login
                        val checker = AccessChecker(call)
                        val isLoggedInSuccessful = checker.isLoggedInSuccessful()
                        val loginMessage: String = checker.getLoginMessage()

                        if (isLoggedInSuccessful) {
                            call.respond(HttpStatusCode.OK, mapOf("message" to "Verification succeed"))
                        } else {
                            call.respond(HttpStatusCode.Found, mapOf("message" to loginMessage))
                        }
                    } catch (e: Exception) {
                        call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                    }
                }

                /**
                 * Send verification code to email for a registration
                 */
                post("/send-code/{registrationId}") {
                    val registrationId: String = call.parameters.getOrFail("registrationId")
                    try {
                        val response = userService.sendVerificationEmail(registrationId)
                        if (response.status.code == Status.Code.OK.value()) {
                            call.respond(
                                HttpStatusCode.OK, mapOf(
                                    "message" to if (response.isSent) "Send verification email succeed" else "Already sent",
                                    "isSent" to response.isSent,
                                    "lastSent" to response.lastSent.seconds, // Epoch seconds
                                )
                            )
                        } else {
                            val statusCode =
                                if (response.status.code == Status.Code.NOT_FOUND.value()) HttpStatusCode.NotFound
                                else HttpStatusCode.InternalServerError
                            call.respond(
                                statusCode, mapOf(
                                    "message" to response.status.message, "backendErrorCode" to response.status.code
                                )
                            )
                        }
                    } catch (e: Exception) {
                        call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                    }
                }
            }

            route("/registrations") {
                /**
                 * Get basic registration metadata
                 */
                get("/{registrationId}") {
                    val registrationId: String = call.parameters.getOrFail("registrationId")
                    try {
                        val response = userService.getRegistrationMetadata(registrationId)
                        if (response.status.code == Status.Code.OK.value()) {
                            call.respond(
                                HttpStatusCode.OK, mapOf(
                                    "registrationId" to response.registrationId,
                                    "registrationType" to response.registrationType,
                                    "email" to response.email,
                                    "isVerified" to response.isVerified,
                                )
                            )
                        } else {
                            val statusCode =
                                if (response.status.code == Status.Code.NOT_FOUND.value()) HttpStatusCode.NotFound
                                else HttpStatusCode.InternalServerError
                            call.respond(
                                statusCode, mapOf(
                                    "message" to response.status.message, "backendErrorCode" to response.status.code
                                )
                            )
                        }
                    } catch (e: Exception) {
                        call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                    }
                }

                /**
                 * Check email is already registered, to be used by the registration form
                 */
                post("/email-exist") {
                    try {
                        val dto = call.receive<RegistrationEmailExistDto>()
                        val checkResult = userService.checkRegistrationEmailExist(dto)

                        if (checkResult.status.code == Status.OK.code.value()) {
                            val statusCode = if (checkResult.isExist) HttpStatusCode.OK else HttpStatusCode.NotFound
                            return@post call.respond(statusCode)
                        }

                        call.respond(
                            HttpStatusCode.InternalServerError, mapOf(
                                "message" to checkResult.status.message,
                                "backendErrorCode" to checkResult.status.code
                            )
                        )
                    } catch (e: Exception) {
                        call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                    }
                }

                /**
                 * Add a missing social registration email if can not infer the email from the social account
                 */
                post("/add-social-email") {
                    try {
                        val dto = call.receive<AddSocialEmailDto>()
                        val response = userService.addMissingSocialEmail(dto)

                        val errorBody = mapOf(
                            "message" to response.status.message,
                            "backendErrorCode" to response.status.code
                        )

                        return@post when (response.status.code) {
                            Status.OK.code.value() -> call.respond(HttpStatusCode.OK)
                            Status.NOT_FOUND.code.value() -> call.respond(HttpStatusCode.NotFound, errorBody)
                            Status.INVALID_ARGUMENT.code.value() -> call.respond(HttpStatusCode.BadRequest, errorBody)
                            else -> call.respond(HttpStatusCode.InternalServerError, errorBody)
                        }
                    } catch (e: Exception) {
                        call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                    }
                }
            }
        }
    }
}

/**
 * Checks if a given username is valid.
 *
 * A valid username is one that matches the following rules:
 * 1. Starts with a letter
 * 2. Followed by 3-29 alphanumeric characters or underscores
 *
 * @param username The username to check
 * @return true if the username is valid, false otherwise
 */
fun isValidUsername(username: String): Boolean {
    val regex = "^[A-Za-z][A-Za-z0-9_.]{3,29}\$".toRegex()
    return regex.matches(username)
}

/**
 * Checks if a given email address is valid.
 *
 * A valid email address is one that matches the following rules:
 * 1. Matches the basic format of an email address (letters, numbers, underscores, periods, and hyphens)
 * 2. Does not contain consecutive dots
 * 3. Has a TLD (top-level domain) of at least 2 characters
 *
 * @param email The email address to check
 * @return true if the email address is valid, false otherwise
 */
fun isValidEmail(email: String): Boolean {
    val emailRegex = "^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$".toRegex()

    if (!emailRegex.matches(email)) return false // Basic format check

    if (".." in email) return false // Prevent consecutive dots

    val domainPart = email.substringAfter("@", "")
    val tld = domainPart.substringAfterLast(".", "")
    if (tld.length < 2) return false // Ensure TLD is at least 2 characters

    return true
}