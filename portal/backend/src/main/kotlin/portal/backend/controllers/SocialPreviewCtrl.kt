package portal.backend.controllers

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.freemarker.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.bson.BsonType
import org.koin.ktor.ext.inject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import portal.backend.gateways.ILSessionServiceGateway
import portal.backend.gateways.MetadataServiceGateway
import portal.backend.models.DOC_IN_PROFILE_TYPE
import portal.backend.models.DocInProfileInfoDetail
import portal.backend.utility.defaultMapper
import portal.backend.utility.respondWithException
import portal.lsession.pojo.toPojo


/**
 *
 * <AUTHOR>
 */

data class PreviewDto(
    val url: String,
    val title: String,
    val imageUrl: String,
    val description: String
)

fun Application.socialPreviewController() {
    val logger: Logger = LoggerFactory.getLogger(javaClass.name)
    val metadataServiceGateway: MetadataServiceGateway by inject()
    val lsessionServiceGateway: ILSessionServiceGateway by inject()

    routing {
        route("/social/preview") {
            get("/doc/{docId}") {
                val docId: String = call.parameters["docId"] ?: run {
                    logger.error("missing docId in request")
                    return@run call.respond(HttpStatusCode.BadRequest, "Missing docId")
                }.toString()

                try {
                    val grpcRes = metadataServiceGateway.loadMetadataDoc(
                        DOC_IN_PROFILE_TYPE, listOfNotNull(
                            Triple("docGlobalId", BsonType.STRING.name, docId),
                        )
                    ).await()

                    val res = grpcRes.metadataList.firstOrNull() ?: kotlin.run {
                        logger.debug("Not found document {} for social preview", docId)
                        return@get call.respondText("Document Not Found", status = HttpStatusCode.NotFound)
                    }

                    val docDetails = defaultMapper.readValue(res.metadataDetails, DocInProfileInfoDetail::class.java)

                    if (!docDetails.shared) {
                        logger.debug("not share document {} for social preview", docId)
                        return@get call.respondText(
                            "Document Not Sharing",
                            status = HttpStatusCode.Forbidden
                        )
                    }

                    val userAgent = call.request.userAgent() ?: ""
                    logger.info("preview social {} user agent {}", docId, userAgent)

                    val preview = getSocialPreviewTemplate(userAgent, docDetails.toPreviewDto(res.editorType))
                    logger.info("preview social {}", preview.toString())
                    return@get call.respond(preview)
                } catch (e: Exception) {
                    logger.error("social preview doc failed... ", e)
                    return@get call.respondWithException(e)
                }
            }

            get("/classroom/{classroomId}") {
                val classroomId: String = call.parameters["classroomId"] ?: run {
                    logger.error("missing classroomId in request")
                    return@run call.respond(HttpStatusCode.BadRequest, "Missing classroomId")
                }.toString()

                try {
                    val grpcRes = lsessionServiceGateway.getLSession(classroomId)
                    val details = grpcRes.lsessionDetails.toPojo()

                    val userAgent = call.request.userAgent() ?: ""
                    logger.info("classroom preview social {} user agent {}", classroomId, userAgent)

                    val preview = getSocialPreviewTemplate(
                        userAgent,
                        PreviewDto(
                            url = "",
                            title = "viclass - Lớp học: ${details.title}",
                            imageUrl = if (details.imgUrl.isNotBlank() && details.imgUrl != "NoAvatar")
                                details.imgUrl else "/static/assets/images/vi-logo.jpg",
                            description = "Lớp học ${details.title} được tạo bởi viclass.vn"
                        )
                    )
                    logger.info("preview social {}", preview.toString())
                    return@get call.respond(preview)
                } catch (e: Exception) {
                    logger.error("social preview classroom failed... ", e)
                    return@get call.respondWithException(e)
                }
            }
        }
    }
}

fun editorTypeToName(editorType: String): String {
    return when (editorType) {
        "GeometryEditor" -> "Hình học"
        "FreeDrawingEditor" -> "Vẽ tự do"
        "WordEditor" -> "Văn bản"
        "MathEditor" -> "Công thức toán"
        "MathGraphEditor" -> "Đồ thị hàm số"
        else -> ""
    }
}

fun DocInProfileInfoDetail.toPreviewDto(editorType: String): PreviewDto {
    val editorName = editorTypeToName(editorType)
    return PreviewDto(
        url = "",
        title = "viclass: " + (this.docName ?: "Tài liệu không tên"),
        imageUrl = this.previewUrl ?: "/static/assets/images/vi-logo.jpg",
        description = "Tài liệu $editorName được tạo bởi "
                + (if (this.ownerUserName.isBlank()) "" else "${this.ownerUserName} ")
                + "viclass.vn"
    )
}

fun getSocialPreviewTemplate(userAgent: String, previewDto: PreviewDto): FreeMarkerContent {
    if (userAgent.contains("_zbot")) {
        return FreeMarkerContent("zalo_preview.ftl", previewDto)
    }
    if (userAgent.contains("facebookexternalhit") || userAgent.contains("facebookcatalog")) {
        return FreeMarkerContent("facebook_preview.ftl", previewDto)
    }
    if (userAgent.contains("Twitterbot")) {
        return FreeMarkerContent("twitter_preview.ftl", previewDto)
    }
    if (userAgent.contains("Google") || userAgent.contains("Gmail")) {
        return FreeMarkerContent("google_preview.ftl", previewDto)
    }

    return FreeMarkerContent("default_preview.ftl", previewDto)
}