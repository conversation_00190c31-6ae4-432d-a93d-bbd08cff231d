package portal.backend.controllers

import common.libs.captcha.CaptchaUtil
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import org.koin.ktor.ext.inject
import portal.backend.models.SupportHomepageFeedbackForm
import portal.backend.gateways.IUserServiceGateway
import portal.backend.gateways.UserServiceGatewayImpl
import kotlin.getValue

fun Application.supportController() {
    val captcha: CaptchaUtil by inject()
    val userService: IUserServiceGateway by inject<UserServiceGatewayImpl>()

    routing {
        route("/api/support") {
            post("/homepage-feedback") {
                try {
                    val body = call.receiveNullable<SupportHomepageFeedbackForm>() ?: return@post call.respond(
                        HttpStatusCode.BadRequest, mapOf("backendErrorCode" to Status.Code.INVALID_ARGUMENT.value())
                    )

                    val isVerifiedCaptcha: Boolean = captcha.isVerified(body.captchaToken)
                    if (!isVerifiedCaptcha) throw IllegalArgumentException("Wrong captcha")

                    // Save feedback form to user service
                    val response = userService.saveFeedback(body)
                    if (!response.success) {
                        throw IllegalStateException("Failed to save feedback")
                    }

                    call.respond(HttpStatusCode.NoContent)
                    return@post
                } catch (e: Exception) {
                    val errMessage: String? = e.message
                    call.respond(
                        HttpStatusCode.InternalServerError, mapOf(
                            "backendErrorCode" to Status.INTERNAL.code.value(),
                            "message" to (errMessage ?: "failed")
                        )
                    )
                }
            }
        }
    }
}
