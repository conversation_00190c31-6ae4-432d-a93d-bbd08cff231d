package portal.backend.controllers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.captcha.CaptchaUtil
import io.grpc.Status
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.util.*
import jayeson.lib.access.basic.BasicLoginData
import jayeson.lib.access.ktor.AccessRequestAttrs
import jayeson.lib.access.ktor.ActionDoLogin
import jayeson.lib.access.ktor.DataMethod
import jayeson.lib.access.ktor.RURL
import jayeson.lib.access.ktor.action.AccessChecker
import jayeson.lib.access.ktor.annotation.DoLoginConfiguration
import jayeson.lib.access.ktor.annotation.DoLogoutConfiguration
import jayeson.lib.access.ktor.router.doLogin
import jayeson.lib.access.ktor.router.doLogout
import jayeson.lib.access.ktor.router.requireLogin
import org.koin.ktor.ext.inject
import portal.backend.configs.ConfigBean
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.controllers.router.retrieveProfile
import portal.backend.gateways.*
import portal.backend.models.*
import portal.backend.utility.LoginUtils
import portal.backend.utility.LoginUtils.VERIFY_ONETIME_LOGIN_PATH
import portal.backend.utility.LoginUtils.VERIFY_ONETIME_LOGIN_QUERY_NAME
import portal.backend.utility.LoginUtils.VERIFY_ONETIME_LOGIN_REDIRECT_URL_KEY
import portal.backend.utility.defaultMapper
import portal.backend.utility.respondWithErrorStatus
import portal.backend.utility.respondWithException
import portal.backend.utility.respondWithRedirect
import portal.user.pojo.RegistrationType
import proto.portal.user.UserMessage
import proto.portal.user.UserMessage.CheckPasswordResponse
import java.net.URL
import java.net.URLEncoder
import java.util.*

val OK_CODE = Status.Code.OK.value()

/**
 * This controller handle interaction related to User Controller
 */
fun Application.userController() {
    val userService: IUserServiceGateway by inject<UserServiceGatewayImpl>()
    val userLoginCache: UserLoginCache by inject<UserLoginCache>()
    val actionDoLogin by inject<ActionDoLogin>()
    val captcha: CaptchaUtil by inject()
    val userForgotPwCache: UserForgotPwCacheService by inject()
    val jwtTokenCache: JwtTokenCacheService by inject()

    /**
     * check email_verified before write session
     */
    fun beforeWriteSession(call: ApplicationCall): Boolean {
        val ld = call.attributes[AccessRequestAttrs.LOGIN_DATA]
        if ((ld.userData as BasicUserData).getEmailVerified() == true) return true

        val failLoginData = BasicLoginData(ld.userData, null, false, "Not verified yet")
        val returnURL = RURL("/registration/verify/${(failLoginData.userData as BasicUserData).getRegistrationId()}")

        call.attributes.put(AccessRequestAttrs.LOGIN_DATA, failLoginData)
        call.attributes.put(AccessRequestAttrs.RETURN_URL, returnURL)

        return false
    }

    routing {
        route("/api/user") {
            /**
             * An action that login the user
             */
            doLogin(
                DoLoginConfiguration(credentialSource = DataMethod.JSON), ::beforeWriteSession
            ) {
                post("/login") {
                    try {
                        val checker = AccessChecker(call)
                        val ar = checker.getAccessRequest()
                        val isLoginSuccess = checker.isLoggedInSuccessful()
                        val loginMessage: String = checker.getLoginMessage()

                        if (ar != null && ar.username != null && isLoginSuccess) {
                            // update last-login
                            userService.updateLastLoginTime(ar.username!!)
                            call.respond(jacksonObjectMapper().createObjectNode())
                        } else if (checker.returnUrl() != "") call.respond(
                            HttpStatusCode.Found, mapOf("rURL" to checker.returnUrl(), "message" to loginMessage)
                        )
                        else call.respond(HttpStatusCode.Unauthorized, message = loginMessage)
                    } catch (e: Exception) {
                        return@post call.respondWithException(e)
                    }
                }
            }

            /**
             * An action that login the user with social account
             */
            post("/login/social") {
                val dto = call.receive<SocialLoginDto>()
                try {
                    val tokenResponse = userService.verifySocialToken(dto)
                    if (tokenResponse.status.code != OK_CODE) return@post call.respondWithErrorStatus(
                        tokenResponse.status
                    )

                    val tokenInfo = tokenResponse.tokenInfo
                    var regId = tokenResponse.registrationId
                    var regEmail = tokenResponse.registrationEmail
                    var isRegVerified = tokenResponse.verified

                    val isNewRegistration = regId.isEmpty()
                    val isSocialEmailChanged =
                        !isNewRegistration && isRegVerified && regEmail.isNotEmpty() && tokenInfo.email.isNotEmpty() && regEmail != tokenInfo.email

                    // handle new registration
                    if (isNewRegistration) {
                        val addSocialReg = userService.addSocialRegistration(tokenInfo)
                        if (addSocialReg.status.code != OK_CODE) return@post call.respondWithErrorStatus(addSocialReg.status)
                        // use info from social token for new registration
                        regId = addSocialReg.registrationId
                        regEmail = tokenInfo.email
                        isRegVerified = tokenInfo.emailVerified
                    }

                    // handle email missing / not verified
                    if (regEmail.isNullOrEmpty() || !isRegVerified) {
                        val redirectUrl = if (regEmail.isNullOrEmpty()) "/registration/missing-email/${regId}"
                        else "/registration/verify/${regId}"

                        return@post call.respondWithRedirect(redirectUrl, "social account email not verified")
                    }

                    // handle unlink / relink on social email changed
                    if (isSocialEmailChanged) {
                        val addSocialEmail = userService.unlinkRelinkSocialRegistration(regId, tokenInfo)
                        if (addSocialEmail.status.code != OK_CODE) return@post call.respondWithErrorStatus(
                            addSocialEmail.status
                        )
                    }

                    // handle create or merge user profile if not exist
                    val userResponse = userService.getUserByEmail(regEmail)
                    if (userResponse.status.code != OK_CODE) return@post call.respondWithErrorStatus(userResponse.status)

                    var profileId = userResponse.user.id
                    if (profileId.isNullOrEmpty()) {
                        val createProfileRes = userService.createOrMergeUserProfile(regId, tokenInfo)
                        if (createProfileRes.status.code != OK_CODE) return@post call.respondWithErrorStatus(
                            createProfileRes.status
                        )
                        profileId = createProfileRes.user.id
                    }

                    // handle internal login with cached temp Password for UserDataAccessor
                    val tempPassword: String = LoginUtils.generateRandomPassword()
                    userLoginCache.set(
                        regEmail, BasicUserData(
                            profileId, regEmail, LoginUtils.doHashMD5(tempPassword), true, regId
                        )
                    )

                    val call: ApplicationCall =
                        LoginUtils.createInternalLoginCall(call, actionDoLogin, regEmail, tempPassword)

                    val checker = AccessChecker(call)
                    val ar = checker.getAccessRequest()
                    val isLoggedInSuccessful = checker.isLoggedInSuccessful()
                    val loginMessage: String = checker.getLoginMessage()

                    //  handle login failed
                    if (!isLoggedInSuccessful) {
                        if (checker.returnUrl().isNotEmpty()) {
                            return@post call.respondWithRedirect(checker.returnUrl(), loginMessage)
                        } else {
                            return@post call.respond(HttpStatusCode.Unauthorized, message = loginMessage)
                        }
                    }

                    // handle get ar fail
                    if (ar == null || ar.username == null) return@post call.respond(
                        HttpStatusCode.Unauthorized, message = loginMessage
                    )

                    // update last-login
                    userService.updateLastLoginTime(ar.username!!)

                    // handle login success with notification redirection
                    if (isNewRegistration) return@post call.respondWithRedirect(
                        "/registration-completion", loginMessage
                    )
                    if (isSocialEmailChanged) return@post call.respondWithRedirect(
                        "/email-change-notification/${
                            URLEncoder.encode(
                                Base64.getEncoder().encodeToString(tokenResponse.registrationEmail.toByteArray()),
                                "UTF-8"
                            )
                        }", loginMessage
                    )
                    else return@post call.respond(HttpStatusCode.OK)
                } catch (e: Exception) {
                    return@post call.respondWithException(e)
                }
            }

            requireLogin {
                /**
                 * Fetch current user profile
                 */
                get("/profile/fetch") {
                    try {
                        val ar = AccessChecker(call).getAccessRequest()
                        val result: UserMessage.GetUserProfileResponse? =
                            ar?.username?.let { userService.getUserProfileByUsernameOrEmail(it) }

                        if (result?.status?.code == Status.Code.NOT_FOUND.value() || result?.userProfile == null) {
                            return@get call.respond(
                                HttpStatusCode.Unauthorized, result?.status?.message ?: "Unauthorized"
                            )
                        }

                        return@get call.respond(Profile.fromGetUserProfileResponseProto(result.userProfile))
                    } catch (e: Throwable) {
                        call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                    }
                }

                retrieveProfile {
                    /**
                     * Update current user profile. To be used in the user info profile page
                     */
                    patch("/profile") {
                        try {
                            val cookieProfile: UserMessage.GetUserResponse =
                                call.attributes[RetrieveProfile.PROFILE_ATTR]
                            val dto = call.receive<UpdateProfileDTO>().copy(email = cookieProfile.user.email)

                            // check profile exists
                            val ar = AccessChecker(call).getAccessRequest()
                            val result: UserMessage.GetUserProfileResponse? =
                                ar?.username?.let { userService.getUserProfileByUsernameOrEmail(it) }
                            if (result?.status?.code == Status.Code.NOT_FOUND.value() || result?.userProfile == null) {
                                return@patch call.respond(
                                    HttpStatusCode.Unauthorized, result?.status?.message ?: "Unauthorized"
                                )
                            }

                            if (!dto.name.isNullOrEmpty() && !isValidFullName(dto.name)) {
                                return@patch call.respond(HttpStatusCode.BadRequest, "Invalid full name")
                            } else if (!dto.phone.isNullOrEmpty() && !isValidVietnamPhoneNumber(dto.phone)) {
                                return@patch call.respond(HttpStatusCode.BadRequest, "Invalid phone number")
                            }

                            val updateRes = userService.updateProfile(dto)
                            if (updateRes.status.code != Status.Code.OK.value()) {
                                return@patch call.respond(HttpStatusCode.InternalServerError)
                            }

                            return@patch call.respond(Profile.fromGetUserProfileResponseProto(updateRes.profile))
                        } catch (e: Throwable) {
                            call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                        }
                    }

                    /**
                     * Unlink social account from the current user profile
                     */
                    delete("/profile/social/{social}") {
                        val social: RegistrationType = RegistrationType.fromSocial(
                            call.parameters["social"] ?: return@delete call.respond(HttpStatusCode.BadRequest)
                        ) ?: return@delete call.respond(HttpStatusCode.BadRequest)
                        val cookieProfile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]

                        // check profile exists
                        val ar = AccessChecker(call).getAccessRequest()
                        val result: UserMessage.GetUserProfileResponse? =
                            ar?.username?.let { userService.getUserProfileByUsernameOrEmail(it) }
                        if (result?.status?.code == Status.Code.NOT_FOUND.value() || result?.userProfile == null) {
                            return@delete call.respond(
                                HttpStatusCode.BadRequest, result?.status?.message ?: "Not found user"
                            )
                        }

                        val unlinkResult = userService.unlinkSocial(cookieProfile.user.email, social)

                        if (unlinkResult.status.code != Status.Code.OK.value()) {
                            return@delete call.respond(HttpStatusCode.InternalServerError)
                        }
                        return@delete call.respond(HttpStatusCode.NoContent)
                    }
                }
            }

            /**
             * Brief profile to be used in the LSession service
             */
            post("/brief-profile/fetch") {
                try {
                    val ids = call.receive<Array<String>>()

                    if (!ids.isArrayOf<String>()) {
                        return@post call.respond(HttpStatusCode.BadRequest, message = "data not valid")
                    }

                    val respond: UserMessage.GetUserProfileByIdsResponse = userService.getUserProfileByIds(ids.toList())

                    if (respond.status.code != Status.Code.OK.value()) {
                        return@post call.respond(
                            HttpStatusCode.InternalServerError, mapOf("backendErrorCode" to respond.status.code)
                        )
                    }


                    val arr = respond.userProfileList.foldRight(arrayListOf<Profile>()) { u, arr ->
                        val r = Profile.fromGetUserProfileResponseProto(u)
                        arr.add(r)
                        return@foldRight arr
                    }

                    return@post call.respond(HttpStatusCode.OK, arr)
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                }
            }

            requireLogin {
                retrieveProfile {
                    /**
                     * Update current user avatar. To be used in the user info profile page
                     * We will upload the original file to filestore service before storing the avatar url in user service
                     */
                    post("/profile/update-avatar") {
                        val profile: UserMessage.GetUserResponse = call.attributes[RetrieveProfile.PROFILE_ATTR]
                        val dto = call.receive<UpdateAvatarDto>()

                        try {
                            val response = userService.updateAvatar(profile.user.id, dto.avatarUrl)
                            if (response.status.code != Status.Code.OK.value()) return@post call.respondWithErrorStatus(
                                response.status
                            )

                            return@post call.respond(HttpStatusCode.OK)
                        } catch (e: Exception) {
                            call.respondWithException(e)
                        }
                    }
                }
            }

            doLogout(DoLogoutConfiguration(autoAnswer = false)) {
                /**
                 * Action to log out the current user
                 */
                post("/logout") {
                    return@post call.respond(HttpStatusCode.OK)
                }
            }

            /**
             * Request forgot password token to be sent to the user email
             */
            post("request-forgot-password") {
                try {
                    val recoveryRequest = call.receive<ForgotPasswordRequest>()

                    // check captcha
                    if (!captcha.isVerified(recoveryRequest.captcha)) return@post call.respond(
                        HttpStatusCode.BadRequest, "captcha error"
                    )

                    val check = userForgotPwCache.get(recoveryRequest.email)
                    // check email request
                    if (check != null) {
                        return@post call.respond(
                            HttpStatusCode.TooManyRequests
                        )
                    }

                    val response: UserMessage.SendEmailResetPasswordResponse =
                        userService.sendEmailResetPassword(recoveryRequest.email)

                    if (response.status.code == Status.Code.NOT_FOUND.value()) {
                        return@post call.respond(
                            HttpStatusCode.NotFound, mapOf(
                                "message" to response.status.message, "backendErrorCode" to response.status.code
                            )
                        )
                    }

                    if (response.status.code != Status.Code.OK.value()) {
                        return@post call.respond(
                            HttpStatusCode.InternalServerError, mapOf(
                                "message" to response.status.message, "backendErrorCode" to response.status.code
                            )
                        )
                    }

                    // cache email
                    userForgotPwCache.set(recoveryRequest.email, response.jwtToken)

                    // cache jwt token
                    jwtTokenCache.set(response.jwtToken, recoveryRequest.email)

                    return@post call.respond(
                        HttpStatusCode.OK
                    )
                } catch (e: Exception) {
                    return@post call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                }
            }

            /**
             * Reset password form, required the token from email to success
             */
            post("reset-password") {
                try {
                    val resetRequest = call.receive<ResetPasswordDto>()

                    if (jwtTokenCache.get(resetRequest.jwtToken) == null) return@post call.respond(
                        HttpStatusCode.InternalServerError, message = "jwt token invalid"
                    )

                    if (!isValidPassword(resetRequest.newPassword)) return@post call.respond(
                        HttpStatusCode.BadRequest, "invalid password"
                    )

                    val response: UserMessage.ResetPasswordResponse = userService.resetPassword(
                        ResetPasswordDto(
                            LoginUtils.doHashMD5(resetRequest.newPassword), resetRequest.jwtToken
                        )
                    )

                    if (response.status.code == Status.Code.OK.value()) {
                        jwtTokenCache.delete(resetRequest.jwtToken)
                        return@post call.respond(
                            HttpStatusCode.OK
                        )
                    }

                    return@post call.respond(
                        HttpStatusCode.InternalServerError, mapOf(
                            "message" to response.status.message, "backendErrorCode" to response.status.code
                        )
                    )
                } catch (e: Exception) {
                    return@post call.respond(HttpStatusCode.InternalServerError, e.message.toString())
                }
            }

            /**
             * Verify the reset password token in the URL.
             * We will block the reset password flow if invalid and prompt user to resent a new one
             */
            get("verify-reset-password-token/{jwtToken}") {
                try {
                    val jwtToken: String = call.parameters.getOrFail("jwtToken")

                    if (jwtTokenCache.get(jwtToken) == null) return@get call.respond(
                        HttpStatusCode.InternalServerError, message = "jwt token invalid"
                    )

                    return@get call.respond(HttpStatusCode.OK)
                } catch (e: Exception) {
                    call.respond(HttpStatusCode.InternalServerError, message = e.message.toString())
                }
            }

            requireLogin {
                /**
                 * Change password from user profile page.
                 * Required the current password to be matched
                 */
                post("change-password") {
                    try {
                        val userSecurity = call.receive<ChangePasswordDto>()
                        val ar = AccessChecker(call).getAccessRequest()

                        if (ar == null || ar.username == null) {
                            return@post call.respond(HttpStatusCode.InternalServerError)
                        }

                        // check current password
                        val checkPasswordRes: CheckPasswordResponse = userService.checkPassword(
                            CheckPassword(
                                email = ar.username!!, password = LoginUtils.doHashMD5(userSecurity.password)
                            )
                        )

                        if (checkPasswordRes.status.code != Status.Code.OK.value()) {
                            return@post call.respond(HttpStatusCode.Unauthorized)
                        }

                        if (!isValidPassword(userSecurity.newPassword)) {
                            return@post call.respond(HttpStatusCode.BadRequest, "Invalid new password")
                        }

                        // update password
                        val updatePasswordRes: UserMessage.UpdateUserEmailRegResponse? =
                            userSecurity.newPassword.takeIf { it.isNotEmpty() }?.let {
                                userService.updateUserEmailReg(
                                    UserEmailReg(
                                        email = ar.username!!,
                                        password = LoginUtils.doHashMD5(it),
                                    )
                                )
                            }

                        updatePasswordRes?.let {
                            if (it.status.code != Status.Code.OK.value()) {
                                return@post call.respond(
                                    HttpStatusCode.InternalServerError, mapOf(
                                        "message" to it.status.message, "backendErrorCode" to it.status.code
                                    )
                                )
                            }
                        }

                        val isUpdatePw = updatePasswordRes?.let { it.status.code == Status.Code.OK.value() } ?: false
                        if (!isUpdatePw) {
                            return@post call.respond(HttpStatusCode.InternalServerError)
                        } else {
                            return@post call.respond(HttpStatusCode.OK)
                        }
                    } catch (e: Exception) {
                        return@post call.respondWithException(e)
                    }
                }
            }


            requireLogin {
                /**
                 * Get last login date and created date of current user.
                 * To be used in the profile page
                 */
                get("user-login-information") {
                    try {
                        val ar = AccessChecker(call).getAccessRequest()

                        if (ar == null || ar.username == null) {
                            return@get call.respond(
                                HttpStatusCode.InternalServerError
                            )
                        }

                        val response: UserMessage.GetUserLoginInformationResponse =
                            userService.getUserLoginInformation(ar.username!!)


                        return@get call.respond(
                            HttpStatusCode.OK, mapOf(
                                "email" to response.email,
                                "lastLogin" to response.lastLogin,
                                "dateCreated" to response.dateCreated
                            )
                        )
                    } catch (e: Exception) {
                        return@get call.respondWithException(e)
                    }
                }

                /**
                 * Get email registration info of the current user.
                 * To be used in the profile security page
                 */
                get("email-reg-info") {
                    try {
                        val ar = AccessChecker(call).getAccessRequest()

                        if (ar == null || ar.username == null) {
                            return@get call.respond(
                                HttpStatusCode.InternalServerError
                            )
                        }

                        val response: UserMessage.EmailRegInfoResponse =
                            userService.getEmailRegistrationInfo(ar.username!!)

                        return@get when (response.status.code) {
                            Status.Code.OK.value() -> call.respond(
                                HttpStatusCode.OK, mapOf(
                                    "email" to response.email, "username" to response.username
                                )
                            )

                            Status.Code.NOT_FOUND.value() -> call.respond(
                                HttpStatusCode.NotFound, message = "email registration not found"
                            )

                            else -> call.respond(
                                HttpStatusCode.InternalServerError,
                                message = response.status.message ?: "unknown error with code ${response.status.code}"
                            )
                        }
                    } catch (e: Exception) {
                        return@get call.respondWithException(e)
                    }
                }
            }
        }
        get(VERIFY_ONETIME_LOGIN_PATH) {
            try {
                // Retrieve the Base64-encoded JSON string from the query parameters
                val base64JsonString = call.request.queryParameters[VERIFY_ONETIME_LOGIN_QUERY_NAME]
                val jsonObject = base64JsonString?.let {
                    // Decode the Base64 string and parse it into a JSON object
                    String(Base64.getDecoder().decode(it))
                }?.let {
                    runCatching { defaultMapper.readValue(it, Map::class.java) }.getOrNull()
                } ?: return@get call.respondRedirect("/login?error=invalid_code")

                // Extract the token from the JSON object
                val token = jsonObject[LoginUtils.VERIFY_ONETIME_LOGIN_CODE_KEY] as? String
                    ?: return@get call.respondRedirect("/login?error=invalid_code")

                // Verify and consume the one-time login token
                val verifyAndConsomeTokenResponse = userService.verifyAndConsumeToken(
                    UserMessage.VerifyAndConsumeTokenRequest.newBuilder().setToken(token).build()
                )
                if (verifyAndConsomeTokenResponse.status.code != Status.Code.OK.value()) {
                    return@get call.respondRedirect("/login?error=invalid_code")
                }

                // Fetch registration metadata using the registration ID
                val registration = userService.getRegistrationMetadata(verifyAndConsomeTokenResponse.regId)
                if (registration.status.code != OK_CODE) {
                    return@get call.respondRedirect("/login?error=registration_error")
                }

                // Extract the email and profile ID from the registration metadata
                val regEmail = registration.email
                val profileId = userService.getUserProfileByUsernameOrEmail(regEmail).userProfile.id
                val tempPassword = LoginUtils.generateRandomPassword()

                // Cache the user login data with a temporary password
                userLoginCache.set(
                    regEmail, BasicUserData(
                        profileId,
                        regEmail,
                        LoginUtils.doHashMD5(tempPassword),
                        true,
                        verifyAndConsomeTokenResponse.regId
                    )
                )

                // Create an internal login call and check if the login is successful
                val loginCall = LoginUtils.createInternalLoginCall(call, actionDoLogin, regEmail, tempPassword)
                val checker = AccessChecker(loginCall)

                if (!checker.isLoggedInSuccessful()) {
                    return@get call.respondRedirect("/login?error=login_failed")
                }

                // Update the last login time for the user
                userService.updateLastLoginTime(checker.getAccessRequest()?.username!!)

                // Redirect the user to the specified URL or the default home page
                val redirectUrl = jsonObject[VERIFY_ONETIME_LOGIN_REDIRECT_URL_KEY] as? String ?: "/"
                call.respondRedirect(redirectUrl)
            } catch (e: Exception) {
                // Handle any exceptions by redirecting to the login page with an error
                call.respondRedirect("/login?error=exception")
            }
        }
    }
}

/**
 * Checks if a given password is valid.
 *
 * A valid password is one that matches the following rules:
 * 1. At least one uppercase letter
 * 2. At least one lowercase letter
 * 3. At least one digit
 * 4. At least one special character
 * 5. Length of at least 6 characters
 *
 * @param password The password to check
 * @return true if the password is valid, false otherwise
 */
fun isValidPassword(password: String): Boolean {
    val regex =
        "^(?=.*[A-Z])(?=.*[a-z])(?=.*\\d)(?=.*[!@#\$%^&*(),.?\":{}|<>])[A-Za-z\\d!@#\$%^&*(),.?\":{}|<>]{6,20}$".toRegex()
    return regex.matches(password)
}

/**
 * Checks if a given phone number is a valid Vietnam phone number.
 *
 * A valid Vietnam phone number is one that matches the following rules:
 * 1. Starts with either "0" (for national numbers) or "+84" (for international numbers)
 * 2. Followed by exactly 9 digits
 *
 * @param phone The phone number to check
 * @return true if the phone number is valid, false otherwise
 */
fun isValidVietnamPhoneNumber(phone: String): Boolean {
    val regex = "^(0\\d{9}|\\+84\\d{9})$".toRegex()
    return regex.matches(phone)
}

/**
 * Checks if a given full name is valid.
 *
 * A valid full name is one that matches the following rules:
 * 1. Contains only letters, spaces, and the characters .',-
 * 2. The length is between 2 and 100 characters
 * 3. Does not start or end with a space
 * 4. Does not contain consecutive spaces
 * 5. Does not contain any digits
 *
 * @param name The full name to check
 * @return true if the full name is valid, false otherwise
 */
fun isValidFullName(name: String): Boolean {
    val regex = "^(?! )[\\p{L} .',-]{2,100}(?<! )$".toRegex()
    // Basic regex validation
    if (!regex.matches(name)) return false
    // Check for consecutive spaces
    if (name.contains("  ")) return false
    // Check for numbers
    if (name.any { it.isDigit() }) return false
    return true
}
