package portal.backend.controllers.action

import io.ktor.server.application.*
import jayeson.lib.access.ktor.action.AccessChecker
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import portal.backend.gateways.IUserServiceGateway
import portal.backend.controllers.annotation.RetrieveProfile
import portal.backend.gateways.UserServiceGatewayImpl

class ActionRetrieveProfile : KoinComponent {
    private val userService: IUserServiceGateway by inject<UserServiceGatewayImpl>()

    suspend fun call(call: ApplicationCall) {
        // username in lib.access is the email in viclass
        val email = AccessChecker(call).getAccessRequest()?.username ?: return
        val respond = userService.getUserByEmail(email)

        call.attributes.put(RetrieveProfile.PROFILE_ATTR, respond)
    }
}