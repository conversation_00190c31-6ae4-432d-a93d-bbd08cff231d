package portal.backend.controllers.router

import io.ktor.server.application.*
import io.ktor.server.routing.*
import io.ktor.util.pipeline.*
import io.ktor.server.application.ApplicationCallPipeline.ApplicationPhase.Plugins

import portal.backend.controllers.action.ActionRetrieveProfile


fun Route.retrieveProfile(build: Route.() -> Unit): Route {
    val retrieveProfilePhase = PipelinePhase("RetrieveProfile")
    val route = createChild(RetrieveProfileSelector())
    val actionRetrieveProfile =  ActionRetrieveProfile()

    route.insertPhaseAfter(Plugins, retrieveProfilePhase)
    route.intercept(retrieveProfilePhase) { actionRetrieveProfile.call(call) }
    route.build()

    return route
}

class RetrieveProfileSelector : RouteSelector() {
    override fun evaluate(context: RoutingResolveContext, segmentIndex: Int) = RouteSelectorEvaluation.Transparent
}