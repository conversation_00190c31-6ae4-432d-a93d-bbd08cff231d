package portal.backend.gateways

import common.libs.logger.Logging
import io.grpc.ManagedChannel
import org.bson.BsonDocument
import org.bson.codecs.DecoderContext
import org.bson.codecs.configuration.CodecRegistry
import portal.lsession.pojo.activity.ClassroomActivity
import proto.portal.classroom.ClassroomMessages
import proto.portal.classroom.ClassroomServiceGrpcKt

class ClassroomServiceGateway(
    private val channel: ManagedChannel, private val codecRegistry: CodecRegistry
) : Logging {

    suspend fun loadClassroomActivities(lsId: String): List<ClassroomActivity> {
        try {
            val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
            val req =
                ClassroomMessages.LoadClassroomActivityRequest.newBuilder()
                    .setRequestId("Req${System.currentTimeMillis()}")
                    .setLsId(lsId).build()

            logger.debug("load classroom activity request: {}", req)
            val res = stub.loadClassroomActivity(req)

            logger.debug("load classroom activity response: {}", res)
            return res.jsonActivityList.map { deserializeClassroomActivity(it) }


        } catch (t: Throwable) {
            logger.error("failed to load classroom activity... ", t)
            throw t
        }
    }

    suspend fun getClassroomActivityById(activityId: String): ClassroomActivity {
        val stub = ClassroomServiceGrpcKt.ClassroomServiceCoroutineStub(channel)
        val req = ClassroomMessages.GetClassroomActivityByIdRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}").setActivityId(activityId).build()

        try {
            logger.debug("get classroom activity by id request: {}", req)
            val res = stub.getClassroomActivityById(req)

            logger.debug("get classroom activity by id response: {}", res)
            return deserializeClassroomActivity(res.jsonActivity)
        } catch (t: Throwable) {
            logger.error("failed to get classroom activity by id... ", t)
            throw t
        }

    }

    private fun deserializeClassroomActivity(json: String): ClassroomActivity {
        val clrActivityCodec = codecRegistry.get(ClassroomActivity::class.java)
        return clrActivityCodec.decode(BsonDocument.parse(json).asBsonReader(), DecoderContext.builder().build())
    }
}
