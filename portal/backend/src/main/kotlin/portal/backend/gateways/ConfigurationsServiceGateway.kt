package portal.backend.gateways

import com.google.protobuf.Empty
import common.libs.logger.Logging
import io.grpc.ManagedChannel
import proto.portal.configurations.ConfigurationsMessages
import proto.portal.configurations.ConfigurationsServiceGrpcKt


class ConfigurationsServiceGatewayImpl(
    private val channel: ManagedChannel
) : IConfigurationsServiceGateway, Logging {

    override suspend fun loadAll(): ConfigurationsMessages.GetAllDataConfigurationsResponse {
        val stub = ConfigurationsServiceGrpcKt.ConfigurationsServiceCoroutineStub(channel)

        try {
            logger.debug("getting all data configurations")
            val response = stub.getAllDataConfigurations(Empty.getDefaultInstance())

            logger.debug("get all data configurations response: {}", response)
            return response
        } catch (t: Throwable) {
            logger.error("failed to get all data configurations: ", t)
            throw t
        }
    }
}
