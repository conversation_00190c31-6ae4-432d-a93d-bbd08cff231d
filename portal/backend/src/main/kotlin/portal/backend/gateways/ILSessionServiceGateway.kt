package portal.backend.gateways

import io.grpc.Status
import portal.backend.models.SearchLSessionSummaryForm
import portal.datastructures.lsession.LSRegStatus
import portal.datastructures.lsession.LSessionStatus
import portal.datastructures.lsession.RaiseHandStatus
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.ClassroomSettings
import portal.lsession.pojo.LSessionDetails
import portal.lsession.pojo.LSessionRegistration
import proto.portal.lsession.LsessionMessage.*
import java.util.concurrent.CompletableFuture

interface ILSessionServiceGateway {
    suspend fun createLSession(ls: LSessionDetails, lsSettings: ClassroomSettings): CreateSessionResponse
    suspend fun getLSession(lsId: String): GetSessionDetailByIdResponse
    fun updateSessionRegistrations(
        lsId: String, registrations: Map<String, LSRegStatus>
    ): CompletableFuture<UpdateSessionRegistrationsResponse>

    suspend fun getSessionOnAllById(lsId: String): GetSessionOnAllByIdResponse
    suspend fun getClassroomSettings(lsId: String): GetClassroomSettingByIdResponse
    suspend fun getAllLSessionSummary(form: SearchLSessionSummaryForm): GetAllLSessionsSummaryResponse

    // learning session registration gateways
    suspend fun createSessionRegistration(lsRegistration: LSessionRegistration): CreateSessionRegistrationResponse
    suspend fun updateRegistrationRegStatus(
        regId: String, newStatus: LSRegStatus, regTime: Long? = null
    ):UpdateRegistrationStatusResponse

    suspend fun getLSessionRegistrationByLsId(lsId: String):GetSessionRegistrationsResponse
    suspend fun getLSessionRegistrationById(redId: String): GetSessionRegistrationResponse
    fun getLSessionRegistrationByIds(regIds: List<String>): CompletableFuture<List<GetSessionRegistrationResponse>>
    suspend fun getLSessionRegistrationByStatus(
        lsId: String, status: List<LSRegStatus>
    ): GetSessionRegistrationsResponse

    suspend fun getSessionRegistrationByLsId(lsId: String, userId: String): GetSessionRegistrationResponse

    fun updateSessionAvatar(lsId: String, imgUrl: String): CompletableFuture<UpdateSessionAvatarResponse>

    suspend fun updateSessionDetails(details: LSessionDetails, settings: ClassroomSettings): Status
    fun updateSessionStatus(lsId: String, status: LSessionStatus): CompletableFuture<UpdateLSessionStateResponse>

    fun updateUserAvailableStatusAsync(
        regId: String, lsId: String, status: UserAvailableStatus
    ): CompletableFuture<UpdateUserAvailableStatusResponse>
    fun updateRaiseHandStatusAsync(regId: String, status: RaiseHandStatus): CompletableFuture<UpdateRaiseHandStatusResponse>
    fun updateShareScreenStatusAsync(regId: String, status: ShareScreenStatus): CompletableFuture<UpdateShareScreenStatusResponse>
}
