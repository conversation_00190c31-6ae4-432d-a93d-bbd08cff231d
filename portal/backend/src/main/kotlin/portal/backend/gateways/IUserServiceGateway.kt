package portal.backend.gateways

import portal.backend.models.*
import portal.user.pojo.RegistrationType
import proto.portal.user.UserMessage.*
import java.util.concurrent.CompletableFuture

interface IUserServiceGateway {
    suspend fun registerByEmail(ru: RegistrationUser): EmailRegistrationResponse
    suspend fun getUserByUsername(username: String): GetUserResponse
    suspend fun getUserByEmail(email: String): GetUserResponse

    /**
     * Find user data by both email or username.
     *
     * !!! Only find the records from the EMAIL registration flow as it mainly used for the form in the login page
     */
    fun loginByUsernameOrEmail(ue: String): CompletableFuture<LoginByUsernameOrEmailResponse>
    suspend fun getUserProfileById(userId: String): GetUserProfileResponse
    suspend fun getUserProfileByUsernameOrEmail(username: String): GetUserProfileResponse
    suspend fun getUserProfileByUsername(username: String): GetUserProfileResponse
    suspend fun getUserProfileByIds(userIds: List<String>): GetUserProfileByIdsResponse
    suspend fun checkRegistrationEmailExist(dto: RegistrationEmailExistDto): CheckRegistrationEmailExistResponse
    suspend fun getLinkedRegistrations(profileEmail: String): GetLinkedRegistrationsResponse
    suspend fun sendVerificationEmail(registrationId: String): SendVerificationEmailResponse
    suspend fun verifyEmail(dto: EmailVerificationDto, skipVerify: Boolean): EmailVerificationResponse
    suspend fun getRegistrationMetadata(registrationId: String): RegistrationMetadataResponse
    suspend fun addMissingSocialEmail(dto: AddSocialEmailDto): AddMissingSocialEmailResponse
    suspend fun createOrMergeUserProfile(
        registrationId: String, tokenInfo: TokenInfo? = null
    ): CreateOrMergeUserProfileResponse

    suspend fun verifySocialToken(dto: SocialLoginDto): VerifySocialTokenResponse
    suspend fun addSocialRegistration(token: TokenInfo): AddSocialRegistrationResponse
    suspend fun unlinkRelinkSocialRegistration(
        registrationId: String, tokenInfo: TokenInfo
    ): UnlinkRelinkSocialRegistrationResponse

    suspend fun sendEmailResetPassword(email: String): SendEmailResetPasswordResponse
    suspend fun resetPassword(resetPassword: ResetPasswordDto): ResetPasswordResponse
    suspend fun updateProfile(data: UpdateProfileDTO): UpdateProfileResponse
    suspend fun unlinkSocial(email: String, social: RegistrationType): UnlinkSocialResponse
    suspend fun updateUserProfile(userProfile: UserProfile): UpdateUserProfileResponse
    suspend fun updateUserEmailReg(userEmailReg: UserEmailReg): UpdateUserEmailRegResponse
    suspend fun getUserLoginInformation(email: String): GetUserLoginInformationResponse
    suspend fun updateLastLoginTime(email: String): UpdateLastLoginTimeResponse
    suspend fun checkPassword(emailAndPassword: CheckPassword): CheckPasswordResponse
    suspend fun updateAvatar(userId: String, avatarUrl: String): UpdateAvatarResponse
    suspend fun getEmailRegistrationInfo(usernameOrEmail: String): EmailRegInfoResponse
    suspend fun createOneTimeLogin(request: CreateOneTimeLoginRequest): CreateOneTimeLoginResponse
    suspend fun verifyAndConsumeToken(request: VerifyAndConsumeTokenRequest): VerifyAndConsumeTokenResponse
    suspend fun saveFeedback(feedback: SupportHomepageFeedbackForm): SaveFeedbackResponse
}
