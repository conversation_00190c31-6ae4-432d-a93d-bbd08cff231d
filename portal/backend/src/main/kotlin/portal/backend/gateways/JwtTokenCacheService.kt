package portal.backend.gateways

import common.libs.cache.RedisBaseCacheService
import io.lettuce.core.api.sync.RedisCommands
import portal.backend.configs.CacheServiceConfig

class JwtTokenCacheService(private val redisCommands: RedisCommands<String, String>, config: CacheServiceConfig) :
    RedisBaseCacheService<String, String>(redisCommands = redisCommands) {

    private val prefix: String = config.jwtToken.prefix
    override val expiration: Long = config.jwtToken.expiration

    override fun getCacheKey(key: String): String {
        return "${prefix}$key"
    }
}