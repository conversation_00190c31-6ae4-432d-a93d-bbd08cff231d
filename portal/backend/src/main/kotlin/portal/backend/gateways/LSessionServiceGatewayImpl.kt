package portal.backend.gateways


import common.libs.logger.Logging
import io.grpc.ManagedChannel
import io.grpc.Status
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.future.asCompletableFuture
import portal.backend.models.SearchLSessionSummaryForm
import portal.datastructures.lsession.*
import portal.lsession.pojo.ClassroomSettings
import portal.lsession.pojo.LSessionDetails
import portal.lsession.pojo.LSessionRegistration
import portal.lsession.pojo.fromPojo
import proto.portal.lsession.LSessionServiceGrpcKt
import proto.portal.lsession.LsessionMessage.*
import java.util.concurrent.CompletableFuture


class LSessionServiceGatewayImpl constructor(
    private val channel: ManagedChannel,
) : ILSessionServiceGateway, Logging {

    /**
     * Creates a new classroom session.
     *
     * @param ls the classroom details
     * @param lsSettings the classroom settings
     * @return the result of the operation
     */
    override suspend fun createLSession(
        ls: LSessionDetails, lsSettings: ClassroomSettings
    ): CreateSessionResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = CreateSessionRequest.newBuilder().setLsessionDetails(LSessionDetailsProto.newBuilder().fromPojo(ls))
            .setClassroomSettings(ClassroomSettingsProto.newBuilder().fromPojo(lsSettings)).build()

        try {
            logger.debug("create lsession request: {}", req)
            val res = stub.createSession(req)

            logger.debug("create lsession response: {}", res)
            return res
        } catch (t: Throwable) {
            logger.error("failed to create lsession... ", t)
            throw t
        }

    }

    /**
     * Updates the details of a classroom session.
     *
     * @param details the classroom details
     * @param settings the classroom settings
     * @return the result of the operation
     */
    override suspend fun updateSessionDetails(
        details: LSessionDetails, settings: ClassroomSettings
    ): Status {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)

        val lsReq = UpdateSessionDetailsRequest.newBuilder().setLsId(details.id)
            .setLsDetails(LSessionDetailsProto.newBuilder().fromPojo(details)).build()
        val clrReq = UpdateClassroomSettingRequest.newBuilder().setLsId(details.id)
            .setClassroomSettings(ClassroomSettingsProto.newBuilder().fromPojo(settings)).build()


        val lsDeferred = try {
            logger.debug("update ls details request: {}", lsReq)
            val res = stub.updateSessionDetails(lsReq)

            logger.debug("update ls details request: {}", lsReq)
            res
        } catch (t: Throwable) {
            logger.error("failed to update ls details for {} ... ", details.id, t)
            null
        }


        val clrDeferred = try {
            logger.debug("update clr settings request: {}", lsReq)
            val res = stub.updateClassroomSettings(clrReq)

            logger.debug("update clr settings response: {}", res)
            res
        } catch (t: Throwable) {
            logger.error("failed to update clr settings for {} ... ", details.id, t)
            null
        }

        return checkUpdateResults(lsDeferred, clrDeferred)

    }

    /**
     * Checks the results of the update operations. If both operations are successful, the returned status is [Status.OK].
     * Otherwise, the returned status is [Status.INTERNAL].
     *
     * @param lsRes the result of the update session details operation
     * @param clrRes the result of the update classroom settings operation
     * @return the result of the operations
     */
    private fun checkUpdateResults(
        lsRes: UpdateSessionDetailsResponse?, clrRes: UpdateClassroomSettingResponse?
    ): Status {
        return if (lsRes != null && lsRes.status.code == Status.Code.OK.value() && clrRes != null && clrRes.status.code == Status.Code.OK.value()) Status.OK
        else Status.INTERNAL
    }

    /**
     * Retrieves a summary of all classrooms based on the provided search criteria.
     *
     * @param form the search form containing the criteria for filtering the classrooms, such as user ID,
     *             session status, and registration status.
     * @return a response containing the summary of all learning sessions that match the search criteria.
     */
    override suspend fun getAllLSessionSummary(form: SearchLSessionSummaryForm): GetAllLSessionsSummaryResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val reqBuilder = SearchLSessionSummaryByUserIdRequest.newBuilder()
        form.userId?.let { reqBuilder.userId = it }
        form.lsStatus?.let { reqBuilder.addAllLsStatus(it.map { it.name }) }
        form.regStatus?.let { reqBuilder.addAllRegStatus(it.map { it.name }) }
        val req = reqBuilder.build()

        try {
            logger.debug("search all lsession summary request: {}", req)
            val res = stub.getAllLSessionSummaryByUserId(req)

            logger.debug("search all lsession summary response: {}", res)
            return res
        } catch (t: Throwable) {
            logger.error("failed to search all lsession summary... ", t)
            throw t
        }
    }

    /**
     * Updates the registration status of multiple users in a classroom session.
     *
     * @param lsId the ID of the classroom session
     * @param registrations a map of user IDs to registration status. The registration status is updated to the new value for each user ID.
     * @return a future containing the result of the operation
     */
    override fun updateSessionRegistrations(
        lsId: String, registrations: Map<String, LSRegStatus>
    ): CompletableFuture<UpdateSessionRegistrationsResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)

        val registrationProtoMap = registrations.mapValues {
            UpdateSessionRegistrationsRequest.RegistrationProto.newBuilder()
                .setRegId(it.key)
                .setRegStatus(it.value.name)
                .build()
        }
        val req = UpdateSessionRegistrationsRequest.newBuilder().setLsId(lsId).putAllRegistrations(registrationProtoMap)
            .build()

        return CoroutineScope(Dispatchers.IO).async { stub.updateSessionRegistrations(req) }.asCompletableFuture()
    }

    /**
     * Creates a new registration in a classroom session.
     *
     * @param lsRegistration the registration information, including the classroom session ID, the ID of the user who owns the classroom session,
     *                       the user ID of the user being registered, the registration status, and the registration time.
     * @return a response containing the ID of the newly created registration.
     */
    override suspend fun createSessionRegistration(
        lsRegistration: LSessionRegistration
    ): CreateSessionRegistrationResponse {
        logger.debug("create lsession registration request: {}", lsRegistration)
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = CreateSessionRegistrationRequest.newBuilder()
            .setLsId(lsRegistration.lsId)
            .setLsOwnerId(lsRegistration.lsOwnerUserId)
            .setUserId(lsRegistration.userId)
            .setRegStatus(lsRegistration.regStatus.name)
            .setRegTime(lsRegistration.regTime)
            .build()
        return stub.createSessionRegistration(request)
    }

    /**
     * Updates the registration status of a user in a classroom session.
     *
     * @param regId the ID of the registration to be updated
     * @param newStatus the new registration status
     * @param regTime the registration time, may be null
     * @return a response containing the result of the operation
     */
    override suspend fun updateRegistrationRegStatus(
        regId: String, newStatus: LSRegStatus, regTime: Long?
    ): UpdateRegistrationStatusResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val builder = UpdateRegistrationStatusRequest.newBuilder().setRegId(regId).setNewStatus(newStatus.name)
        regTime?.let { builder.regTime = regTime }
        val request = builder.build()
        logger.debug("update lsession registration status request: {}", request)
        return stub.updateRegistrationStatus(request)
    }

    /**
     * Gets a classroom registration by its ID.
     *
     * @param redId the ID of the registration
     * @return a response containing the registration information
     */
    override suspend fun getLSessionRegistrationById(
        redId: String
    ): GetSessionRegistrationResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = GetSessionRegistrationByIdRequest.newBuilder().setRegId(redId).build()
        return stub.getSessionRegistrationById(request)
    }

    /**
     * Gets multiple classroom registrations by their IDs.
     *
     * @param regIds the IDs of the registrations to be retrieved
     * @return a future containing a list of registration information
     */
    override fun getLSessionRegistrationByIds(
        regIds: List<String>
    ): CompletableFuture<List<GetSessionRegistrationResponse>> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = regIds.map {
            GetSessionRegistrationByIdRequest.newBuilder().setRegId(it).build()
        }
        return CoroutineScope(Dispatchers.IO).async { stub.getSessionRegistrationsByIds(request.asFlow()).toList() }
            .asCompletableFuture()
    }

    /**
     * Retrieves a list of classroom registrations by the specified classroom ID.
     *
     * @param lsId the ID of the classroom
     * @return a response containing the registrations associated with the specified classroom ID
     */
    override suspend fun getLSessionRegistrationByLsId(
        lsId: String
    ): GetSessionRegistrationsResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = GetSessionRegistrationsByLsIdRequest.newBuilder().setLsId(lsId).build()
        return stub.getSessionRegistrationsByLsId(request)
    }

    /**
     * Retrieves a list of classroom registrations by the specified classroom ID and registration status.
     *
     * @param lsId the ID of the classroom
     * @param status the registration status to be retrieved
     * @return a future containing the registrations associated with the specified classroom ID and registration status
     */
    override suspend fun getLSessionRegistrationByStatus(
        lsId: String, status: List<LSRegStatus>
    ): GetSessionRegistrationsResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = GetSessionRegistrationsByLsIdAndStatusRequest.newBuilder().setLsId(lsId)
            .addAllRegStatus(status.map { it.name }).build()
        try {
            logger.debug("get lsession registration by status request: {}", request)
            val res = stub.getSessionRegistrationsByLsIdAndStatus(request)

            logger.debug("get lsession registration by status response: {}", res)
            return res
        } catch (t: Throwable) {
            logger.error("failed to get lsession registration by status: ", t)
            throw t
        }
    }

    /**
     * Retrieves a classroom registration by the specified classroom ID and user ID.
     *
     * @param lsId the ID of the classroom
     * @param userId the ID of the user
     * @return a response containing the registration associated with the specified classroom ID and user ID
     */
    override suspend fun getSessionRegistrationByLsId(
        lsId: String, userId: String
    ): GetSessionRegistrationResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val request = GetSessionRegistrationByLsIdAndUserIdRequest.newBuilder().setLsId(lsId).setUserId(userId).build()
        try {
            logger.debug("get lsession registration by lsId request: {}", request)
            val res = stub.getSessionRegistrationByLsIdAndUserId(request)

            logger.debug("get lsession registration by lsId response: {}", res)
            return res
        } catch (t: Throwable) {
            logger.error("failed to get lsession registration by lsId: ", t)
            throw t
        }

    }

    /**
     * Updates the avatar image for a specified classroom session.
     *
     * @deprecated
     */
    override fun updateSessionAvatar(
        lsId: String, imgUrl: String
    ): CompletableFuture<UpdateSessionAvatarResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val builder = UpdateSessionAvatarRequest.newBuilder()
        builder.lsId = lsId
        builder.imgUrl = imgUrl
        val request = builder.build()
        logger.debug("updateSessionAvatar request: {}", request)
        return CoroutineScope(Dispatchers.IO).async { stub.updateSessionAvatar(request) }.asCompletableFuture()
    }

    /**
     * Retrieves a classroom session by its ID.
     *
     * @param lsId the ID of the classroom session
     * @return a response containing the classroom session information
     */
    override suspend fun getLSession(lsId: String): GetSessionDetailByIdResponse {
        try {
            val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
            val req = GetSessionDetailByIdRequest.newBuilder().setLsId(lsId).build()

            logger.debug("get lsession request: {}", req)
            val res = stub.getSessionDetailsById(req)

            logger.debug("get lsession response: {}", res)
            return res


        } catch (t: Throwable) {
            logger.error("failed to get lsession... ", t)
            throw t
        }
    }

    /**
     * Retrieves a classroom session by its ID, including its registration data.
     *
     * @param lsId the ID of the classroom session
     * @return a response containing the classroom session information and its registration data
     */
    override suspend fun getSessionOnAllById(lsId: String): GetSessionOnAllByIdResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val builder = GetSessionOnAllByIdRequest.newBuilder()
        builder.lsId = lsId
        val request = builder.build()
        return stub.getSessionOnAllById(request)
    }

    /**
     * Retrieves the classroom settings for a specified classroom session.
     *
     * @param lsId the ID of the classroom session
     * @return a response containing the classroom settings
     */
    override suspend fun getClassroomSettings(lsId: String): GetClassroomSettingByIdResponse {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req = GetClassroomSettingByIdRequest.newBuilder().setLsId(lsId).build()
        try {
            logger.debug("get classroom settings request: {}", req)
            val res = stub.getClassroomSettingsById(req)

            logger.debug("get classroom settings response: {}", res)
            return res
        } catch (t: Throwable) {
            logger.error("failed to get classroom settings... ", t)
            throw t
        }

    }

    /**
     * Updates the status of a specified classroom session.
     *
     * @param lsId the ID of the classroom session
     * @param status the new status of the classroom session
     * @return a future containing the updated classroom session state
     */
    override fun updateSessionStatus(
        lsId: String, status: LSessionStatus
    ): CompletableFuture<UpdateLSessionStateResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)

        val stateBuilder = LSessionStateProto.newBuilder()
        stateBuilder.status = status.name
        if (status == LSessionStatus.STARTED) stateBuilder.startedAt = System.currentTimeMillis()
        else if (status == LSessionStatus.ENDED) stateBuilder.endedAt = System.currentTimeMillis()

        val req = UpdateLSessionStateRequest.newBuilder().setLsId(lsId).setState(stateBuilder).build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("update lsession status request: {}", req)
                val res = stub.updateSessionState(req)

                logger.debug("update lsession status response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("failed to update lsession status... ", t)
                throw t
            }
        }.asCompletableFuture()
    }

    /**
     * Updates the availability status of a classroom user asynchronously.
     *
     * @param regId the ID of the registration
     * @param lsId the ID of the classroom session
     * @param status the new availability status of the user
     * @return a future containing the updated availability status of the user
     */
    override fun updateUserAvailableStatusAsync(
        regId: String, lsId: String, status: UserAvailableStatus
    ): CompletableFuture<UpdateUserAvailableStatusResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req =
            UpdateUserAvailableStatusRequest.newBuilder().setLsId(lsId).setRegId(regId).setStatus(status.name).build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("updateClassroomUserAvailableStatusAsync request: {}", req)
                val res = stub.updateUserAvailableStatus(req)

                logger.debug("updateClassroomUserAvailableStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateClassroomUserAvailableStatusAsync failed: ", t)
                throw t
            }
        }.asCompletableFuture()
    }

    /**
     * Updates the activity status of a classroom user asynchronously.
     *
     * @param regId the ID of the registration
     * @param lsId the ID of the classroom session
     * @param status the new activity status of the user
     * @return a future containing the updated activity status of the user
     */
    override fun updateRaiseHandStatusAsync(
        regId: String,
        status: RaiseHandStatus,
    ): CompletableFuture<UpdateRaiseHandStatusResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req =
            UpdateRaiseHandStatusRequest.newBuilder().setRegId(regId).setStatus(status.name).build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("updateClassroomRaiseHandStatusAsync request: {}", req)
                val res = stub.updateRaiseHandStatus(req)

                logger.debug("updateClassroomRaiseHandStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateClassroomRaiseHandStatusAsync failed: ", t)
                throw t
            }
        }.asCompletableFuture()
    }

    override fun updateShareScreenStatusAsync(
        regId: String,
        status: ShareScreenStatus,
    ): CompletableFuture<UpdateShareScreenStatusResponse> {
        val stub = LSessionServiceGrpcKt.LSessionServiceCoroutineStub(channel)
        val req =
            UpdateShareScreenStatusRequest.newBuilder().setRegId(regId).setStatus(status.name).build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("updateClassroomRaiseHandStatusAsync request: {}", req)
                val res = stub.updateShareScreenStatus(req)

                logger.debug("updateClassroomRaiseHandStatusAsync response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("updateClassroomRaiseHandStatusAsync failed: ", t)
                throw t
            }
        }.asCompletableFuture()
    }
}
