package portal.backend.gateways

import com.google.protobuf.StringValue
import com.google.protobuf.UInt32Value
import common.libs.logger.Logging
import io.grpc.ManagedChannel
import kotlinx.coroutines.*
import org.bson.BsonType
import portal.backend.models.DocumentInfoResponse
import portal.backend.models.LoadDocumentInfoRequest
import portal.backend.utility.defaultMapper
import vi.metadata.doc.proto.MetadataDocMessage.*
import vi.metadata.doc.proto.MetadataDocMessage.LoadMetadataDocRequest.DataFilter
import vi.metadata.doc.proto.MetadataDocServiceGrpcKt
import java.util.*


data class LoadMetadataOptions(
    val limit: Int = 1,
    val offset: Int = 0,
    val textSearch: String? = null,
    val shouldCount: Boolean = false,
)

/**
 * Gateway for accessing Metadata service through gRPC.
 */
class MetadataServiceGateway constructor(
    private val channel: ManagedChannel,
) : Logging {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * Creates a new metadata document.
     *
     * @param docId The global ID of the document.
     * @param editorType The type of editor associated with the document.
     * @param metadataType The type of metadata associated with the document.
     * @param details The details of the document, serialized to JSON.
     *
     * @return A deferred response containing the created document.
     */
    suspend fun createMetadataDoc(
        docId: String, editorType: String, metadataType: String, details: Any,
    ): Deferred<MetadataDocResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val req = CreateMetadataDocRequest.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setMetadata(
                MetadataDocProto.newBuilder()
                    .setDocId(docId)
                    .setEditorType(editorType)
                    .setMetadataType(metadataType)
                    .setMetadataDetails(defaultMapper.writeValueAsString(details))
            )
            .build()

        return scope.async { stub.createMetadataDoc(req) }
    }


    /**
     * Loads metadata documents based on the specified criteria.
     *
     * @param metadataType The type of metadata to filter by.
     * @param filterFields A list of triples representing field name, data type, and value to filter the metadata.
     * @param editorType An optional editor type to filter by. Defaults to null.
     * @param docGlobalId An optional global document ID to filter by. Defaults to null.
     * @param options Additional options for loading metadata, such as limit, offset, text search, and count flag.
     *
     * @return A deferred response containing the loaded metadata documents.
     */
    suspend fun loadMetadataDoc(
        metadataType: String,
        filterFields: List<Triple<String, String, String>>,
        editorType: String? = null,
        docGlobalId: String? = null,
        options: LoadMetadataOptions = LoadMetadataOptions(),
    ): Deferred<MetadataDocsResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val reqBuilder = LoadMetadataDocRequest.newBuilder()
            .setMetadataType(StringValue.of(metadataType))
            .addAllDataFilter(filterFields.map { (f, t, v) ->
                DataFilter.newBuilder()
                    .setFieldName(f)
                    .setDataType(t)
                    .setValue(v)
                    .build()
            })
            .setLimit(UInt32Value.of(options.limit))
            .setOffset(UInt32Value.of(options.offset))

        docGlobalId?.let { reqBuilder.setDocId(StringValue.of(it)) }
        editorType?.let { reqBuilder.setEditorType(StringValue.of(it)) }

        options.textSearch?.let { reqBuilder.setTextSearch(StringValue.of(it)) }
        options.shouldCount.let { reqBuilder.setShouldCount(it) }

        return scope.async { stub.loadMetadataDoc(reqBuilder.build()) }
    }


    /**
     * Updates a document metadata by its ID.
     *
     * @param id The ID of the document to update.
     * @param details The new document details.
     *
     * @return A deferred response containing the updated document.
     */
    suspend fun updateMetadataDocById(
        id: String, details: Any,
    ): Deferred<MetadataDocResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val req = UpdateMetadataDocByIdRequest.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setId(id)
            .setMetadataDetails(defaultMapper.writeValueAsString(details))
            .build()

        return scope.async { stub.updateMetadataDocById(req) }
    }


    /**
     * Delete many metadata documents matching the given criteria.
     *
     * @param docId The document ID to filter by.
     * @param metadataType The type of metadata to filter by.
     * @param editorType The editor type to filter by. Optional, defaults to empty string.
     * @param filterFields Additional fields to filter by. Each triple consists of a field name, a data type,
     *                     and a value.
     *
     * @return A deferred response containing the delete status.
     */
    suspend fun deleteManyMetadataDocs(
        docId: String,
        metadataType: String,
        editorType: String? = "",
        filterFields: List<Triple<String, String, String>> = emptyList(),
    ): Deferred<DeleteManyMetadataDocsResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val req = DeleteManyMetadataDocsRequest.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setDocId(docId)
            .setMetadataType(metadataType)
            .setEditorType(editorType ?: "")
            .addAllDataFilters(filterFields.map { (f, t, v) ->
                DeleteManyMetadataDocsRequest.DataFilter.newBuilder()
                    .setFieldName(f)
                    .setDataType(t)
                    .setValue(v)
                    .build()
            })
            .build()

        return scope.async { stub.deleteManyMetadataDocs(req) }
    }


    /**
     * Utility function to loads document information based on the provided request and metadata type.
     *
     * @param request The request containing the document global ID and editor type.
     * @param metadataType The type of metadata to filter by.
     * @param metadataDetailsClass The class type of the metadata details to deserialize.
     * @param <T> The type of the metadata details.
     *
     * @return A pair consisting of the DocumentInfoResponse and the metadata details of type T,
     *         or null values if the document metadata is not found.
     */
    suspend fun <T : Any> loadDocInfo(request: LoadDocumentInfoRequest, metadataType: String, metadataDetailsClass: Class<T>): Pair<DocumentInfoResponse?, T?> {
        var docInfo: DocumentInfoResponse? = null
        var docDetails: T? = null

        val docMetadata = this.loadMetadataDoc(
            metadataType = metadataType,
            filterFields = listOfNotNull(
                Triple("docGlobalId", BsonType.STRING.name, request.docGlobalId),
            ),
            editorType = request.editorType,
            docGlobalId = request.docGlobalId
        ).await().metadataList.firstOrNull()

        if (docMetadata != null) {
            docDetails = defaultMapper.readValue(
                docMetadata.metadataDetails,
                metadataDetailsClass
            )
            docInfo = DocumentInfoResponse(
                docGlobalId = docMetadata.docId,
                editorType = docMetadata.editorType,
                docType = docMetadata.metadataType,
                details = docDetails
            )
        }

        return Pair(docInfo, docDetails)
    }
}
