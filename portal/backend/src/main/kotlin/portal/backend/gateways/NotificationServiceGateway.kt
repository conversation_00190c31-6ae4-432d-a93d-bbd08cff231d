package portal.backend.gateways

import com.mongodb.reactivestreams.client.MongoClients
import common.libs.logger.Logging
import io.grpc.ManagedChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.asCompletableFuture
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.json.JsonWriter
import portal.notification.pojo.Notification
import proto.portal.notification.NotificationMessages
import proto.portal.notification.NotificationMessages.SaveNotificationResponse
import proto.portal.notification.NotificationServiceGrpcKt
import java.io.StringWriter
import java.util.concurrent.CompletableFuture

class NotificationServiceGateway(
    private val channel: ManagedChannel
) : Logging {

    private val notificationCodec = CodecRegistries.fromRegistries(
        MongoClients.getDefaultCodecRegistry(),
        CodecRegistries.fromProviders(PojoCodecProvider.builder().automatic(true).build())
    ).get(Notification::class.java)

    fun saveNotification(notification: Notification, skipPersist: Boolean = false): CompletableFuture<SaveNotificationResponse> {
        val writer = StringWriter()
        notificationCodec.encode(
            JsonWriter(writer),
            notification,
            EncoderContext.builder().build()
        )
        val jsonNotification = writer.toString()

        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.SaveNotificationRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setJsonNotification(jsonNotification)
            .setSkipPersist(skipPersist)
            .build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("save notification request: {}", req)
                val res = stub.saveNotification(req)

                logger.debug("save notification response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("failed to save notification: ", t)
                throw t
            }
        }.asCompletableFuture()
    }

    suspend fun loadNotificationById(id: String): String {
        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.LoadNotificationByIdRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}").setNotificationId(id).build()

        try {
            val res = stub.loadNotificationById(req)
            return res.jsonNotification
        } catch (t: Throwable) {
            throw t
        }
    }

    suspend fun loadClassroomNotification(lsId: String, userId: String): List<String> {
        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.LoadClassroomNotificationByCriteriaRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}").setLsId(lsId).setUserId(userId).build()

        try {
            logger.debug("load classroom notification request: {}", req)
            val res = stub.loadClassroomNotificationByCriteria(req)
            logger.debug("load classroom notification response: {}", res)

            logger.debug("Loaded {} valid notifications", res.jsonNotificationCount)
            return res.jsonNotificationList
        } catch (t: Throwable) {
            throw t
        }

    }
}
