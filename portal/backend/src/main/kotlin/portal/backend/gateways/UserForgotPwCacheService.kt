package portal.backend.gateways

import common.libs.cache.RedisBaseCacheService
import io.lettuce.core.api.sync.RedisCommands
import portal.backend.configs.CacheServiceConfig


class UserForgotPwCacheService(redisCommands: RedisCommands<String, String>, config: CacheServiceConfig) :
    RedisBaseCacheService<String, String>(redisCommands) {
    private val prefix: String = config.userForgotPw.prefix
    override val expiration: Long = config.userForgotPw.expiration

    override fun getCacheKey(key: String): String {
        return "${prefix}$key"
    }
}
