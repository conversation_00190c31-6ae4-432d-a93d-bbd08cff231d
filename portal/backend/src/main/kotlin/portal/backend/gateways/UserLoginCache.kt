package portal.backend.gateways

import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import common.libs.cache.ICacheService
import portal.backend.models.BasicUserData
import java.util.concurrent.TimeUnit


class UserLoginCache : ICacheService<String, BasicUserData> {
    private val cache: Cache<String, BasicUserData> =
        CacheBuilder.newBuilder().maximumSize(1000).expireAfterAccess(10, TimeUnit.MINUTES).build()

    override fun get(key: String): BasicUserData? {
        return cache.getIfPresent(key)
    }

    override fun set(key: String, value: BasicUserData) {
        cache.put(key, value)
    }

    override fun delete(key: String) {
        cache.invalidate(key)
    }

    override fun contains(key: String): Boolean {
        return cache.getIfPresent(key) != null
    }
}