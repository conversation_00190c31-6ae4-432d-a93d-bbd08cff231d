package portal.backend.gateways


import io.grpc.ManagedChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.asCompletableFuture
import portal.backend.models.*
import portal.user.pojo.RegistrationType
import proto.portal.user.UserMessage
import proto.portal.user.UserMessage.*
import proto.portal.user.UserServiceGrpcKt
import java.util.concurrent.CompletableFuture


class UserServiceGatewayImpl(
    private var channel: ManagedChannel,
) : IUserServiceGateway {
    /**
     * Register a new user by the email-password flow
     *
     * @param ru the RegistrationUser POJO containing the registration information.
     * @return the EmailRegistrationResponse containing the result of the registration.
     */
    override suspend fun registerByEmail(ru: RegistrationUser): EmailRegistrationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val req =
            EmailRegistrationRequest.newBuilder().setUsername(ru.username).setEmail(ru.email).setPassword(ru.password)
                .setIsVerified(ru.isVerified).build()

        return stub.registerUserByEmail(req)
    }

    /**
     * Login by either username or email of the email-password flow
     *
     * @param ue the username or email to login with
     * @return a CompletableFuture containing the LoginByUsernameOrEmailResponse
     */
    override fun loginByUsernameOrEmail(ue: String): CompletableFuture<LoginByUsernameOrEmailResponse> {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = LoginByUsernameOrEmailRequest.newBuilder().setUsernameOrEmail(ue).build()

        return CoroutineScope(Dispatchers.IO).async { stub.loginByUsernameOrEmail(request) }.asCompletableFuture()
    }

    /**
     * Find user profile data by the username in the email-password flow
     *
     * @param username the username to find
     * @return a GetUserResponse containing the result of the query
     */
    override suspend fun getUserByUsername(username: String): GetUserResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserByUsernameRequest.newBuilder().setUsername(username).build()

        return stub.getUserByUsername(request)
    }

    /**
     * Find user profile data by the email
     *
     * @param email the email to find
     * @return a GetUserResponse containing the result of the query
     */
    override suspend fun getUserByEmail(email: String): GetUserResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserByEmailRequest.newBuilder().setEmail(email).build()

        return stub.getUserByEmail(request)
    }

    /**
     * Find user profile data by the list of user ids.
     * To be used by the brief-profile API
     *
     * @param userIds the list of user ids to find
     * @return a GetUserProfileByIdsResponse containing the result of the query
     */
    override suspend fun getUserProfileByIds(userIds: List<String>): GetUserProfileByIdsResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserProfileByIdsRequest.newBuilder().addAllUserId(userIds).build()

        try {
            val res = stub.getUserProfileByIds(request)
            return res
        } catch (t: Throwable) {
            throw t
        }
    }

    /**
     * Find user profile data by the username or email
     *
     * @param ue the username or email to find
     * @return a GetUserProfileResponse containing the result of the query
     */
    override suspend fun getUserProfileByUsernameOrEmail(ue: String): GetUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserByUsernameRequest.newBuilder().setUsername(ue).build()
        try {
            val res = stub.getUserProfileByUsernameOrEmail(request)
            return res
        } catch (t: Throwable) {
            throw t
        }
    }


    /**
     * Check if the registration email exists or not.
     * To be used by the registration form to block duplicate emails
     *
     * @param dto the [RegistrationEmailExistDto] containing the registration type and the email
     * @return the [CheckRegistrationEmailExistResponse] containing the result of the check
     */
    override suspend fun checkRegistrationEmailExist(dto: RegistrationEmailExistDto): CheckRegistrationEmailExistResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = CheckRegistrationEmailExistRequest.newBuilder().setRegistrationType(dto.registrationType)
            .setEmail(dto.email).build()

        return stub.checkRegistrationEmailExist(request)
    }

    /**
     * Find all linked registrations for a given profile email, i.e. register by both email and social accounts
     * This method is used by the profile page to display all the linked registrations.
     *
     * @param profileEmail the email of the profile to find linked registrations for
     * @return a [GetLinkedRegistrationsResponse] containing the result of the query
     */
    override suspend fun getLinkedRegistrations(profileEmail: String): GetLinkedRegistrationsResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetLinkedRegistrationsRequest.newBuilder().setProfileEmail(profileEmail).build()
        return stub.getLinkedRegistrations(request)
    }

    /**
     * Sends a verification email to the user associated with the specified registration ID.
     *
     * @param registrationId the ID of the registration for which the verification email is to be sent.
     * @return a [SendVerificationEmailResponse] containing the result of the email send operation.
     */
    override suspend fun sendVerificationEmail(registrationId: String): SendVerificationEmailResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = SendVerificationEmailRequest.newBuilder().setRegistrationId(registrationId).build()
        return stub.sendVerificationEmail(request)
    }

    /**
     * Verifies the email associated with the given registration ID and verification code.
     *
     * @param dto the [EmailVerificationDto] containing the registration ID and verification code.
     * @return an [EmailVerificationResponse] containing the result of the email verification.
     */
    override suspend fun verifyEmail(
        dto: EmailVerificationDto, skipVerify: Boolean
    ): EmailVerificationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = EmailVerificationRequest.newBuilder().setRegistrationId(dto.registrationId)
            .setVerificationCode(dto.verificationCode).setSkipVerification(skipVerify).build()

        return stub.verifyEmail(request)
    }

    /**
     * Retrieve metadata associated with a given registration ID.
     * Include the registration ID, type (email or social), the email and is email verified.
     *
     * @param registrationId the registration ID for which to retrieve metadata.
     * @return a [RegistrationMetadataResponse] containing the retrieved metadata.
     */
    override suspend fun getRegistrationMetadata(registrationId: String): RegistrationMetadataResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = RegistrationMetadataRequest.newBuilder().setRegistrationId(registrationId).build()

        return stub.getRegistrationMetadata(request)
    }

    /**
     * When register by social account but can not infer the email from the social account, user need to add it manually.
     * Adds a missing social registration email to a registration profile.
     *
     * @param dto the [AddSocialEmailDto] containing the registration ID and the email.
     * @return an [AddMissingSocialEmailResponse] containing the result of the email addition.
     */
    override suspend fun addMissingSocialEmail(dto: AddSocialEmailDto): AddMissingSocialEmailResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request =
            AddMissingSocialEmailRequest.newBuilder().setRegistrationId(dto.registrationId).setEmail(dto.email).build()

        return stub.addMissingSocialEmail(request)
    }

    /**
     * We can have many registration but only one user profile of the same email.
     * Creates or merges a user profile based on the provided registration ID
     * and optional social token information when register with social account.
     *
     * @param registrationId The ID associated with the registration to create or merge a user profile.
     * @param tokenInfo Optional social token information that may include additional user data for profile creation or merging.
     * @return A [CreateOrMergeUserProfileResponse] containing the result of the operation, either creating a new user profile or merging with an existing one.
     */
    override suspend fun createOrMergeUserProfile(
        registrationId: String,
        tokenInfo: TokenInfo?,
    ): CreateOrMergeUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = CreateOrMergeUserProfileRequest.newBuilder().setRegistrationId(registrationId)

        if (tokenInfo != null) request.setTokenInfo(tokenInfo)

        return stub.createOrMergeUserProfile(request.build())
    }

    /**
     * Verifies a social authentication token.
     * This function checks the validity of a social login token provided by the social provider.
     *
     * @param dto the [SocialLoginDto] containing the social provider and authentication token.
     * @return a [VerifySocialTokenResponse] containing the result of the token verification.
     */
    override suspend fun verifySocialToken(dto: SocialLoginDto): VerifySocialTokenResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request =
            VerifySocialTokenRequest.newBuilder().setProvider(dto.provider).setAuthToken(dto.authToken).build()
        return stub.verifySocialToken(request)
    }

    /**
     * Registers a new user registration via social authentication.
     * This function registers a new user associated with the provided social authentication token.
     *
     * @param token The social authentication token provided by the social provider.
     * @return an [AddSocialRegistrationResponse] containing the result of the registration.
     */
    override suspend fun addSocialRegistration(token: TokenInfo): AddSocialRegistrationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = AddSocialRegistrationRequest.newBuilder().setTokenInfo(token).build()
        return stub.addSocialRegistration(request)
    }

    /**
     * Unlinks and relinks a social registration with a given user profile.
     *
     * This function processes the unlinking of an existing social registration
     * and the relinking of a new one using the provided registration ID and token information.
     *
     * @param registrationId the ID of the registration to be unlinked and relinked.
     * @param tokenInfo the token information required for the relinking process.
     * @return an [UnlinkRelinkSocialRegistrationResponse] containing the result of the operation.
     */
    override suspend fun unlinkRelinkSocialRegistration(
        registrationId: String,
        tokenInfo: TokenInfo,
    ): UnlinkRelinkSocialRegistrationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request =
            UnlinkRelinkSocialRegistrationRequest.newBuilder().setRegistrationId(registrationId).setTokenInfo(tokenInfo)
                .build()
        return stub.unlinkRelinkSocialRegistration(request)
    }

    /**
     * Retrieve user profile data by user ID.
     *
     * @param userId The ID of the user whose profile is to be retrieved.
     * @return A [GetUserProfileResponse] containing the user profile data.
     */
    override suspend fun getUserProfileById(userId: String): GetUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserProfileByIdRequest.newBuilder().setUserId(userId).build()

        return stub.getUserProfileById(request)
    }

    /**
     * Retrieve user profile data by username.
     *
     * @param username The username of the user whose profile is to be retrieved.
     * @return A [GetUserProfileResponse] containing the user profile data.
     */
    override suspend fun getUserProfileByUsername(username: String): GetUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserProfileByUsernameRequest.newBuilder().setUsername(username).build()

        return stub.getUserProfileByUsername(request)
    }

    /**
     * Sends an email to the specified email address containing the code to reset the password.
     *
     * @param email The email address to which the reset password instructions will be sent.
     * @return A [SendEmailResetPasswordResponse] containing the result of the email send operation.
     */
    override suspend fun sendEmailResetPassword(email: String): SendEmailResetPasswordResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = SendEmailResetPasswordRequest.newBuilder().setEmail(email).build()

        return stub.sendEmailResetPassword(request)
    }

    /**
     * Resets the password for the user associated with the specified reset password token.
     *
     * @param resetPassword A [ResetPasswordDto] containing the new password and the reset password token.
     * @return A [ResetPasswordResponse] containing the result of the password reset operation.
     */
    override suspend fun resetPassword(resetPassword: ResetPasswordDto): ResetPasswordResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = ResetPasswordRequest.newBuilder().setNewPassword(resetPassword.newPassword)
            .setJwtToken(resetPassword.jwtToken).build()

        return stub.resetPassword(request)
    }

    /**
     * Updates the user profile with the provided data.
     *
     * @param data the [UpdateProfileDTO] containing the updated profile information.
     * @return an [UpdateProfileResponse] indicating the result of the update operation.
     */
    override suspend fun updateProfile(data: UpdateProfileDTO): UpdateProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        return stub.updateProfile(data.toUpdateProfileRequest())
    }

    /**
     * Updates the avatar URL associated with the provided user ID.
     *
     * @param userId The ID of the user whose avatar URL is to be updated.
     * @param avatarUrl The new avatar URL to be associated with the user.
     * @return An [UpdateAvatarResponse] containing the result of the update operation.
     */
    override suspend fun updateAvatar(userId: String, avatarUrl: String): UpdateAvatarResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = UpdateAvatarRequest.newBuilder().setUserId(userId).setAvatarUrl(avatarUrl).build()
        return stub.updateAvatar(request)
    }

    /**
     * Gets the email registration information for the specified username or email.
     *
     * @param usernameOrEmail The username or email of the user whose registration information is to be retrieved.
     * @return An [EmailRegInfoResponse] containing the email registration information for the specified username or email
     *         or an error status if the user is not found.
     */
    override suspend fun getEmailRegistrationInfo(usernameOrEmail: String): EmailRegInfoResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = EmailRegInfoRequest.newBuilder().setUsernameOrEmail(usernameOrEmail).build()
        return stub.getEmailRegInfo(request)
    }

    /**
     * This function sends a request to unlink the specified social registration
     * associated with the given email.
     *
     * @param email The email address of the user whose social registration is to be unlinked.
     * @param social The type of social registration to be unlinked.
     * @return An [UnlinkSocialResponse] indicating the result of the unlink operation.
     */
    override suspend fun unlinkSocial(email: String, social: RegistrationType): UnlinkSocialResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request =
            UnlinkSocialRequest.newBuilder().setEmail(email).setSocial(UserMessage.Social.valueOf(social.toString()))
                .build()
        return stub.unlinkSocial(request)
    }

    /**
     * Updates the user profile with the provided data.
     *
     * @param userProfile A [UserProfile] containing the updated profile information.
     * @return An [UpdateUserProfileResponse] indicating the result of the update operation.
     */
    override suspend fun updateUserProfile(userProfile: UserProfile): UpdateUserProfileResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)

        val request = UpdateUserProfileRequest.newBuilder().apply {
            setEmail(userProfile.email)
            userProfile.phone?.let {
                setPhone(it)
            }
            userProfile.username?.let {
                setUsername(it)
            }
            userProfile.avatarUrl?.let {
                setAvatarUrl(it)
            }
        }.build()

        return stub.updateUserProfile(request)
    }

    /**
     * Updates the user email registration with the provided data.
     *
     * @param userEmailReg A [UserEmailReg] containing the updated registration information.
     * @return An [UpdateUserEmailRegResponse] indicating the result of the update operation.
     */
    override suspend fun updateUserEmailReg(userEmailReg: UserEmailReg): UpdateUserEmailRegResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)

        val request = UpdateUserEmailRegRequest.newBuilder().apply {
            setEmail(userEmailReg.email)
            userEmailReg.phone?.let {
                setPhone(it)
            }
            userEmailReg.username?.let {
                setUsername(it)
            }
            userEmailReg.password?.let {
                setPassword(it)
            }
        }.build()

        return stub.updateUserEmailReg(request)
    }

    /**
     * Gets the user login information (with last login time and created date) for the given email.
     *
     * @param email The email of the user to get the login information for.
     * @return A [GetUserLoginInformationResponse] containing the user login information.
     */
    override suspend fun getUserLoginInformation(email: String): GetUserLoginInformationResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = GetUserLoginInformationRequest.newBuilder().setEmail(email).build()

        return stub.getUserLoginInformation(request)
    }

    /**
     * Updates the last login time for the user associated with the specified email.
     *
     * @param email The email of the user to update the last login time for.
     * @return An [UpdateLastLoginTimeResponse] containing the result of the update operation.
     */
    override suspend fun updateLastLoginTime(email: String): UpdateLastLoginTimeResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = UpdateLastLoginTimeRequest.newBuilder().setEmail(email).build()

        return stub.updateLastLoginTime(request)
    }

    /**
     * Checks the password for the given email registration is correct.
     * Used in the change password flow to verify the old password
     *
     * @param emailAndPassword A [CheckPassword] object containing the email and password to check.
     * @return A [CheckPasswordResponse] containing the result of the password check.
     */
    override suspend fun checkPassword(emailAndPassword: CheckPassword): CheckPasswordResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request =
            CheckPasswordRequest.newBuilder().setEmail(emailAndPassword.email).setPassword(emailAndPassword.password)
                .build()

        return stub.checkPassword(request)
    }

    override suspend fun createOneTimeLogin(request: CreateOneTimeLoginRequest): CreateOneTimeLoginResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = CreateOneTimeLoginRequest.newBuilder().setRegId(request.regId).build()
        return stub.createOneTimeLogin(request)
    }

    override suspend fun verifyAndConsumeToken(request: VerifyAndConsumeTokenRequest): VerifyAndConsumeTokenResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = VerifyAndConsumeTokenRequest.newBuilder().setToken(request.token).build()
        return stub.verifyAndConsumeToken(request)
    }

    /**
     * Saves feedback form data to the user service
     *
     * @param feedback The feedback form data to save
     * @return A SaveFeedbackResponse containing the result of the operation
     */
    override suspend fun saveFeedback(feedback: SupportHomepageFeedbackForm): SaveFeedbackResponse {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val request = SaveFeedbackRequest.newBuilder()
            .setUserType(feedback.userType)
            .setEmail(feedback.email)
            .setMessage(feedback.message)

        feedback.name?.let { request.setName(it) }
        feedback.phone?.let { request.setPhone(it) }

        return stub.saveFeedback(request.build())
    }
}
