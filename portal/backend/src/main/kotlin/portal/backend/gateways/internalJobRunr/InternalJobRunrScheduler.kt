package portal.backend.gateways.internalJobRunr


import kotlinx.coroutines.*
import kotlinx.coroutines.channels.consumeEach

class InternalJobRunrScheduler(
    private val internalJobRunrService: InternalJobRunrService,
) {
    private val executionScope = CoroutineScope(Dispatchers.IO)
    private val retryScope = CoroutineScope(Dispatchers.IO)

    fun startSchedulers() {
        executionScope.launch {
            internalJobRunrService.executionChannel.consumeEach {
                internalJobRunrService.execution(it)
            }
        }

        retryScope.launch {
            internalJobRunrService.retryChannel.consumeEach {
                internalJobRunrService.execution(it)
            }
        }
    }

    fun stopSchedulers() {
        executionScope.cancel()
        retryScope.cancel()
    }
}