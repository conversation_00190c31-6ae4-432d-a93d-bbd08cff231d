package portal.backend.gateways.internalJobRunr

import common.libs.logger.Logging
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import portal.backend.configs.DeleteShareWithMeConfig
import portal.backend.configs.JobRunrServiceConfig
import portal.jobrunr.datastructure.JobStatus
import portal.jobrunr.request.JobRequest
import kotlin.reflect.KSuspendFunction1

class InternalJobRunrService(
    private val config: DeleteShareWithMeConfig,
    private val client: HttpClient,
    private val jobProcessingCache: JobProcessingCache,
    private val jobRunrServiceConf: JobRunrServiceConfig
) : Logging {
    private val jobProcesses = mutableMapOf<String, suspend (JobRequest) -> Boolean>()

    val executionChannel = Channel<JobRequest>(
        capacity = this.config.deletingBuffer,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
        onUndeliveredElement = {
            logger.error("[JobRunr][executionChannel] Channel dropped jobId: ${it.jobId}")
        })

    val retryChannel = Channel<JobRequest>(
        capacity = this.config.retryingBuffer,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
        onUndeliveredElement = {
            logger.error("[JobRunr][retryChannel] Channel dropped jobId:  ${it.jobId}")
        })


    /**
     * Add a job process to the jobProcesses map.
     *
     * @param jobName the name of the job
     * @param job the job function to be added
     */
    fun addJobProcess(jobName: String, job: KSuspendFunction1<JobRequest, Boolean>) {
        // Add the job function to the jobProcesses map
        jobProcesses[jobName] = job
    }


    /**
     * Executes a job based on the provided JobRequest.
     * If the job execution is successful, the job will be deleted.
     * @param jobReq the JobRequest containing information about the job to be executed.
     */
    suspend fun execution(jobReq: JobRequest) {
        try {
            // Check if the jobName is in the list of jobProcesses and execute the corresponding job function
            val isExecutionSuccess: Boolean = when (jobReq.jobName) {
                in jobProcesses.keys -> jobProcesses[jobReq.jobName]?.invoke(jobReq) == true
                else -> {
                    false
                }
            }

            // Delete the job if the execution was successful
            if (isExecutionSuccess && jobProcessingCache.get(jobReq.jobId.toString()) != null) {
                jobReq.status = JobStatus.Completed
                jobProcessingCache.set(jobReq.jobId.toString(), jobReq)
                return
            }

            // job failed, retry job
            jobProcessingCache.delete(jobReq.jobId.toString())

            logger.error("[JobRunr][execution] execution fail jobId:  ${jobReq.jobId}")
        } catch (t: Throwable) {
            logger.error("[JobRunr][execution] execution error jobId:  ${jobReq.jobId}")
            throw t
        }
    }

    fun getJobInChannel(jobId: String): JobRequest? {
        return jobProcessingCache.get(jobId)
    }


    /**
     * Enqueues a job for retry if it's not already in process.
     * @param jobReq the JobRequest to enqueue for retry
     */
    suspend fun enqueueJobRetry(jobReq: JobRequest) {
        coroutineScope {
            launch {
                if (jobProcessingCache.get(jobReq.jobId.toString()) == null) {
                    jobProcessingCache.set(jobReq.jobId.toString(), jobReq)
                    retryChannel.send(jobReq)
                }
            }
        }
    }


    /**
     * Enqueues a job request and returns a boolean indicating success.
     * @param jobReq the job request to be enqueued
     * @return a boolean indicating whether the job request was successfully enqueued
     */
    suspend fun enqueueJob(jobReq: JobRequest) {
        try {
            jobProcessingCache.set(jobReq.jobId.toString(), jobReq)
            executionChannel.send(jobReq)

            createJob(jobReq)
        } catch (t: Throwable) {
            logger.error("[JobRunr][enqueueJob] enqueueJob error jobId:  ${jobReq.jobId}")
            throw t
        }
    }

    private suspend fun createJob(jobReq: JobRequest): HttpResponse {
        try {
            return client.post(this.jobRunrServiceConf.uri) {
                contentType(ContentType.Application.Json)
                setBody(jobReq)
            }
        } catch (t: Throwable) {
            logger.error("[JobRunr][createJob] createJob error jobId:  ${jobReq.jobId}")
            throw t
        }
    }
}
