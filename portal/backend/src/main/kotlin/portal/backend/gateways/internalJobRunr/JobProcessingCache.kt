package portal.backend.gateways.internalJobRunr

import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import common.libs.cache.ICacheService
import portal.jobrunr.request.JobRequest
import java.util.concurrent.TimeUnit


class JobProcessingCache : ICacheService<String, JobRequest> {
    private val cache: Cache<String, JobRequest> =
        CacheBuilder.newBuilder().maximumSize(200).expireAfterAccess(30, TimeUnit.MINUTES).build()

    override fun get(key: String): JobRequest? {
        return cache.getIfPresent(key)
    }

    override fun set(key: String, value: JobRequest) {
        cache.put(key, value)
    }

    override fun delete(key: String) {
        cache.invalidate(key)
    }

    override fun contains(key: String): Boolean {
        return cache.getIfPresent(key) != null
    }
}