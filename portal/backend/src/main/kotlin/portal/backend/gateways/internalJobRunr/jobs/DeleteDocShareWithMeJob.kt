package portal.backend.gateways.internalJobRunr.jobs

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.grpc.Status
import org.bson.BsonType
import portal.backend.gateways.MetadataServiceGateway
import portal.backend.gateways.internalJobRunr.InternalJobRunrService
import portal.backend.models.*
import portal.backend.utility.defaultMapper
import portal.jobrunr.datastructure.JobExecutorServiceName
import portal.jobrunr.request.JobRequest
import java.util.*

class DeleteDocShareWithMeJob(
    private val metadataServiceGateway: MetadataServiceGateway,
    private val jobRunrService: InternalJobRunrService
) {
    private val mapper = jacksonObjectMapper()

    init {
        jobRunrService.addJobProcess(JobName.DeleteDocShareWithMe.toString(), this::deleteDocShareWithMe)
    }

    private suspend fun deleteDocShareWithMe(req: JobRequest): Bo<PERSON>an {
        try {
            val jobDesc: DeleteDocShareWithMe = mapper.readValue(
                req.jobDesc, DeleteDocShareWithMe::class.java
            )

            val grpcRes = metadataServiceGateway.loadMetadataDoc(
                DOC_IN_PROFILE_TYPE, listOfNotNull(
                    Triple("docGlobalId", BsonType.STRING.name, jobDesc.docId),
                ), null, jobDesc.docId
            ).await()

            val docRes = grpcRes.metadataList.firstOrNull() ?: return false

            val docDetails = defaultMapper.readValue(
                docRes.metadataDetails,
                DocInProfileInfoDetail::class.java
            )

            // delete doc-share-with-me
            if (!docDetails.shared) {
                val res = metadataServiceGateway.deleteManyMetadataDocs(
                    jobDesc.docId, DOC_SHARE_WITH_ME_TYPE
                ).await()

                // if delete doc-share-with-me fail retry job
                if (Status.Code.OK.value() != res.status.code) {
                    return false
                }
            }
            return true
        } catch (t: Throwable) {
            throw t
        }
    }

    /**
     * Deletes a document shared with me.
     *
     * @param docId The ID of the document to delete.
     * @param metadataType The type of metadata associated with the document.
     */
    suspend fun enqueueShareWithMeDelete(docId: String, metadataType: String) {
        try {
            // Create the DeleteDocShareWithMe object
            val docDeleteShareWithMe = DeleteDocShareWithMe(docId, metadataType)
            // Create a job request for the JobRunr service
            val doc = JobRequest(
                jobId = UUID.randomUUID(),
                service = JobExecutorServiceName.BackendService,
                jobName = JobName.DeleteDocShareWithMe.toString(),
                jobDesc = mapper.writeValueAsString(docDeleteShareWithMe),
                retry = -1,
                delayInSecond = 30 // check status and delete job after 30 seconds
            )

            this.jobRunrService.enqueueJob(doc)
        } catch (t: Throwable) {
            throw t
        }
    }
}