package portal.backend.koin

import com.google.common.reflect.ClassPath
import com.mongodb.reactivestreams.client.MongoClients
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import portal.backend.configs.ConfigBean
import java.util.stream.Collectors

@Module
class CodecRegistryModule {
    @Single
    fun provideCodecRegistry(configBean: ConfigBean): CodecRegistry {
        return CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(), CodecRegistries.fromProviders(
                PojoCodecProvider.builder().automatic(true)
                    .register(*configBean.registerPojoPackages.toTypedArray<String>())
                    .register(*loadAllBsonDiscriminatorClasses(configBean.registerPojoPackages).toTypedArray<Class<*>>())
                    .build()
            )
        )
    }

    private fun loadAllBsonDiscriminatorClasses(packages: List<String>): List<Class<*>> {
        return try {
            ClassPath.from(javaClass.getClassLoader()).allClasses.stream()
                .filter { c -> packages.contains(c.packageName) }.map { c -> c.load() }
                .filter { c -> c.isAnnotationPresent(BsonDiscriminator::class.java) }.collect(Collectors.toList())
        } catch (t: Throwable) {
            emptyList()
        }
    }
}

