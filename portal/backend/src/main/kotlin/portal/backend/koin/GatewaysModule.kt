package portal.backend.koin


import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Single
import portal.backend.configs.*

@Module
class GrpcChannelModule {
    @Single
    @Named(METADATA_SERVICE_CHANNEL)
    fun provideMetadataServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.metadataService.host, seConf.metadataService.port).usePlaintext().build();
    }

    @Single
    @Named(USER_SERVICE_CHANNEL)
    fun provideUserServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.userService.host, seConf.userService.port).usePlaintext().build()
    }

    @Single
    @Named(LSESSION_SERVICE_CHANNEL)
    fun provideLSessionServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.lSessionService.host, seConf.lSessionService.port).usePlaintext()
            .build()
    }

    @Single
    @Named(CONF_SERVICE_CHANNEL)
    fun provideConfServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.dataConfService.host, seConf.dataConfService.port).usePlaintext()
            .build()
    }

    @Single
    @Named(NOTIFICATION_SERVICE_CHANNEL)
    fun provideNotificationServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.notificationService.host, seConf.notificationService.port)
            .usePlaintext().build()
    }

    @Single
    @Named(FILE_STORE_SERVICE_CHANNEL)
    fun provideFileStoreServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.fileStoreService.host, seConf.fileStoreService.port)
            .usePlaintext().build()
    }
}
