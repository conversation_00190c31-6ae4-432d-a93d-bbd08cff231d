package portal.backend.koin

import org.koin.core.qualifier.named
import org.koin.dsl.bind
import org.koin.dsl.module
import org.koin.ksp.generated.module
import portal.backend.gateways.*
import portal.backend.gateways.internalJobRunr.InternalJobRunrScheduler
import portal.backend.gateways.internalJobRunr.InternalJobRunrService
import portal.backend.gateways.internalJobRunr.JobProcessingCache
import portal.backend.gateways.internalJobRunr.jobs.DeleteDocShareWithMeJob


val koinApplicationModule = module(createdAtStart = true) {
    includes(
        ConfigurationsModule().module,
        GrpcChannelModule().module,
        CodecRegistryModule().module
    )

    single { UserServiceGatewayImpl(get(named(USER_SERVICE_CHANNEL))) } bind IUserServiceGateway::class
    single { LSessionServiceGatewayImpl(get(named(LSESSION_SERVICE_CHANNEL))) } bind ILSessionServiceGateway::class
    single { ConfigurationsServiceGatewayImpl(get(named(CONF_SERVICE_CHANNEL))) } bind IConfigurationsServiceGateway::class
    single { ClassroomServiceGateway(get(named(LSESSION_SERVICE_CHANNEL)), get()) }
    single { NotificationServiceGateway(get(named(NOTIFICATION_SERVICE_CHANNEL))) }
    single { MetadataServiceGateway(get(named(METADATA_SERVICE_CHANNEL))) }
    single { FileStoreServiceGateway(get(named(FILE_STORE_SERVICE_CHANNEL))) } bind IFileStoreServiceGateway::class
    single { UserLoginCache() } bind UserLoginCache::class
    single { JobProcessingCache() } bind JobProcessingCache::class
    single { JwtTokenCacheService(get(), get()) } bind JwtTokenCacheService::class
    single { UserForgotPwCacheService(get(), get()) } bind UserForgotPwCacheService::class
    single<InternalJobRunrScheduler> { InternalJobRunrScheduler(get()) }
    single<InternalJobRunrService> { InternalJobRunrService(get(), get(), get(), get()) }
    single<DeleteDocShareWithMeJob> { DeleteDocShareWithMeJob(get(), get()) }
}
