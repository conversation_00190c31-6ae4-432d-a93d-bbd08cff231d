package portal.backend.models

import portal.backend.models.DocumentInfoResponse
import java.util.Date


/**
 *
 * <AUTHOR>
 */

const val DOC_IN_PROFILE_TYPE = "doc-in-profile"


data class DocInProfileInfoDetail(
    val docGlobalId: String,
    val docLocalId: Int,
    val ownerUserId: String,
    val ownerUserName: String,
    val source: String,
    val docName: String? = null,
    val previewUrl: String? = null,
    val savedToProfile: Boolean = true,
    val savedDate: Date? = null,
    val shared: Boolean = false,
    val embedded: Boolean = false,
    val sourceDocId: String? = null,
)

data class CreateDocInProfileInfoRequest(
    val docGlobalId: String,
    val editorType: String,
    val details: DocInProfileInfoDetail
)

data class DocPreviewUploadTokenDto(
    val docGlobalId: String,
    val allowFileTypes: List<String>,
    val maxFileSize: Int,
    val metadataDocType: String
)

data class SaveDocToProfileDto(
    val docGlobalId: String,
    val docName: String,
    val previewUrl: String,
)

data class GetSavedDocsDto(
    val editorType: String? = null,
    val textSearch: String? = null,
    val page: Int = 1,
    val size: Int = 20,
    val startDate: Date? = null,
    val endDate: Date? = null,
)

data class GetSavedDocsResponse(
    val totalCount: Int,
    val page: Int,
    val pageSize: Int,
    val hasPrevious: Boolean,
    val hasNext: Boolean,
    val documents: List<DocumentInfoResponse>,
)