package portal.backend.models

/**
 *
 * <AUTHOR>
 */

const val DOC_OWNERSHIP_TYPE = "doc-ownership"

data class DocOwnershipInfoDetail(
    val docGlobalId: String,
    val docLocalId: Int,
    val ownerUserId: String,
    val ownerUserName: String,
    val docName: String? = null,
)

data class CreateDocOwnershipInfoRequest(
    val docGlobalId: String,
    val editorType: String,
    val details: DocOwnershipInfoDetail
)

data class CheckPublicDocumentRequest(
    val docGlobalId: String,
    val editorType: String
)

data class CheckPublicDocumentResponse(
    val isPublic: Boolean,
    val validEditorType: Boolean
)