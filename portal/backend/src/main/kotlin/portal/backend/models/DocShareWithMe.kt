package portal.backend.models

import java.util.*


/**
 *
 * <AUTHOR>
 */

const val DOC_SHARE_WITH_ME_TYPE = "doc-share-with-me"

data class DocShareWithMeInfoDetail(
    val docGlobalId: String,
    val docLocalId: Int,
    val ownerUserId: String,
    val ownerUserName: String,
    val source: String,
    val docName: String? = null,
    val previewUrl: String? = null,
    val savedToProfile: Boolean = true,
    val savedDate: Date? = null,
    val shared: Boolean = false,
    val recipient: String
)

data class DocShareWithMeInfoRequest(
    val docGlobalId: String,
    val editorType: String,
    val details: DocShareWithMeInfoDetail
)
