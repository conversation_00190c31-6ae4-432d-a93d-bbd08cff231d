package portal.backend.models

import portal.datastructures.lsession.LSRegStatus
import portal.lsession.pojo.LSessionState
import kotlin.properties.Delegates

data class LSessionSummary(
    var id: String,
    var title: String,
    var grade: String,
    var subject: String,
    var imgUrl: String,
    var startTime: Long,
    var expectedDuration: Int,
    var maxStudent: Int,
    var state: LSessionState? = null,
    var regId: String? = null,
    var regStatus: LSRegStatus? = null,
)
