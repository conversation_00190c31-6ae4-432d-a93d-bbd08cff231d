package portal.backend.models

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import portal.user.pojo.Address
import portal.user.pojo.Gender
import portal.user.pojo.fromAddressProto
import proto.portal.user.UserMessage
import java.util.*

@Serializable
data class Profile(
    val id: String = "",
    val email: String = "",
    val username: String = "",
    val name: String = "",
    val avatarUrl: String = "",
    val phone: String = "",
    val gender: Gender? = null,
    @Contextual
    val dateOfBirth: Date? = null,
    @Contextual
    val address: Address? = null,
) {
    companion object {}
}

fun Profile.Companion.fromGetUserProfileResponseProto(it: UserMessage.UserProfileProto) =
    Profile(
        id = it.id,
        email = it.email,
        username = it.username,
        name = it.name,
        avatarUrl = it.avatarUrl,
        phone = it.phone,
        gender = Gender.valueOf(it.gender.toString()),
        address = Address.fromAddressProto(it.address),
        dateOfBirth = if (it.hasDateOfBirth()) Date(it.dateOfBirth) else null
    )