package portal.backend.models

import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import portal.user.pojo.Address
import portal.user.pojo.Gender
import portal.user.pojo.toAddressProto
import proto.portal.user.UserMessage
import java.util.*

@Serializable
data class UpdateProfileDTO(
    val email: String = "",
    val name: String? = null,
    val phone: String? = null,
    @Contextual
    val dateOfBirth: Date? = null,
    val gender: Gender? = null,
    @Contextual
    val address: Address? = null,
) {

}

    /**
     * Converts the [UpdateProfileDTO] to a [UserMessage.UpdateProfileRequest] proto message
     *
     * @return a [UserMessage.UpdateProfileRequest] that contains the data from [UpdateProfileDTO]
     */
fun UpdateProfileDTO.toUpdateProfileRequest(): UserMessage.UpdateProfileRequest {
    val requestBuilder = UserMessage.UpdateProfileRequest.newBuilder()
    requestBuilder.setEmail(email)

    name?.let { requestBuilder.setName(it) }
    dateOfBirth?.let { requestBuilder.setDateOfBirth(it.time) }
    gender?.let { requestBuilder.setGender(UserMessage.Gender.valueOf(it.toString())) }
    address?.let { requestBuilder.setAddress(it.toAddressProto()) }
    phone?.let { requestBuilder.setPhone(it) }

    return requestBuilder.build()
}