package portal.backend.plugins

import io.ktor.server.application.*
import portal.backend.controllers.*

fun Application.configureRouting() {
    userController()
    registrationController()
    classroomController()
    configurationsController()
    documentController()
    LSessionController()
    LSessionRegistrationController()
    NotificationController()
    fileStoreController()
    socialPreviewController()
    docInProfileController()
    supportController()
    internalController()
}
