package portal.backend.useraccessor

import jayeson.lib.access.UserDataAccessor
import jayeson.lib.access.datastructure.ResourceData
import jayeson.lib.access.datastructure.RoleData
import jayeson.lib.access.datastructure.UserData
import kotlinx.coroutines.*
import kotlinx.coroutines.future.asCompletableFuture
import org.koin.core.component.KoinComponent
import org.koin.core.component.get
import org.koin.core.component.inject
import portal.backend.gateways.IUserServiceGateway
import portal.backend.gateways.UserLoginCache
import portal.backend.models.BasicUserData
import proto.portal.user.UserMessage
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage

class UserDataAccessorImpl(
) : UserDataAccessor, KoinComponent {
    private var usGateway: IUserServiceGateway = get()
    private val userLoginCache: UserLoginCache by inject<UserLoginCache>()

    override fun findUserByName(name: String): CompletionStage<UserData?> {
        // this cache is for the temp password generated by social login
        val tempPasswordUser: BasicUserData? = userLoginCache.get(name)
        if (tempPasswordUser != null)
            return findForSocialLogin(tempPasswordUser)

        return findForEmailPasswordLogin(name)
    }

    private fun findForSocialLogin(userLogin: BasicUserData): CompletableFuture<UserData?> {
        return CoroutineScope(Dispatchers.IO).async {
            userLogin.username?.let { userLoginCache.delete(it) }

            return@async BasicUserData(
                userLogin.getId(),
                userLogin.username,
                userLogin.password,
                userLogin.getEmailVerified(),
                userLogin.getRegistrationId()
            )
        }.asCompletableFuture()
    }

    private fun findForEmailPasswordLogin(name: String): CompletableFuture<UserData?> {
        return usGateway.loginByUsernameOrEmail(name).thenApply { res: UserMessage.LoginByUsernameOrEmailResponse ->
            if (res.status.code != 0) {
                return@thenApply null
            }
            // username is the concept of lib.access, in viclass we use email to differentiate the profiles
            BasicUserData(
                res.user.id,
                username = res.user.email,
                res.password,
                res.user.verified,
                res.user.registrationId
            )
        }
    }

    override fun findUserById(id: Int): CompletionStage<UserData> {

        return CompletableFuture.completedFuture(null)
    }

    override fun findUserByEmail(email: String): CompletionStage<UserData> {
        return CompletableFuture.completedFuture(null)
    }

    override fun findResourceByUsername(name: String): CompletionStage<ResourceData> {
        return CompletableFuture.completedFuture(null)
    }

    override fun findResourceByUserId(id: Int): CompletionStage<ResourceData> {
        return CompletableFuture.completedFuture(null)
    }

    override fun findRoleByUsername(name: String): CompletionStage<RoleData> {
        return CompletableFuture.completedFuture(null)
    }

    override fun findRoleByUserId(id: Int): CompletionStage<RoleData> {
        return CompletableFuture.completedFuture(null)
    }
}
