package portal.backend.utility

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import jayeson.lib.access.ktor.ActionDoLogin
import jayeson.lib.access.ktor.DataMethod
import jayeson.lib.access.ktor.annotation.DoLoginConfiguration
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import org.apache.commons.codec.binary.Hex
import java.security.MessageDigest
import org.apache.commons.codec.binary.Base64

object LoginUtils {
    val VERIFY_ONETIME_LOGIN_PATH: String = "api/user/onetime-login"
    val VERIFY_ONETIME_LOGIN_QUERY_NAME: String = "data"
    val VERIFY_ONETIME_LOGIN_CODE_KEY: String = "code"
    val VERIFY_ONETIME_LOGIN_REDIRECT_URL_KEY: String = "redirect_url"

    // sync with generateOneTimeLoginLink in portal.beta
    fun generateOneTimeLoginLink(token: String, redirectUrl: String?): String {
        val json = buildJsonObject {
            put(VERIFY_ONETIME_LOGIN_CODE_KEY, token)
            put(VERIFY_ONETIME_LOGIN_REDIRECT_URL_KEY, redirectUrl)
        }
        val jsonString = json.toString()
        val encodedBase64JsonString = Base64.encodeBase64String(jsonString.toByteArray())
        return "/${VERIFY_ONETIME_LOGIN_PATH}?${VERIFY_ONETIME_LOGIN_QUERY_NAME}=${encodedBase64JsonString}"
    }

    /**
     * Create an internal call to execute the login action of access lib with the Header DataMethod.
     * Use this to perform a login inside the logic of Controllers, as Ktor does not natively support internal redirect.
     */
    suspend fun createInternalLoginCall(
        call: ApplicationCall,
        actionDoLogin: ActionDoLogin,
        email: String,
        password: String,
    ): ApplicationCall {
        val loginCall: ApplicationCall = createCustomHeaderCall(
            call, mapOf("X-Username" to email, "X-AccessToken" to password)
        )

        return actionDoLogin.call(loginCall, DoLoginConfiguration(credentialSource = DataMethod.HEADER))
    }

    /**
     * create call with override header to simulate an internal redirect
     */
    private fun createCustomHeaderCall(call: ApplicationCall, headerData: Map<String, String>): ApplicationCall {
        val req = object : ApplicationRequest by call.request {
            override val headers: Headers = Headers.build {
                headerData.forEach { (key, value) ->
                    set(key, value)
                }
            }
        }
        return object : ApplicationCall by call {
            override val request: ApplicationRequest = req
        }
    }

    /**
     * generate random password
     */
    fun generateRandomPassword(length: Int = 15): String {
        val alphanumeric = ('A'..'Z') + ('a'..'z') + ('0'..'9')
        return buildString {
            repeat(length) {
                append(alphanumeric.random())
            }
        }
    }

    /**
     * Hash a string using the MD5 algorithm.
     */
    fun doHashMD5(value: String, charsetName: String = "utf-8"): String {
        val digest: ByteArray = MessageDigest.getInstance("MD5").digest(value.toByteArray(charset(charsetName)))
        return String(Hex.encodeHex(digest))
    }
}