package portal.beta.configuration

import common.libs.jwt.JwtConfig

data class Configuration(
    val serverConf: ServerConfig,
    val dbConf: DatabaseConfig,
    val seConf: ServiceExplorerConfig,
    val emailConf: NotificationEmailConfig,
    val jwtConf: JwtConfig,
    val cacheServiceConf: CacheServiceConf,
    val betaWhiteListIPAndHost: List<String> = emptyList(),
    val slack: SlackConfig
)
