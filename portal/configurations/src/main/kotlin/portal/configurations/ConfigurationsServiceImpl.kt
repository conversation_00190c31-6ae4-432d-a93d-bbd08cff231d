package portal.configurations

import com.google.protobuf.Empty
import com.google.rpc.Status
import common.libs.logger.Logging
import kotlinx.coroutines.rx3.await
import proto.portal.configurations.ConfigurationsMessages
import proto.portal.configurations.ConfigurationsServiceGrpcKt

class ConfigurationsServiceImpl constructor(
    private val gateway: DataCollectionsGateway
) : ConfigurationsServiceGrpcKt.ConfigurationsServiceCoroutineImplBase(), Logging {

    override suspend fun getAllDataConfigurations(request: Empty): ConfigurationsMessages.GetAllDataConfigurationsResponse {
        val responseBuilder = ConfigurationsMessages.GetAllDataConfigurationsResponse.newBuilder()
        val statusBuilder = Status.newBuilder()
        return try {
            val grades = gateway.loadAll()
                .toList()
                .map { list ->
                    val converted = list.map { p ->
                        val values = p.second.map { it.toJson() }.toString()
                        val proto = ConfigurationsMessages.DataConfigurationsProto.newBuilder()
                            .setFieldName(p.first)
                            .setValues(values)
                            .build()
                        p.first to proto
                    }
                    mapOf(*converted.toTypedArray())
                }
                .await()

            statusBuilder.code = io.grpc.Status.Code.OK.value()
            statusBuilder.message = "successful"

            responseBuilder.setStatus(statusBuilder)
                .putAllConfigs(grades)
                .build()
        } catch (t: Throwable) {
            logger.error("Failed to load all data configurations ", t)
            statusBuilder.code = io.grpc.Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed"
            responseBuilder.setStatus(statusBuilder).build()
        }
    }

}