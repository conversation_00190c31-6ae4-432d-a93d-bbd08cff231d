package portal.configurations

import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.BackpressureStrategy
import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Flowable
import org.bson.Document

class DataCollectionsGateway constructor(
    private val collections: Map<String, MongoCollection<Document>>
): Logging {

    fun loadAll(): Flowable<Pair<String, List<Document>>> {
        return Flowable.create({ emitter ->
            val completables = collections.mapValues { en ->
                Flowable.defer { en.value.find() }
                    .toList()
                    .doOnError { logger.error("Failed to load data configurations for {}: ", en.key, it) }
                    .doOnSuccess { r: List<Document> ->
                        r.forEach { convertIdToString(it) }
                        emitter.onNext(en.key to r)
                    }
                    .ignoreElement()
                    .onErrorComplete()
            }

            Completable.concat(completables.values)
                .subscribe { emitter.onComplete() }
        }, BackpressureStrategy.BUFFER)
    }

    private fun convertIdToString(document: Document) {
        try {
            val objectId = document.getObjectId("_id")
            document.replace("_id", objectId.toHexString())
        } catch (t: Throwable) {
            // ignore error
        }
    }
}