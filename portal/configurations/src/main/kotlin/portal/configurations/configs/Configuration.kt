package portal.configurations.configs

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * @property serverConf Server
 * @property dbConf DatabaseConfig
 *
 * <AUTHOR>
 */
data class Configuration @JsonCreator constructor(
    @JsonProperty("serverConf") val serverConf: ServerConfig,
    @JsonProperty("dbConf") val dbConf: DatabaseConfig
)
