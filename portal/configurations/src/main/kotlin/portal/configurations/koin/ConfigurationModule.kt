package portal.configurations.koin

import jayeson.utility.JacksonConfig
import jayeson.utility.JacksonConfigFormat
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import portal.configurations.configs.Configuration
import portal.configurations.configs.DatabaseConfig
import portal.configurations.configs.ServerConfig
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton

@Module
class ConfigurationModule {
    private val logger: Logger = LoggerFactory.getLogger(ConfigurationModule::class.java)
    private val config: Configuration

    private val configPath = "conf/config.json";
    private val configVar = "ConfigurationsServiceConf";

    init {
        config = loadConfig()
    }

    private fun loadConfig(): Configuration {
        return try {
            val config = JacksonConfig.readConfig(
                configPath,
                configVar,
                Configuration::class.java,
                JacksonConfigFormat.JSON
            )
            if (config == null) {
                logger.error("Failed to load configurations")
                throw RuntimeException("Failed to load configurations")
            }
            config
        } catch (e: Exception) {
            logger.error("Failed to load configurations ", e)
            throw RuntimeException(e)
        }
    }

    @Singleton
    fun provideServiceConfig(): Configuration = config

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig = config.serverConf

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig = config.dbConf
}