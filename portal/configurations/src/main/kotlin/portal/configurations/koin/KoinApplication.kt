package portal.configurations.koin

import com.mongodb.reactivestreams.client.MongoCollection
import org.bson.Document
import org.koin.core.qualifier.named
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import portal.configurations.ConfigurationsServer
import portal.configurations.ConfigurationsServiceImpl
import portal.configurations.DataCollectionsGateway

val koinApplication = koinApplication {
    modules(
        module {
            single<DataCollectionsGateway> { DataCollectionsGateway(get(named(DatabaseModule.MONGO_COLLECTIONS))) }
            single<ConfigurationsServiceImpl> { ConfigurationsServiceImpl(get()) }
            single<ConfigurationsServer> {ConfigurationsServer(get(), get())}
        }
    )
}