package portal.datastructures.lsession


/**
 *  Đ<PERSON><PERSON> là trạng thái liên quan đến toàn bộ buổi học hoặc khóa học.
 *  <PERSON><PERSON> bao gồm các thông tin về:
 *  + <PERSON><PERSON><PERSON> trình của buổi học/kh<PERSON><PERSON> học (đ<PERSON> lê<PERSON> lị<PERSON>, đang <PERSON> ra, đã kết thúc).
 *  + Trạng thái tổng thể của buổi học/kh<PERSON><PERSON> học (đã bắt đầu, đã hoàn thành, bị hủy).
 *  Nó tập trung vào "vòng đời" của buổi học/khóa học.
 * <AUTHOR>
 */
enum class LSessionStatus {
    /**
     * Buổi học/Khóa học chưa bắt đầu.
     */
    NOT_STARTED,

    /**
     * <PERSON>uổ<PERSON> học/<PERSON>h<PERSON><PERSON> học đã bắt đầu và đang diễn ra.
     */
    STARTED,

    /**
     * <PERSON><PERSON><PERSON><PERSON> học/<PERSON>h<PERSON><PERSON> học đã kết thúc, nh<PERSON>ng dữ liệu vẫn có thể được truy cập.
     */
    ENDED,

    /**
     * Buổi học/Khóa học đã hoàn toàn kết thúc và dữ liệu không còn được truy cập.
     */
    CLOSED,
}
