package portal.filestore

import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.plugins.contentnegotiation.*
import portal.filestore.koin.configureKoin
import portal.filestore.plugins.configureCORS
import portal.filestore.plugins.configureRouting

fun main(args: Array<String>): Unit = io.ktor.server.netty.EngineMain.main(args)

fun Application.module() {
    install(ContentNegotiation) {
        jackson {
            // configure Jackson here if needed
        }
    }

    configureKoin()
    configureRouting()
    configureCORS()

    // start after <PERSON><PERSON> is configured
    startGrpcServer()
}