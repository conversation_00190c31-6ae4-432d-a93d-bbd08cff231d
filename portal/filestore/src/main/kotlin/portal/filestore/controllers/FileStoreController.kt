package portal.filestore.controllers

import com.auth0.jwt.exceptions.JWTVerificationException
import io.ktor.http.*
import io.ktor.http.content.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.reactive.asFlow
import org.koin.ktor.ext.inject
import org.slf4j.LoggerFactory
import portal.filestore.configs.UploadConfig
import portal.filestore.models.UploadRequestDto
import portal.filestore.services.FileService
import portal.filestore.services.IFileService
import portal.filestore.utility.FileUtils

fun Application.fileUploadController() {
    val fileService: IFileService by inject<FileService>()
    val uploadConfig: UploadConfig by inject()

    val logger = LoggerFactory.getLogger("FileUploadController")

    routing {
        route("/filestore") {
            post("/upload") {
                val multipart = call.receiveMultipart()

                val dto = try {
                    val reqDto = UploadRequestDto()
                    multipart.forEachPart { part ->
                        when (part) {
                            is PartData.FileItem -> {
                                reqDto.filename = part.originalFileName
                                reqDto.fileBytes = part.streamProvider().readBytes()
                            }

                            is PartData.FormItem -> {
                                if (part.name == "uploadToken") {
                                    reqDto.token = part.value
                                } else {
                                    logger.debug("Unknown form field: ${part.name} = ${part.value}")
                                }
                            }

                            else -> {
                                logger.debug("Unknown part type: {}", part.javaClass)
                            }
                        }
                        part.dispose()
                    }
                    reqDto
                } catch (e: Exception) {
                    logger.error("Error processing multipart data: ${e.message}", e)
                    call.respond(HttpStatusCode.BadRequest, "Invalid multipart data")
                    return@post
                }

                try {
                    val fileId = fileService.uploadFile(dto)
                    call.respond(
                        HttpStatusCode.OK, mapOf(
                            "fileId" to fileId,
                            "fileUrl" to uploadConfig.fileUrlTemplate.replace("{fileId}", fileId)
                        )
                    )
                } catch (e: IllegalArgumentException) {
                    call.respond(HttpStatusCode.BadRequest, e.message ?: "Invalid file upload request")
                } catch (e: JWTVerificationException) {
                    call.respond(HttpStatusCode.Unauthorized, e.message ?: "Invalid or expired upload token")
                } catch (e: Exception) {
                    logger.error("Unknown Error Uploading: ${e.message} with token $${dto.token}", e)
                    call.respond(HttpStatusCode.InternalServerError, e.message ?: "File upload failed")
                }
            }

            get("/download/{fileId}") {
                val fileId = call.parameters["fileId"]
                if (fileId.isNullOrBlank()) {
                    call.respond(HttpStatusCode.BadRequest, "File ID is required")
                    return@get
                }

                try {
                    val result = fileService.downloadToPublisher(fileId)
                    val fileName = result.metadata.originalFileName
                    val contentType = FileUtils.getContentTypeFromFileName(fileName)

                    call.response.header(
                        HttpHeaders.ContentDisposition, ContentDisposition.Attachment.withParameter(
                            ContentDisposition.Parameters.FileName, fileName
                        ).toString()
                    )

                    // Stream the file bytes to the client without loading all into memory
                    call.respondOutputStream(
                        contentType = contentType,
                        contentLength = result.metadata.fileSizeByte.toLong()
                    ) {
                        // Convert to Flow<ByteBuffer> and write each chunk
                        result.publisher.asFlow().collect { byteBuffer ->
                            // Read the ByteBuffer into a byte array and write to OutputStream
                            val bytes = ByteArray(byteBuffer.remaining())
                            byteBuffer.get(bytes)
                            write(bytes)
                        }
                    }
                } catch (e: NoSuchElementException) {
                    call.respond(HttpStatusCode.NotFound, e.message ?: "File not found")
                } catch (e: Exception) {
                    logger.error("Unknown Error Downloading: ${e.message}", e)
                    call.respond(HttpStatusCode.InternalServerError, e.message ?: "File download failed")
                }
            }
        }
    }
}