package portal.filestore.dbgateway

import com.mongodb.client.model.Filters
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import kotlinx.coroutines.reactive.awaitFirstOrNull
import org.bson.types.ObjectId
import org.koin.core.annotation.Single
import portal.filestore.models.FileMetadata
import java.util.*

@Single
class FileMetadataGateway(
    private val collection: MongoCollection<FileMetadata>,
) : IFileMetadataGateway, Logging {

    override suspend fun insertFileMetadata(metadata: FileMetadata): String {
        metadata.createdAt = Date()
        return Flowable.defer {
            collection.insertOne(metadata)
        }.awaitFirstOrNull()?.insertedId?.asObjectId()?.value?.toHexString()
            ?: throw IllegalStateException("File metadata insert failed")
    }

    override suspend fun getFileMetadata(id: String): FileMetadata? {
        return Flowable.defer {
            collection.find(Filters.eq("_id", ObjectId(id))).limit(1)
        }.awaitFirstOrNull()
    }

    override suspend fun updateFileMetadata(metadata: FileMetadata): UpdateResult? {
        return Flowable.defer {
            collection.replaceOne(
                Filters.eq("_id", ObjectId(metadata.id)),
                metadata.apply { updatedAt = Date() }
            )
        }.awaitFirstOrNull()
    }
}