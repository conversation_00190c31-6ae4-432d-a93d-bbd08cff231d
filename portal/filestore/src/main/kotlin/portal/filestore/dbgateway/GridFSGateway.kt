package portal.filestore.dbgateway

import com.mongodb.reactivestreams.client.gridfs.GridFSBucket
import io.reactivex.rxjava3.core.Flowable
import kotlinx.coroutines.reactive.awaitFirst
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.reactive.awaitSingle
import kotlinx.coroutines.rx3.await
import org.bson.types.ObjectId
import org.koin.core.annotation.Single
import org.reactivestreams.Publisher
import portal.filestore.configs.UploadConfig
import portal.filestore.models.FileMetadata
import portal.filestore.utility.toBufferPublisher
import java.nio.ByteBuffer
import java.util.concurrent.TimeUnit

@Single
class GridFSGateway(
    private val uploadConfig: UploadConfig,
    private val gridFsBucket: GridFSBucket,
) : IFileStorageGateway {
    override suspend fun uploadFile(file: ByteArray, filename: String): String {
        if (file.isEmpty()) throw IllegalArgumentException("File is empty")
        if (filename.isBlank()) throw IllegalArgumentException("Filename is empty")

        return Flowable.defer {
            gridFsBucket.uploadFromPublisher(filename, file.toBufferPublisher())
        }.timeout(uploadConfig.uploadTimeoutSeconds, TimeUnit.SECONDS).awaitFirstOrNull()?.toHexString()
            ?: throw IllegalStateException("File upload failed")
    }

    override suspend fun downloadFile(fileMetadata: FileMetadata): ByteArray {
        return Flowable.defer {
            gridFsBucket.downloadToPublisher(ObjectId(fileMetadata.fileId))
        }.collectInto(ByteBuffer.allocate(fileMetadata.fileSizeByte)) { buffer, data ->
            buffer.put(data)
        }.map { buffer ->
            buffer.flip() // cleanup the buffer to prepare for reading
            ByteArray(buffer.remaining()).apply {
                buffer.get(this)
            }
        }.await()
    }

    override suspend fun downloadToPublisher(fileMetadata: FileMetadata): Publisher<ByteBuffer> {
        return gridFsBucket.downloadToPublisher(ObjectId(fileMetadata.fileId))
    }

    override suspend fun deleteFile(fileId: String) {
        Flowable.defer {
            gridFsBucket.delete(ObjectId(fileId))
        }.awaitFirstOrNull()
    }
}