package portal.filestore.dbgateway

import com.mongodb.client.result.UpdateResult
import portal.filestore.models.FileMetadata

interface IFileMetadataGateway {
    /**
     * Inserts file metadata into the database
     * @return the id of the file metadata document
     */
    suspend fun insertFileMetadata(metadata: FileMetadata): String

    /**
     * Retrieves file metadata from the database
     * @return the file metadata document
     */
    suspend fun getFileMetadata(id: String): FileMetadata?
    suspend fun updateFileMetadata(metadata: FileMetadata): UpdateResult?
}