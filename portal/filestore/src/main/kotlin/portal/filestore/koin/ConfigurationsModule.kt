package portal.filestore.koin

import com.typesafe.config.ConfigFactory
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import portal.filestore.configs.*

@Module
class ConfigurationsModule {

    private val configFactory = ConfigFactory.load()
    private val config: ConfigBean

    init {
        config = loadConfig()
    }

    private fun loadConfig(): ConfigBean {
        return ConfigBean(
            serverConf = GrpcServerConfig(
                port = configFactory.getInt("serverConf.port")
            ),
            dbConf = DatabaseConfig(
                connectionString = configFactory.getString("dbConf.connectionString"),
                dbName = configFactory.getString("dbConf.dbName"),
                gridFsBucketName = configFactory.getString("dbConf.gridFsBucketName")
            ),
            uploadConf = UploadConfig(
                allowFileTypes = configFactory.getStringList("uploadConf.allowFileTypes").toSet(),
                maxFileSizeBytes = configFactory.getInt("uploadConf.maxFileSizeBytes"),
                uploadTimeoutSeconds = configFactory.getLong("uploadConf.uploadTimeoutSeconds"),
                fileUrlTemplate = configFactory.getString("uploadConf.fileUrlTemplate")
            ),
            jwtConf = JwtConfig(
                secret = configFactory.getString("jwtConf.secret"),
                expiresInMinutes = configFactory.getLong("jwtConf.expiresInMinutes")
            ),
            cacheServiceConf = CacheServiceConfig(
                host = configFactory.getString("cacheServiceConf.host"),
                port = configFactory.getInt("cacheServiceConf.port"),
                uploadedToken = CacheServiceConfig.CacheConfig(
                    prefix = configFactory.getString("cacheServiceConf.uploadedToken.prefix"),
                    expiration = configFactory.getLong("cacheServiceConf.uploadedToken.expiration")
                )
            )
        )
    }

    @Single
    fun provideConfigBean(): ConfigBean {
        return config
    }

    @Single
    fun provideGrpcServerConfig(): GrpcServerConfig {
        return config.serverConf
    }

    @Single
    fun provideDatabaseConfig(): DatabaseConfig {
        return config.dbConf
    }

    @Single
    fun provideUploadConfig(): UploadConfig {
        return config.uploadConf
    }

    @Single
    fun provideJwtConfig(): JwtConfig {
        return config.jwtConf
    }

    @Single
    fun provideCacheServiceConfig(): CacheServiceConfig {
        return config.cacheServiceConf
    }
}