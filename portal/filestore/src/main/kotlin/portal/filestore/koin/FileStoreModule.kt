package portal.filestore.koin

import io.lettuce.core.RedisClient
import io.lettuce.core.api.sync.RedisCommands
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import portal.filestore.configs.CacheServiceConfig

@Module
@ComponentScan("portal.filestore")
class FileStoreModule {
    @Single
    fun provideRedisCommands(cacheConfig: CacheServiceConfig): RedisCommands<String, String> {
        return RedisClient.create("${cacheConfig.host}:${cacheConfig.port}").connect().sync()
    }
}