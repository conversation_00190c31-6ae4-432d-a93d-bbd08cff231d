package portal.filestore.services

import org.bson.types.ObjectId
import org.koin.core.annotation.Single
import portal.filestore.dbgateway.IFileMetadataGateway
import portal.filestore.dbgateway.IFileStorageGateway
import portal.filestore.models.*
import portal.filestore.utility.FileUtils
import portal.filestore.utility.defaultMapper

@Single
class FileService(
    private val jwtService: JwtService,
    private val fileStorageGateway: IFileStorageGateway,
    private val fileMetadataGateway: IFileMetadataGateway,
    private val uploadedTokenCacheService: UploadedTokenCacheService,
) : IFileService {
    override suspend fun getMetadata(fileId: String): FileMetadata {
        return fileMetadataGateway.getFileMetadata(fileId)
            ?: throw NoSuchElementException("File not found")
    }

    override suspend fun uploadFile(dto: UploadRequestDto): String {
        val token = dto.token ?: throw IllegalArgumentException("Token is required")
        if (token.isBlank())
            throw IllegalArgumentException("Token can not be blank")
        if (uploadedTokenCacheService.contains(token))
            throw IllegalArgumentException("Token has been used")

        val fileBytes = dto.fileBytes
        val filename = dto.filename
        if (fileBytes == null || fileBytes.isEmpty() || filename.isNullOrBlank())
            throw IllegalArgumentException("Upload file is required")

        val (userId, payloadJson) = jwtService.verifyJwt(token)
        val payload = defaultMapper.readValue(payloadJson, UploadTokenPayload::class.java)
        verifyUploadConditions(payload, fileBytes, filename)

        var metadata: FileMetadata? =
            if (!payload.overrideFileId.isNullOrEmpty()) getMetadata(payload.overrideFileId) else null

        // store the file
        val gridFsId = fileStorageGateway.uploadFile(fileBytes, filename)

        val metadataId: String
        if (metadata != null) {
            // update metadata & delete old file
            val oldFileId = metadata.fileId
            metadata = metadata.copy(
                fileId = gridFsId,
                ownerId = userId,
                originalFileName = filename,
                fileSizeByte = fileBytes.size,
                version = metadata.version + 1,
            )
            fileMetadataGateway.updateFileMetadata(metadata)
            fileStorageGateway.deleteFile(oldFileId) // TODO: could keep the old file for versioning instead of deleting

            metadataId = metadata.id
        } else {
            // store metadata
            val id = if (payload.preparedFileId.isNullOrEmpty()) ObjectId().toHexString() else payload.preparedFileId
            metadata = FileMetadata(
                id = id,
                fileId = gridFsId,
                ownerId = if (userId == "GUEST") null else userId,
                originalFileName = filename,
                fileSizeByte = fileBytes.size,
                version = 1,
            )
            metadataId = fileMetadataGateway.insertFileMetadata(metadata)
        }


        // cache the token to prevent reuse
        uploadedTokenCacheService.setToken(token)
        return metadataId
    }

    override suspend fun downloadFile(fileId: String): DownloadResultDto {
        val metadata = getMetadata(fileId)
        val fileBytes = fileStorageGateway.downloadFile(metadata)

        return DownloadResultDto(
            metadata = metadata,
            file = fileBytes,
        )
    }

    override suspend fun downloadToPublisher(fileId: String): DownloadToPublisherResultDto {
        val metadata = getMetadata(fileId)
        val publisher = fileStorageGateway.downloadToPublisher(metadata)

        return DownloadToPublisherResultDto(
            metadata = metadata,
            publisher = publisher,
        )
    }

    /**
     * Verify upload conditions in payload with the actual file.
     */
    @Throws(IllegalArgumentException::class)
    private fun verifyUploadConditions(
        payload: UploadTokenPayload,
        fileBytes: ByteArray,
        filename: String,
    ) {
        if (payload.maxFileSizeBytes < fileBytes.size)
            throw IllegalArgumentException("File size is too large")

        val fileExtension = FileUtils.getFileExtension(filename)
        if (payload.allowFileTypes.contains(fileExtension).not())
            throw IllegalArgumentException("File type '$fileExtension' is not allowed")
    }
}