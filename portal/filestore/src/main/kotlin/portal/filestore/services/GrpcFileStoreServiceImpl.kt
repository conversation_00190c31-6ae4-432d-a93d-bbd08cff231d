package portal.filestore.services

import common.libs.logger.Logging
import io.grpc.Status
import org.bson.types.ObjectId
import org.koin.core.annotation.Single
import portal.filestore.configs.UploadConfig
import portal.filestore.models.UploadTokenPayload
import portal.filestore.utility.defaultMapper
import proto.portal.filestore.FileStoreServiceGrpcKt
import proto.portal.filestore.FilestoreMessage

@Single
class GrpcFileStoreServiceImpl(
    private val uploadConfig: UploadConfig,
    private val jwtService: IJwtService,
    private val fileService: IFileService,
) : FileStoreServiceGrpcKt.FileStoreServiceCoroutineImplBase(), Logging {

    private val downloadUrlRegex =
        Regex("""^${uploadConfig.fileUrlTemplate.replace("{fileId}", """([a-f\d]{24})""")}$""")

    override suspend fun getUploadToken(request: FilestoreMessage.UploadTokenRequest): FilestoreMessage.UploadTokenResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = FilestoreMessage.UploadTokenResponse.newBuilder()

        try {
            checkValidAllowTypes(request.allowTypesList)

            val overrideFileId = getOverrideFileId(request.overrideFileUrl)
            if (overrideFileId != null) {
                val metadata = fileService.getMetadata(overrideFileId)
                if (request.hasUploadUserId() && metadata.ownerId != request.uploadUserId)
                    throw IllegalArgumentException("Can not override $overrideFileId. The file is not owned by user")
            }

            var preparedFileId: String? = null
            if (request.hasIncludeDownloadUrl() && request.includeDownloadUrl) {
                preparedFileId = overrideFileId ?: ObjectId.get().toHexString()
                builder.setDownloadUrl(uploadConfig.fileUrlTemplate.replace("{fileId}", preparedFileId))
            }

            val payload =
                UploadTokenPayload(
                    request.allowTypesList,
                    request.maxSize,
                    if (request.hasUploadUserId()) request.uploadUserId else null,
                    overrideFileId,
                    preparedFileId,
                )
            val payloadJson = defaultMapper.writeValueAsString(payload)

            val token =
                jwtService.createJwt(if (request.hasUploadUserId()) request.uploadUserId else "GUEST", payloadJson)
            builder.setUploadToken(token)



            code = Status.Code.OK
        } catch (e: Exception) {
            logger.warn("Failed to get upload token", e)
            message = e.message ?: "Failed to get upload token"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    @Throws(IllegalArgumentException::class)
    private fun checkValidAllowTypes(types: Iterable<String>): Unit {
        types.forEach {
            if (!uploadConfig.allowFileTypes.contains(it.lowercase())) {
                throw IllegalArgumentException("File type $it is not allowed")
            }
        }
    }

    private fun getOverrideFileId(overrideUrl: String?): String? {
        if (overrideUrl == null) return null

        val fileId = downloadUrlRegex.matchEntire(overrideUrl)?.groupValues?.get(1) ?: return null
        if (!ObjectId.isValid(fileId)) throw IllegalArgumentException("Invalid override URL $overrideUrl")

        return fileId
    }
}