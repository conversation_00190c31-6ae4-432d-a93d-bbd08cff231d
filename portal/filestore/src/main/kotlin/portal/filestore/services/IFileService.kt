package portal.filestore.services

import portal.filestore.models.DownloadResultDto
import portal.filestore.models.DownloadToPublisherResultDto
import portal.filestore.models.FileMetadata
import portal.filestore.models.UploadRequestDto

interface IFileService {
    suspend fun getMetadata(fileId: String): FileMetadata

    suspend fun uploadFile(dto: UploadRequestDto): String

    suspend fun downloadFile(fileId: String): DownloadResultDto

    suspend fun downloadToPublisher(fileId: String): DownloadToPublisherResultDto
}