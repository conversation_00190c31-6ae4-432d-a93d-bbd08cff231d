package portal.filestore.services

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.DecodedJWT
import io.ktor.util.*
import org.koin.core.annotation.Single
import portal.filestore.configs.JwtConfig
import java.util.Date

@Single
class JwtService(
    private val jwtConfig: JwtConfig,
) : IJwtService {
    private val algorithm = Algorithm.HMAC256(jwtConfig.secret)
    private val userIdClaim = "userId"

    override fun createJwt(userId: String, payloadJson: String): String {
        return JWT.create()
            .withPayload(payloadJson)
            .withClaim(userIdClaim, userId)
            .withExpiresAt(Date(System.currentTimeMillis() + jwtConfig.expiresInMinutes * 60 * 1000))
            .sign(algorithm)
    }

    override fun verifyJwt(jwt: String): Pair<String, String> {
        val decodedJWT: DecodedJWT = JWT.require(algorithm)
            .withClaimPresence(userIdClaim) // TODO: can verify the user permission here
            .build()
            .verify(jwt)

        val userId = decodedJWT.getClaim(userIdClaim).asString()
        return userId to decodedJWT.payload.decodeBase64String()
    }
}