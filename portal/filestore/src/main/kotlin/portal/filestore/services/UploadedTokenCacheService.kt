package portal.filestore.services

import common.libs.cache.RedisBaseCacheService
import io.lettuce.core.api.sync.RedisCommands
import org.koin.core.annotation.Single
import portal.filestore.configs.CacheServiceConfig

/**
 * Service to cache uploaded tokens, to avoid user use a single token to upload multiple times
 */
@Single
class UploadedTokenCacheService(
    private val redisCommands: RedisCommands<String, String>,
    private val config: CacheServiceConfig,
) : RedisBaseCacheService<String, String>(redisCommands) {

    override val expiration: Long = config.uploadedToken.expiration

    override fun getCacheKey(key: String) = "${config.uploadedToken.prefix}$key"

    /**
     * Set a token into the cache with a dummy value since we only care about the key
     */
    fun setToken(key: String) {
        super.set(key, "1")
    }
}