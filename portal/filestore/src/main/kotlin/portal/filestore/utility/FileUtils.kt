package portal.filestore.utility

import io.ktor.http.*
import io.reactivex.rxjava3.core.Flowable
import org.reactivestreams.Publisher
import java.net.URLConnection
import java.nio.ByteBuffer

object FileUtils {
    /**
     * Get file extension from filename.
     * Ex: "file.PNG" -> ".png"
     */
    fun getFileExtension(filename: String): String {
        val lastDotIndex = filename.lastIndexOf('.')
        return if (lastDotIndex > 0) filename.substring(lastDotIndex).lowercase() else ""
    }

    /**
     * Get content type from filename.
     * Ex: "file.PNG" -> ContentType.Image.PNG
     */
    fun getContentTypeFromFileName(filename: String): ContentType {
        val contentTypeStr = URLConnection.guessContentTypeFromName(filename);
        if (!contentTypeStr.isNullOrBlank()) return ContentType.parse(contentTypeStr);

        return when (getFileExtension(filename)) {
            ".jpg", ".jpeg" -> ContentType.Image.JPEG
            ".png" -> ContentType.Image.PNG
            ".gif" -> ContentType.Image.GIF
            ".svg" -> ContentType.Image.SVG
            ".ico" -> ContentType.Image.XIcon
            ".bmp", ".webp", ".tiff" -> ContentType.Image.Any
            ".mp3" -> ContentType.Audio.MPEG
            ".mp4" -> ContentType.Video.MP4
            ".mpeg" -> ContentType.Video.MPEG
            ".ogg" -> ContentType.Video.OGG
            ".mov" -> ContentType.Video.QuickTime
            ".webm", ".mkv", ".avi", ".wmv", ".flv" -> ContentType.Video.Any
            ".txt" -> ContentType.Text.Plain
            ".json" -> ContentType.Application.Json
            ".xml" -> ContentType.Application.Xml
            ".csv" -> ContentType.Text.CSV
            else -> ContentType.Application.OctetStream
        }
    }
}

fun ByteArray.toBufferPublisher(): Publisher<ByteBuffer> {
    return Flowable.fromArray(ByteBuffer.wrap(this))
}