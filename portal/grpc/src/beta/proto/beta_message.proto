syntax = 'proto3';

package proto.portal.beta;

import "google/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

// GetOrCreateBetaRegistration
message GetOrCreateBetaRegistrationRequest {
  string email = 1;
}
message BetaRegistration {
  string email = 1;
  optional string code = 2;
  bool success = 3;
  google.rpc.Status status = 4;
}

// InviteJoinBeta
message InviteJoinBetaAccountRequest {
  string email = 1;
  optional string password = 2;
  optional string redirect_url = 3;
}
message InviteJoinBetaRequest {
  repeated InviteJoinBetaAccountRequest accounts = 1;
}
message InviteJoinBetaAccountResponse {
  google.rpc.Status status = 1;
  string email = 2;
}
message InviteJoinBetaResponse {
  repeated InviteJoinBetaAccountResponse results = 1;
}

// ApplyInvitationCode
message ApplyInvitationCodeRequest {
  string email = 1;
  string invitation_code = 2;
}
message ApplyInvitationCodeResponse {
  google.rpc.Status status = 2;
}