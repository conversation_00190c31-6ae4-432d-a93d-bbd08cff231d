syntax = 'proto3';

package vi.metadata.doc.proto;

import "google/rpc/status.proto";
import "google/protobuf/wrappers.proto";

/**
 *
 * <AUTHOR>
 */

message MetadataDocProto {
  string id = 1;
  string doc_id = 2;
  string editor_type = 3;
  string metadata_type = 4;
  string metadata_details = 5;
}

message CreateMetadataDocRequest {
  string request_id = 1;
  MetadataDocProto metadata = 2;
}

message LoadMetadataDocRequest {
  message DataFilter {
    string field_name = 1;
    string data_type = 2;
    string value = 3;
  }

  google.protobuf.StringValue doc_id = 1;
  google.protobuf.StringValue editor_type = 2;
  google.protobuf.StringValue metadata_type = 3;
  repeated DataFilter data_filter = 4;
  google.protobuf.UInt32Value limit = 5;
  google.protobuf.UInt32Value offset = 6;
  google.protobuf.StringValue text_search = 7;

  optional bool should_count = 8;
}

message UpdateMetadataDocRequest {
  string request_id = 1;
  string doc_id = 2;
  string metadata_details = 3;
  string metadata_type = 4;

}

message UpdateMetadataDocByIdRequest {
  string request_id = 1;
  string id = 2;
  string metadata_details = 3;
  string metadata_type = 4;
}

message UpdateMetadataDocFieldRequest {
  message DataUpdate {
    string field_name = 1;
    string data_type = 2;
    string value = 3;
  }

  string request_id = 1;
  string doc_id = 2;
  repeated DataUpdate metadata_details = 3;
  string editor_type = 4;
  string metadata_type = 5;
}

message DeleteMetadataDocRequest {
  string request_id = 1;
  string doc_id = 2;
}

message DuplicateMetadataDocRequest {
  message DataReplace {
    string field_name = 1;
    string data_type = 2;
    string value = 3;
  }

  string request_id = 1;
  string doc_id = 2;
  string new_doc_id = 3;
  string metadata_type = 4;
  repeated DataReplace metadata_details = 5;
}

message MetadataDocResponse {
  string request_id = 1;
  google.rpc.Status status = 2;
  MetadataDocProto metadata = 3;
}

message MetadataDocsResponse {
  string request_id = 1;
  google.rpc.Status status = 2;
  repeated MetadataDocProto metadata = 3;
  optional google.protobuf.UInt32Value total_count = 4;
}


message DeleteManyMetadataDocsRequest{
  message DataFilter {
    string field_name = 1;
    string data_type = 2;
    string value = 3;
  }

  string request_id = 1;
  string doc_id = 2;
  string metadata_type = 3;
  string editor_type = 4;
  repeated DataFilter data_filters = 5;
}

message DeleteManyMetadataDocsResponse{
  string request_id = 1;
  google.rpc.Status status = 2;
}


