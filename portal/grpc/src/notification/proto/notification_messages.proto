syntax = 'proto3';

package proto.portal.notification;

import "google/rpc/status.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";

// REQUESTS
message SaveNotificationRequest {
  string request_id = 1;
  string jsonNotification = 2;
  bool skipPersist = 3; // skip persist to db if true, default as false
}

message LoadNotificationByIdRequest {
  string request_id = 1;
  string notification_id = 2;
}

message LoadClassroomNotificationByCriteriaRequest {
  string request_id = 1;
  string ls_id = 2;
  string user_id = 3;
}

message CreateEmailTemplateRequest {
  string template_id = 1;
  string content = 2;
  repeated string referred_template_ids = 3;
  bool is_html = 4;
}

message SendEmailRequest {
  string to_address = 1;
  string to_name = 2;
  string subject = 3;
  string template_id = 4;
  string json_data = 5;
}

// RESPONSES
message SaveNotificationResponse {
  string request_id = 1;
  google.rpc.Status status = 2;
  string notification_id = 3;
}

message LoadNotificationByIdResponse {
  string request_id = 1;
  google.rpc.Status status = 2;
  string jsonNotification = 3;
}

message LoadClassroomNotificationByCriteriaResponse {
  string request_id = 1;
  google.rpc.Status status = 2;
  repeated string jsonNotification = 3;
}

message CreateEmailTemplateResponse {
  string template_id = 1;
  google.rpc.Status status = 2;
}

message SendEmailResponse {
  google.rpc.Status status = 2;
}