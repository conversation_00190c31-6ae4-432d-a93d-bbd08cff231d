syntax = 'proto3';

package proto.portal.notification;

import "notification_messages.proto";
import "google/protobuf/empty.proto";

service NotificationService {

  rpc SaveNotification (SaveNotificationRequest) returns (SaveNotificationResponse);
  rpc LoadNotificationById (LoadNotificationByIdRequest) returns (LoadNotificationByIdResponse);
  rpc LoadClassroomNotificationByCriteria (LoadClassroomNotificationByCriteriaRequest) returns (LoadClassroomNotificationByCriteriaResponse);

  rpc CreateEmailTemplate(CreateEmailTemplateRequest) returns (CreateEmailTemplateResponse);
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse);
}