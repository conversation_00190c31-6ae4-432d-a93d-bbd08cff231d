syntax = 'proto3';

package proto.portal.user;

import "google/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

/**
 *
 * <AUTHOR>
 */

enum Gender {
  MALE = 0;
  FEMALE = 1;
  OTHER = 2;
}

enum Social {
  FACEBOOK = 0;
  GOOGLE = 1;
}

message Address {
  optional string country = 1;
  optional string city = 2;
  optional string district = 3;
  optional string ward = 4;
  optional string street = 5;
}

message UserProto {
  string id = 1;
  string username = 2;
  string email = 3;
  string phone = 4;
  bool verified = 5;
  string registration_id = 6;
}

message UserProfileProto {
  string id = 1;
  string username = 2;
  string email = 3;
  string phone = 4;
  string avatar_url = 5;
  optional int64 date_of_birth = 6;
  Gender gender = 7;
  Address address = 8;
  string name = 9;
}

message EmailRegistrationRequest {
  string username = 1;
  string email = 2;
  string phone = 3;
  string password = 4;
  string avatar_url = 5;
  bool is_verified = 6;
}

message EmailRegistrationResponse {
  google.rpc.Status status = 1;
  string user_id = 2;
}

message GetUserByIdRequest {
  string user_id = 1;
}

message GetUserByUsernameRequest {
  string username = 1;
}

message GetUserByEmailRequest {
  string email = 1;
}

message GetUserByPhoneRequest {
  string phone = 1;
}

message GetUserResponse {
  google.rpc.Status status = 1;
  UserProto user = 2;
}

message GetUserProfileByIdRequest {
  string user_id = 1;
}

message GetUserProfileByUsernameRequest {
  string username = 1;
}

message GetUserProfileByIdsRequest {
  repeated string user_id = 1;
}

message GetUserProfileByIdsResponse {
  google.rpc.Status status = 1;
  repeated UserProfileProto user_profile = 2;
}

message GetUserProfileResponse {
  google.rpc.Status status = 1;
  UserProfileProto user_profile = 2;
}

message CheckRegistrationEmailExistRequest {
  string email = 1;
  string registration_type = 2;
}

message CheckRegistrationEmailExistResponse {
  google.rpc.Status status = 1;
  bool is_exist = 2;
}

message SendVerificationEmailRequest {
  string registration_id = 1;
}

message SendVerificationEmailResponse {
  google.rpc.Status status = 1;
  bool is_sent = 2;
  google.protobuf.Timestamp last_sent = 3;
}

message EmailVerificationRequest {
  string registration_id = 1;
  string verification_code = 2;
  bool skip_verification = 3;
}

message EmailVerificationResponse {
  google.rpc.Status status = 1;
  string registration_id = 2;
}

message RegistrationMetadataRequest {
  string registration_id = 1;
}

message RegistrationMetadataResponse {
  google.rpc.Status status = 1;
  string registration_id = 2;
  string registration_type = 3;
  string email = 4;
  bool is_verified = 5;
}

message AddMissingSocialEmailRequest {
  string registration_id = 1;
  string email = 2;
}

message AddMissingSocialEmailResponse {
  google.rpc.Status status = 1;
}

message GetLinkedRegistrationsRequest {
  string profile_email = 1;
}

message GetLinkedRegistrationsResponse {
  google.rpc.Status status = 1;
  repeated string registration_name = 2;
}

message LoginByUsernameOrEmailRequest {
  string username_or_email = 1;
}

message LoginByUsernameOrEmailResponse {
  google.rpc.Status status = 1;
  UserProto user = 2;
  string password = 3;
}

message TokenInfo {
  string provider = 1;
  string social_id = 2;
  string email = 3;
  bool email_verified = 4;
  string username = 5;
  string avatar_url = 6;
  string first_name = 7;
  string last_name = 8;
}

message CreateOrMergeUserProfileRequest {
  string registration_id = 1;
  optional TokenInfo token_info = 2;
}

message CreateOrMergeUserProfileResponse {
  google.rpc.Status status = 1;
  UserProto user = 2;
}

message VerifySocialTokenRequest {
  string provider = 1;
  string auth_token = 2;
}

message VerifySocialTokenResponse {
  google.rpc.Status status = 1;
  TokenInfo token_info = 2;
  string registration_id = 3;
  string registration_email = 4;
  bool verified = 5;
}

message AddSocialRegistrationRequest {
  TokenInfo token_info = 1;
}

message AddSocialRegistrationResponse {
  google.rpc.Status status = 1;
  string registration_id = 2;
}

message UnlinkRelinkSocialRegistrationRequest {
  string registration_id = 1;
  TokenInfo token_info = 2;
}

message UnlinkRelinkSocialRegistrationResponse {
  google.rpc.Status status = 1;
  string profile_id = 2;
}

message SendEmailResetPasswordRequest {
  string email = 1;
}

message SendEmailResetPasswordResponse {
  google.rpc.Status status = 1;
  string jwtToken = 2;
}

message ResetPasswordResponse {
  google.rpc.Status status = 1;
}

message ResetPasswordRequest {
  string newPassword = 1;
  string jwtToken = 2;
}

message UpdateAvatarRequest {
  string user_id = 1;
  string avatar_url = 2;
}

message UpdateAvatarResponse {
  google.rpc.Status status = 1;
}
message ChangePhoneNumberRequest {
  string phoneNumber = 1;
  string email = 2;
}

message ChangePasswordRequest {
  string newPassword = 1;
  string email = 2;
}

message ChangePasswordResponse {
  google.rpc.Status status = 1;
}

message ChangePhoneNumberResponse {
  google.rpc.Status status = 1;
}

message GetUserLoginInformationRequest {
  string email = 1;
}

message GetUserLoginInformationResponse {
  google.rpc.Status status = 1;
  string date_created = 2;
  string last_login = 3;
  string email = 4;
}

message UpdateLastLoginTimeResponse {
  google.rpc.Status status = 1;
}

message UpdateLastLoginTimeRequest {
  string email = 1;
}

message CheckPasswordRequest {
  string email = 1;
  string password = 2;
}

message CheckPasswordResponse {
  google.rpc.Status status = 1;
}


message UpdateUserProfileRequest {
  string username = 1;
  string email = 2;
  string phone = 3;
  string avatarUrl = 4;
}

message UpdateUserProfileResponse {
  google.rpc.Status status = 1;
}

message UpdateUserEmailRegRequest {
  string email = 1;
  string username = 2;
  string phone = 3;
  string password = 4;
}


message UpdateUserEmailRegResponse {
  google.rpc.Status status = 1;
}

message UpdateProfileRequest {
  string email = 1;
  optional string name = 2;
  optional int64 date_of_birth = 3;
  optional Gender gender = 4;
  optional Address address = 5;
  optional string phone = 6;
}

message UpdateProfileResponse {
  google.rpc.Status status = 1;
  UserProfileProto profile = 2;
}

message UnlinkSocialRequest {
  string email = 1;
  Social social = 2;
}

message UnlinkSocialResponse {
  google.rpc.Status status = 1;
}

message EmailRegInfoRequest {
  string username_or_email = 1;
}

message EmailRegInfoResponse {
  google.rpc.Status status = 1;
  optional string email = 2;
  optional string username = 3;
}

message CreateOneTimeLoginRequest {
  string reg_id = 1;
}

message CreateOneTimeLoginResponse {
  google.rpc.Status status = 1;
  string key = 2;
}

message VerifyAndConsumeTokenRequest {
  string token = 1;
}

message VerifyAndConsumeTokenResponse {
  google.rpc.Status status = 1;
  string reg_id = 2;
}

message SaveFeedbackRequest {
  string user_type = 1;
  optional string name = 2;
  optional string phone = 3;
  string email = 4;
  string message = 5;
}

message SaveFeedbackResponse {
  google.rpc.Status status = 1;
  bool success = 2;
}