syntax = 'proto3';

package proto.portal.user;

import "user_message.proto";

/**
 *
 * <AUTHOR>
 */

service UserService {
  rpc RegisterUserByEmail (EmailRegistrationRequest) returns (EmailRegistrationResponse);
  rpc GetUserById (GetUserByIdRequest) returns (GetUserResponse);
  rpc GetUserByUsername (GetUserByUsernameRequest) returns (GetUserResponse);
  rpc LoginByUsernameOrEmail(LoginByUsernameOrEmailRequest) returns (LoginByUsernameOrEmailResponse);
  rpc GetUserByEmail (GetUserByEmailRequest) returns (GetUserResponse);
  rpc GetUserByPhone (GetUserByPhoneRequest) returns (GetUserResponse);
  rpc GetUserProfileById (GetUserProfileByIdRequest) returns (GetUserProfileResponse);
  rpc GetUserProfileByUsername (GetUserProfileByUsernameRequest) returns (GetUserProfileResponse);
  rpc GetUserProfileByIds (GetUserProfileByIdsRequest) returns (GetUserProfileByIdsResponse);
  rpc GetUserProfileByUsernameOrEmail (GetUserByUsernameRequest) returns (GetUserProfileResponse);
  rpc CheckRegistrationEmailExist (CheckRegistrationEmailExistRequest) returns (CheckRegistrationEmailExistResponse);
  rpc GetLinkedRegistrations (GetLinkedRegistrationsRequest) returns (GetLinkedRegistrationsResponse);
  rpc SendVerificationEmail (SendVerificationEmailRequest) returns (SendVerificationEmailResponse);
  rpc VerifyEmail (EmailVerificationRequest) returns (EmailVerificationResponse);
  rpc GetRegistrationMetadata (RegistrationMetadataRequest) returns (RegistrationMetadataResponse);
  rpc AddMissingSocialEmail (AddMissingSocialEmailRequest) returns (AddMissingSocialEmailResponse);
  rpc CreateOrMergeUserProfile (CreateOrMergeUserProfileRequest) returns (CreateOrMergeUserProfileResponse);
  rpc VerifySocialToken (VerifySocialTokenRequest) returns (VerifySocialTokenResponse);
  rpc AddSocialRegistration (AddSocialRegistrationRequest) returns (AddSocialRegistrationResponse);
  rpc UnlinkRelinkSocialRegistration (UnlinkRelinkSocialRegistrationRequest) returns (UnlinkRelinkSocialRegistrationResponse);
  rpc SendEmailResetPassword(SendEmailResetPasswordRequest) returns (SendEmailResetPasswordResponse);
  rpc ResetPassword(ResetPasswordRequest) returns (ResetPasswordResponse);
  rpc UpdateAvatar(UpdateAvatarRequest) returns (UpdateAvatarResponse);
  rpc UpdateProfile(UpdateProfileRequest) returns (UpdateProfileResponse);
  rpc UnlinkSocial(UnlinkSocialRequest) returns (UnlinkSocialResponse);
  rpc GetUserLoginInformation(GetUserLoginInformationRequest) returns (GetUserLoginInformationResponse);
  rpc UpdateLastLoginTime(UpdateLastLoginTimeRequest) returns (UpdateLastLoginTimeResponse);
  rpc CheckPassword(CheckPasswordRequest) returns (CheckPasswordResponse);
  rpc UpdateUserProfile(UpdateUserProfileRequest) returns (UpdateUserProfileResponse);
  rpc UpdateUserEmailReg(UpdateUserEmailRegRequest) returns (UpdateUserEmailRegResponse);
  rpc GetEmailRegInfo(EmailRegInfoRequest) returns (EmailRegInfoResponse);
  rpc CreateOneTimeLogin(CreateOneTimeLoginRequest) returns (CreateOneTimeLoginResponse);
  rpc VerifyAndConsumeToken(VerifyAndConsumeTokenRequest) returns (VerifyAndConsumeTokenResponse);
  rpc SaveFeedback(SaveFeedbackRequest) returns (SaveFeedbackResponse);
}
