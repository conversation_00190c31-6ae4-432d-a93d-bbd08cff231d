package portal.jobrunr.execution

import io.ktor.server.plugins.requestvalidation.*
import java.util.*

/**
 * <AUTHOR>
 */


data class JobExecutionRequest(
    val jobId: UUID,
    val jobName: String,
    val jobDesc: String,
)

fun JobExecutionRequest.validateRequest(): ValidationResult {
    return if (this.jobName.isBlank()) ValidationResult.Invalid("should include jobName")
    else if (this.jobDesc.isBlank()) ValidationResult.Invalid("should include jobDesc")
    else ValidationResult.Valid
}
