package portal.jobrunr

import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.client.MongoClient
import com.mongodb.client.MongoClients
import com.mongodb.client.MongoDatabase
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import org.bson.UuidRepresentation
import org.bson.codecs.EnumCodecProvider
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.jobrunr.configuration.JobRunr
import org.jobrunr.scheduling.JobScheduler
import org.jobrunr.server.BackgroundJobServerConfiguration
import org.jobrunr.storage.nosql.mongo.MongoDBStorageProvider
import org.jobrunr.utils.mapper.jackson.JacksonJsonMapper
import org.koin.core.Koin
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import portal.jobrunr.configuration.Configuration
import portal.jobrunr.configuration.ServiceExplorerConfig
import portal.jobrunr.controller.JobController
import portal.jobrunr.jobrunr.JobrunrActivator
import portal.jobrunr.jobrunr.JobrunrBackgroundService
import portal.jobrunr.jobrunr.JobrunrRecurrentlyService
import portal.jobrunr.peer.*
import java.io.File

fun configurationModule(ktorEnv: ApplicationEnvironment) = module {
    single { ktorEnv }

    single<Configuration> {
        val jsonMapper: ObjectMapper by inject()
        jsonMapper.readValue(File("./conf/config.json"), Configuration::class.java)
    }

    single<ServiceExplorerConfig> {
        val config: Configuration by inject()
        config.se
    }

    single<JsonNodeFactory> { JsonNodeFactory.instance }
    single<ObjectMapper> { ObjectMapper().registerModules(KotlinModule.Builder().build(), JavaTimeModule()) }
}

val databaseModule = module {

    single<CodecRegistry> {
        CodecRegistries.fromRegistries(
            MongoClientSettings.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                EnumCodecProvider(),
                PojoCodecProvider.builder()
                    .automatic(true)
                    .register("metaticket.order.pojo")
                    .build(),
            )
        )
    }

    // provide mongodb
    single<MongoClient> {
        val env: ApplicationEnvironment by inject()
        val connectionString = env.config.property("db.connection").getString()
        val registry: CodecRegistry by inject()
        val settings = MongoClientSettings.builder()
            .applyConnectionString(ConnectionString(connectionString))
            .uuidRepresentation(UuidRepresentation.JAVA_LEGACY)
            .codecRegistry(registry)
            .build()

        MongoClients.create(settings)
    }

    single<MongoDatabase> {
        val env: ApplicationEnvironment by inject()
        val dbName = env.config.property("db.dbName").getString()
        val client: MongoClient by inject()
        client.getDatabase(dbName)
    }
}


val httpClient = module {
    single<HttpClient> {
        HttpClient(CIO) {
            expectSuccess = false
            install(ContentNegotiation) {
                jackson {
                    setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                        indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                        indentObjectsWith(DefaultIndenter("  ", "\n"))
                    })
                    registerModule(JavaTimeModule())  // support java.time.* types
                }
            }
        }
    }

    single<BackendClient> { BackendClient(get(), get()) }
}

val controllerModule = module {

    single<JobScheduler> {
        val env: ApplicationEnvironment by inject()
        val dashboardPort = env.config.property("dashboard.port").getString().toInt()

        val jobActivator = JobrunrActivator(get(), get())
        val objectMapper: ObjectMapper by inject()

        val jobServerConfiguration = BackgroundJobServerConfiguration
            .usingStandardBackgroundJobServerConfiguration()
            .andPollIntervalInSeconds(5)

        val storageProvider: MongoDBStorageProvider by inject()

        val runr = JobRunr.configure().useJsonMapper(JacksonJsonMapper(objectMapper)).useJobActivator(jobActivator)
            .useStorageProvider(storageProvider).useBackgroundJobServer(jobServerConfiguration)
            .useDashboard(dashboardPort).initialize().jobScheduler

        runr
    }

    single<JobController> { JobController() }
}

val jobrunrModule = module {

    single<MongoDBStorageProvider> {
        val env: ApplicationEnvironment by inject()
        val dbName = env.config.property("db.dbName").getString()
        val mongoClient: MongoClient by inject()

        MongoDBStorageProvider(mongoClient, dbName)
    }

    single<JobrunrBackgroundService> { JobrunrBackgroundService(get(), get()) }
    single<JobrunrRecurrentlyService> { JobrunrRecurrentlyService() }
}

class ApplicationDI(ktorEnv: ApplicationEnvironment) : DIContext(ktorEnv) {

    val documentCtrl: JobController by inject()
    val jobScheduler: JobScheduler by inject()
}

abstract class DIContext(ktorEnv: ApplicationEnvironment) : KoinComponent {

    private val koinApp = koinApplication {
        // declare used modules
        modules(configurationModule(ktorEnv), databaseModule, httpClient, controllerModule, jobrunrModule)
    }

    private val koin = koinApp.koin
    override fun getKoin(): Koin {
        return koin
    }
}
