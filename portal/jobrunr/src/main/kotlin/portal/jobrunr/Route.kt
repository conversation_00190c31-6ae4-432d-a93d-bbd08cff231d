package portal.jobrunr

import io.ktor.server.application.*
import io.ktor.server.plugins.requestvalidation.*
import io.ktor.server.routing.*
import portal.jobrunr.controller.JobController
import portal.jobrunr.request.JobRequest
import portal.jobrunr.request.validateRequest

fun Route.documentRoutes(c : JobController) {
    route("/internal/jobrunr/job/schedule") {
        post { c.scheduleJob(call) }
        install(RequestValidation) {
            validate<JobRequest>{ it.validateRequest() }
        }
    }
}
