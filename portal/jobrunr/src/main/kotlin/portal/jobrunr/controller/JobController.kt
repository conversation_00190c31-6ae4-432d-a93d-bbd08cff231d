package portal.jobrunr.controller

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.jobrunr.scheduling.BackgroundJob
import org.jobrunr.storage.nosql.mongo.MongoDBStorageProvider
import portal.jobrunr.request.JobRequest
import portal.jobrunr.utils.scheduleRequest

class JobController : Logging {

    suspend fun scheduleJob(call: ApplicationCall) {
        val jr = call.receive<JobRequest>()

        try {
            logger.info("job {} scheduling job request {}", jr.jobId, jr)
            scheduleRequest(jr)
        } catch (t: Throwable) {
            logger.error("job {} failed to schedule job {} ...", jr.jobId, jr, t)
        }

        call.respond(HttpStatusCode.OK)
    }
}
