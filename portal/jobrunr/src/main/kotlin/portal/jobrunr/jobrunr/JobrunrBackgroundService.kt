package portal.jobrunr.jobrunr

import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import portal.jobrunr.datastructure.JobExecutorServiceName
import portal.jobrunr.peer.*
import portal.jobrunr.request.JobRequest
import portal.jobrunr.utils.scheduleRequest
import org.jobrunr.jobs.states.IllegalJobStateChangeException
import org.jobrunr.scheduling.BackgroundJob
import org.jobrunr.storage.JobNotFoundException
import org.jobrunr.storage.nosql.mongo.MongoDBStorageProvider


/**
 *
 * <AUTHOR>
 */
class JobrunrBackgroundService(
    private val backgroundService: BackendClient,
    private val storageProvider: MongoDBStorageProvider,
) : Logging {

    fun submit(jr: JobRequest) {
        runBlocking(Dispatchers.IO) {
            try {
                val client = when (jr.service) {
                    JobExecutorServiceName.BackendService -> backgroundService
                }

                val jobNextAction = client.executeJob(jr)

                logger.info("job {} will {} executing job request {}", jr.jobId, jobNextAction, jr)

                if (jobNextAction == portal.jobrunr.datastructure.JobNextAction.DoNothing) return@runBlocking

                if (jobNextAction == portal.jobrunr.datastructure.JobNextAction.Stop) {
                    logger.info("job {} remove job {}", jr.jobId, jr)
                    BackgroundJob.delete(jr.jobId)
                    storageProvider.deletePermanently(jr.jobId)
                } else if (jr.intervalInSecond == null && jobNextAction == portal.jobrunr.datastructure.JobNextAction.Continue) {
                    // with non-interval job, need to remove the old job one before execute the new job that same id
                    storageProvider.deletePermanently(jr.jobId)
                    scheduleRequest(jr)
                }
            } catch (ex: IllegalJobStateChangeException) {
                logger.debug("job {} deleted already", jr.jobId)
                storageProvider.deletePermanently(jr.jobId)
            } catch (ex: JobNotFoundException) {
                logger.debug("job {} isn't found", jr.jobId)
            } catch (t: Throwable) {
                logger.info("job {} retry schedule job exception... ", jr.jobId, t.cause)
                logger.trace("job {} trace log retry schedule job {} exception... ", jr.jobId, jr, t)
                if (jr.intervalInSecond == null && (jr.retry < 0 || jr.retry-- > 0)) {
                    storageProvider.deletePermanently(jr.jobId)
                    scheduleRequest(jr)
                }
            }
        }
    }
}

