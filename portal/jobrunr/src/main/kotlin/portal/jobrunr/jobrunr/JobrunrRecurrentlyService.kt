package portal.jobrunr.jobrunr


import common.libs.logger.Logging
import portal.jobrunr.request.JobRequest
import org.jobrunr.scheduling.BackgroundJob
import java.time.Duration


/**
 *
 * <AUTHOR>
 */
class JobrunrRecurrentlyService(

): Logging {

    fun schedule(jr: JobRequest) {
        try {
            logger.info("job {} scheduling job request {}", jr.jobId, jr)
            processRequest(jr)
        } catch (t: Throwable) {
            logger.error("job {} failed to schedule job {} ...", jr.jobId, jr, t)
        }
    }

    private fun processRequest(jr: JobRequest) {
        BackgroundJob.scheduleRecurrently<JobrunrBackgroundService>(
            jr.jobId.toString(),
            Duration.ofSeconds(jr.intervalInSecond!!.toLong())
        ) { service ->
            service.submit(jr)
        }
    }
}
