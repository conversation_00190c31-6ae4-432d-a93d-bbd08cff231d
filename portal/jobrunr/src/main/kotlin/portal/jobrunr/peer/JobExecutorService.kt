package portal.jobrunr.peer

import common.libs.logger.Logging
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import portal.jobrunr.configuration.ServiceExplorerConfig
import portal.jobrunr.datastructure.JobNextAction
import portal.jobrunr.execution.JobExecutionRequest
import portal.jobrunr.execution.JobExecutionResponse
import portal.jobrunr.request.JobRequest
import portal.jobrunr.utils.buildRequest


/**
 *
 * <AUTHOR>
 */
abstract class JobExecutorService(
    private val peerConfig: ServiceExplorerConfig.ServicePeerConfig,
    private val client: HttpClient,
) : Logging {
    protected abstract val path: String

    suspend fun executeJob(jr: JobRequest): JobNextAction {
        val requestBuilder = peerConfig.buildRequest {
            method = HttpMethod.Post
            url.path(path)
            contentType(ContentType.Application.Json)
            setBody(jr)
        }

        logger.info("job {} executing {}", jr.jobId, jr)

        try {
            val httpResponse = withContext(Dispatchers.IO) { client.request(requestBuilder) }
            val response = httpResponse.body<JobExecutionResponse>()

            logger.info("job {} receive response {} for job request {}", jr.jobId, jr, response)

            return response.nextAction

        } catch (t: Throwable) {
            logger.error("job {} failed to execute job request {} cause... ", jr.jobId, jr, t)
            if (jr.intervalInSecond != null) return JobNextAction.Continue

            // by default, if retry < 0 meaning retry forever
            if (jr.retry < 0 || jr.retry-- > 0) {
                return JobNextAction.Continue
            }

            return JobNextAction.Stop
        }
    }
}

