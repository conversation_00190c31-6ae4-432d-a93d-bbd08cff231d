package portal.jobrunr.utils

import portal.jobrunr.request.JobRequest
import portal.jobrunr.jobrunr.JobrunrBackgroundService
import portal.jobrunr.jobrunr.JobrunrRecurrentlyService
import org.jobrunr.scheduling.BackgroundJob
import java.time.Instant
import java.time.temporal.ChronoUnit


/**
 *
 * <AUTHOR>
 */
fun scheduleRequest(jr: JobRequest, retryDelayInS: Int = 0) {
    if (jr.intervalInSecond != null) {
        // submit to recurrent service to schedule request after a delay time
        BackgroundJob.schedule<JobrunrRecurrentlyService>(
            jr.jobId,
            Instant.now().plus(jr.delayInSecond, ChronoUnit.SECONDS)
        ) { service ->
            service.schedule(jr)
        }
    } else {
        BackgroundJob.schedule<JobrunrBackgroundService>(
            jr.jobId,
            Instant.now().plus(jr.delayInSecond + retryDelayInS, ChronoUnit.SECONDS)
        ) { service ->
            service.submit(jr)
        }
    }
}
