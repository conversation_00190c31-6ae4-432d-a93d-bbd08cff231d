package portal.kafka.api.serdes

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import org.apache.kafka.common.serialization.Deserializer
import portal.kafka.api.KafkaEventData

class KafkaEventDeserializer : Deserializer<KafkaEventData>, Logging {

    override fun deserialize(topic: String, data: ByteArray): KafkaEventData {
        try {
            val mapper = jacksonObjectMapper()
            val className: String = mapper.readTree(data).get(KafkaEventData::clazz.name).asText()
            val clazz: Class<KafkaEventData> = Class.forName(className) as Class<KafkaEventData>
            return mapper.readValue(data, clazz)
        } catch (e: Throwable) {
            logger.error("exception when deserialize topic {} with data {} ...", topic, data, e)
            throw e
        }
    }
}