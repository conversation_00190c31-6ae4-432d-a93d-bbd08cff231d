package portal.kafka.api.serdes

import com.fasterxml.jackson.databind.ObjectMapper
import common.libs.logger.Logging
import org.apache.kafka.common.serialization.Serializer
import portal.kafka.api.KafkaEventData

class KafkaEventSerializer<T : KafkaEventData>() : Serializer<T>, Logging {

    override fun serialize(topic: String, data: T): ByteArray {
        val objectMapper = ObjectMapper()
        try {
            return objectMapper.writeValueAsBytes(data)
        } catch (e: Throwable) {
            logger.error("Exception when serialize topic {} with data {} ...", topic, data, e)
            throw e
        }
    }
}
