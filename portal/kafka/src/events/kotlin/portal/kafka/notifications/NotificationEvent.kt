package portal.kafka.notifications

import portal.kafka.api.KafkaEventData
import portal.kafka.notifications.notidata.NotificationData

data class NotificationEvent(
    val id: String, // notification id
    val emittedBy: String, // user id that emit this notification
    val targetTo: NotificationTarget,
    val message: String,
    val createdTime: Long,
    val expiredTime: Long,
    val data: NotificationData? = null,
    val show: Boolean = true
) : KafkaEventData()