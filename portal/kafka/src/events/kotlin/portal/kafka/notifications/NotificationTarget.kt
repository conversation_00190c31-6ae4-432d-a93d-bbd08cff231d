package portal.kafka.notifications

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "targetType"
)
@JsonSubTypes(
    JsonSubTypes.Type(UserTarget::class, name = "UserTarget"),
    JsonSubTypes.Type(GroupTarget::class, name = "GroupTarget"),
    JsonSubTypes.Type(ClassroomTarget::class, name = "ClassroomTarget"),
    JsonSubTypes.Type(MultiTarget::class, name = "MultiTarget"),
)
interface NotificationTarget