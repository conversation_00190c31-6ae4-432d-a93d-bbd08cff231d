package portal.kafka.koin

import common.libs.thread.ThreadFactoryWithName
import kotlinx.coroutines.asCoroutineDispatcher
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Single
import portal.kafka.api.IKafkaConsumerManager
import portal.kafka.api.IKafkaEventReactor
import portal.kafka.api.IKafkaProducerManager
import portal.kafka.api.configs.KafkaConfig
import portal.kafka.consumer.KafkaConsumerManagerImpl
import portal.kafka.producer.KafkaProducerManagerImpl
import portal.kafka.reactor.EventReactorDelegate
import java.util.concurrent.Executors
import kotlin.coroutines.CoroutineContext

@Module
@ComponentScan("portal.kafka")
class KafkaModule {
    @Single
    @Named(PROCESS_CONTEXT)
    fun provideProcessContext(kafkaConf: KafkaConfig): CoroutineContext {
        return Executors.newFixedThreadPool(
            kafkaConf.processPoolSize,
            ThreadFactoryWithName("consumer.process-%d")
        ).asCoroutineDispatcher()
    }

    @Single
    fun provideConsumerManager(kafkaConf: KafkaConfig): IKafkaConsumerManager {
        return KafkaConsumerManagerImpl(kafkaConf)
    }

    @Single
    fun provideProducerManager(kafkaConf: KafkaConfig): IKafkaProducerManager {
        return KafkaProducerManagerImpl(kafkaConf)
    }

    @Single
    @Named(REACTOR_DELEGATE_LIST)
    fun provideReactorDelegates(
        kafkaConf: KafkaConfig,
        @Named(KAFKA_EVENT_REACTOR_LIST) originReactors: Set<IKafkaEventReactor>,
    ): Set<EventReactorDelegate> {
        val reactorConfigMap = kafkaConf.reactorConfigs.associateBy { it.reactorName }
        return originReactors.map {
            val config = reactorConfigMap[it.reactorName]
                ?: throw RuntimeException("Missing config for reactor ${it.reactorName}")
            EventReactorDelegate(it, config)
        }.toSet()
    }
}
