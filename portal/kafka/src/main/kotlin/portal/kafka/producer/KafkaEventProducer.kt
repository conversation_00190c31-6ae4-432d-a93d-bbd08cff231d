package portal.kafka.producer

import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Maybe
import io.reactivex.rxjava3.schedulers.Schedulers
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.RecordMetadata
import org.slf4j.LoggerFactory
import portal.kafka.api.KafkaEventData
import portal.kafka.api.configs.KafkaProducerConfig

class KafkaEventProducer constructor(
    val producerConf: KafkaProducerConfig,
) {
    private val logger = LoggerFactory.getLogger("producer.${producerConf.producerName}")
    private val producer = KafkaProducer<String, KafkaEventData>(producerConf.properties)

    fun sendEvent(topic: String, event: KafkaEventData): Maybe<RecordMetadata> {
        val eventType = event.javaClass.simpleName
        val topics = producerConf.eventToTopicMap[eventType]
            ?: return Maybe.empty()

        if (!topics.contains(topic)) return Maybe.empty()

        return send(topic, event)
    }

    private fun send(topic: String, event: KafkaEventData): Maybe<RecordMetadata> {
        return Maybe.fromFuture(producer.send(ProducerRecord(topic, event)))
            .subscribeOn(Schedulers.io())
            .observeOn(Schedulers.computation())
            .doOnError { logger.error("Produce kafka event {} failed: ", event, it) }
            .doOnSuccess { logger.trace("Produce kafka event {} success: {}", event, it) }
    }

    fun close(): Completable {
        return Completable.fromAction { producer.close() }
    }

}