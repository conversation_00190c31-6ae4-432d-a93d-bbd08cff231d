package portal.kafka.producer

import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.schedulers.Schedulers
import portal.kafka.api.IKafkaProducerManager
import portal.kafka.api.KafkaEventData
import portal.kafka.api.configs.KafkaConfig

class KafkaProducerManagerImpl constructor(
    private val kafkaConfig: KafkaConfig,
) : IKafkaProducerManager {

    private val producers = initProducers()

    private fun initProducers(): List<KafkaEventProducer> {
        return kafkaConfig.producerConfigs.map { KafkaEventProducer(it) }
    }

    override fun sendEvent(event: KafkaEventData): Completable {
        val eventType = event.javaClass.simpleName

        val completableList = producers.flatMap { producer ->
            val completableList = producer.producerConf.eventToTopicMap[eventType]
                ?.map { producer.sendEvent(it, event).ignoreElement() }
            completableList ?: emptyList()
        }

        if (completableList.isEmpty()) return Completable.error(Exception("Cannot produce event $eventType by all producers"))

        return Completable.merge(
            Flowable.fromIterable(completableList)
        ).observeOn(Schedulers.computation())
    }

    override fun close(): Completable {
        return Completable.merge(
            producers.map { it.close() }
        )
    }

}