package portal.kafka.reactor

import io.reactivex.rxjava3.core.Completable
import org.apache.kafka.clients.consumer.ConsumerRecord
import portal.kafka.api.IKafkaEventReactor
import portal.kafka.api.KafkaEventData
import portal.kafka.api.configs.EventReactorConfig

class EventReactorDelegate constructor(
    private val originReactor: IKafkaEventReactor,
    val reactorConf: EventReactorConfig
) : IKafkaEventReactor {
    override val reactorName: String = originReactor.reactorName

    override fun process(record: ConsumerRecord<String, KafkaEventData>): Completable {
        return originReactor.process(record)
    }
}
