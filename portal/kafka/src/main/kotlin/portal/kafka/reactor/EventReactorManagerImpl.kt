package portal.kafka.reactor

import io.reactivex.rxjava3.core.Completable
import io.reactivex.rxjava3.core.Flowable
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.kafka.api.IEventReactorManager
import portal.kafka.api.KafkaEventData
import portal.kafka.koin.REACTOR_DELEGATE_LIST

@Singleton
class EventReactorManagerImpl constructor(
    @Named(REACTOR_DELEGATE_LIST) private val reactors: Set<EventReactorDelegate>
) : IEventReactorManager {
    override fun process(record: ConsumerRecord<String, KafkaEventData>): Completable {
        val eventType = record.value().javaClass.simpleName

        val completableList = reactors.filter {
            it.reactorConf.eventToTopicMap[eventType]?.contains(record.topic()) ?: false
        }.map { it.process(record) }

        return Completable.merge(
            Flowable.fromIterable(completableList)
        )
    }

}
