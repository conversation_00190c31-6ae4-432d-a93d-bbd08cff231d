plugins {
    id "hnct.build"
    id "kotlin"
    id "application"
    id "com.google.devtools.ksp"
}

application {
    mainClass = 'portal.lsession.Launcher'
}

run {
    classpath += files("conf")
}

tasks.withType(AbstractCopyTask) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {

    sourceSets {
        implementation pojo
    }

    internal {
        implementation([id: ':portal.grpc', src: ['classroom']], "viclass:portal.grpc-classroom:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['lsession']], "viclass:portal.grpc-lsession:1.0.0", true)
        implementation([id: ':portal.datastructures', src: ['lsession']], "viclass:portal.datastructures-lsession:1.0.0", true)
        pojoImplementation([id: ':portal.datastructures', src: ['lsession']], "viclass:portal.datastructures-lsession:1.0.0", true)
        pojoImplementation([id: ':portal.grpc', src: ['lsession']], "viclass:portal.grpc-lsession:1.0.0", true)
        implementation([id: ':common.libs', src: ['codec']], "viclass:common.libs-codec:1.0.0", true)
        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
    }

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation "io.insert-koin:koin-ktor:$koinVs"
    implementation "io.insert-koin:koin-logger-slf4j:$koinVs"
    implementation "io.ktor:ktor-server-core-jvm"
    // implementation "org.koin:koin-java:2.0.1"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    pojoImplementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"
    implementation "io.grpc:grpc-netty:$grpcVs"

    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.13.1'
}

group = 'viclass'
version = '1.0.0'
