package portal.lsession.dbgateway

import com.mongodb.client.model.Filters
import com.mongodb.client.model.Filters.eq
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import portal.lsession.pojo.ClassroomSettings


/**
 *
 * <AUTHOR>
 */
class ClassroomSettingGateway constructor(
    private val collection: MongoCollection<ClassroomSettings>,
) : Logging {
    fun insertOne(ls: ClassroomSettings): Single<InsertOneResult> {
        return Flowable.defer {
            collection.insertOne(ls)
        }
        .doOnError {
            logger.error("insert classroom setting {} failed... ", ls, it)
        }.firstOrError()
    }

    fun getAllClassroomSettings(): Single<List<ClassroomSettings>> {
        return Flowable.defer { collection.find() }
                .doOnError { logger.error("failed to get all classroom settings: ", it) }
                .toList()
    }

    fun updateClassroomSettingById(id: ObjectId, settings: Bson): Single<UpdateResult> {
        val queries = eq("_id", id)
        return Flowable.defer { collection.updateOne(queries, settings) }
            .doOnError {
                logger.error("update classroom setting {} - {} failed... ", id, settings, it)
            }.firstOrError()
    }

    fun findClassroomSettingById(id: ObjectId): Single<ClassroomSettings> {
        return Flowable.defer { collection.find(eq("_id", id)) }
            .doOnError {
                logger.error("find classroom setting by id {} failed... ", id, it)
            }.firstOrError()
    }

    fun findClassroomSettingByIds(ids: List<ObjectId>): Flowable<ClassroomSettings> {
        return Flowable.defer { collection.find(Filters.`in`("_id", ids)) }
            .doOnError {
                logger.error("find classroom setting by id {} failed... ", ids, it)
            }
    }
}
