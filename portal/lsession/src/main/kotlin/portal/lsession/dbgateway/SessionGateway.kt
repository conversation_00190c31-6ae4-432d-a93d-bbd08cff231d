package portal.lsession.dbgateway

import com.mongodb.client.model.*
import com.mongodb.client.model.Filters.eq
import com.mongodb.client.model.Filters.`in`
import com.mongodb.client.result.DeleteResult
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import portal.datastructures.lsession.LSessionStatus
import portal.lsession.pojo.LSessionDetails
import portal.lsession.pojo.LSessionState


/**
 *
 * <AUTHOR>
 */
class SessionGateway constructor(
    private val collection: MongoCollection<LSessionDetails>,
) : Logging {
    fun insertOne(ls: LSessionDetails): Single<InsertOneResult> {
        return Flowable.defer { collection.insertOne(ls) }
            .doOnError {
                logger.error("insert session {} failed... ", ls, it)
            }.firstOrError()
    }

    fun updateSessionById(id: ObjectId, details: Bson): Single<UpdateResult> {
        val filter = eq("_id", id)
        return Flowable.defer { collection.updateOne(filter, details) }
            .doOnError {
                logger.error("update session {} failed... ", id, it)
            }.firstOrError()
    }

    fun findAndUpdateByLsId(id: ObjectId, details: Bson): Single<LSessionDetails> {
        val filter = eq("_id", id)
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return Flowable.defer { collection.findOneAndUpdate(filter, details, ops) }
            .doOnError {
                logger.error("update session {} failed... ", id, it)
            }.firstOrError()
    }

    fun getAllLSessionDetails(): Single<List<LSessionDetails>> {
        return Flowable.defer { collection.find() }
                .doOnError { logger.error("failed to get all lsession details: ", it) }
                .toList()
    }

    fun findSessionById(id: ObjectId): Single<LSessionDetails> {
        return Flowable.defer { collection.find(eq("_id", id)) }.firstOrError()
            .doOnError {
                logger.error("find session by id {} failed... ", id, it)
            }
    }

    fun updateImgUrl(id : ObjectId, imgUrl : String) : Single<UpdateResult> {
        val filter = eq("_id", id)

        val update = Updates.set(LSessionDetails::imgUrl.name, imgUrl)
        return Flowable.defer { collection.updateOne(filter, update) }
                .doOnError {
                    logger.error("update session image url for id {} has failed... ", id, it)
                }.firstOrError()
    }

    fun updateRegistered(lsId: ObjectId, delta: Int): Single<UpdateResult> {
        val filter = eq("_id", lsId)
        val update = Updates.inc("${LSessionDetails::state.name}.${LSessionState::registered.name}", delta)

        return Flowable.defer { collection.updateOne(filter, update) }
            .doOnError {
                logger.error("update registered for {} failed... ", lsId, it)
            }.firstOrError()
    }

    fun updateClassroomState(lsId: ObjectId, state: LSessionState): Single<UpdateResult> {
        val filter = eq("_id", lsId)
        val updates = ArrayList<Bson>()
        updates.add(Updates.set("${LSessionDetails::state.name}.${LSessionState::status.name}", state.status))
        state.startedAt?.let { updates.add(Updates.set("${LSessionDetails::state.name}.${LSessionState::startedAt.name}", state.status)) }
        state.endedAt?.let { updates.add(Updates.set("${LSessionDetails::state.name}.${LSessionState::endedAt.name}", state.status)) }
        return Flowable.defer { collection.updateOne(filter, updates) }
            .doOnError {
                logger.error("update registered for {} failed... ", lsId, it)
            }.firstOrError()
    }

    fun findSessionByIds(ids: List<ObjectId>, lsStatus: List<LSessionStatus> = emptyList()): Flowable<LSessionDetails> {
        val filters = Filters.and(
            listOfNotNull(
                `in`("_id", ids),
                if (lsStatus.isNotEmpty()) Filters.`in`("state.status", lsStatus) else null
            )
        )
        return Flowable.defer { collection.find(filters) }
            .doOnError {
                logger.error("find session by ids {} failed... ", ids, it)
            }
    }

    fun findSessionByOwnerId(creatorId: ObjectId): Flowable<LSessionDetails> {
        return Flowable.defer { collection.find(eq(LSessionDetails::creatorId.name, creatorId)) }
            .doOnError {
                logger.error("find session by creator id {} failed... ", creatorId, it)
            }
    }

    fun deleteSessionById(id: ObjectId): Single<DeleteResult> {
        return Flowable.defer { collection.deleteOne(eq("_id", id)) }
            .doOnError {
                logger.error("delete session by id {} failed... ", id, it)
            }.firstOrError()
    }

    fun getLatestLSession(): Single<LSessionDetails> {
        return Flowable.defer {
            collection.find().sort(Sorts.descending("_id")).limit(1)
        }.doOnError {
            logger.error("Failed to load latest lsession ", it)
        }.firstOrError()
    }
}
