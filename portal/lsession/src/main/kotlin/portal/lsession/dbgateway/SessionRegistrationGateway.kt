package portal.lsession.dbgateway

import com.mongodb.client.model.Filters
import com.mongodb.client.model.Filters.*
import com.mongodb.client.model.FindOneAndUpdateOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.client.model.Updates
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Maybe
import io.reactivex.rxjava3.core.Single
import kotlinx.coroutines.reactive.awaitFirstOrNull
import org.bson.Document
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import portal.datastructures.lsession.LSRegStatus
import portal.datastructures.lsession.RaiseHandStatus
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.ClassroomUserState
import portal.lsession.pojo.LSessionRegistration
import portal.lsession.pojo.RequestPinTabState
import portal.lsession.pojo.RequestPinTabStatus

/**
 *
 * <AUTHOR>
 */
class SessionRegistrationGateway constructor(
    private val collection: MongoCollection<LSessionRegistration>,
) : Logging {
    fun insertOne(registration: LSessionRegistration): Single<InsertOneResult> {
        return Flowable.defer { collection.insertOne(registration) }
            .doOnError {
                logger.error("insert session registration {} failed... ", registration, it)
            }.firstOrError()
    }

    fun updateStatusById(id: ObjectId, status: LSRegStatus, regTime: Long? = null): Single<LSessionRegistration> {
        val queries = eq("_id", id)
        val update = ArrayList<Bson>()
        update.add(Updates.set(LSessionRegistration::regStatus.name, status))
        regTime?.let { update.add(Updates.set(LSessionRegistration::regTime.name, it)) }

        val option = FindOneAndUpdateOptions().returnDocument(ReturnDocument.BEFORE)

        return Flowable.defer { collection.findOneAndUpdate(queries, update, option)}
            .doOnError {
                logger.error("insert session registration {} failed... ", id, it)
            }.firstOrError()
    }

    fun updateRegistrationById(id: ObjectId, details: Bson): Single<UpdateResult> {
        val filter = eq("_id", id)
        return Flowable.defer { collection.updateOne(filter, details) }
            .doOnError {
                logger.error("update registration {} failed... ", id, it)
            }.firstOrError()
    }

    fun findAndUpdateById(id: ObjectId, details: Bson): Single<LSessionRegistration> {
        val filter = eq("_id", id)
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return Flowable.defer { collection.findOneAndUpdate(filter, details, ops) }
            .doOnError {
                logger.error("update registration {} failed... ", id, it)
            }.firstOrError()
    }

    fun findRegistrationById(id: ObjectId): Single<LSessionRegistration> {
        return Flowable.defer { collection.find(eq("_id", id)) }
            .doOnError {
                logger.error("find session registrations by id {} failed... ", id, it)
            }.firstOrError()
    }

    fun findRegistrationsByLsId(lsId: ObjectId): Flowable<LSessionRegistration> {
        return Flowable.defer { collection.find(eq(LSessionRegistration::lsId.name, lsId)) }
            .doOnError {
                logger.error("find session registrations by lsession id {} failed... ", lsId, it)
            }
    }

    fun findRegistrationsByLsIdWithRegStatus(lsId: ObjectId, regStatus: List<LSRegStatus>): Flowable<LSessionRegistration> {
        val queries = and(
            eq(LSessionRegistration::lsId.name, lsId),
            `in`(LSessionRegistration::regStatus.name, regStatus)
        )
        return Flowable.defer { collection.find(queries) }
            .doOnError {
                logger.error("find session registrations by lsession id {} failed... ", lsId, it)
            }
    }

    fun findRegistrationsByLsOwnerId(lsOwnerId: ObjectId): Flowable<LSessionRegistration> {
        return Flowable.defer { collection.find(eq(LSessionRegistration::lsOwnerUserId.name, lsOwnerId)) }
            .doOnError {
                logger.error("find session registrations by lsession owner id {} failed... ", lsOwnerId, it)
            }
    }

    fun findRegistrationsByUserId(userId: ObjectId, regStatus: List<LSRegStatus> = emptyList()): Flowable<LSessionRegistration> {
        val filters = and(
            listOfNotNull(
                eq(LSessionRegistration::userId.name, userId),
                if (regStatus.isNotEmpty()) { `in`(LSessionRegistration::regStatus.name, regStatus) } else null
            )
        )
        return Flowable.defer { collection.find(filters) }
            .doOnError {
                logger.error("find session registrations by user id {} failed... ", userId, it)
            }
    }

    fun findRegistrationsByUserIdAndLsId(lsId: ObjectId, userId: ObjectId): Single<LSessionRegistration> {
        val queries = and(
            eq(LSessionRegistration::userId.name, userId),
            eq(LSessionRegistration::lsId.name, lsId)
        )
        return Flowable.defer { collection.find(queries) }
            .doOnError {
                logger.error("find session registrations by user id {} failed... ", userId, it)
            }.firstOrError()
    }

    fun cancelAllRaisingHand(lsId: String, status: RaiseHandStatus): Single<MutableList<UpdateResult>> {
        val queries = and(
            eq(LSessionRegistration::lsId.name, ObjectId(lsId)),
            eq("${LSessionRegistration::userState.name}.${ClassroomUserState::raiseHandStatus.name}", RaiseHandStatus.RAISE_HAND)
        )
        val update = ArrayList<Bson>()
        update.add(Updates.set("${LSessionRegistration::userState.name}.${ClassroomUserState::raiseHandStatus.name}", status))


        return Flowable.defer { collection.updateMany(queries, update)}
            .doOnNext {
                logger.info("[{}] canceled raising hand {}", lsId, it)
            }
            .doOnError {
                logger.error("[{}] cancel raising hand failed... ", lsId, it)
            }.toList()
    }

    fun updateRaiseHandStatus(regId: String, status: RaiseHandStatus): Single<LSessionRegistration> {
        val queries = and(
            eq("_id", ObjectId(regId)),
        )
        val update = ArrayList<Bson>()
        update.add(Updates.set("${LSessionRegistration::userState.name}.${ClassroomUserState::raiseHandStatus.name}", status))


        return Flowable.defer { collection.findOneAndUpdate(queries, update)}
            .doOnNext {
                logger.info("[{}] updateRaiseHandStatus {}", regId, it)
            }
            .doOnError {
                logger.error("[{}] updateRaiseHandStatus failed... ", regId, it)
            }.firstOrError()
    }

    fun updateShareScreenStatus(regId: String, status: ShareScreenStatus): Single<LSessionRegistration> {
        val queries = and(
            eq("_id", ObjectId(regId)),
        )
        val update = ArrayList<Bson>()
        update.add(Updates.set("${LSessionRegistration::userState.name}.${ClassroomUserState::shareScreenStatus.name}", status))


        return Flowable.defer { collection.findOneAndUpdate(queries, update)}
            .doOnNext {
                logger.info("[{}] updateShareScreenStatus {}", regId, it)
            }
            .doOnError {
                logger.error("[{}] updateShareScreenStatus failed... ", regId, it)
            }.firstOrError()
    }


    fun updateClrRegistrationUserStatus(regId: String, status: UserAvailableStatus): Single<LSessionRegistration> {
        val queries = and(
            eq("_id", ObjectId(regId)),
        )
        val update = ArrayList<Bson>()
        update.add(Updates.set("${LSessionRegistration::userState.name}.${ClassroomUserState::availableStatus.name}", status))


        return Flowable.defer { collection.findOneAndUpdate(queries, update)}
            .doOnNext {
                logger.info("[{}] updateClrRegistrationUserStatus {}", regId, it)
            }
            .doOnError {
                logger.error("[{}] updateClrRegistrationUserStatus failed... ", regId, it)
            }.firstOrError()
    }

    /**
     * Updates the status of an existing pin tab request or adds a new one if it doesn't exist,
     * using a single findOneAndUpdate operation with an update pipeline.
     * - If a request for the `tabId` exists, its status (and timestamp) is updated to `newStatus`.
     * - If a request for the `tabId` does not exist, it's added with `tabId`, `newStatus`, and current timestamp.
     *
     * @param regId The ID of the LSessionRegistration.
     * @param tabId The ID of the tab to request pinning.
     * @param newStatus The new status to set for the pin tab request.
     * @param tabName The new atb name to set for the pin tab request.
     * @return The updated LSessionRegistration if a change was made (added or status updated),
     *         or null if the regId was not found.
     */
    suspend fun updatePinTabStatus(regId: String, tabId: String, newStatus: RequestPinTabStatus? = null, tabName: String? = null): LSessionRegistration? {
        val objectIdRegId = ObjectId(regId)
        val userStatePath = LSessionRegistration::userState.name
        val requestPinTabStateArrayPath = "$userStatePath.${ClassroomUserState::requestPinTabState.name}"

        // Tên các trường trong RequestPinTabState POJO
        val fieldTabId = RequestPinTabState::tabId.name
        val fieldTabName = RequestPinTabState::tabName.name
        val fieldStatus = RequestPinTabState::status.name

        // BSON document cho phần tử mới hoặc phần tử được cập nhật
        val newOrUpdatedPinTabFields = Document()
        newStatus?.let { newOrUpdatedPinTabFields.append(fieldStatus, it.name) }
        tabName?.let { newOrUpdatedPinTabFields.append(fieldTabName, it) }

        // BSON document đầy đủ cho một phần tử mới nếu nó được thêm vào
        val newPinTabElementBson = Document(fieldTabId, tabId).append(fieldStatus, RequestPinTabStatus.PENDING).append(fieldTabName, tabName)

        val updatePipeline = listOf(
            Updates.set(
                requestPinTabStateArrayPath,
                Document("\$let", // Sử dụng $let để định nghĩa biến cho dễ đọc và xử lý giá trị mặc định
                    Document("vars", Document("currentArray", Document("\$ifNull", listOf("\$$requestPinTabStateArrayPath", emptyList<Document>()))))
                        .append("in",
                            Document("\$cond",
                                Document("if", // Điều kiện: Kiểm tra xem tabId đã tồn tại trong currentArray chưa
                                    Document("\$anyElementTrue",
                                        listOf(
                                            Document("\$map",
                                                Document("input", "$\$currentArray")
                                                    .append("as", "item")
                                                    .append("in", Document("\$eq", listOf("$\$item.$fieldTabId", tabId)))
                                            )
                                        )
                                    )
                                )
                                    .append("then", // Nếu tabId tồn tại: ánh xạ qua mảng và cập nhật phần tử khớp
                                        Document("\$map",
                                            Document("input", "$\$currentArray")
                                                .append("as", "item")
                                                .append("in",
                                                    Document("\$cond",
                                                        Document("if", Document("\$eq", listOf("$\$item.$fieldTabId", tabId)))
                                                            .append("then", Document("\$mergeObjects", listOf("$\$item", newOrUpdatedPinTabFields))) // Gộp để cập nhật status và timestamp
                                                            .append("else", "$\$item") // Giữ nguyên các phần tử khác
                                                    )
                                                )
                                        )
                                    )
                                    .append("else", // Nếu tabId không tồn tại: nối phần tử mới vào mảng
                                        Document("\$concatArrays", listOf("$\$currentArray", listOf(newPinTabElementBson)))
                                    )
                            )
                        )
                )
            )
        )

        val filter = Filters.eq("_id", objectIdRegId)
        val options = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

        return collection.findOneAndUpdate(filter, updatePipeline, options).awaitFirstOrNull()
    }

    fun updateAllClassroomUserState(lsId: String, status: RaiseHandStatus): Maybe<UpdateResult> {
        val filter = eq(LSessionRegistration::lsId.name, ObjectId(lsId))
        val updates = Updates.set("${LSessionRegistration::userState.name}.${ClassroomUserState::raiseHandStatus.name}", status)
        return Flowable.defer { collection.updateMany(filter, updates) }
            .firstElement()
            .doOnError {
                logger.error("failed to update all classroom user states in room {}: ", lsId, it)
            }
    }
}
