package portal.lsession.koin

import com.mongodb.client.model.Indexes
import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import common.libs.codec.EnumCodecProvider
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.lsession.configuration.Configuration
import portal.lsession.configuration.DatabaseConfig
import portal.lsession.pojo.ClassroomSettings
import portal.lsession.pojo.LSessionDetails
import portal.lsession.pojo.LSessionRegistration
import portal.lsession.pojo.activity.ClassroomActivity

/**
 *
 * <AUTHOR>
 */
@Module
class DatabaseModule(): Logging {
    @Singleton
    fun provideCodecRegistry(config: Configuration): CodecRegistry {
        return CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                PojoCodecProvider.builder().automatic(true).register(*config.registerPojoPackages.toTypedArray()).build(),
                EnumCodecProvider()
            )
        )
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig, codecRegistry: CodecRegistry): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(codecRegistry)
    }

    companion object {
        const val SESSION_DETAILS_COLLECTION = "SessionCollection"
        const val SESSION_REGISTRATION_COLLECTION = "SessionRegistrationCollection"
        const val CLASSROOM_SETTING_COLLECTION = "ClassroomSettingCollection"
        const val CLASSROOM_ACTIVITIES_COLLECTION = "ClassroomQuestionCollection"
    }

    @Singleton
    @Named(SESSION_DETAILS_COLLECTION)
    fun provideSessionCollection(db: MongoDatabase): MongoCollection<LSessionDetails> {
        return db.getCollection("lsession-details", LSessionDetails::class.java)
    }

    @Singleton
    @Named(SESSION_REGISTRATION_COLLECTION)
    fun provideSessionRegistrationCollection(db: MongoDatabase): MongoCollection<LSessionRegistration> {
        val collection = db.getCollection("lsession-registrations", LSessionRegistration::class.java)
        val resultCreateIndex  = Flowable.defer { collection.createIndex(Indexes.ascending("lsId", "regId"))}.blockingFirst()
        logger.info("Index created {}", resultCreateIndex)
        return collection
    }

    @Singleton
    @Named(CLASSROOM_SETTING_COLLECTION)
    fun provideClassroomSettingCollection(db: MongoDatabase): MongoCollection<ClassroomSettings> {
        return db.getCollection("classroom-settings", ClassroomSettings::class.java)
    }

    @Singleton
    @Named(CLASSROOM_ACTIVITIES_COLLECTION)
    fun provideClassroomQuestionCollection(db: MongoDatabase): MongoCollection<ClassroomActivity> {
        return db.getCollection("classroom-activities", ClassroomActivity::class.java)
    }
}
