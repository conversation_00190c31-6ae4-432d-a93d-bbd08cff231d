package portal.lsession.pojo

import com.mongodb.client.model.Updates
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import portal.datastructures.lsession.*
import proto.portal.lsession.LsessionMessage
import proto.portal.lsession.LsessionMessage.RequestPinTabStateProto

fun LsessionMessage.LSessionStateProto.Builder.fromPojo(pojo: LSessionState): LsessionMessage.LSessionStateProto {
    this.status = pojo.status.name
    this.registered = pojo.registered
    pojo.startedAt?.let { this.startedAt = it }
    pojo.endedAt?.let { this.endedAt = it }
    return this.build()
}

fun RequestPinTabStateProto.Builder.fromPojo(pojo: RequestPinTabState): RequestPinTabStateProto {
    this.status = pojo.status.name
    this.tabId = pojo.tabId
    this.tabName = pojo.tabName
    return this.build()
}

fun RequestPinTabStateProto.toPojo(): RequestPinTabState {
    return RequestPinTabState(
        tabId = this.tabId,
        status = RequestPinTabStatus.valueOf(this.status),
        tabName = this.tabName,
    )
}

fun LsessionMessage.ClassroomUserStateProto.Builder.fromPojo(pojo: ClassroomUserState): LsessionMessage.ClassroomUserStateProto {
    this.status = pojo.raiseHandStatus.name
    this.userStatus = pojo.availableStatus.name
    pojo.shareScreenStatus?.let {
        this.shareScreenStatus = it.name
    }
    this.addAllRequestPinTabState(pojo.requestPinTabState.map { RequestPinTabStateProto.newBuilder().fromPojo(it) })
    pojo.joinedTime?.let { this.joinedTime = it }
    return this.build()
}

// create LSessionDetailsProto message from a pojo
fun LsessionMessage.LSessionDetailsProto.Builder.fromPojo(pojo: LSessionDetails): LsessionMessage.LSessionDetailsProto {
    this.id = pojo.id
    this.creatorId = pojo.creatorId
    this.title = pojo.title
    this.description = pojo.description
    this.imgUrl = pojo.imgUrl
    this.grade = pojo.grade
    this.subject = pojo.subject
    this.startTime = pojo.startTime
    this.startDate = pojo.startDate
    this.expectedDuration = pojo.expectedDuration
    pojo.state?.let { this.state = LsessionMessage.LSessionStateProto.newBuilder().fromPojo(it) }
    return this.build()
}

fun LsessionMessage.ClassroomSettingsProto.Builder.fromPojo(pojo: ClassroomSettings): LsessionMessage.ClassroomSettingsProto {
    return this.setCamera(pojo.camera)
        .setAudio(pojo.audio)
        .setChatBox(pojo.chatBox)
        .setAutoRecord(pojo.autoRecord)
        .setBoardType(pojo.boardType.name)
        .setMaxRegistration(pojo.maxRegistration)
        .setRejectLateRegistrationAfterMinute(pojo.rejectLateRegistrationAfterMinute)
        .setChattingFiler(pojo.chattingFilter)
        .build()
}

fun LsessionMessage.LSessionDetailsProto.toPojo(): LSessionDetails {
    return LSessionDetails(
        this.id, this.creatorId, this.title, this.description, this.imgUrl,
        this.grade, this.subject, this.startTime, this.startDate, this.expectedDuration,
        if (this.hasState()) this.state.toPojo() else null
    )
}

fun LsessionMessage.LSessionStateProto.toPojo(): LSessionState {
    val proto = this
    return LSessionState(LSessionStatus.valueOf(this.status), this.registered).apply {
        if (proto.hasStartedAt()) this.startedAt = proto.startedAt
        if (proto.hasEndedAt()) this.endedAt = proto.endedAt
    }
}

fun LsessionMessage.ClassroomUserStateProto.toPojo(): ClassroomUserState {
    val proto = this
    val shareScreenStatus = try {
        ShareScreenStatus.valueOf(this.shareScreenStatus)
    } catch (e: IllegalArgumentException) {
        null // or ShareScreenStatus.INACTIVE if you want a default
    }
    return ClassroomUserState(
        shareScreenStatus = shareScreenStatus,
        raiseHandStatus = RaiseHandStatus.valueOf(this.status),
        availableStatus = UserAvailableStatus.valueOf(this.userStatus),
        requestPinTabState = proto.requestPinTabStateList.map { it.toPojo() }
    ).apply {
        if (proto.hasJoinedTime()) this.joinedTime = proto.joinedTime
    }
}

fun ClassroomUserState.toProto(): LsessionMessage.ClassroomUserStateProto {
    val pojo = this
    return LsessionMessage.ClassroomUserStateProto.newBuilder()
        .setStatus(this.raiseHandStatus.name)
        .setUserStatus(this.availableStatus.name)
        .apply {
            pojo.joinedTime?.let { this.joinedTime = it }
        }.build()
}

fun LSessionState.toProto(): LsessionMessage.LSessionStateProto {
    val pojo = this
    return LsessionMessage.LSessionStateProto.newBuilder()
        .setRegistered(this.registered)
        .setStatus(this.status.name)
        .apply {
            pojo.startedAt?.let { this.startedAt = it }
            pojo.endedAt?.let { this.endedAt = it }
        }.build()
}


fun LsessionMessage.LSessionRegistrationProto.toPojo(): LSessionRegistration {
    val proto = this
    return LSessionRegistration(
        this.id,
        this.lsId,
        this.lsOwner,
        this.userId,
        LSRegStatus.valueOf(this.regStatus),
        this.regTime,
        proto.state.toPojo()
    )
}

fun LsessionMessage.ClassroomSettingsProto.toPojo(): ClassroomSettings {
    return ClassroomSettings(
        "",
        this.camera,
        this.audio,
        this.chatBox,
        this.autoRecord,
        BoardType.valueOf(this.boardType),
        this.maxRegistration,
        this.chattingFiler,
        this.rejectLateRegistrationAfterMinute
    )
}

fun LsessionMessage.ClassroomSettingsProto.toBsonUpdater(): Bson {
    val settings = ArrayList<Bson>()
    settings.add(Updates.set(ClassroomSettings::camera.name, this.camera))
    settings.add(Updates.set(ClassroomSettings::audio.name, this.audio))
    settings.add(Updates.set(ClassroomSettings::chatBox.name, this.chatBox))
    settings.add(Updates.set(ClassroomSettings::autoRecord.name, this.audio))
    settings.add(Updates.set(ClassroomSettings::boardType.name, this.boardType))
    settings.add(Updates.set(ClassroomSettings::chattingFilter.name, this.chattingFiler))
    settings.add(Updates.set(ClassroomSettings::maxRegistration.name, this.maxRegistration))
    settings.add(
        Updates.set(
            ClassroomSettings::rejectLateRegistrationAfterMinute.name,
            this.rejectLateRegistrationAfterMinute
        )
    )
    return Updates.combine(settings)
}

fun LsessionMessage.LSessionDetailsProto.toBsonUpdater(): Bson {
    val details = ArrayList<Bson>()
    details.add(Updates.set(LSessionDetails::creatorId.name, ObjectId(this.creatorId)))
    details.add(Updates.set(LSessionDetails::title.name, this.title))
    details.add(Updates.set(LSessionDetails::description.name, this.description))
    details.add(Updates.set(LSessionDetails::imgUrl.name, this.imgUrl))
    details.add(Updates.set(LSessionDetails::grade.name, this.grade))
    details.add(Updates.set(LSessionDetails::subject.name, this.subject))
    details.add(Updates.set(LSessionDetails::startTime.name, this.startTime))
    details.add(Updates.set(LSessionDetails::startDate.name, this.startDate))
    details.add(Updates.set(LSessionDetails::expectedDuration.name, this.expectedDuration))
    return Updates.combine(details)
}

fun LsessionMessage.LSessionStateProto.toBsonUpdater(): Bson {
    val details = ArrayList<Bson>()
    val state = LSessionDetails::state.name
    if (this.hasStatus()) {
        details.add(Updates.set("$state.${LSessionState::status.name}", LSessionStatus.valueOf(this.status)))
    }
    if (this.hasRegistered()) {
        details.add(Updates.set("$state.${LSessionState::registered.name}", this.registered))
    }
    if (this.hasStartedAt()) {
        details.add(Updates.set("$state.${LSessionState::startedAt.name}", this.startedAt))
    }
    if (this.hasEndedAt()) {
        details.add(Updates.set("$state.${LSessionState::endedAt.name}", this.endedAt))
    }
    return Updates.combine(details)
}

fun LsessionMessage.LSessionRegistrationProto.Builder.fromPojo(mb: LSessionRegistration): LsessionMessage.LSessionRegistrationProto {

    this.setLsId(mb.lsId)
        .setId(mb.id)
        .setLsOwner(mb.lsOwnerUserId)
        .setUserId(mb.userId)
        .setRegStatus(mb.regStatus.name)
        .setRegTime(mb.regTime)
        .setState(LsessionMessage.ClassroomUserStateProto.newBuilder().fromPojo(mb.userState))
    return build()
}
