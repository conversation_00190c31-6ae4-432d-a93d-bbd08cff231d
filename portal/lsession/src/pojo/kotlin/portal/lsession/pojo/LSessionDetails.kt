package portal.lsession.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId


/**
 *
 * <AUTHOR>
 */
data class LSessionDetails @BsonCreator constructor(

    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    var id: String = ObjectId().toHexString(), // the learning session id

    @BsonProperty("creatorId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    var creatorId: String,

    @BsonProperty("title") var title: String,
    @BsonProperty("description") var description: String,
    @BsonProperty("imgUrl") var imgUrl: String,
    @BsonProperty("grade") var grade: String,
    @BsonProperty("subject") var subject: String,
    @BsonProperty("startTime") var startTime: Long,
    @BsonProperty("startDate") var startDate: Long,
    @BsonProperty("expectedDuration") var expectedDuration: Int,
    @BsonProperty("state") var state: LSessionState? = null,
)
