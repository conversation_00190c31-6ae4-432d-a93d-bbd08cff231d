package portal.lsession.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId
import portal.datastructures.lsession.LSRegStatus

/**
 * make sure create unique index for lsId and userId
 */
data class LSessionRegistration @BsonCreator constructor(

    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    val id: String = ObjectId().toHexString(),     // id of the document representing a registration

    @BsonProperty("lsId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsId: String,                               // the learning session id

    @BsonProperty("lsOwnerUserId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsOwnerUserId: String,                      // id of the user who created the learning session

    @BsonProperty("userId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val userId: String,                           // id of the user who registers for the learning session

    @BsonProperty("regStatus")
    val regStatus: LSRegStatus,

    @BsonProperty("regTime")
    val regTime: Long,

    // state of the user in the class
    @BsonProperty("userState")
    val userState : ClassroomUserState,
)
