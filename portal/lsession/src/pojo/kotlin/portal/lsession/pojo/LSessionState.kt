package portal.lsession.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty
import portal.datastructures.lsession.LSessionStatus

data class LSessionState @BsonCreator constructor(
    @BsonProperty("status") var status : LSessionStatus,
    @BsonProperty("registered") var registered : Int = 0,
    @BsonProperty("startedAt") var startedAt : Long? = null,
    @BsonProperty("endedAt") var endedAt: Long? = null,
)
