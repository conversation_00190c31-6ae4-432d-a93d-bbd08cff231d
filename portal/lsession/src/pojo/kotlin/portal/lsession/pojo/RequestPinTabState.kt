package portal.lsession.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty

enum class RequestPinTabStatus { PENDING, REJECTED, CANCELLED, APPROVED, UNPINNED }

data class RequestPinTabState @BsonCreator constructor(
    @BsonProperty("tabId") val tabId: String,
    @BsonProperty("status") var status: RequestPinTabStatus,
    @BsonProperty("tabName") var tabName: String,
)