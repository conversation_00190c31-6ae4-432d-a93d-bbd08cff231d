package portal.lsession.pojo.activity

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

@BsonDiscriminator(value = "QuickQuestionAD", key = "activityType")
data class QuickQuestionAD @BsonCreator constructor(
    @BsonProperty("question")
    val question: String,

    @BsonProperty("responses")
    val responses: Map<String, YesNoResponse> = emptyMap(), // map by userId
): ActivityData