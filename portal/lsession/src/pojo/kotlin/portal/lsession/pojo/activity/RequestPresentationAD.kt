package portal.lsession.pojo.activity

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation

@BsonDiscriminator(value = "RequestPresentationAD", key = "activityType")
data class RequestPresentationAD @BsonCreator constructor(
    @BsonProperty("requestedTo")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val requestedTo: String, // this is userId

    @BsonProperty("response")
    val response: YesNoResponse? = null
): ActivityData