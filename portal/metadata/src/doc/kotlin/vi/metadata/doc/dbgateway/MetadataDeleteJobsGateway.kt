package vi.metadata.doc.dbgateway

import com.mongodb.client.model.*
import com.mongodb.kotlin.client.coroutine.MongoCollection
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext
import org.bson.types.ObjectId
import vi.metadata.doc.pojo.MetadataDeleteJobs
import java.util.*


/**
 *
 * <AUTHOR>
 */
class MetadataDeleteJobsGateway constructor(
    private val col: MongoCollection<MetadataDeleteJobs>,
) : Logging {

    suspend fun insert(metadataDoc: MetadataDeleteJobs): String? {
        return withContext(Dispatchers.IO) { col.insertOne(metadataDoc) }.insertedId?.asObjectId()?.value?.toString()
    }

    suspend fun deleteById(id: String) {
        val filter = Filters.eq("_id", ObjectId(id))
        return withContext(Dispatchers.IO) { col.deleteOne(filter) }
    }

    suspend fun loadAll(): List<MetadataDeleteJobs> {
        return withContext(Dispatchers.IO) {
            val results = col.find()
            results
        }.toList()
    }
}
