package vi.metadata.doc.dbgateway

import com.mongodb.client.model.*
import com.mongodb.kotlin.client.coroutine.MongoCollection
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.count
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withContext
import org.bson.*
import org.bson.conversions.Bson
import org.bson.types.Decimal128
import org.bson.types.ObjectId
import vi.metadata.doc.pojo.MetadataDocDocument
import vi.metadata.doc.pojo.MetadataField


/**
 *
 * <AUTHOR>
 */
class MetadataDocGateway constructor(
    private val col: MongoCollection<MetadataDocDocument>,
) : Logging {

    suspend fun insert(metadataDoc: MetadataDocDocument): String? {
        return withContext(Dispatchers.IO) { col.insertOne(metadataDoc) }.insertedId?.toString()
    }

    suspend fun load(
        dataFields: List<MetadataField>,
        docId: String?,
        metaType: String?,
        edType: String?,
        limit: Int? = null,
        offset: Int? = null,
        textSearch: String? = null,
    ): List<MetadataDocDocument> {
        val filter = Filters.and(
            listOfNotNull(
                edType?.let { Filters.eq("editorType", it) },
                metaType?.let { Filters.eq("metadataType", it) },
                docId?.let { Filters.eq("docId", ObjectId(it)) },
                *dataFields.map { (f, t, v) -> parseFilter(f, t, v) }.toTypedArray(),
                textSearch?.let {
                    if (it.isNotBlank()) Filters.text(it, TextSearchOptions().caseSensitive(false)) else null
                }
            )
        )

        return withContext(Dispatchers.IO) {
            val results = col.find(filter)
                .sort(Sorts.descending("createdAt"))
            limit?.let { results.limit(limit) }
            offset?.let { results.skip(offset) }
            results
        }.toList()
    }

    suspend fun count(
        dataFields: List<MetadataField>,
        docId: String?,
        metaType: String?,
        edType: String?,
        textSearch: String? = null,
    ): Int {
        val filter = Filters.and(
            listOfNotNull(
                edType?.let { Filters.eq("editorType", it) },
                metaType?.let { Filters.eq("metadataType", it) },
                docId?.let { Filters.eq("docId", ObjectId(it)) },
                *dataFields.map { (f, t, v) -> parseFilter(f, t, v) }.toTypedArray(),
                textSearch?.let {
                    if (it.isNotBlank()) Filters.text(it, TextSearchOptions().caseSensitive(false)) else null
                }
            )
        )

        return withContext(Dispatchers.IO) { col.find(filter).count() }
    }


    suspend fun duplicate(
        dataReplaces: List<MetadataField>,
        docId: String,
        newDocId: String,
        metadataType: String,
    ): MetadataDocDocument? {
        val filter = Filters.and(
            Filters.eq("docId", ObjectId(docId)),
            Filters.eq("metadataType", metadataType)
        )

        val origin = withContext(Dispatchers.IO) { col.find(filter) }.firstOrNull() ?: return null
        origin.id = ObjectId().toHexString()
        origin.docId = newDocId
        dataReplaces.map { (f, t, v) ->
            origin.metadataDetails[f] = parseDataToBsonValue(v, t)
        }
        val newId = insert(origin)

        return origin
    }

    suspend fun patchDocData(
        dataReplaces: List<MetadataField>,
        docId: String,
        editorType: String,
        metadataType: String,
    ): MetadataDocDocument? {
        val filter = Filters.and(
            Filters.eq("docId", ObjectId(docId)),
            Filters.eq("metadataType", metadataType),
            Filters.eq("editorType", editorType),
        )

        val updates = dataReplaces.map { (field, dataType, value) ->
            Updates.set("metadataDetails.$field", parseDataToBsonValue(value, dataType))
        }.toTypedArray()
        val update = Updates.combine(*updates)

        val options = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return withContext(Dispatchers.IO) { col.findOneAndUpdate(filter, update, options) }
    }

    suspend fun updateDocData(docId: String, data: BsonDocument): MetadataDocDocument? {
        val filter = Filters.eq("docId", ObjectId(docId))
        val update = Updates.set("metadataDetails", data)
        val options = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return withContext(Dispatchers.IO) { col.findOneAndUpdate(filter, update, options) }
    }

    suspend fun updateDocDataById(id: String, data: BsonDocument): MetadataDocDocument? {
        val filter = Filters.eq("_id", ObjectId(id))
        val update = Updates.set("metadataDetails", data)
        val options = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        return withContext(Dispatchers.IO) { col.findOneAndUpdate(filter, update, options) }
    }


    suspend fun deleteManyDocData(
        docId: String,
        metadataType: String,
        editorType: String,
        dataFields: List<MetadataField> = emptyList(),
    ) {
        val filter = Filters.and(
            listOfNotNull(
                Filters.eq("docId", ObjectId(docId)),
                Filters.eq("metadataType", metadataType),
                editorType.isEmpty().let { if (it) null else Filters.eq("editorType", editorType) },
                *dataFields.map { (f, t, v) -> parseFilter(f, t, v) }.toTypedArray(),
            )
        )
        return withContext(Dispatchers.IO) { col.deleteMany(filter) }
    }


    private fun parseFilter(filterName: String, dataType: String, value: String): Bson {
        val filterValue = parseFilterData(value, dataType)

        // fieldName can be in the format of: "{operator}:{fieldName}" or just "{fieldName}"
        val filterParts = filterName.split(":")
        if (filterParts.size == 1) {
            return Filters.eq("metadataDetails.$filterName", filterValue);
        }

        val operator = filterParts[0]
        val fieldName = filterParts[1]

        return when (operator) {
            "eq" -> Filters.eq("metadataDetails.$fieldName", filterValue)
            "ne" -> Filters.ne("metadataDetails.$fieldName", filterValue)
            "gt" -> Filters.gt("metadataDetails.$fieldName", filterValue)
            "lt" -> Filters.lt("metadataDetails.$fieldName", filterValue)
            "gte" -> Filters.gte("metadataDetails.$fieldName", filterValue)
            "lte" -> Filters.lte("metadataDetails.$fieldName", filterValue)
            else -> throw UnsupportedOperationException("Unsupported operator: $operator")
        }
    }

    private fun parseFilterData(v: String, t: String): Any {
        return when (BsonType.valueOf(t)) {
            BsonType.STRING -> v
            BsonType.OBJECT_ID -> ObjectId(v)
            BsonType.BOOLEAN -> v.toBoolean()
            BsonType.INT32 -> v.toInt()
            BsonType.INT64 -> v.toLong()
            BsonType.DOUBLE -> v.toDouble()
            BsonType.DECIMAL128 -> v.toBigDecimal()
            else -> v
        }
    }

    private fun parseDataToBsonValue(v: String, t: String): BsonValue {
        return when (BsonType.valueOf(t)) {
            BsonType.STRING -> BsonString(v)
            BsonType.OBJECT_ID -> BsonObjectId(ObjectId(v))
            BsonType.BOOLEAN -> BsonBoolean(v.toBoolean())
            BsonType.INT32 -> BsonInt32(v.toInt())
            BsonType.INT64 -> BsonInt64(v.toLong())
            BsonType.DOUBLE -> BsonDouble(v.toDouble())
            BsonType.DECIMAL128 -> BsonDecimal128(Decimal128(v.toBigDecimal()))
            else -> throw UnsupportedOperationException("Not support type $t with value $v")
        }
    }
}
