package vi.metadata.doc.pojo

import org.bson.BsonDocument
import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId
import java.util.*


/**
 *
 * <AUTHOR>
 */

data class MetadataDocDocument(
    @BsonId @BsonRepresentation(BsonType.OBJECT_ID)
    var id: String = ObjectId().toHexString(),

    @BsonRepresentation(BsonType.OBJECT_ID)
    var docId: String,

    val editorType: String,
    val metadataType: String,
    val metadataDetails: BsonDocument,

    val createdAt: Date = Date(),
    val lastModified: Date = Date(),
    val version: Int = 0,
)

data class MetadataField(
    val fieldName: String,
    val dataType: String,
    val value: String
)

