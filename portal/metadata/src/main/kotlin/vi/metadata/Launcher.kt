package vi.metadata

import common.libs.logger.Logging
import org.koin.core.context.GlobalContext.getKoinApplicationOrNull
import org.koin.core.context.GlobalContext.startKoin
import vi.metadata.koin.koinApplication
import vi.metadata.server.Server


/**
 *
 * <AUTHOR>
 */
object Launcher : Logging {
    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching metadata service")

        startKoin(koinApplication)
        val server = getKoinApplicationOrNull()!!.koin.get<Server>()

        // start account manager server
        server.start()

        //Add a shutdown hook so that if the JVM is stopped, the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            logger.info("Shutting down metadata service")
            server.shutdown()
            logger.info("Shutdown metadata service")
        })

        server.blockUntilShutdown()
    }
}
