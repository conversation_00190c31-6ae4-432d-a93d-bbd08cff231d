package vi.metadata.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import org.koin.core.annotation.Factory
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import vi.metadata.configuration.Configuration
import vi.metadata.configuration.DatabaseConfig
import vi.metadata.configuration.ServerConfig
import java.io.File
import kotlin.system.exitProcess

/**
 *
 * <AUTHOR>
 */
@Module
class ConfigurationModule : Logging {
    private val configPath = "conf/config.json"

    @Singleton
    fun provideConfiguration(): Configuration = try {
        val mapper = jacksonObjectMapper()
        mapper.readValue(File(configPath), Configuration::class.java)
    } catch (t: Throwable) {
        logger.error("Exception when load configurations... ", t)
        exitProcess(1)
    }

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig {
        return config.serverConf
    }

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig {
        return config.dbConf
    }
}

