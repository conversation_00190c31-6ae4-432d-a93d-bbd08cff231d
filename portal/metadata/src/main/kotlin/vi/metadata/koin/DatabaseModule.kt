package vi.metadata.koin

import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.kotlin.client.coroutine.MongoClient
import com.mongodb.kotlin.client.coroutine.MongoCollection
import com.mongodb.kotlin.client.coroutine.MongoDatabase
import common.libs.codec.EnumCodecProvider
import common.libs.logger.Logging
import org.bson.codecs.CollectionCodecProvider
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import vi.metadata.configuration.Configuration
import vi.metadata.configuration.DatabaseConfig
import vi.metadata.doc.pojo.MetadataDeleteJobs
import vi.metadata.doc.pojo.MetadataDocDocument

/**
 *
 * <AUTHOR>
 */
@Module
class DatabaseModule(): Logging {
    @Singleton
    fun provideCodecRegistry(config: Configuration): CodecRegistry {
        return CodecRegistries.fromRegistries(
            MongoClientSettings.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                EnumCodecProvider(),
                CollectionCodecProvider(),
                PojoCodecProvider.builder()
                    .automatic(true)
                    .register(*config.registerPojoPackages.toTypedArray())
                    .build()
            )
        )
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig, codecRegistry: CodecRegistry): MongoDatabase {
        val settings = MongoClientSettings.builder()
            .applyConnectionString(ConnectionString(dbConf.connectionString))
            .codecRegistry(codecRegistry)
            .build()
        val mongoClient = MongoClient.create(settings)
        return mongoClient.getDatabase(dbConf.dbName)
    }

    companion object {
        const val METADATA_DOC_COLLECTION = "MetadataDocCollection"
        const val DELETE_DOC_SHARE_WITH_ME_QUEUE = "DeleteDocShareWithMeQueue"
    }

    @Singleton
    @Named(METADATA_DOC_COLLECTION)
    fun provideMetadataDocCollection(db: MongoDatabase): MongoCollection<MetadataDocDocument> {
        return db.getCollection("metadata-doc", MetadataDocDocument::class.java)
    }


    @Singleton
    @Named(DELETE_DOC_SHARE_WITH_ME_QUEUE)
    fun provideDeleteMetadataDocCollection(db: MongoDatabase): MongoCollection<MetadataDeleteJobs> {
        return db.getCollection("metadata-delete-jobs", MetadataDeleteJobs::class.java)
    }

}
