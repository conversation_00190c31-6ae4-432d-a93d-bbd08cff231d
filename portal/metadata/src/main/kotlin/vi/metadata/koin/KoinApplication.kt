package vi.metadata.koin

import com.mongodb.kotlin.client.coroutine.MongoCollection
import org.koin.core.qualifier.named
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.koin.ksp.generated.module
import org.koin.logger.slf4jLogger
import vi.metadata.configuration.Configuration
import vi.metadata.doc.MetadataDocService
import vi.metadata.doc.dbgateway.MetadataDeleteJobsGateway
import vi.metadata.doc.dbgateway.MetadataDocGateway
import vi.metadata.doc.pojo.MetadataDocDocument
import vi.metadata.server.Server
import vi.metadata.doc.pojo.MetadataDeleteJobs

val koinApplication = koinApplication {
    slf4jLogger()
    modules(
        ConfigurationModule().module,
        DatabaseModule().module,
        module {
            single<MetadataDocGateway> {
                MetadataDocGateway(
                    get<MongoCollection<MetadataDocDocument>>(
                        named(DatabaseModule.METADATA_DOC_COLLECTION)
                    )
                )
            }
            single<MetadataDeleteJobsGateway> {
                MetadataDeleteJobsGateway(
                    get<MongoCollection<MetadataDeleteJobs>>(
                        named(DatabaseModule.DELETE_DOC_SHARE_WITH_ME_QUEUE)
                    )
                )
            }

            single<MetadataDocService> {
                MetadataDocService(
                    get<MetadataDocGateway>(),
                    get<MetadataDeleteJobsGateway>()
                )
            }
            single<Server> { Server(get<Configuration>(), get<MetadataDocService>()) }
        }
    )
}