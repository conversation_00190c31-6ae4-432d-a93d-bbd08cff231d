package portal.notification

import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import io.grpc.Status
import io.grpc.StatusRuntimeException
import kotlinx.coroutines.rx3.await
import org.bson.codecs.DecoderContext
import org.bson.codecs.configuration.CodecRegistry
import org.bson.json.JsonReader
import org.koin.core.annotation.Single
import portal.kafka.api.IKafkaProducerManager
import portal.kafka.notifications.NotificationEvent
import portal.notification.db.NotificationDBGateway
import portal.notification.email.EmailTemplateService
import portal.notification.email.SendEmailService
import portal.notification.pojo.*
import portal.notification.pojo.notidata.*
import proto.portal.notification.NotificationMessages.*
import proto.portal.notification.NotificationServiceGrpcKt

@Single
class NotificationServiceImpl(
    private val notificationDBGateway: NotificationDBGateway,
    private val producerManager: IKafkaProducerManager,
    codecRegistry: CodecRegistry,
    private val emailTemplateService: EmailTemplateService,
    private val sendEmailService: SendEmailService,
) : NotificationServiceGrpcKt.NotificationServiceCoroutineImplBase(), Logging {

    private val notificationCodec = codecRegistry.get(Notification::class.java)
    private val objectMapper = jacksonObjectMapper()

    override suspend fun saveNotification(request: SaveNotificationRequest): SaveNotificationResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = SaveNotificationResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            var pojo = decodeToPojo(request.jsonNotification)

            if (!request.skipPersist) {
                pojo = notificationDBGateway.insertNotification(pojo).await()
            }

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.notificationId = pojo.id
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            // init and produce event
            val event = createEvent(pojo)
            produceEvent(event)

            response
        } catch (t: Throwable) {
            logger.error("[{}] failed by: ", request.requestId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to save notification"
            responseBuilder.setStatus(statusBuilder).build()
        }
    }

    private fun convertTarget(target: NotificationTarget): portal.kafka.notifications.NotificationTarget {
        return when (target) {
            is ClassroomTarget -> portal.kafka.notifications.ClassroomTarget(target.lsId, target.usersExcluded)
            is UserTarget -> portal.kafka.notifications.UserTarget(target.userId)
            is GroupTarget -> portal.kafka.notifications.GroupTarget(target.userIds)
            is MultiTarget -> portal.kafka.notifications.MultiTarget(target.targets.map { convertTarget(it) })
            else -> throw Exception("target type ${target.javaClass} not supported")
        }
    }

    private fun createEvent(pojo: Notification): NotificationEvent {
        val eventTarget = convertTarget(pojo.targetTo)

        val notificationData = pojo.data?.let {
            when (it) {
                is AcceptRaiseHandND -> portal.kafka.notifications.notidata.AcceptRaiseHandND(
                    it.lsId, it.targetUserId,
                )

                is AcceptPresentationRequestND -> portal.kafka.notifications.notidata.AcceptPresentationRequestND(
                    it.lsId, it.callingUserId, it.activityId
                )

                is AcceptRegistrationND -> portal.kafka.notifications.notidata.AcceptRegistrationND(
                    it.lsId, it.targetUserId,
                )

                is NewQuestionND -> portal.kafka.notifications.notidata.NewQuestionND(
                    it.lsId, it.activityId, it.jsonActivity
                )

                is RequestPresentationND -> portal.kafka.notifications.notidata.RequestPresentationND(
                    it.lsId, it.targetUserId, it.activityId, it.jsonActivity
                )

                is RejectRaiseHandND -> portal.kafka.notifications.notidata.RejectRaiseHandND(
                    it.lsId, it.targetUserId,
                )

                is ReqShareScreenND -> portal.kafka.notifications.notidata.ReqShareScreenND(
                    it.lsId, it.callingUserId,
                )

                is AcceptShareScreenND -> portal.kafka.notifications.notidata.AcceptShareScreenND(
                    it.lsId, it.targetUserId,
                )

                is CancelShareScreenND -> portal.kafka.notifications.notidata.CancelShareScreenND(
                    it.lsId, it.callingUserId,
                )


                is RejectShareScreenND -> portal.kafka.notifications.notidata.RejectShareScreenND(
                    it.lsId, it.targetUserId,
                )

                is CancelRaiseHandND -> portal.kafka.notifications.notidata.CancelRaiseHandND(
                    it.lsId, it.callingUserId,
                )

            
                is ShareScreenRemovedND -> portal.kafka.notifications.notidata.ShareScreenRemovedND(
                    it.lsId, it.callingUserId,
                )

                is JoinClassND -> portal.kafka.notifications.notidata.JoinClassND(
                    it.lsId, it.callingUserId,
                )

                is LeaveClassND -> portal.kafka.notifications.notidata.LeaveClassND(
                    it.lsId, it.callingUserId,
                )

                is RaiseHandND -> portal.kafka.notifications.notidata.RaiseHandND(
                    it.lsId, it.callingUserId,
                )

                is RegisterND -> portal.kafka.notifications.notidata.RegisterND(
                    it.lsId, it.regId,
                )

                is RegistrationCancelledND -> portal.kafka.notifications.notidata.RegistrationCancelledND(
                    it.lsId, it.callingUserId,
                )

                is RejectPresentationRequestND -> portal.kafka.notifications.notidata.RejectPresentationRequestND(
                    it.lsId, it.targetUserId, it.activityId
                )

                is RejectRegistrationND -> portal.kafka.notifications.notidata.RejectRegistrationND(
                    it.lsId, it.targetUserId,
                )

                is StartClassND -> portal.kafka.notifications.notidata.StartClassND(
                    it.lsId, it.ownerId
                )

                is StopClassND -> portal.kafka.notifications.notidata.StopClassND(
                    it.lsId, it.ownerId
                )

                is StopPresentationND -> portal.kafka.notifications.notidata.StopPresentationND(
                    it.lsId, it.targetUserId,
                )

                is StopQuestionND -> portal.kafka.notifications.notidata.StopQuestionND(
                    it.lsId, it.activityId
                )

                is CancelPresentationRequestND -> portal.kafka.notifications.notidata.CancelPresentationRequestND(
                    it.lsId, it.targetUserId, it.activityId
                )

                is RequestPinTabND -> portal.kafka.notifications.notidata.RequestPinTabND(
                    it.lsId, it.callingUserId, it.coordStateId
                )

                is CancelRequestPinTabND -> portal.kafka.notifications.notidata.CancelRequestPinTabND(
                    it.lsId, it.callingUserId, it.coordStateId
                )

                is RejectRequestPinTabND -> portal.kafka.notifications.notidata.RejectRequestPinTabND(
                    it.lsId, it.targetUserId, it.coordStateId
                )

                is ApproveRequestPinTabND -> portal.kafka.notifications.notidata.ApproveRequestPinTabND(
                    it.lsId, it.targetUserId, it.coordStateId
                )

                is UpdateRequestPinTabND -> portal.kafka.notifications.notidata.UpdateRequestPinTabND(
                    it.lsId, it.callingUserId, it.coordStateId, it.status, it.title
                )

                else -> throw Exception("notification data type ${it.javaClass} not supported")
            }
        }
        return NotificationEvent(
            pojo.id, pojo.emittedBy, eventTarget, pojo.message,
            pojo.createdTime.toEpochMilli(), pojo.expiredTime.toEpochMilli(),
            notificationData, pojo.show
        )
    }

    private fun produceEvent(event: NotificationEvent) {
        producerManager.sendEvent(event)
            .doOnError { logger.error("failed to produce event {}: ", event, it) }
            .onErrorComplete()
            .subscribe()
    }

    override suspend fun loadNotificationById(request: LoadNotificationByIdRequest): LoadNotificationByIdResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = LoadNotificationByIdResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            val pojo = notificationDBGateway.findNotificationById(request.notificationId).await()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            responseBuilder.jsonNotification = serializeNotification(pojo)
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            logger.error("[{}] failed by: ", request.requestId, t)

            statusBuilder.code = Status.Code.INTERNAL.value()
            statusBuilder.message = t.message ?: "failed to get notification"
            responseBuilder.setStatus(statusBuilder).build()
        }
    }

    private fun serializeNotification(pojo: Notification): String {
        val event = createEvent(pojo)
        val obj = objectMapper.valueToTree<ObjectNode>(event)

        obj.remove(NotificationEvent::clazz.name)
        obj.remove(NotificationEvent::targetTo.name)

        return obj.toString()
    }

    override suspend fun loadClassroomNotificationByCriteria(request: LoadClassroomNotificationByCriteriaRequest): LoadClassroomNotificationByCriteriaResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = LoadClassroomNotificationByCriteriaResponse.newBuilder()
        responseBuilder.requestId = request.requestId

        logger.debug("[{}] processing request: {}", request.requestId, request)
        return try {
            val notifications =
                notificationDBGateway.findClassroomNotificationByCriteria(request.lsId, request.userId).await()

            statusBuilder.code = Status.OK.code.value()
            statusBuilder.message = "successful"

            val jsonList = notifications.map { serializeNotification(it) }
            responseBuilder.addAllJsonNotification(jsonList)
            responseBuilder.status = statusBuilder.build()

            val response = responseBuilder.setStatus(statusBuilder).build()
            logger.debug("[{}] return response: {}", response.requestId, response)

            response
        } catch (t: Throwable) {
            if (t is StatusRuntimeException) throw t

            logger.error("[{}] failed by: ", request.requestId, t)
            throw Status.INTERNAL.withCause(t)
                .withDescription("failed to load classroom notifications by criteria")
                .augmentDescription("Cause message: ${t.message}")
                .asRuntimeException()
        }
    }

    private fun decodeToPojo(jsonNotification: String): Notification {
        return notificationCodec.decode(JsonReader(jsonNotification), DecoderContext.builder().build())
    }


    override suspend fun createEmailTemplate(request: CreateEmailTemplateRequest): CreateEmailTemplateResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = CreateEmailTemplateResponse.newBuilder()

        try {
            val template = emailTemplateService.createEmailTemplate(request)
            responseBuilder.setTemplateId(template.templateId)
            statusBuilder.code = Status.OK.code.value()
        } catch (t: NoSuchElementException) {
            statusBuilder.code = Status.NOT_FOUND.code.value()
            statusBuilder.message = t.message ?: "reference template not found"
        } catch (t: Throwable) {
            statusBuilder.code = Status.INTERNAL.code.value()
            statusBuilder.message = t.message ?: "failed to create email template"
        }

        return responseBuilder
            .setStatus(statusBuilder.build())
            .build()
    }

    override suspend fun sendEmail(request: SendEmailRequest): SendEmailResponse {
        val statusBuilder = com.google.rpc.Status.newBuilder()
        val responseBuilder = SendEmailResponse.newBuilder()

        try {
            sendEmailService.euqueueEmail(request)
            statusBuilder.code = Status.OK.code.value()
        } catch (t: Throwable) {
            statusBuilder.code = Status.INTERNAL.code.value()
            statusBuilder.message = t.message ?: "failed to enqueue email"
        }

        return responseBuilder
            .setStatus(statusBuilder.build())
            .build()
    }
}
