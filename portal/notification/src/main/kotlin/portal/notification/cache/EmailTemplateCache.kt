package portal.notification.cache

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.rx3.await
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import portal.notification.db.EmailTemplateDBGateway
import portal.notification.pojo.email.EmailTemplate
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.jvm.optionals.getOrNull

@Single
class EmailTemplateCache constructor(
    private val templateDBGateway: EmailTemplateDBGateway
) : Logging {

    private val loader: CacheLoader<String, Optional<EmailTemplate>> =
        object : CacheLoader<String, Optional<EmailTemplate>>() {
            override fun load(key: String): Optional<EmailTemplate> {
                return runBlocking(Dispatchers.IO) {
                    val template: EmailTemplate = templateDBGateway.findTemplateById(key).await()
                    logger.info("load mail template {} from db {}", key, template)
                    Optional.ofNullable(template)
                }
            }

            override fun loadAll(keys: MutableIterable<String>): MutableMap<String, Optional<EmailTemplate>> {
                return runBlocking(Dispatchers.IO) {
                    val templates: List<EmailTemplate> = templateDBGateway.findTemplatesById(keys).await()
                    logger.info("load mail templates {} from db {}", keys, templates)

                    mutableMapOf<String, Optional<EmailTemplate>>().apply {
                        templates.forEach {
                            this[it.templateId] = Optional.ofNullable(it)
                        }
                    }
                }
            }
        }

    private val cache: LoadingCache<String, Optional<EmailTemplate>> =
        CacheBuilder.newBuilder().maximumSize(1000).expireAfterAccess(1, TimeUnit.HOURS).build(loader)

    /**
     * Get template by ID
     */
    suspend fun get(id: String): EmailTemplate? = withContext(Dispatchers.IO) {
        return@withContext cache.get(id).getOrNull()
    }

    /**
     * Get template list by IDs
     */
    suspend fun getAll(ids: Iterable<String>): Iterable<EmailTemplate> = withContext(Dispatchers.IO) {
        return@withContext cache.getAll(ids).values.mapNotNull {
            it.getOrNull()
        }
    }
}