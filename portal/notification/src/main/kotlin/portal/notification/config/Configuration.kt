package portal.notification.config

import portal.kafka.api.configs.KafkaConfig
import portal.notification.pojo.Notification

/**
 * @property serverConf Server
 * @property dbConf DatabaseConfig
 *
 * <AUTHOR>
 */
data class Configuration constructor(
    val serverConf: ServerConfig,
    val dbConf: DatabaseConfig,
    val kafkaConf: KafkaConfig,
    val mailerConf: EmailConfig,
    val notificationPackages: List<String> = listOf(Notification::class.java.`package`.name),
)
