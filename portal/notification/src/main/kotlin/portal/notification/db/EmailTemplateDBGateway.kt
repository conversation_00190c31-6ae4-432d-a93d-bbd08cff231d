package portal.notification.db

import com.mongodb.client.model.Filters
import com.mongodb.client.model.FindOneAndReplaceOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.notification.koin.EMAIL_TEMPLATE_COLLECTION
import portal.notification.pojo.email.EmailTemplate

@Singleton
class EmailTemplateDBGateway constructor(
    @Named(EMAIL_TEMPLATE_COLLECTION) private val templateCollection: MongoCollection<EmailTemplate>
) : Logging {

    fun insertTemplate(emailTemplate: EmailTemplate): Single<EmailTemplate> {
        val filter = Filters.eq(EmailTemplate::templateId.name, emailTemplate.templateId)
        val options = FindOneAndReplaceOptions().upsert(true).returnDocument(ReturnDocument.AFTER)
        return Flowable.defer { templateCollection.findOneAndReplace(filter, emailTemplate, options) }
            .firstOrError()
            .doOnError {
                logger.error("failed to insert emailTemplate {}: ", emailTemplate, it)
            }
    }

    fun findTemplateById(id: String): Single<EmailTemplate> {
        val filters = Filters.eq(EmailTemplate::templateId.name, id)
        return Flowable.defer { templateCollection.find(filters).limit(1) }
            .firstOrError()
            .doOnError {
                logger.error("failed to find template for ID {}: ", id, it)
            }
    }

    fun findTemplatesById(ids: Iterable<String>): Single<List<EmailTemplate>> {
        val filters = Filters.`in`(EmailTemplate::templateId.name, ids)
        return Flowable.defer { templateCollection.find(filters) }
            .toList()
            .doOnError {
                logger.error("failed to find templates for IDs {}: ", ids, it)
            }
    }

    fun isTemplateIdUnique(id: String): Single<Boolean> {
        val filter = Filters.eq(EmailTemplate::templateId.name, id)
        return Flowable.defer { templateCollection.find(filter).limit(1) }
            .firstElement().isEmpty()
            .doOnError {
                logger.error("failed to check if template ID {} unique: ", id, it)
            }
    }
}