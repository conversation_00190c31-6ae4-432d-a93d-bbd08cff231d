package portal.notification.db

import com.mongodb.client.model.Filters
import com.mongodb.client.model.FindOneAndReplaceOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.notification.koin.NOTIFICATION_COLLECTION
import portal.notification.pojo.*
import java.time.Instant

@Singleton
class NotificationDBGateway constructor(
    @Named(NOTIFICATION_COLLECTION) private val notificationCollection: MongoCollection<Notification>,
): Logging {

    fun insertNotification(notification: Notification): Single<Notification> {
        val filter = Filters.eq(Notification::id.name, notification.id)
        val options = FindOneAndReplaceOptions().upsert(true).returnDocument(ReturnDocument.AFTER)
        return Flowable.defer { notificationCollection.findOneAndReplace(filter, notification, options) }
            .firstOrError()
            .doOnError {
                logger.error("failed to insert notification {}: ", notification, it)
            }
    }

    fun findNotificationById(id: String): Single<Notification> {
        val filter = Filters.eq("_id", ObjectId(id))
        return Flowable.defer { notificationCollection.find(filter) }
            .firstOrError()
            .doOnError {
                logger.error("failed to find notification {}: ", id, it)
            }
    }

    fun findClassroomNotificationByCriteria(lsId: String, userId: String): Single<List<Notification>> {
        val filterClassroomTarget = createClassroomTargetFilter(lsId, Notification::targetTo.name)
        val filterUserTarget = createUserTargetFilter(userId, Notification::targetTo.name)
        val filterGroupTarget = createGroupTargetFilter(userId, Notification::targetTo.name)

        val filterMultiTarget = Filters.and(
            Filters.eq("${Notification::targetTo.name}.targetType", "MultiTarget"),
            Filters.elemMatch("${Notification::targetTo.name}.${MultiTarget::targets.name}",
                Filters.or(createClassroomTargetFilter(lsId), createUserTargetFilter(userId), createGroupTargetFilter(userId))
            )
        )

        val filters = Filters.and(
            Filters.or(filterClassroomTarget, filterUserTarget, filterGroupTarget, filterMultiTarget),
            Filters.gt(Notification::expiredTime.name, Instant.now())
        )

        return Flowable.defer { notificationCollection.find(filters) }
            .toList()
            .doOnError {
                logger.error("failed to find notifications for user {} and lsId {}: ", userId, lsId, it)
            }
    }

    private fun createGroupTargetFilter(userId: String, prefix: String? = null): Bson {
        val targetTypeFieldName = prefix?.plus(".targetType") ?: "targetType"
        val userIdsFieldName = prefix?.plus(".${GroupTarget::userIds.name}") ?: GroupTarget::userIds.name

        return Filters.and(
            Filters.eq(targetTypeFieldName, "GroupTarget"),
            Filters.eq(userIdsFieldName, userId)
        )
    }

    private fun createClassroomTargetFilter(lsId: String, prefix: String? = null): Bson {
        val targetTypeFieldName = prefix?.plus(".targetType") ?: "targetType"
        val lsIdFieldName = prefix?.plus(".${ClassroomTarget::lsId.name}") ?: ClassroomTarget::lsId.name

        return Filters.and(
            Filters.eq(targetTypeFieldName, "ClassroomTarget"),
            Filters.eq(lsIdFieldName, ObjectId(lsId))
        )
    }

    private fun createUserTargetFilter(userId: String, prefix: String? = null): Bson {
        val targetTypeFieldName = prefix?.plus(".targetType") ?: "targetType"
        val userIdFieldName = prefix?.plus(".${UserTarget::userId.name}") ?: UserTarget::userId.name

        return Filters.and(
            Filters.eq(targetTypeFieldName, "UserTarget"),
            Filters.eq(userIdFieldName, ObjectId(userId))
        )
    }

}