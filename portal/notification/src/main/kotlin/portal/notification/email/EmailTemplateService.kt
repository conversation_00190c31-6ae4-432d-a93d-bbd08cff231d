package portal.notification.email

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import common.libs.logger.Logging
import freemarker.cache.StringTemplateLoader
import freemarker.cache.TemplateLoader
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Single
import portal.notification.cache.EmailTemplateCache
import portal.notification.db.EmailTemplateDBGateway
import portal.notification.pojo.email.EmailTemplate
import proto.portal.notification.NotificationMessages.CreateEmailTemplateRequest
import java.io.StringWriter
import freemarker.template.Configuration as FreeMarkerConfiguration

@Single
class EmailTemplateService constructor(
    private val templateDBGateway: EmailTemplateDBGateway,
    private val templateCache: EmailTemplateCache
) : Logging {

    private val objectMapper = ObjectMapper().registerModule(KotlinModule.Builder().build())

    /**
     * Resolve template into string content with the provided jsonData
     * @return Pair<String, Boolean> the string content & is content HTML
     */
    suspend fun resolveEmailContent(templateId: String, jsonData: String): Pair<String, Boolean> {
        val dataModel = objectMapper.readValue(jsonData, Map::class.java)

        val (loader, isHtml) = loadTemplates(templateId)
        val template = FreeMarkerConfiguration(FreeMarkerConfiguration.VERSION_2_3_28).apply {
            templateLoader = loader
        }.getTemplate(templateId)

        // Merge data model with template
        val writer = StringWriter()
        template.process(dataModel, writer)

        val result = writer.toString()
        return result to isHtml
    }

    /**
     * Load main template and all referred templates into FreeMarker loader
     * @return Pair<TemplateLoader, Boolean> the loader & is the main template an HTML template
     */
    private suspend fun loadTemplates(templateId: String): Pair<TemplateLoader, Boolean> {
        val templates = mutableListOf<EmailTemplate>()
        val loadedTemplateIds = HashSet<String>()
        val referTemplateIds = HashSet<String>()

        // load main template
        val mainTemplate: EmailTemplate = templateCache.get(templateId)
            ?: throw NoSuchElementException("Template '$templateId' not found")
        templates.add(mainTemplate)
        loadedTemplateIds.add(mainTemplate.templateId)
        referTemplateIds.addAll(mainTemplate.referTemplateIds)

        // recursive load all referred templates
        var loadDepth = 0
        val maxDepth = 20
        while (referTemplateIds.isNotEmpty()) {
            if (++loadDepth > maxDepth)
                throw RuntimeException("Recursive load refer template for '$templateId' over the depth limit of $maxDepth")

            val referTemplates: Iterable<EmailTemplate> = templateCache.getAll(referTemplateIds)
            referTemplates.forEach {
                if (it.templateId in loadedTemplateIds) return@forEach
                templates.add(it)
                loadedTemplateIds.add(it.templateId)
                referTemplateIds.addAll(it.referTemplateIds)
            }
            // filter the loaded templates from the refer templates
            referTemplateIds.removeAll(loadedTemplateIds)
        }

        // load templates into FreeMarker loader
        val loader = StringTemplateLoader()
        templates.forEach {
            loader.putTemplate(it.templateId, it.content)
        }

        return loader to mainTemplate.isHtml;
    }

    /**
     * Create new email template. The template ID must be unique.
     */
    suspend fun createEmailTemplate(request: CreateEmailTemplateRequest): EmailTemplate {
        val isUniqueName = templateDBGateway.isTemplateIdUnique(request.templateId).await()
        if (!isUniqueName) {
            throw IllegalArgumentException("Template name '${request.templateId}' already exists")
        }

        val referTemplateIds: List<String> = request.referredTemplateIdsList.toList()
        if (referTemplateIds.isNotEmpty()) {
            val referTemplates = templateCache.getAll(referTemplateIds)
            if (referTemplates.count() != referTemplateIds.size)
                throw NoSuchElementException("Some referred templates not found")
        }

        val template = EmailTemplate(
            templateId = request.templateId,
            content = request.content,
            referTemplateIds = referTemplateIds,
            isHtml = request.isHtml,
        )
        return templateDBGateway.insertTemplate(template).await()
    }
}