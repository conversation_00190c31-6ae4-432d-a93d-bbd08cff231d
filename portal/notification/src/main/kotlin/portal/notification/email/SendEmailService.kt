package portal.notification.email

import common.libs.logger.Logging
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single
import portal.notification.config.Configuration
import portal.notification.email.client.MailClient
import portal.notification.pojo.email.SendEmailJob
import proto.portal.notification.NotificationMessages.SendEmailRequest

@Single
class SendEmailService constructor(
    private val templateService: EmailTemplateService,
    private val mailClient: MailClient,
    private val config: Configuration
) : Logging {

    val sendingChannel = Channel<SendEmailJob>(
        capacity = config.mailerConf.sendingBuffer,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
        onUndeliveredElement = {
            logger.error("[EMAIL][SEND] Channel dropped email: ${it.toAddress}: ${it.subject}")
        })
    val retryingChannel = Channel<SendEmailJob>(
        capacity = config.mailerConf.retryingBuffer,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
        onUndeliveredElement = {
            logger.error("[EMAIL][RETRY] Channel dropped email: ${it.toAddress}: ${it.subject}")
        })

    /**
     * Resolve email content and send it to the sending channel
     */
    suspend fun euqueueEmail(request: SendEmailRequest) {
        val (body, isHtml) = templateService.resolveEmailContent(request.templateId, request.jsonData)

        val job = SendEmailJob(
            fromAddress = config.mailerConf.sender,
            fromName = config.mailerConf.senderName,
            toAddress = request.toAddress,
            toName = request.toName,
            subject = request.subject,
            body = body,
            isHtml = isHtml,
            attempt = 0
        )

        sendingChannel.send(job)
    }

    /**
     *  Sending email to the SMTP server.
     *  To be called from the SendEmailScheduler
     */
    suspend fun sendEmail(email: SendEmailJob) {
        logger.info("[EMAIL][SEND] Send email START: ${email.toAddress}: ${email.subject}")

        try {
            mailClient.sendMail(email).await()
            logger.info("[EMAIL][SEND] Send email SUCCEED: ${email.toAddress}: ${email.subject}")
        } catch (e: Exception) {
            logger.error("[EMAIL][SEND] Send email FAILED: ${email.toAddress}: ${email.subject}\n", e)

            // Always retry as the problems like invalid recipient email will be handled by the SMTP server itself.
            // And simplejavamail only throw a simple MailException for all other errors
            delayedEnqueueRetry(email)
        }
    }

    /**
     *  Retry sending email to the SMTP server.
     *  To be called from the SendEmailScheduler
     */
    suspend fun retryEmail(email: SendEmailJob) {
        val curAttempt = email.attempt
        logger.info("[EMAIL][RETRY_$curAttempt] Send email START: ${email.toAddress}: ${email.subject}")

        try {
            mailClient.sendMail(email).await()
            logger.info("[EMAIL][RETRY_$curAttempt] Send email SUCCEED: ${email.toAddress}: ${email.subject}")
        } catch (e: Exception) {
            logger.error("[EMAIL][RETRY_$curAttempt] Send email FAILED: ${email.toAddress}: ${email.subject}\n", e)

            if (email.attempt < config.mailerConf.maxAttempts) {
                delayedEnqueueRetry(email)
            } else {
                logger.warn("[EMAIL][RETRY_$curAttempt] Failed $curAttempt times. Dropped email: ${email.toAddress}: ${email.subject}")
            }
        }
    }

    /**
     * Prevent retrying too fast when there are only a small number of emails in the queue
     */
    private suspend fun delayedEnqueueRetry(email: SendEmailJob) {
        val delaySecs = config.mailerConf.retryAfterSeconds

        coroutineScope {
            launch {
                delay(delaySecs * 1000L)
                email.attempt++
                retryingChannel.send(email)
            }
        }
    }
}