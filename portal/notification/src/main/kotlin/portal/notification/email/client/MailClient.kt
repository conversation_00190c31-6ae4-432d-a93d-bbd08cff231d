package portal.notification.email.client

import common.libs.logger.Logging
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.asDeferred
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single
import org.simplejavamail.api.mailer.Mailer
import org.simplejavamail.email.EmailBuilder
import portal.notification.pojo.email.SendEmailJob

@Single
class MailClient constructor(
    private val mailer: Mailer
) : Logging {

    suspend fun sendMail(emailJob: SendEmailJob): Deferred<Void> {
        val emailBuilder = EmailBuilder.startingBlank()
            .from(emailJob.fromName, emailJob.fromAddress)
            .to(emailJob.toName, emailJob.toAddress)
            .withSubject(emailJob.subject)

        if (emailJob.isHtml) {
            emailBuilder.withHTMLText(emailJob.body)
        } else {
            emailBuilder.withPlainText(emailJob.body)
        }

        val email = emailBuilder.buildEmail()
        return with<PERSON>ontext(Dispatchers.IO) { mailer.sendMail(email, true) }.asDeferred()
    }
}
