package portal.notification.koin

import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Single
import portal.kafka.api.IKafkaEventReactor
import portal.kafka.koin.KAFKA_EVENT_REACTOR_LIST

@ComponentScan("portal.notification")
@Module
class ApplicationModule {
    @Single
    @Named(KAFKA_EVENT_REACTOR_LIST)
    fun provideKafkaReactors(): Set<IKafkaEventReactor> {
        return setOf()
    }
}