package portal.notification.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import org.simplejavamail.config.ConfigLoader
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import portal.kafka.api.configs.KafkaConfig
import portal.notification.config.Configuration
import portal.notification.config.DatabaseConfig
import portal.notification.config.ServerConfig
import java.io.File

@Module
class ConfigurationModule {
    private val logger: Logger = LoggerFactory.getLogger(ConfigurationModule::class.java)
    private val config: Configuration

    private val configPath = "conf/config.json"
    private val mailerConfigPath = "conf/simplejavamail.properties"

    init {
        config = loadConfig()
        loadSimpleJavaMailConfig()
    }

    private fun loadConfig(): Configuration {
        return try {
            val mapper = jacksonObjectMapper()
            val config = mapper.readValue(File(configPath), Configuration::class.java)
            if (config == null) {
                logger.error("Failed to load configurations")
                throw RuntimeException("Failed to load configurations")
            }
            config
        } catch (e: Exception) {
            logger.error("Failed to load configurations ", e)
            throw RuntimeException(e)
        }
    }

    private fun loadSimpleJavaMailConfig() {
        try {
            ConfigLoader.loadProperties(File(mailerConfigPath), true)
        } catch (e: Exception) {
            logger.error("Failed to load Simple Java Mail configuration", e)
            throw RuntimeException(e)
        }
    }

    @Singleton
    fun provideServiceConfig(): Configuration {
        return config
    }

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig {
        return config.serverConf
    }

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig {
        return config.dbConf
    }

    @Singleton
    fun provideKafkaConfig(config: Configuration): KafkaConfig {
        return config.kafkaConf
    }
}