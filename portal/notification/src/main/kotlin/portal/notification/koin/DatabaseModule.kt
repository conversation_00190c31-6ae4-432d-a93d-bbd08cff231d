package portal.notification.koin

import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import common.libs.codec.EnumCodecProvider
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.notification.config.Configuration
import portal.notification.config.DatabaseConfig
import portal.notification.pojo.Notification
import portal.notification.pojo.email.EmailTemplate

@Module
class DatabaseModule {

    @Singleton
    fun provideCodecRegistry(config: Configuration): CodecRegistry {
        return CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                PojoCodecProvider.builder().automatic(true).register(*config.notificationPackages.toTypedArray()).build(),
                EnumCodecProvider()
            )
        )
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig, codecRegistry: CodecRegistry): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(codecRegistry)
    }

    @Singleton
    @Named(NOTIFICATION_COLLECTION)
    fun provideNotificationCollection(database: MongoDatabase): MongoCollection<Notification> {
        return database.getCollection("notifications", Notification::class.java)
    }

    @Singleton
    @Named(EMAIL_TEMPLATE_COLLECTION)
    fun provideEmailTemplateCollection(database: MongoDatabase): MongoCollection<EmailTemplate> {
        return database.getCollection("email-templates", EmailTemplate::class.java)
    }
}
