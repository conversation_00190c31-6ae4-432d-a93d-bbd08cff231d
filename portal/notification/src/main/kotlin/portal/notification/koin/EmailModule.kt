package portal.notification.koin

import org.simplejavamail.api.mailer.Mailer
import org.simplejavamail.mailer.MailerBuilder
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import org.koin.core.annotation.Factory

@Module
class EmailModule {
    @Singleton
    fun provideMailer(): Mailer {
        // properties already loaded directly into the library environment
        return MailerBuilder.buildMailer()
    }
}