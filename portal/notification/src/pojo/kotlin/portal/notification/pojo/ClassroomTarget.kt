package portal.notification.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation

@BsonDiscriminator(value = "ClassroomTarget", key = "targetType")
data class ClassroomTarget @BsonCreator constructor(
    @BsonProperty("lsId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsId: String,
    @BsonProperty("usersExcluded")
    val usersExcluded: List<String>? = listOf()
): NotificationTarget
