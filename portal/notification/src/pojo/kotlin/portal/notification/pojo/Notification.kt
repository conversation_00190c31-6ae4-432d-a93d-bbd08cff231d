package portal.notification.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import org.bson.types.ObjectId
import portal.notification.pojo.notidata.NotificationData
import java.time.Instant

data class Notification @BsonCreator constructor(
        @BsonProperty("emittedBy")
        @BsonRepresentation(BsonType.OBJECT_ID)
        val emittedBy: String,

        @BsonProperty("targetTo")
        val targetTo: NotificationTarget,

        @BsonProperty("message")
        val message: String,

        @BsonProperty("expiredTime")
        val expiredTime: Instant,

        @BsonProperty("data")
        val data: NotificationData? = null,

        @BsonProperty("show")
        val show: Boolean = true,

        @BsonProperty("createdTime")
        val createdTime: Instant = Instant.now(),

        @BsonId
        @BsonRepresentation(BsonType.OBJECT_ID)
        val id: String = ObjectId().toHexString()
)
