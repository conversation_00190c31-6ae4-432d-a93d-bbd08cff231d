package portal.notification.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation

@BsonDiscriminator(value = "UserTarget", key = "targetType")
data class UserTarget @BsonCreator constructor(
    @BsonProperty("userId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val userId: String
): NotificationTarget