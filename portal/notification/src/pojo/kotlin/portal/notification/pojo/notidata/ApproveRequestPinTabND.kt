package portal.notification.pojo.notidata

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation

@BsonDiscriminator(value = "ApproveRequestPinTabND", key = "entityType")
data class ApproveRequestPinTabND @BsonCreator constructor(
    @BsonProperty("lsId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsId: String,

    @BsonProperty("targetUserId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val targetUserId: String,

    @BsonProperty("coordStateId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val coordStateId: String,

    ): NotificationData
