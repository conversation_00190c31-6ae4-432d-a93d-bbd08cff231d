package portal.notification.pojo.notidata

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation

@BsonDiscriminator(value = "NewQuestionND", key = "entityType")
data class NewQuestionND @BsonCreator constructor(
    @BsonProperty("lsId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsId: String,

    @BsonProperty("activityId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val activityId: String,

    @BsonProperty("jsonActivity")
    val jsonActivity: String,
): NotificationData