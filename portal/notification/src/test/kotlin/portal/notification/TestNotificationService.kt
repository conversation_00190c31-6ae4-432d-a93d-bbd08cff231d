package portal.notification

import com.mongodb.reactivestreams.client.MongoClients
import common.libs.codec.EnumCodecProvider
import common.libs.logger.Logging
import io.grpc.ManagedChannelBuilder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.asCompletableFuture
import org.bson.codecs.DecoderContext
import org.bson.codecs.EncoderContext
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.json.JsonReader
import org.bson.json.JsonWriter
import portal.notification.pojo.Notification
import portal.notification.pojo.notidata.NotificationData
import proto.portal.notification.NotificationMessages
import proto.portal.notification.NotificationServiceGrpcKt
import java.io.StringWriter
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CountDownLatch

class TestNotificationService: Logging {
    private val channel = ManagedChannelBuilder.forAddress("localhost", 1155).usePlaintext().build()

    private val notificationCodec = CodecRegistries.fromRegistries(
        MongoClients.getDefaultCodecRegistry(),
        CodecRegistries.fromProviders(
            PojoCodecProvider.builder().automatic(true).register(Notification::class.java.`package`.name, NotificationData::class.java.`package`.name).build(),
            EnumCodecProvider()
        )
    ).get(Notification::class.java)

    fun saveNotification(notification: Notification): CompletableFuture<NotificationMessages.SaveNotificationResponse> {
        val writer = StringWriter()
        notificationCodec.encode(
            JsonWriter(writer),
            notification,
            EncoderContext.builder().build()
        )
        val jsonNotification = writer.toString()

        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.SaveNotificationRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setJsonNotification(jsonNotification)
            .build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("save notification request: {}", req)
                val res = stub.saveNotification(req)

                logger.debug("save notification response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("failed to save notification: ", t)
                throw t
            }
        }.asCompletableFuture()
    }

    fun loadNotificationById(id: String): CompletableFuture<String> {
        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.LoadNotificationByIdRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setNotificationId(id)
            .build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("load notification request: {}", req)
                val res = stub.loadNotificationById(req)

                logger.debug("load notification response: {}", res)
                res.jsonNotification
            } catch (t: Throwable) {
                logger.error("failed to load notification {}: ", id, t)
                throw t
            }
        }.asCompletableFuture()
    }

    fun loadClassroomNotification(lsId: String, userId: String): CompletableFuture<List<Notification>> {
        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(channel)
        val req = NotificationMessages.LoadClassroomNotificationByCriteriaRequest.newBuilder()
            .setRequestId("Req${System.currentTimeMillis()}")
            .setLsId(lsId)
            .setUserId(userId)
            .build()

        return CoroutineScope(Dispatchers.IO).async {
            try {
                logger.debug("load classroom notification request: {}", req)
                val res = stub.loadClassroomNotificationByCriteria(req)

                logger.debug("load classroom notification response: {}", res)
                val notifications = res.jsonNotificationList.map {
                    notificationCodec.decode(JsonReader(it), DecoderContext.builder().build())
                }.toList()

                logger.debug("Loaded {} valid notifications: {}", notifications.size, notifications)

                notifications
            } catch (t: Throwable) {
                logger.error("failed to load classroom {} notification for {}: ", lsId, userId, t)
                throw t
            }
        }.asCompletableFuture()
    }
}

fun main() {
    val tester = TestNotificationService()
    val lsId = "6090a80ba777de44906662b0"
    val userId = "6081486758008b2b148946cd"
    val notiId = "60de8b8679b0ee5becaae2ed"

    // init and save notification
//    tester.loadClassroomNotification(lsId, userId)
    tester.loadNotificationById(notiId)

    CountDownLatch(1).await()
}