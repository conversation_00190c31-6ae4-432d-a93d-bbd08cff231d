import { Component, Input, OnInit } from '@angular/core';
import { LSessionDetails } from '@viclass/portal.common';

@Component({
  selector: 'ls-description-tab',
  templateUrl: './description-tab.component.html',
  styleUrls: ['./description-tab.component.sass']
})
export class DescriptionTabComponent implements OnInit {

  @Input() lsDetails: LSessionDetails

  constructor() { }

  ngOnInit(): void {
  }

}
