<div
  class="container p-0 m-3 document-tab-content"
  *ngIf="documents.length; else elseBlock"
>
  <div class="row row-cols-2 m-0">
    <div
      *ngFor="let doc of documents"
      class="col ps-0 pe-2 mb-3 d-flex align-items-center"
      [ngSwitch]="doc.type"
    >
      <span
        *ngSwitchCase="'text'"
        class="vcon-onl vcon_editor_word border rounded-3 me-2 p-1"
      ></span>
      <span
        *ngSwitchCase="'pdf'"
        class="vcon-onl vcon_editor_pdf border rounded-3 me-2 p-1"
      ></span>
      <span
        *ngSwitchCase="'img'"
        class="vcon-onl vcon_editor_photo border rounded-3 me-2 p-1"
      ></span>
      <span
        *ngSwitchCase="'drawing'"
        class="vcon-onl vcon_editor_freedrawing border rounded-3 me-2 p-1"
      ></span>
      <p class="my-0">{{ doc.title }}</p>
    </div>
  </div>
</div>
<ng-template #elseBlock>
  <div
    class="document-tab-content-empty d-flex align-items-center justify-center"
  >
    <span class="vcon-general vcon_general_empty-document me-3"></span>
    <span>Chưa có tài liệu</span>
  </div>
</ng-template>
