<table class="table my-3 ms-3 general-tab-content">
  <tbody>
    <tr>
      <td class="d-flex">
        <span class="vcon-general vcon_user_teacher me-1"></span>Giảng viên
      </td>
      <td class="border-start ps-3">
        <img class="border rounded-circle me-1 creator-avatar" [attr.src]="creator ? creator.avatarUrl : ''">
        {{creator ? creator.username : ""}}
      </td>
    </tr>
    <tr>
      <td class="d-flex">
        <span class="vcon-general vcon_user_students me-1"></span>Học viên đã đăng ký
      </td>
      <td class="border-start ps-3 total-registered" [class.full-slot]="isFullSlot()">{{getTotalRegisteredMembers()}}</td>
    </tr>
    <tr *ngIf="!isLSessionEnded()">
      <td class="d-flex">
        <span class="vcon-general vcon_user_students me-1"></span><PERSON><PERSON> học viên tối đa
      </td>
      <td class="border-start ps-3">{{getMaxMemberSetting()}}</td>
    </tr>
    <tr>
      <td class="d-flex">
        <i class="vcon-general vcon_calendar me-1"></i>Ngày học
      </td>
      <td class="border-start ps-3">{{lsDetails.startDate*1000 | date: 'dd/MM/yyyy'}}</td>
    </tr>
    <tr>
      <td class="d-flex">
        <i class="vcon-general vcon_session_time me-1"></i>Giờ học
      </td>
      <td class="border-start ps-3">
        {{getStartTime() < 0 ? '_ : _' : getStartTime() | date: 'HH:mm'}} đến {{getEndTime() < 0 ? '_ : _' : getEndTime() | date: 'HH:mm'}}
      </td>
    </tr>
    <tr *ngIf="!isLSessionEnded()">
      <td class="d-flex">
        <i class="vcon-general vcon_session_title_future me-1"></i>Thời lượng dự kiến
      </td>
      <td class="border-start ps-3">{{expectedDuration()}}</td>
    </tr>
    <tr *ngIf="isLSessionEnded()">
      <td class="d-flex">
        <div><i class="vcon-general vcon_session_title_past me-1"></i>Thời lượng</div>
      </td>
      <td class="border-start ps-3">{{getActualDuration()}}</td>
    </tr>
    <tr *ngIf="!isLSessionEnded() && !startAfter24h()">
      <td class="d-flex" [ngSwitch]="lsDetails.state?.status">
        <div *ngSwitchCase="'STARTED'" class="clearfix">
          <i class="vcon-general vcon_session_title_now me-1"></i>Buổi học đã bắt đầu
        </div>
        <div *ngSwitchCase="'NOT_STARTED'">
          <div *ngIf="!isLate()">
            <i class="vcon-general vcon_session_title_future me-1"></i>Buổi học bắt đầu trong
          </div>
          <div *ngIf="isLate()">
            <i class="vcon-general vcon_session_title_future me-1"></i>Buổi học đã muộn
          </div>
        </div>
      </td>
      <td class="border-start ps-3" [ngClass]="{'lsession-started': isGoingOn() || isLate(), 'lsession-not-started': isUpcoming()}">{{time()}}</td>
    </tr>
  </tbody>
</table>
