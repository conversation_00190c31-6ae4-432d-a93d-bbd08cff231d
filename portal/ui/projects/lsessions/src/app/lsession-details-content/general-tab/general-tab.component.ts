import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { LSessionDetails, LSessionMemberViewModel, UserProfile, LSessionSettings } from '@viclass/portal.common';
import * as moment from "moment";
import {Subscription, timer} from "rxjs";
import momentDurationFormatSetup from "moment-duration-format";
import {Duration} from "moment/moment";

momentDurationFormatSetup(moment);
moment.locale("vi")

@Component({
  selector: 'ls-general-tab',
  templateUrl: './general-tab.component.html',
  styleUrls: ['./general-tab.component.sass']
})
export class GeneralTabComponent implements OnInit, OnDestroy {

  @Input() lsDetails: LSessionDetails
  @Input() creator: UserProfile
  @Input() members: LSessionMemberViewModel[]
  @Input() settings : LSessionSettings
  @Input() curMember: LSessionMemberViewModel
  @Input() loggedInUser: UserProfile

  private timer: Subscription;
  private now: number

  constructor() { }

  ngOnInit(): void {
    this.timer = timer(0, 1000).subscribe((seconds) => {
      this.now = Date.now()
    })
  }

  ngOnDestroy(): void {
    this.timer.unsubscribe();
  }

  isFullSlot(): boolean {
    return this.settings && this.lsDetails.state && this.settings.maxMember >= 0 && (this.lsDetails.state?.registered >= this.settings.maxMember)
  }

  getTotalRegisteredMembers() {
    return this.lsDetails.state?.registered
  }

  getMaxMemberSetting(): string {
    if (!this.settings || this.settings.maxMember < 0) {
      return 'Không giới hạn'
    }

    return this.settings.maxMember.toString()
  }

  getStartTime() {
    if (!this.lsDetails || !this.lsDetails.state) return -1

    if (this.lsDetails.state?.status == 'NOT_STARTED' || !this.lsDetails.state?.startedAt) {
      return this.getScheduledStartTime()
    }

    return this.lsDetails.state.startedAt
  }

  getEndTime() {
    if (!this.lsDetails || !this.lsDetails.state) return -1

    if (this.lsDetails.state?.status != 'ENDED' || !this.lsDetails.state?.endedAt) {
      return this.getScheduledStartTime() + this.lsDetails.expectedDuration*60*1000
    }

    return this.lsDetails.state.endedAt
  }

  isLSessionEnded() {
    return this.lsDetails.state?.status == 'ENDED'
  }

  getActualDuration() {
    let startTime = this.getStartTime()
    let endTime = this.getEndTime()

    if (startTime < 0 || endTime < 0) return '--'

    let duration = moment.duration(endTime - startTime, 'milliseconds')
    return this.displayTime(duration)
  }

  expectedDuration(): string {
    return moment.duration(this.lsDetails.expectedDuration, 'minute').humanize()
  }

  displayTime(duration: Duration, trim: string | false | string[] = "large") : string {
    if (duration.years() || duration.months() || duration.days()) return duration.humanize()
    else return duration.format("hh:mm:ss[s]", {trim: trim})
  }

  timeLearning() {
    if (this.isEnd()) {
      let duration = moment.duration(this.lsDetails.state?.endedAt - this.lsDetails.state?.startedAt, "millisecond")
      return this.displayTime(duration)
    }
    let duration = moment.duration(this.now - this.lsDetails.state?.startedAt, "millisecond")
    return this.displayTime(duration, false)
  }

  timeToStart() {
    let duration =  moment.duration(this.getScheduledStartTime() - this.now)
    return this.displayTime(duration, false)
  }

  timeLate() {
    let duration
    if (this.isTeacherLate()) duration = moment.duration(this.now - this.getScheduledStartTime(), "millisecond")
    else if (this.isStudentLate()) duration = moment.duration(this.now - this.lsDetails.state.startedAt, "millisecond")
    else return "--"
    return this.displayTime(duration)
  }

  time(): string {
    if (this.isLate()) return this.timeLate()
    else if (this.isUpcoming()) return this.timeToStart()
    else return this.timeLearning()
  }

  isUpcoming() {
    return this.lsDetails.state?.status == "NOT_STARTED" && this.now < this.getScheduledStartTime()
  }

  startIn24h(): boolean {
    return this.lsDetails.state?.status == "NOT_STARTED" && this.getScheduledStartTime() - this.now <= 24*60*60*1000 && this.getScheduledStartTime() - this.now > 0
  }

  startAfter24h(): boolean {
    return this.lsDetails.state?.status == "NOT_STARTED" && this.getScheduledStartTime() - this.now > 24*60*60*1000
  }

  isEnd(): boolean {
    return this.lsDetails.state?.status == "ENDED"
  }

  isGoingOn(): boolean {
    return this.lsDetails.state?.status == "STARTED"
  }

  isTeacherLate(): boolean {
    return this.isOwn() && this.lsDetails.state?.status == "NOT_STARTED" && this.now > this.getScheduledStartTime()
  }

  isStudentLate(): boolean {
    return !this.isOwn() && this.lsDetails.state?.status == "NOT_STARTED" && this.now > this.getScheduledStartTime()
  }

  isLate(): boolean {
    return this.isTeacherLate() || this.isStudentLate()
  }

  isRegistered(): boolean {
    return !this.isOwn() && this.curMember?.regStatus == "REGISTERED"
  }

  isOwn(): boolean {
    return this.lsDetails.creatorId == this.loggedInUser?.id
  }

  getScheduledStartTime() {
    return (this.lsDetails.startDate + this.lsDetails.startTime)*1000
  }
}
