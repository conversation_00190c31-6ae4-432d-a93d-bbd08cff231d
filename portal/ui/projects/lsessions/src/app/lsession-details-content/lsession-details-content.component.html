<div class="row ls-details-content">
  <div class="col-md-auto col-xl-auto text-center me-4 ls-details-img">
    <img [attr.src]="lsDetails.imgUrl" alt="">
  </div>
  <div class="col-md-6 col-xl-8 ls-details-tabs">
    <div class="d-flex">
      <div class="ls-details-tabs-title d-inline-block">
        <h2>{{subjects[lsDetails.subject]}} - {{grades[lsDetails.grade]}} - {{lsDetails.title}}</h2>
      </div>
      <div>
        <div *ngIf="isWaitingConfirm()" class="status-tag status-tag-confirm ms-2"><span class="status-tag-content">Chờ xác nhận</span></div>
        <div *ngIf="isRegistered()" class="status-tag status-tag-registered ms-2"><span class="status-tag-content">Đ<PERSON> đăng ký</span></div>
        <div *ngIf="isRejected()" class="status-tag status-tag-rejected ms-2"><span class="status-tag-content">Đ<PERSON> từ chối</span></div>
        <div *ngIf="isLoggedIn() && isFullSlot()" class="status-tag status-tag-full ms-2"><span class="status-tag-content">Hết chỗ</span></div>
      </div>
    </div>
    <div class="ls-details-tabs-nav">
      <ul class="nav mt-3">
        <li class="nav-item">
          <a class="nav-link p-0 pb-1" aria-current="page" (click)="selectedTab='general-tab'">
            <span [class.selected-tab]="selectedTab=='general-tab'">Tổng quan</span>
          </a>
          <hr class="m-0" *ngIf="selectedTab=='general-tab'">
        </li>
        <span class="border-start mx-3 vertical-tab-line"></span>
        <li class="nav-item">
          <a class="nav-link p-0 pb-1" (click)="selectedTab='description-tab'">
            <span [class.selected-tab]="selectedTab=='description-tab'">Mô tả</span>
          </a>
          <hr class="m-0" *ngIf="selectedTab=='description-tab'">
        </li>
        <span class="border-start mx-3 vertical-tab-line"></span>
        <li class="nav-item">
          <a class="nav-link p-0 pb-1" (click)="selectedTab='document-tab'">
            <span [class.selected-tab]="selectedTab=='document-tab'">Tài liệu</span>
          </a>
          <hr class="m-0" *ngIf="selectedTab=='document-tab'">
        </li>
        <span class="border-start mx-3 vertical-tab-line" *ngIf="isCreator()"></span>
        <li class="nav-item" *ngIf="isCreator()">
          <a class="nav-link p-0 pb-1" (click)="selectedTab='member-tab'">
            <span [class.selected-tab]="selectedTab=='member-tab'">Học viên</span>
          </a>
          <hr class="m-0" *ngIf="selectedTab=='member-tab'">
        </li>
        <span class="border-start mx-3 vertical-tab-line" *ngIf="isCreator()"></span>
        <li class="nav-item" *ngIf="isCreator()">
          <a class="nav-link p-0 pb-1" (click)="selectedTab='settings-tab'">
            <span [class.selected-tab]="selectedTab=='settings-tab'">Cài đặt</span>
          </a>
          <hr class="m-0" *ngIf="selectedTab=='settings-tab'">
        </li>
      </ul>
    </div>
    <div class="ls-details-tabs-content border mt-2 mb-4 overflow-auto" [ngSwitch]="selectedTab">
      <ls-general-tab *ngSwitchDefault [lsDetails]="lsDetails" [creator]="creator" [members]="members" [settings]="settings"></ls-general-tab>
      <ls-description-tab *ngSwitchCase="'description-tab'" [lsDetails]="lsDetails"></ls-description-tab>
      <ls-document-tab *ngSwitchCase="'document-tab'" [documents]="documents"></ls-document-tab>
      <ls-member-list *ngSwitchCase="'member-tab'" [members]="members"></ls-member-list>
      <ls-settings-tab *ngSwitchCase="'settings-tab'" [settings]="settings"></ls-settings-tab>
    </div>
    <div class="ls-details-tabs-buttons" *ngIf="!isPreview">
      <a class="btn btn-primary btn-sm" target="_blank" role="button" *ngIf="isCreator() || isSentRegister(); else register"
         href="/classrooms/{{lsDetails.id}}">
        <span *ngIf="!isEnd()">Vào học</span>
        <span *ngIf="isEnd()">Xem lại</span>
      </a>
      <ng-template #register>
        <a class="btn btn-primary btn-sm" role="button" (click)="sendRegister()" [ngClass]="{'disabled': isFullSlot()}">Đăng ký</a>
      </ng-template>
      <a class="btn btn-outline-primary btn-sm" role="button" (click)="share()">Chia sẻ</a>
      <a class="btn btn-outline-primary btn-sm" role="button" *ngIf="isCreator()" [routerLink]="'/editsession/'+lsDetails.id">Chỉnh sửa</a>
      <a class="btn btn-outline-primary btn-sm" role="button" *ngIf="isCreator()">Hủy buổi học</a>
      <a class="btn btn-outline-primary btn-sm" role="button" *ngIf="!isCreator() && isSentRegister()" (click)="sendUnregister()">Hủy đăng ký</a>
    </div>
  </div>
</div>
