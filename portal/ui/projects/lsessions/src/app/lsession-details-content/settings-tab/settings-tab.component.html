<div class="p-0 m-3 settings-tab-content">
  <div class="masonry">
    <div class="masonry-brick" *ngIf="settings.camera">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span>Camera
      </div>
    </div>
    <div class="masonry-brick" *ngIf="settings.maxMember > 0">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span>Tối đa {{settings.maxMember}} học viên
      </div>
    </div>
    <div class="masonry-brick" *ngIf="settings.audio">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span>Audio
      </div>
    </div>
    <div class="masonry-brick" *ngIf="settings.chattingFilter">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span><PERSON><PERSON><PERSON> từ xấu / phân biệt chủng tộc
      </div>
    </div>
    <div class="masonry-brick" *ngIf="settings.chatBox">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span>Hộp thoại chat
      </div>
    </div>
    <div class="masonry-brick" *ngIf="settings.rejectLateMemberAfterMinute > 0">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span>Sau {{settings.rejectLateMemberAfterMinute}} phút, học viên vào trễ không được tham gia
      </div>
    </div>
    <div class="masonry-brick" *ngIf="settings.autoRecord">
      <div class="d-flex mb-2 masonry-brick">
        <span class="vcon-general vcon_general_yes me-2"></span>Tự động ghi lớp học
      </div>
    </div>
  </div>
  <div class="d-flex align-items-center whiteboard-options" [ngSwitch]="settings.boardType">
    <span>Tùy chọn bảng trắng</span>
    <i class="vcon-general vcon_wb_bg_white-grid ms-2" *ngSwitchCase="'WHITE_GRID'"></i>
    <i class="vcon-general vcon_wb_bg_black-grid ms-2" *ngSwitchCase="'BLACK_GRID'"></i>
    <i class="vcon-general vcon_wb_bg_black ms-2" *ngSwitchCase="'BLACK'"></i>
    <i class="vcon-general vcon_wb_bg_white ms-2" *ngSwitchCase="'WHITE'"></i>
  </div>
</div>
