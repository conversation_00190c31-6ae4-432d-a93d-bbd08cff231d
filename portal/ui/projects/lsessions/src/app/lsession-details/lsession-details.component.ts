import {Component, Inject, OnInit} from '@angular/core';
import {ActivatedRoute, ParamMap, Router} from '@angular/router';
import {Location} from '@angular/common'
import {ConfigService, Document, DocumentService, LSessionDetails, LSessionMemberViewModel, LSessionService,
  LSessionSettings, LsessionSummary,  MemberRegistrationAction, MemberService, UserProfile, UserService, USER_PROFILE,
  AuthflowService, ENVIRONMENT
} from '@viclass/portal.common';
import {of} from 'rxjs';
import {map, switchMap, tap} from 'rxjs/operators';
import * as _ from "lodash"
import Swal from 'sweetalert2'

@Component({
  selector: 'ls-lsession-details',
  templateUrl: './lsession-details.component.html',
  styleUrls: ['./lsession-details.component.sass'],
})
export class LsessionDetailsComponent implements OnInit {

  lsDetails: LSessionDetails
  lsSettings : LSessionSettings
  creator: UserProfile
  documents: Document[] = []
  members: LSessionMemberViewModel[] = []
  curMember: LSessionMemberViewModel

  subjects: any;
  grades : any;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private location: Location,
    private userService: UserService,
    private lsService: LSessionService,
    private docService: DocumentService,
    private memService: MemberService,
    private configService : ConfigService,
    @Inject(USER_PROFILE) public loggedInUser : UserProfile,
    @Inject(ENVIRONMENT) public environment: any,
    private authService: AuthflowService,
  ) {
    this.route.queryParams.subscribe(params => {
      let reg: MemberRegistrationAction = params.reg as MemberRegistrationAction
      if (!this.isLoggedIn() && reg) {
        this.authService.requireLogin()
        throw new Error("require login")
      }
    })
    this.subjects = _.reduce(this.configService.subjects(), (sum : any, item : any) => { sum[item._id] = item.value; return sum; }, {})
    this.grades = _.reduce(this.configService.grades(), (sum : any, item : any) => { sum[item._id] = item.value; return sum; }, {})
  }

  ngOnInit(): void {
    this.route.paramMap.pipe(
      // load lsession
      switchMap((params: ParamMap) => {
        let id = params.get('id')
        return this.lsService.getLSessionDetailsById(id).pipe(
          tap(ls => {this.lsDetails = ls})
        )
      }),
      // load settings
      switchMap((ls: LSessionDetails) => {
        return this.lsService.getClassroomSettings(ls.id).pipe(
          tap(settings => {this.lsSettings = settings}),
          map(_ => ls)
        )
      }),
      // load creator
      switchMap((ls: LSessionDetails) => {
        if (this.loggedInUser && ls && this.loggedInUser.id == ls.creatorId) {
          this.creator = this.loggedInUser
          return of(ls)
        }

        return this.userService.briefProfiles(ls.creatorId).pipe(
          tap(u => {this.creator = u[0]}),
          map(_ => ls)
        )
      }),
      // load members
      switchMap((ls: LSessionDetails) => {
        if (!this.isLoggedIn()) return of(ls)
        return this.memService.getMembersWithProfile(ls.id).pipe(
          tap(mems => {
            this.members = mems.filter(m=>m.profile.id != this.creator.id)
            this.curMember = mems.find(m => m.profile.id == this.loggedInUser.id && m.profile.id != this.creator.id)
          })
        )
      }),
      switchMap(() => {
        return this.route.queryParams
      })
    ).subscribe(params => {
      let reg: MemberRegistrationAction = params.reg as MemberRegistrationAction
      this.router.navigate([], {queryParams: {'reg': null}, queryParamsHandling: 'merge', replaceUrl: true})
      switch (reg) {
        case 'register':
          if (this.curMember?.id && ['REJECTED', 'CANCELLED'].includes(this.curMember?.regStatus)) {
            this.reRegisterThisSession()
            break
          }
          if (this.curMember?.regStatus == 'WAITING_CONFIRMED') {
            Swal.fire({
              icon: 'success',
              text: 'Yêu cầu tham gia buổi học đã được gửi thành công đến người tạo lớp'
            })
            break
          }
          if (this.curMember?.regStatus == 'REGISTERED') {
            Swal.fire({
              icon: 'success',
              text: 'Yêu cầu tham gia buổi học đã được chấp thuận'
            })
            break
          }
          this.registerThisSession()
          break
        case 'unregister':
          this.unregisterThisSession()
          break
        case 're-register':
          this.reRegisterThisSession()
          break
        default:
          break
      }
    }, e => {
      console.error(e)
    })
  }

  private registerThisSession() {
    if (this.isCreator()) {
      // do nothing if this lsession is own
      return
    }
    try {
      this.memService.register(this.lsDetails.id).pipe(
        switchMap(resp => {
          Swal.fire({
            icon: 'success',
            text: 'Yêu cầu tham gia buổi học đã được gửi thành công đến người tạo lớp'
          })
          return this.memService.getMemberWithId(resp.lsmId)
        }),
      ).subscribe(mem => {
        this.members.push(mem)
        this.curMember = mem
      }, e => {
        Swal.fire({
          icon: 'error',
          text: 'Yêu cầu tham gia buổi học không thành công'
        })
      })
    } catch (e) {
      Swal.fire({
        icon: 'error',
        text: 'Yêu cầu tham gia buổi học không thành công'
      })
    }
  }

  private reRegisterThisSession() {
    if (this.isCreator()) {
      // do nothing if this lsession is own
      return
    }
    try {
      this.memService.register(this.lsDetails.id).subscribe(
        res => {
          this.curMember.regStatus = "WAITING_CONFIRMED"
          Swal.fire({
            icon: 'success',
            text: 'Yêu cầu tham gia buổi học đã được gửi thành công đến người tạo lớp'
          })
        }, e => {
          Swal.fire({
            icon: 'error',
            text: 'Yêu cầu tham gia buổi học không thành công'
          })
        })
    } catch (e) {
      Swal.fire({
        icon: 'error',
        text: 'Yêu cầu tham gia buổi học không thành công'
      })
    }
  }

  private unregisterThisSession() {
    if (this.isCreator()) {
      // do nothing if this lsession is own
      return
    }

    Swal.fire({
      icon: "warning",
      title: 'Tiếp tục hủy đăng ký buổi học?',
      showCancelButton: true,
      cancelButtonText: 'Thoát',
      confirmButtonText: `Hủy đăng ký`
    }).then((result) => {
      if (result.isConfirmed) {
        this.doUnregisterThisSession()
      }
    })
  }

  private doUnregisterThisSession() {
    try {
      if (this.curMember) {
        this.memService.unregister(this.curMember.id).subscribe(
          resp => {
            if (this.curMember.regStatus == "REGISTERED") {
              this.lsDetails.state.registered -= 1
            }
            this.curMember.regStatus = "CANCELLED"
            Swal.fire({
              icon: 'success',
              text: 'Đã hủy đăng ký buổi học thành công'
            })
          }, e => {
            Swal.fire({
              icon: 'error',
              text: e?.error?.message
            })
          })
      }
    } catch (e) {
      Swal.fire({
        icon: 'error',
        text: 'Hủy đăng ký buổi học không thành công'
      })
    }
  }

  sendRegistrationThisSession(event : MemberRegistrationAction) {
    if (!this.isLoggedIn()) {
      if (window.location.search) {
        this.authService.requireLogin(`${window.location.href}&reg=${event}`)
      } else {
        this.authService.requireLogin(`${window.location.href}?reg=${event}`)
      }
    }
    if (event == "register") this.registerThisSession()
    else if (event == 're-register') this.reRegisterThisSession()
    else if (event == 'unregister') this.unregisterThisSession()
  }

  sendRegistrationLSession(event : MemberRegistrationAction, summary: LsessionSummary) {
    this.router.navigate([`/lsession-details/${summary.id}`], {queryParams: {reg: event}})
  }

  isLoggedIn(): boolean {
    return this.loggedInUser?.id && true
  }

  isCreator(): boolean {
    return this.isLoggedIn() && this.loggedInUser.id == this.creator.id
  }

  summaries: any[] = []
}
