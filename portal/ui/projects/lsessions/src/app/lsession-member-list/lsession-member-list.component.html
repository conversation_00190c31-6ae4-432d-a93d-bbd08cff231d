<div class="container p-0">
  <div class="row m-0">
    <div class="col-3 p-0 member-tab-nav-side">
      <div
        class="align-items-center d-flex p-3 pointer"
        (click)="selectedSubTab = 'total-tab'"
      >
        <span class="vcon-general vcon_user_all-students me-2"></span>Tổng học
        viên
      </div>
      <div
        class="align-items-center d-flex p-3 pointer"
        (click)="selectedSubTab = 'confirmed-tab'"
      >
        <span class="vcon-general vcon_user_students me-2"></span>Đã đăng ký
      </div>
      <div
        class="align-items-center d-flex p-3 pointer"
        (click)="selectedSubTab = 'waiting-tab'"
      >
        <span class="vcon-general vcon_user_waiting-students me-2"></span>Chờ
        xác nhận
      </div>
    </div>
    <div class="col-9 p-0 member-tab-content">
      <div
        class="member-tab-header p-3 ps-0 ms-4 border-bottom"
        [ngSwitch]="selectedSubTab"
      >
        <span *ngSwitchCase="'total-tab'"
          >Tổng học viên: {{ getMembersBaseOnSelected().length }}</span
        >
        <span *ngSwitchCase="'confirmed-tab'"
          >Danh sách học viên đã đăng ký:
          {{ getMembersBaseOnSelected().length }}</span
        >
        <span *ngSwitchCase="'waiting-tab'"
          >Danh sách học viên chờ xác nhận:
          {{ getMembersBaseOnSelected().length }}</span
        >
      </div>
      <div class="overflow-auto member-tab-body p-3 ps-0 ms-4">
        <div
          class="row row-cols-3 m-0"
          *ngIf="getMembersBaseOnSelected().length; else elseBlock"
        >
          <div
            class="col p-0 mb-2"
            *ngFor="let member of getMembersBaseOnSelected()"
          >
            <ls-lsession-member-tag
              [member]="member"
              (change)="send($event, member)"
            ></ls-lsession-member-tag>
          </div>
        </div>
        <ng-template #elseBlock>
          <div
            class="member-tab-content-empty d-flex align-items-center justify-center"
          >
            <span class="vcon-general vcon_general_empty-document me-3"></span>
            <span>Chưa có học viên</span>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
