import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LSessionMemberViewModel, LSRegStatus } from '@viclass/portal.common';
import { MemberTagAction } from './lsession-member-tag/lsession-member-tag.component';

@Component({
  selector: 'ls-member-list',
  templateUrl: './lsession-member-list.component.html',
  styleUrls: ['./lsession-member-list.component.sass']
})
export class LSessionMemberListComponent implements OnInit {

  @Input()
  isEditing : boolean = false

  @Input() members: LSessionMemberViewModel[]
  selectedSubTab: "total-tab" | "confirmed-tab" | "waiting-tab" = "total-tab"

  @Output()
  approve = new EventEmitter<LSessionMemberViewModel>()

  @Output()
  reject = new EventEmitter<LSessionMemberViewModel>()

  constructor() { }

  ngOnInit(): void {
  }

  getMembersBaseOnSelected(): LSessionMemberViewModel[] {
    switch (this.selectedSubTab) {
      case 'total-tab': return this.members.filter(m => m.regStatus != "REJECTED" && m.regStatus != "CANCELLED")  // only show non-rejected members
      case 'confirmed-tab': return this.members.filter(m => m.regStatus == "REGISTERED")
      case 'waiting-tab': return this.members.filter(m => m.regStatus == "WAITING_CONFIRMED")
    }
  }

  send(event : MemberTagAction, member : LSessionMemberViewModel) {
    if (event == "approve") this.approve.emit(member)
    else if (event == 'reject') this.reject.emit(member)
  }
}
