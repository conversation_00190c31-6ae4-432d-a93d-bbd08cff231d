<div class="profile-avatar lsession-member-tag align-items-center d-flex" [ngClass]="{'editable': isEditing }">
    <img class="member-avatar border rounded-circle me-2" [attr.src]="member.profile.avatarUrl">
    <span class="vcon-general vcon_session_time waiting-icon" *ngIf="isWaitingStateMember()"></span>
    <div class='me-auto'>
        <div>
            <span class="username" [ngClass]="{'username-black': !isWaitingStateMember(), 'username-orange': isWaitingStateMember()}">{{member.profile.username}}</span>
        </div>
        <div>
            <span>{{ getTimeSinceRegistration() }}</span>
        </div>
    </div>
    <div class="actions-list">
      <i class="vcon-general vcon_general_yes me-2 action" *ngIf="isWaitingStateMember()" (click)="sendApprove()"></i>
      <i class="vcon-general vcon_delete me-2 action" (click)="sendReject()"></i>
    </div>
</div>
