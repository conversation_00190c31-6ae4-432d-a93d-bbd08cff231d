import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LSessionMemberViewModel } from '@viclass/portal.common';
import * as moment from 'moment';
import { LSessionMemberListComponent } from '../lsession-member-list.component';


export type MemberTagAction = "approve" | "reject"

/**
 * Display a tag with editing capability
 */
@Component({
  selector: 'ls-lsession-member-tag',
  templateUrl: './lsession-member-tag.component.html',
  styleUrls: ['./lsession-member-tag.component.sass']
})
export class LSessionMemberTagComponent implements OnInit {

  @Input()
  member : LSessionMemberViewModel

  @Output()
  change = new EventEmitter<MemberTagAction>()

  isEditing : boolean
  now : number
  refreshInterval: any;

  constructor(private mbList : LSessionMemberListComponent) {

  }

  ngOnInit(): void {
    this.isEditing = this.mbList.isEditing
    this.now = Date.now();
    this.refreshInterval = setInterval(() => { this.now = Date.now() }, 1000)
  }

  ngOnDestroy() {
    clearInterval(this.refreshInterval)
  }

  isWaitingStateMember(): boolean {
    return this.member.regStatus == "WAITING_CONFIRMED"
  }

  getTimeSinceRegistration() : string {
    return moment(this.member.regTime).from(Date.now())
  }

  sendApprove() {
    this.change.emit("approve")
  }

  sendReject() {
    this.change.emit("reject")
  }
}
