import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { LSessionsComponent } from './lsessions.component';

describe('LSessionsComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule
      ],
      declarations: [
        LSessionsComponent
      ],
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(LSessionsComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it(`should have as title 'lsessions'`, () => {
    const fixture = TestBed.createComponent(LSessionsComponent);
    const app = fixture.componentInstance;
    expect(app.title).toEqual('lsessions');
  });

  it('should render title', () => {
    const fixture = TestBed.createComponent(LSessionsComponent);
    fixture.detectChanges();
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.content span').textContent).toContain('lsessions app is running!');
  });
});
