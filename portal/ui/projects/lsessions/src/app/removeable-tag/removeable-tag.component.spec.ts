import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RemoveableTagComponent } from './removeable-tag.component';

describe('RemoveableTagComponent', () => {
  let component: RemoveableTagComponent;
  let fixture: ComponentFixture<RemoveableTagComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ RemoveableTagComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RemoveableTagComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
