<ng-template fflow let-fum [fflowBy]="buildForm" [fflowSubmit]="submitSearch" [fflowNoNav]="false" [fflowNav]="processNavigation" #formFlow="fflow">
  <form class="searchbar d-flex" [formGroup]="fum">
      <i class="vcon-general vcon_general_search"></i>
      <ls-removeable-tag class="pill-tag" *ngFor="let l of tags; index as i" [index]="i" [label]="l" (onRemove)="removeTag($event)"></ls-removeable-tag>
      <input #textinput formControlName="keywords" class="searchbar-input w-100" (keyup)="checkEnter($event, formFlow)"/>
      <div class="searchbar-actions d-flex">
          <a class="vcon-link"><i class="vcon-general vcon_delete" (click)="clearTags(); textinput.value = ''"></i></a>
          <common-date-range-picker class="border-start"></common-date-range-picker>
          <!-- <a class="vcon-link border-start">
              <i class="vcon-general vcon_calendar" (click)="picker.open()">
                  <div class="searchbar-datepicker hide-datepicker">
                      <mat-date-range-input [rangePicker]="picker">
                          <input matStartDate placeholder="Start date">
                          <input matEndDate placeholder="End date">
                      </mat-date-range-input>
                      <mat-date-range-picker #picker>
                          <mat-date-range-picker-actions>
                              <a class="timebutton" [ngClass]="{'active' : timeFrame == 'morning'}" (click)="selectTimeFrame('morning')"><i class="vcon-general vcon_calendar_morning"></i><span>Sáng</span></a>
                              <a class="timebutton" [ngClass]="{'active' : timeFrame == 'afternoon'}" (click)="selectTimeFrame('afternoon')"><i class="vcon-general vcon_calendar_afternoon"></i><span>Chiều</span></a>
                              <a class="timebutton" [ngClass]="{'active' : timeFrame == 'night'}" (click)="selectTimeFrame('night')"><i class="vcon-general vcon_calendar_night"></i><span>Tối</span></a>
                          </mat-date-range-picker-actions>
                      </mat-date-range-picker>
                  </div>
              </i>
          </a> -->
          <a class="vcon-link border-start">
              <i class="vcon-general vcon_general_filterr" (click)="isFilterOpen = !isFilterOpen" >
                  <div class="searchbar-filter-origin hide-datepicker" cdkOverlayOrigin #filterTrigger="cdkOverlayOrigin"></div>
                  <ng-template
                      cdkConnectedOverlay
                      [cdkConnectedOverlayOrigin]="filterTrigger"
                      [cdkConnectedOverlayOpen]="isFilterOpen"
                      [cdkConnectedOverlayHasBackdrop]="true"
                      cdkConnectedOverlayBackdropClass="mat-overlay-transparent-backdrop"
                      cdkConnectedOverlayTransformOriginOn=".searchbar-filter-origin"
                      [cdkConnectedOverlayPush]="true"
                      (backdropClick)="isFilterOpen = false">
                      <div class="searchbar-filter mat-elevation-z3">
                          <div>Loại buổi học</div>
                          <div class="d-flex flex-wrap ms-4">
                              <div class="pill-tag"><div><span>Tạo bởi tôi</span></div></div>
                              <div class="pill-tag active"><div><span>Còn chỗ</span></div></div>
                              <div class="pill-tag active"><div><span>Đã diễn ra</span></div></div>
                              <div class="pill-tag active"><div><span>Đang diễn ra</span></div></div>
                              <div class="pill-tag"><div><span>Sắp diễn ra</span></div></div>
                          </div>
                          <hr/>
                          <div>Trạng thái đăng ký</div>
                          <div class="d-flex flex-wrap ms-4">
                              <div class="pill-tag"><div><span>Chưa đăng ký</span></div></div>
                              <div class="pill-tag"><div><span>Chờ xác nhận</span></div></div>
                              <div class="pill-tag"><div><span>Đã đăng ký</span></div></div>
                              <div class="pill-tag active"><div><span>Đã từ chối</span></div></div>
                          </div>
                          <hr/>
                          <div>Số học viên tối đa</div>
                          <div class="d-flex flex-wrap ms-4">
                              <div class="pill-tag"><div><span>Dưới 20</span></div></div>
                              <div class="pill-tag active"><div><span>20 - 30</span></div></div>
                              <div class="pill-tag"><div><span>Trên 30</span></div></div>
                          </div>
                          <hr/>
                          <div>Thời lượng</div>
                          <div class="d-flex flex-wrap ms-4">
                              <div class="pill-tag"><div><span>Dưới 60 phút</span></div></div>
                              <div class="pill-tag active"><div><span>60 - 90 phút</span></div></div>
                              <div class="pill-tag"><div><span>Trên 90 phút</span></div></div>
                          </div>
                          <hr/>
                          <div>Cấp lớp</div>
                          <div class="d-flex flex-wrap ms-4">
                              <div class="pill-tag"><div><span>Lớp 6</span></div></div>
                              <div class="pill-tag"><div><span>Lớp 7</span></div></div>
                              <div class="pill-tag"><div><span>Lớp 8</span></div></div>
                              <div class="pill-tag"><div><span>Lớp 9</span></div></div>
                              <div class="pill-tag"><div><span>Lớp 10</span></div></div>
                              <div class="pill-tag"><div><span>Lớp 11</span></div></div>
                              <div class="pill-tag"><div><span>Lớp 12</span></div></div>
                          </div>
                          <hr/>
                          <div>Môn học</div>
                          <div class="d-flex flex-wrap ms-4">
                              <div class="pill-tag"><div><span>Toán</span></div></div>
                              <div class="pill-tag active"><div><span>Văn</span></div></div>
                          </div>
                      </div>
                  </ng-template>
              </i>
          </a>
      </div>
  </form>
</ng-template>
