<div class="actionbar">
    <div class="col">
        <div class="content d-flex align-items-center">
            <div class="me-auto">
                ViClass / <a class="active">Tạo buổi học</a>
            </div>
            <div>
                <button class="btn btn-primary btn-sm ms-1" (click)="sessionForm.onPreview()" [disabled]="!sessionForm.canSubmit() || !sessionForm.dirty()"
                  [ngClass]="{'preview-on': sessionForm.previewMode}">
                  <i class="vcon-general p-0" [ngClass]="sessionForm.previewMode ? 'vcon_general_preview_view' : 'vcon_general_preview_hide'"></i>
                </button>
                <button class="btn btn-primary btn-sm ms-1" [disabled]="!sessionForm.canSubmit()" (click)="sessionForm.submitForm()">Tạo mới</button>
                <button class="btn btn-outline-primary btn-sm ms-1" routerLink="/">Hủy</button>
            </div>
        </div>
    </div>
</div>
<div class="form-title mt-4 text-center" [ngClass]="sessionForm.previewMode ? 'd-none' : 'd-block'">Tạo buổi học</div>
<ls-session-form #sessionForm (submit)="submitNewSession($event)" [initial]="initial"></ls-session-form>
