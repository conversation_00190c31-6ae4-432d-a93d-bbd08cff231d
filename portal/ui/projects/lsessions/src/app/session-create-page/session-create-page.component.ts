import {Component, Inject, OnInit, ViewChild} from '@angular/core';
import {Router} from '@angular/router';
import {
  defaultSessionDetails,
  defaultSessionSettings,
  LSessionService,
  SessionFormData,
  USER_PROFILE,
  UserProfile
} from '@viclass/portal.common';
import {of} from 'rxjs';
import {catchError, finalize, map, mergeMap} from 'rxjs/operators';
import {SessionFormComponent} from '../session-form/session-form.component';

@Component({
    selector: 'ls-sessionform',
    templateUrl: './session-create-page.component.html',
    styleUrls: ['./session-create-page.component.sass']
})
export class SessionCreatePageComponent implements OnInit {

    @ViewChild("sessionForm")
    sformComp: SessionFormComponent

    initial: SessionFormData

    private creating = false

    constructor(private lsessionService: LSessionService, @Inject(USER_PROFILE) private loggedIn: UserProfile, private router: Router) {
    }

    ngOnInit(): void {
        this.initial = {
            details: defaultSessionDetails(this.loggedIn.id),
            settings: defaultSessionSettings()
        }
    }

    submitNewSession = (data: SessionFormData) => {
        if (this.creating) return
        this.creating = true

        this.lsessionService.createSession(data).pipe(
            mergeMap(result => {
                let lsessionId = result.lSessionId
                if (!lsessionId) throw new Error("Unable to find created session id")
                else {
                    if (data.imgToUpload) {

                        let imageForm = new FormData()
                        imageForm.append("image", data.imgToUpload)

                        return this.lsessionService.uploadAvatar(imageForm, lsessionId).pipe(map(resp => {
                            return result
                        }), catchError(err => {
                            return of(result)    // if upload not successfully, still redirect to the correct sesssion details
                        }))
                    } else return of(result)
                }
            }),
            finalize(() => this.creating = false),
        ).subscribe(resp => {
            if (resp["lSessionId"]) this.router.navigate(["lsession-details", resp["lSessionId"]])
            else throw new Error("Unable to find session id of the created session")
        })
    }

}
