import * as _ from 'lodash';
import { Observable, of } from 'rxjs';
import {catchError, finalize, map, mergeAll, mergeMap, toArray} from 'rxjs/operators';
import {Component, Inject, Injectable, OnInit} from '@angular/core';
import {ActivatedRoute, ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot} from '@angular/router';
import Swal from "sweetalert2";
import {
  LSessionService, MemberService, SessionFormData, LSessionMemberViewModel, USER_PROFILE, UserProfile
} from '@viclass/portal.common';

// this resolver resolve the necessary data before loading the edit page
@Injectable({
  providedIn: 'root'
})
export class SessionFormResolver implements Resolve<SessionFormData> {

  constructor(private sessionService : LSessionService, private memberService : MemberService) {

  }

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): SessionFormData | Observable<SessionFormData> | Promise<SessionFormData> {
    if (route.paramMap.get("id")) {
      let id = route.paramMap.get("id")
      return of(
        this.sessionService.getLSessionDetailsById(id).pipe(map(data => ( { details: data } ) )),
        this.sessionService.getClassroomSettings(id).pipe(map(data => ( { settings : data }) )),
        this.memberService.getMembersWithProfile(id).pipe(map(data => ({ members : data } ) ))).pipe(mergeAll(), toArray(),
          map((arr : any[]) => {
            let result = {}
            _.merge(result, ...arr)  // combine fragments into the complete session form
            let r: SessionFormData = result as SessionFormData
            r.members = r.members.filter(m=>m.profile.id != r.details.creatorId)
            return r as SessionFormData
          })
        )

    } else {
      console.log("Error. Session ID is not present. Go back to homepage.")
      window.location.href = "/"
      return null
    }
  }
}

@Component({
  selector: 'ls-session-edit-page',
  templateUrl: './session-edit-page.component.html',
  styleUrls: ['./session-edit-page.component.sass']
})
export class SessionEditPageComponent implements OnInit {

  sessionFormData: SessionFormData;
  sessionMembers : LSessionMemberViewModel[]
  private updating = false

  constructor(
    @Inject(USER_PROFILE) public userProfile : UserProfile,
    private lsessionService : LSessionService,
    private memberService : MemberService,
    private router : Router, private route : ActivatedRoute
  ) {
    if (route.snapshot.data["sessionFormData"]) {
      this.sessionFormData = route.snapshot.data["sessionFormData"]
      this.sessionMembers = this.sessionFormData.members
      delete this.sessionFormData.members // member is not edited as part of the form
    } else {
      throw new Error("Unable to resolve data for the session.")
    }
  }

  ngOnInit(): void {
    if (this.userProfile.id != this.sessionFormData.details.creatorId) {
      this.router.navigate(['/lsession-details', this.sessionFormData.details.id])
      throw new Error("access denied")
    }
  }

  isLimitMember(maxMember ?: number): boolean {
    return maxMember && maxMember > 0
  }

  isOverMember(maxMember ?: number): boolean {
    return this.isLimitMember(maxMember) && maxMember < this.sessionMembers.filter(mb => mb.regStatus == 'REGISTERED').length
  }

  submitEditSession = (data : SessionFormData) => {
    if (this.updating) return;
    this.updating = true

    if (this.isOverMember(data.settings.maxMember)) {
      Swal.fire({
        icon: 'error',
        text: 'Buổi học đã hết chỗ, vui lòng cập nhật lại số học viên tối đa'
      })
      this.updating = false
      return
    }
    this.lsessionService.updateLSession(data).pipe(
      mergeMap(result => {
      let lsessionId = result.lSessionId
      if (!lsessionId) throw new Error("Unable to find created session id")
      else {
        if (data.imgToUpload) {

          let imageForm = new FormData()
          imageForm.append("image", data.imgToUpload)

          return this.lsessionService.uploadAvatar(imageForm, lsessionId).pipe(map(resp => {
            return result
          }), catchError(err => {
            return of(result)    // if upload not successfully, still redirect to the correct sesssion details
          }))
        } else return of(result)
      }
    }),
      finalize(() => {this.updating = false})
    ).subscribe(resp => {
      if (resp["lSessionId"]) this.router.navigate(["lsession-details", resp["lSessionId"]])
      else throw new Error("Unable to find session id of the created session")
    })
  }

  approveMember(member : LSessionMemberViewModel) {
    if (this.isOverMember(this.sessionFormData.settings.maxMember)) {
      Swal.fire({
        icon: 'error',
        text: 'Buổi học đã hết chỗ, vui lòng cập nhật lại số học viên tối đa'
      })
      return
    }
    this.lsessionService.approveRegistration(member.id).subscribe(
      () => {
        member.regStatus = "REGISTERED"
      }, (e: any) => {
        Swal.fire({
          icon: 'error',
          text: e?.error?.message
        })
      })
  }

  rejectMember(member : LSessionMemberViewModel) {
    this.lsessionService.rejectRegistration(member.id).subscribe(
      () => {
        member.regStatus = "REJECTED"
      }, (e: any) => {
        Swal.fire({
          icon: 'error',
          text: e?.error?.message
        })
      })
  }

}
