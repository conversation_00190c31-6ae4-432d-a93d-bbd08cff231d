import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  Output,
  ViewChild,
} from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  BoardType,
  DateTimeSelected,
  ErrorModel,
  FormBuildingResult,
  FormCreator,
  FormFlowSubmitEvent,
  SessionFormData,
  UserProfile,
  USER_PROFILE,
  LSessionDetails,
  FormFlowDirective,
  LSessionSettings,
  LSessionMemberViewModel,
  ConfigService,
} from "@viclass/portal.common";
import * as moment from "moment";
import * as _ from "lodash";

/**
 * Contains the common form used by both create and edit session page.
 *
 */

@Component({
  selector: "ls-session-form",
  templateUrl: "./session-form.component.html",
  styleUrls: ["./session-form.component.sass"],
})
export class SessionFormComponent implements AfterViewInit {
  @Input()
  initial: SessionFormData;

  initialAvatarUrl: string;

  formError: ErrorModel;

  form: UntypedFormGroup;

  isStudentLimit: boolean = false;
  isNoEntryTimeLimit: boolean = true;

  // declaration for errormodels
  titleError: ErrorModel;
  imgUrlError: ErrorModel;
  gradeError: ErrorModel;
  subjectError: ErrorModel;
  expectedDurationError: ErrorModel;
  descriptionError: ErrorModel;
  imageToUpload: File;

  mon_hoc = "";
  khoi_lop = "";

  @Output()
  submit = new EventEmitter<SessionFormData>();

  @ViewChild("submitButton")
  element: ElementRef<HTMLButtonElement>;

  @ViewChild("formFlow")
  formFlow: FormFlowDirective;

  @Input()
  isEditing: boolean = false;

  @Input()
  sessionMembers: LSessionMemberViewModel[] = [];

  @Output()
  approveMember = new EventEmitter<LSessionMemberViewModel>();

  @Output()
  rejectMember = new EventEmitter<LSessionMemberViewModel>();

  previewMode: boolean = false;
  lsDetails: LSessionDetails;
  lsSettings: LSessionSettings;

  constructor(
    private fb: UntypedFormBuilder,
    @Inject(USER_PROFILE) private loggedIn: UserProfile,
    public configService: ConfigService
  ) {}

  ngAfterViewInit() {
    console.log("Submit button of session form ", this.element);
  }

  // removeInvitation(tag : RemoveableTagComponent) {
  //   this.invitations.splice(tag.index, 1)
  // }

  buildForm = (data?: SessionFormData): FormBuildingResult => {
    data = data || this.initial;

    console.log("Data to create session form : ", data);

    let details = data.details;

    if (data.settings.maxMember < 0) {
      this.isStudentLimit = false;
      data.settings.maxMember = 20; // set default value if it is chosen
    } else this.isStudentLimit = true;

    if (data.settings.rejectLateMemberAfterMinute < 0) {
      this.isNoEntryTimeLimit = true;
      data.settings.rejectLateMemberAfterMinute = 15; // set default value if it is chosen
    } else this.isNoEntryTimeLimit = false;

    if (!details.creatorId) details.creatorId = this.loggedIn.id;

    if (this.isEditing) {
      this.initialAvatarUrl = this.initial.details.imgUrl; // store the image url first so that we can display it
      this.initial.details.imgUrl = null; // if editting, we can set this to null first so that it is not assigned to the file input and cause error
    }

    let result = new FormCreator(this.fb, data)
      .validators({
        details: {
          subject: [Validators.required],
          grade: [Validators.required],
          title: [Validators.required],
          imgUrl: this.isEditing ? [] : [Validators.required], // if we are editing, we don't have to force imgUrl to be required as it has already been set
          description: [Validators.required],
          expectedDuration: [Validators.required],
          startTime: [Validators.required],
          startDate: [Validators.required],
        },
        settings: {
          maxMember: [Validators.pattern(/^(\-1)|([0-9][0-9]*)$/)],
          rejectLateMemberAfterMinute: [
            Validators.pattern(/^(\-1)|([0-9][0-9]*)*$/),
          ],
        },
        members: {
          __self__: [],
          __ignore_arr__: true,
        },
      })
      .validatorMessages({
        details: {
          subject: { required: "Bắt buộc" },
          grade: { required: "Bắt buộc" },
          title: { required: "Trường này bắt buộc" },
          expectedDuration: { required: "Trường này bắt buộc" },
          startTime: { required: "Trường này bắt buộc" },
          imgUrl: { required: "Bắt buộc" },
          description: {
            required: "Bạn cần nhập vào mô tả của buổi học",
          },
        },
        settings: {
          maxMember: { pattern: "Điền vào một số" },
          rejectLateMemberAfterMinute: { pattern: "Điền vào một số" },
        },
      })
      .build();

    this.form = result.control as UntypedFormGroup;

    return result;
  };

  dateTimePicked(event: DateTimeSelected) {
    let epoch = moment()
      .date(event.day)
      .month(event.month)
      .year(event.year)
      .hour(0)
      .minute(0)
      .second(0)
      .millisecond(0);
    let startDateControl = this.form.get(["details", "startDate"]);
    let dateEpochMillis = epoch.valueOf();
    startDateControl.setValue(dateEpochMillis / 1000);
    startDateControl.markAsDirty();
    let startTimeControl = this.form.get(["details", "startTime"]);
    epoch.hour(event.hour).minute(event.minute);
    let timeEpochMillis = epoch.valueOf() - dateEpochMillis;
    startTimeControl.setValue(timeEpochMillis / 1000);
    startTimeControl.markAsDirty();
  }

  initialPickedTime(): DateTimeSelected {
    if (
      this.initial &&
      this.initial.details.startDate &&
      this.initial.details.startTime
    ) {
      let epoch =
        this.initial.details.startDate + this.initial.details.startTime;
      let m = moment.unix(epoch);
      return {
        day: m.date(),
        month: m.month(),
        year: m.year(),
        hour: m.hour(),
        minute: m.minute(),
        formattedString: "",
      };
    }

    return null;
  }

  selectBoardType(e: BoardType | string) {
    let control = this.form.get(["settings", "boardType"]);
    control.setValue(e);
    control.markAsDirty();
  }

  switchStudentLimit(event: Event) {
    this.isStudentLimit = (event.target as HTMLInputElement).checked;
    let control = this.form.get(["settings", "maxMember"]);
    control.markAsDirty();
  }

  switchNoEntryTimeLimit(event: Event) {
    this.isNoEntryTimeLimit = (event.target as HTMLInputElement).checked;
    let control = this.form.get(["settings", "rejectLateMemberAfterMinute"]);
    control.markAsDirty();
  }

  setImgUrl(event: Event) {
    let fileInput = event.target as HTMLInputElement;

    this.imageToUpload = fileInput.files[0];
  }

  submitSession = (e: FormFlowSubmitEvent) => {
    let data = e.data as SessionFormData;
    let details = data.details;
    let settings = data.settings;

    if (this.imageToUpload) {
      data.imgToUpload = this.imageToUpload;
      details.imgUrl = this.imageToUpload.name;
    } else {
      if (this.initialAvatarUrl) details.imgUrl = this.initialAvatarUrl;
    }

    if (!this.isStudentLimit) settings.maxMember = -1;
    if (this.isNoEntryTimeLimit) settings.rejectLateMemberAfterMinute = -1;

    this.submit.emit(data);
  };

  avatarFileName() {
    if (this.imageToUpload) return this.imageToUpload.name;
    return this.initialAvatarUrl;
  }

  submitForm() {
    this.formFlow.startSearchProcess();
  }

  canSubmit() {
    return this.formFlow && this.formFlow.canSubmit();
  }

  dirty() {
    return this.formFlow && this.formFlow.dirty();
  }

  onPreview() {
    this.previewMode = !this.previewMode;

    if (this.previewMode) {
      let data = this.form.getRawValue() as SessionFormData;
      let details = data.details;
      let settings = data.settings;

      if (this.imageToUpload) {
        let reader = new FileReader();
        reader.addEventListener(
          "load",
          () => {
            details.imgUrl = reader.result as string;
          },
          false
        );
        reader.readAsDataURL(this.imageToUpload);
      } else {
        if (this.initialAvatarUrl) details.imgUrl = this.initialAvatarUrl;
      }

      if (!this.isStudentLimit) settings.maxMember = -1;
      if (this.isNoEntryTimeLimit) settings.rejectLateMemberAfterMinute = -1;

      details.state = {
        status: "NOT_STARTED",
        registered: 0,
      };

      this.lsDetails = details;
      this.lsSettings = settings;

      console.log("lsDetails: ", this.lsDetails);
      console.log("lsSettings: ", this.lsSettings);
    }
  }

  getLoggedInUser() {
    return this.loggedIn;
  }

  grades() {
    return _.sortBy(this.configService.grades(), (o) => o._id);
  }
  subjects() {
    return _.sortBy(this.configService.subjects(), (o) => o._id);
  }
}
