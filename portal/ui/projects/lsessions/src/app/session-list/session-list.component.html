<div class="header-banner"></div>
<div class="px-xxl-70px px-xl-40px px-lg-40px">
  <div class="content" [style.margin-top]="'-20px'">
    <div class="d-flex justify-center">
      <ls-searchbar (submit)="submitSearchSession($event)"></ls-searchbar>
      <div class="d-flex align-items-center ms-3">
        <a class="btn btn btn-primary" routerLink="createsession"
          >Tạo buổi học</a
        >
      </div>
    </div>
  </div>
  <div
    class="content text-center mt-30px mb-30px fst-italic"
    *ngIf="lsContainers"
  >
    <span>{{ lsContainers.length }} kết quả tìm kiếm</span>
  </div>

  <div class="lsessions-summary" *ngIf="lsContainers">
    <div class="center-x max-w-lg-870px max-w-xl-1170px">
      <div>
        <div class="sessions">
          <div *ngFor="let s of lsContainers" class="session-item">
            <common-lsession-summary
              [userProfile]="userProfile"
              [creator]="s.creator"
              [lsession]="s.summary"
              (regChange)="sendRegistrationLSession($event, s.summary)"
            ></common-lsession-summary>
          </div>
        </div>
        <div class="view-more">
          <a href="#">
            <span>Xem thêm </span>
            <i class="vcon-general vcon_general_view-more"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
