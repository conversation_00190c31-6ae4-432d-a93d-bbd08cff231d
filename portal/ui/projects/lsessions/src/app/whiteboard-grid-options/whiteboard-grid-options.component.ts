import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { BoardType } from '@viclass/portal.common';

@Component({
  selector: 'ls-whiteboard-grid-options',
  templateUrl: './whiteboard-grid-options.component.html',
  styleUrls: ['./whiteboard-grid-options.component.sass']
})
export class WhiteboardGridOptionsComponent implements OnInit {

  @Input()
  selected : BoardType | string

  @Output()
  onSelect : EventEmitter<BoardType | string> = new EventEmitter<BoardType | string>()

  constructor() { }

  ngOnInit(): void {
  }

  setSelection(e : BoardType | string) {
    this.selected = e
    this.onSelect.emit(this.selected)
  }

}
