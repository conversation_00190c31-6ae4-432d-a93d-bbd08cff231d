const webpack = require('webpack')

module.exports = {
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
    }),
  ],
  resolve: {
    alias: {
      process: "process/browser"
    },
    fallback: {
      os: require.resolve('os-browserify/browser'),
      vm: require.resolve('vm-browserify'),
      http: require.resolve('stream-http'),
      https: require.resolve('https-browserify'),
      stream: require.resolve('stream-browserify'),
      crypto: require.resolve('crypto-browserify'),
      util: require.resolve('util')
    }
  },
  node: {
    global: true
  }
};
