plugins {
    id "hnct.build"
    id "kotlin"
    id "application"
    id "com.google.devtools.ksp"
}

tasks.withType(AbstractCopyTask) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

dependencies {
    sourceSets {
        implementation pojo
    }

    internal {
        implementation([id: ':portal.grpc', src: ['user']], "viclass:portal.grpc-user:1.0.0", true)
        pojoImplementation([id: ':portal.grpc', src: ['user']], "viclass:portal.grpc-user:1.0.0", true)
        implementation([id: ':portal.datastructures', src: ['lsession']], "viclass:portal.datastructures-lsession:1.0.0", true)
        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
        implementation([id: ':common.libs', src: ['jwt']], "viclass:common.libs-jwt:1.0.0", true)

        implementation([id: ':portal.grpc', src: ['notification']], "viclass:portal.grpc-notification:1.0.0", true)

        implementation(['id': ":jayeson.lib.session", "src": "lettuce"], "jayeson:jayeson.lib.session-lettuce:$sessionVs", false)
        implementation(['id': ":jayeson.lib.session", "src": "memory"], "jayeson:jayeson.lib.session-memory:$sessionVs", false)
    }

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"
    pojoImplementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"
    implementation "io.grpc:grpc-netty:$grpcVs"

    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.13.1'

    implementation "com.google.api-client:google-api-client:$googleApiClientVs"

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation "io.insert-koin:koin-ktor:$koinVs"
    implementation "io.insert-koin:koin-logger-slf4j:$koinVs"
    implementation "io.ktor:ktor-server-core-jvm"
    // implementation "org.koin:koin-java:2.0.1"

    // redis
    implementation("io.lettuce:lettuce-core:$lettuceVs")
}

group = 'viclass'
version = '1.0.0'

application {
    mainClass.set("portal.user.Launcher")
}
