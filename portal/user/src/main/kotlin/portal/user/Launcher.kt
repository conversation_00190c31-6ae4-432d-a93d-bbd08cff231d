package portal.user

import common.libs.logger.Logging
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import org.koin.core.context.GlobalContext
import org.koin.dsl.koinApplication
import org.koin.ksp.generated.module
import portal.user.koin.KoinApplicationModule
import portal.user.server.Server

/**
 *
 * <AUTHOR>
 */
object Launcher : Logging {
    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching user service")

        RxJavaPlugins.setErrorHandler {
            logger.error("Uncaught error... ", it)
        }

        val koinApplication = koinApplication { modules(KoinApplicationModule().module) }
        GlobalContext.startKoin(koinApplication)

        val server = koinApplication.koin.get<Server>()

        // start account manager server
        server.start()

        //Add a shutdown hook so that if the JVM is stopped the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            logger.info("Shutting down user service")
            server.shutdown()
            logger.info("Shutdown user service")
        })

        server.blockUntilShutdown()
    }
}
