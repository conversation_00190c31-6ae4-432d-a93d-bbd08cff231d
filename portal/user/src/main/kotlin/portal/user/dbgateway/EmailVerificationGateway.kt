package portal.user.dbgateway

import com.mongodb.client.model.Filters
import com.mongodb.client.model.FindOneAndUpdateOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.client.model.Updates
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.user.koin.DatabaseModule
import portal.user.pojo.EmailRegistrationPojo
import portal.user.pojo.EmailRegistrationStatus
import portal.user.pojo.RegistrationType
import portal.user.pojo.UserRegistrationPojo
import portal.user.utils.MongoUtils
import java.time.Instant
import java.time.temporal.ChronoUnit

/**
 *  Handle data for the email validation flow
 */
@Singleton
class EmailVerificationGateway constructor(
    @Named(DatabaseModule.MONGO_COLLECTION_USER_REGISTRATION_POJO) private val collection: MongoCollection<UserRegistrationPojo>
) : IEmailVerificationGateway, Logging {

    /**
     * Save the verification code for the email with timestamp to determine the time it can be sent again
     */
    override fun setVerificationCode(registrationId: String, verificationCode: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.findOneAndUpdate(
                Filters.and(
                    // match email and match registration type
                    Filters.eq("_id", ObjectId(registrationId)),
                    Filters.eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    // valid verification status
                    Filters.`in`(
                        EmailRegistrationPojo::status.name,
                        EmailRegistrationStatus.NEWLY_CREATED.name,
                        EmailRegistrationStatus.VERIFICATION_PENDING.name
                    )
                ),
                MongoUtils.updatesWithTimestamp(
                    Updates.set(EmailRegistrationPojo::status.name, EmailRegistrationStatus.VERIFICATION_PENDING.name),
                    Updates.set(EmailRegistrationPojo::verificationCode.name, verificationCode.uppercase()),
                    Updates.currentDate(EmailRegistrationPojo::lastSent.name),
                ),
                FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
            )
        }.doOnError {
            logger.error("Set verification code for ID {} failed ... ", registrationId, it)
        }.firstOrError()
    }

    /**
     * Find and validate the email with verification code
     */
    override fun verifyEmail(registrationId: String, verificationCode: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.findOneAndUpdate(
                Filters.and(
                    // match email and match registration type
                    Filters.eq("_id", ObjectId(registrationId)),
                    Filters.eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    // match verification code
                    Filters.eq(EmailRegistrationPojo::verificationCode.name, verificationCode.uppercase()),
                    // verification status == VERIFICATION_PENDING && lastSent is not expired (30 minutes)
                    Filters.eq(EmailRegistrationPojo::status.name, EmailRegistrationStatus.VERIFICATION_PENDING.name),
                    Filters.gte(EmailRegistrationPojo::lastSent.name, Instant.now().minus(30, ChronoUnit.MINUTES)),
                ),
                MongoUtils.updatesWithTimestamp(
                    Updates.set(EmailRegistrationPojo::status.name, EmailRegistrationStatus.VERIFIED.name),
                    Updates.unset(EmailRegistrationPojo::verificationCode.name),
                ),
                FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
            )
        }.doOnError {
            logger.error("Verify ID {} failed with verificationCode {} ... ", registrationId, verificationCode, it)
        }.firstOrError()
    }

    override fun forceVerify(registrationId: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.findOneAndUpdate(
                Filters.and(
                    // match email and match registration type
                    Filters.eq("_id", ObjectId(registrationId)),
                    Filters.eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    // verification status == VERIFICATION_PENDING && lastSent is not expired (30 minutes)
                    Filters.eq(EmailRegistrationPojo::status.name, EmailRegistrationStatus.VERIFICATION_PENDING.name),
                ),
                MongoUtils.updatesWithTimestamp(
                    Updates.set(EmailRegistrationPojo::status.name, EmailRegistrationStatus.VERIFIED.name),
                    Updates.unset(EmailRegistrationPojo::verificationCode.name),
                ),
                FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
            )
        }.doOnError {
            logger.error("Verify ID {} failed ... ", registrationId, it)
        }.firstOrError()
    }
}