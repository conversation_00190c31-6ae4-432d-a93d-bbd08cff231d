package portal.user.dbgateway

import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Single
import org.koin.core.annotation.Singleton
import portal.user.pojo.FeedbackPojo

/**
 * Implementation of the IFeedbackGateway interface for MongoDB
 */
@Singleton
class FeedbackGateway(
    private val mongoDatabase: MongoDatabase
) : IFeedbackGateway, Logging {

    private fun getCollection(): MongoCollection<FeedbackPojo> {
        return mongoDatabase.getCollection("feedback", FeedbackPojo::class.java)
    }

    /**
     * Saves a feedback submission to the database
     * 
     * @param feedback The feedback data to save
     * @return A Single that emits the saved feedback with its ID
     */
    override fun saveFeedback(feedback: FeedbackPojo): Single<FeedbackPojo> {
        return Single.defer {
            logger.info("Saving feedback from: ${feedback.email}")
            Single.fromPublisher(getCollection().insertOne(feedback)).map { feedback }
        }
    }
}
