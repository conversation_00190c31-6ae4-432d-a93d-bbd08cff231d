package portal.user.dbgateway

import io.reactivex.rxjava3.core.Single
import portal.user.pojo.UserRegistrationPojo

interface IEmailVerificationGateway {
    fun setVerificationCode(registrationId: String, verificationCode: String): Single<UserRegistrationPojo>
    fun verifyEmail(registrationId: String, verificationCode: String): Single<UserRegistrationPojo>
    fun forceVerify(registrationId: String): Single<UserRegistrationPojo>
}