package portal.user.dbgateway

import io.reactivex.rxjava3.core.Single
import portal.user.pojo.FeedbackPojo

/**
 * Interface for feedback database operations
 */
interface IFeedbackGateway {
    /**
     * Saves a feedback submission to the database
     * 
     * @param feedback The feedback data to save
     * @return A Single that emits the saved feedback with its ID
     */
    fun saveFeedback(feedback: FeedbackPojo): Single<FeedbackPojo>
}
