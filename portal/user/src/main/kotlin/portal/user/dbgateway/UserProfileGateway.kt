package portal.user.dbgateway

import com.mongodb.client.model.Filters
import com.mongodb.client.model.Updates
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import kotlinx.coroutines.reactive.awaitFirstOrNull
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import portal.user.koin.DatabaseModule
import portal.user.pojo.UserProfilePojo
import portal.user.utils.MongoUtils
import proto.portal.user.UserMessage.UpdateUserProfileRequest
import java.util.*


/**
 *
 * <AUTHOR>
 */
@org.koin.core.annotation.Singleton
class UserProfileGateway constructor(
    @Named(DatabaseModule.MONGO_COLLECTION_USER_PROFILE_POJO) private val collection: MongoCollection<UserProfilePojo>,
) : Logging {
    fun insertOne(userProfile: UserProfilePojo): Single<InsertOneResult> {
        userProfile.createdAt = Date()
        return Flowable.defer { collection.insertOne(userProfile) }.firstOrError()
    }

    fun findUserProfileByUsername(username: String): Single<UserProfilePojo> {
        return Flowable.defer { collection.find(Filters.eq(UserProfilePojo::username.name, username)).first() }
            .firstOrError()
    }

    fun findUserProfileById(id: String): Single<UserProfilePojo> {
        return Flowable.defer { collection.find(Filters.eq("_id", ObjectId(id))).first() }.firstOrError()
    }

    fun findUserProfileByUsernameOrEmail(username: String): Single<UserProfilePojo> {
        return Flowable.defer {
            collection.find(
                Filters.or(
                    Filters.eq(UserProfilePojo::username.name, username),
                    Filters.eq(UserProfilePojo::email.name, username)
                )
            )
        }.doOnError {
            logger.error("find user by username {} failed... ", username, it)
        }.firstOrError()
    }


    fun findUserProfileByIds(ids: List<String>): Single<MutableList<UserProfilePojo>> {
        return Flowable.defer { collection.find(Filters.`in`("_id", ids.map { ObjectId(it) })) }.toList()
    }

    /**
     * For the email verification, we don't want to throw error on new user so just return null if not found
     */
    suspend fun findUserProfileByEmail(email: String): UserProfilePojo? {
        return collection.find(Filters.eq(UserProfilePojo::email.name, email)).awaitFirstOrNull()
    }

    /**
     * Update last_login
     */
    suspend fun updateLastLogin(usernameOrEmail: String): UpdateResult? {
        return collection.updateOne(
            Filters.or(
                Filters.eq(UserProfilePojo::username.name, usernameOrEmail),
                Filters.eq(UserProfilePojo::email.name, usernameOrEmail)
            ),
            MongoUtils.updatesWithTimestamp(
                Updates.set(UserProfilePojo::lastLogin.name, Date())
            ),
        ).awaitFirstOrNull()
    }


    /**
     * Update username with a newly created one in the email verification flow
     */
    suspend fun updateUsername(email: String, username: String): UpdateResult? {
        return collection.updateOne(
            Filters.eq(UserProfilePojo::email.name, email),
            MongoUtils.updatesWithTimestamp(
                Updates.set(UserProfilePojo::username.name, username)
            ),
        ).awaitFirstOrNull()
    }

    /**
     * Update username and avatarURl by email in user creation flow using social
     */
    suspend fun updateUsernameAndAvatarUrl(email: String, username: String, avatarUrl: String): UpdateResult? {
        return collection.updateOne(
            Filters.eq(UserProfilePojo::email.name, email), MongoUtils.updatesWithTimestamp(
                Updates.set(UserProfilePojo::username.name, username),
                Updates.set(UserProfilePojo::avatarUrl.name, avatarUrl),
            )
        ).awaitFirstOrNull()
    }

    /**
     * Updates the user profile in the database with the provided details.
     *
     * @param userProfile The user profile containing updated information.
     * @return The result of the update operation, or null if the update did not occur.
     */
    suspend fun updateProfile(userProfile: UserProfilePojo): UpdateResult? {
        return collection.updateOne(
            Filters.eq(UserProfilePojo::email.name, userProfile.email), MongoUtils.updatesWithTimestamp(
                Updates.set(UserProfilePojo::name.name, userProfile.name),
                Updates.set(UserProfilePojo::dateOfBirth.name, userProfile.dateOfBirth),
                Updates.set(UserProfilePojo::address.name, userProfile.address),
                Updates.set(UserProfilePojo::gender.name, userProfile.gender),
                Updates.set(UserProfilePojo::phone.name, userProfile.phone),
            )
        ).awaitFirstOrNull()
    }

    /**
     * Updates the avatar URL for a user in the database.
     *
     * @param userId The unique ID of the user whose avatar URL is to be updated.
     * @param avatarUrl The new avatar URL for the user.
     * @return The result of the update operation, or null if the update did not occur.
     */
    suspend fun updateAvatar(userId: String, avatarUrl: String): UpdateResult? {
        return collection.updateOne(
            Filters.eq("_id", ObjectId(userId)), MongoUtils.updatesWithTimestamp(
                Updates.set(UserProfilePojo::avatarUrl.name, avatarUrl)
            )
        ).awaitFirstOrNull()
    }

    /**
     * Update phone number by email
     */
    suspend fun updatePhoneNumberByEmail(email: String, phoneNumber: String): UpdateResult? {
        return collection.updateOne(
            Filters.eq(UserProfilePojo::email.name, email),
            MongoUtils.updatesWithTimestamp(
                Updates.set(UserProfilePojo::phone.name, phoneNumber)
            ),
        ).awaitFirstOrNull()
    }

    suspend fun updateByEmail(response: UpdateUserProfileRequest): UpdateResult? {

        val updates = listOfNotNull(
            response.username?.takeIf { it.isNotBlank() }?.let { Updates.set(UserProfilePojo::username.name, it) },
            response.phone?.takeIf { it.isNotBlank() }?.let { Updates.set(UserProfilePojo::phone.name, it) },
            response.avatarUrl?.takeIf { it.isNotBlank() }?.let { Updates.set(UserProfilePojo::avatarUrl.name, it) }
        )

        return collection.updateOne(
            Filters.eq(UserProfilePojo::email.name, response.email), Updates.combine(
                updates
            )
        ).awaitFirstOrNull()
    }
}


