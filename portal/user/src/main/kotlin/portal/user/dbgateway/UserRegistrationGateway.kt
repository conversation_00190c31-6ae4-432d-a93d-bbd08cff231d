package portal.user.dbgateway

import com.mongodb.client.model.Filters.*
import com.mongodb.client.model.Updates
import com.mongodb.client.model.Updates.set
import com.mongodb.client.result.DeleteResult
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import io.reactivex.rxjava3.core.Single
import kotlinx.coroutines.reactive.awaitFirstOrNull
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import portal.user.koin.DatabaseModule
import portal.user.pojo.EmailRegistrationPojo
import portal.user.pojo.RegistrationType
import portal.user.pojo.UserRegistrationPojo
import portal.user.utils.MongoUtils
import proto.portal.user.UserMessage
import java.util.*


/**
 *
 * <AUTHOR>
 */
@org.koin.core.annotation.Singleton
class UserRegistrationGateway constructor(
    @Named(DatabaseModule.MONGO_COLLECTION_USER_REGISTRATION_POJO) private val collection: MongoCollection<UserRegistrationPojo>,
) : Logging {
    fun insertOne(user: UserRegistrationPojo): Single<InsertOneResult> {
        user.createdAt = Date()
        return Flowable.defer { collection.insertOne(user) }
            .doOnError {
                logger.error("insert user failed {} ... ", user, it)
            }.firstOrError()
    }

    fun findRegistrationById(id: String): Single<UserRegistrationPojo> {
        return Flowable.defer { collection.find(eq("_id", ObjectId(id))) }
            .doOnError {
                logger.error("find user by id {} failed... ", id, it)
            }.firstOrError()
    }

    /**
     * Find user registration by email or username from EMAIL regType.
     * Use for the form in login page
     */
    fun findEmailRegistrationByUsernameOrEmail(ue: String): Single<EmailRegistrationPojo> {
        @Suppress("UNCHECKED_CAST")
        return Flowable.defer {
            collection.find(
                and(
                    eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name),
                    or(
                        eq(UserRegistrationPojo::email.name, ue),
                        eq(UserRegistrationPojo::username.name, ue),
                    )
                )
            )
        }
            .doOnError {
                logger.error("find user by username {} failed... ", ue, it)
            }.firstOrError() as Single<EmailRegistrationPojo>
    }

    /**
     * Find user registration by username
     * ! limit to the EMAIL registration type as it the only username we can guaranty the uniqueness
     */
    fun findRegistrationByUsername(username: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.find(
                and(
                    eq(UserRegistrationPojo::username.name, username),
                    eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL.name)
                )
            )
        }
            .doOnError {
                logger.error("find user by username {} failed... ", username, it)
            }.firstOrError()
    }

    fun findRegistrationByEmail(email: String): Single<UserRegistrationPojo> {
        return Flowable.defer { collection.find(eq(UserRegistrationPojo::email.name, email)) }
            .doOnError {
                logger.error("find user by email {} failed... ", email, it)
            }.firstOrError()
    }

    fun findRegistrationByPhone(phone: String): Single<UserRegistrationPojo> {
        return Flowable.defer {
            collection.find(
                and(
                    eq(UserRegistrationPojo::phone.name, phone),
                )
            )
        }
            .doOnError {
                logger.error("find user by phone {} failed... ", phone, it)
            }.firstOrError()
    }

    fun deleteRegistrationById(id: String): Single<DeleteResult> {
        return Flowable.defer { collection.deleteOne(eq("_id", ObjectId(id))) }
            .doOnError {
                logger.error("delete user by id {} failed... ", id, it)
            }.firstOrError()
    }

    /**
     * For Email registration flow, check if the username or email was registered or not
     */
    fun isEmailRegistrationUnique(email: String, username: String): Single<Boolean> {
        return Flowable.defer {
            collection.find(
                and(
                    eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL),
                    or(
                        eq(UserRegistrationPojo::username.name, username),
                        eq(UserRegistrationPojo::email.name, email)
                    )
                )
            ).limit(1)
        }
            .doOnError {
                logger.error("find duplicated user by email {} or username {} failed... ", email, username, it)
            }.firstElement().isEmpty()
    }

    suspend fun getSocialRegistration(socialId: String, regType: String): UserRegistrationPojo? {
        return collection.find(
            and(
                eq(UserRegistrationPojo::regType.name, regType),
                eq("socialId", socialId)
            )
        ).limit(1).awaitFirstOrNull()
    }

    /**
     * Get all registrations linked with the profile email
     */
    fun getLinkedRegistrations(email: String): Single<MutableList<UserRegistrationPojo>> {
        return Flowable.defer {
            collection.find(eq(UserRegistrationPojo::email.name, email))
        }.doOnError {
            logger.error("get linked registrations by email {} failed ", email, it)
        }.toList()
    }

    /**
     * Get registration by email.
     */
    suspend fun getRegistrationByEmail(registrationType: RegistrationType, email: String): EmailRegistrationPojo? {
        return collection.find(
            and(
                eq(UserRegistrationPojo::regType.name, registrationType),
                eq(UserRegistrationPojo::email.name, email)
            )
        ).limit(1).awaitFirstOrNull() as EmailRegistrationPojo?
    }

    suspend fun setEmailForSocialRegistration(
        registrationId: String,
        newEmail: String,
        isVerified: Boolean,
    ): UserRegistrationPojo? {
        return collection.findOneAndUpdate(
            and(
                eq("_id", ObjectId(registrationId)),
                ne(UserRegistrationPojo::email.name, RegistrationType.EMAIL)
            ),
            MongoUtils.updatesWithTimestamp(
                set(UserRegistrationPojo::email.name, newEmail),
                set("verified", isVerified)
            )
        ).awaitFirstOrNull()
    }

    suspend fun setPasswordForEmailRegistration(
        email: String,
        newPassword: String,
    ): UserRegistrationPojo? {
        return collection.findOneAndUpdate(
            and(
                eq(EmailRegistrationPojo::email.name, email),
                eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL)
            ),
            MongoUtils.updatesWithTimestamp(
                set(EmailRegistrationPojo::password.name, newPassword),
            )
        ).awaitFirstOrNull()
    }

    suspend fun updateUserEmailRegByEmail(response: UserMessage.UpdateUserEmailRegRequest): UpdateResult? {
        val updates = listOfNotNull(
            response.username?.takeIf { it.isNotBlank() }?.let { set(EmailRegistrationPojo::username.name, it) },
            response.phone?.takeIf { it.isNotBlank() }?.let { set(EmailRegistrationPojo::phone.name, it) },
            response.password?.takeIf { it.isNotBlank() }?.let { set(EmailRegistrationPojo::password.name, it) }
        )
        return collection.updateOne(
            and(
                eq(EmailRegistrationPojo::email.name, response.email),
                eq(UserRegistrationPojo::regType.name, RegistrationType.EMAIL)
            ),
            Updates.combine(updates)
        ).awaitFirstOrNull()
    }
}
