package portal.user.gateway

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.logger.Logging
import io.grpc.ManagedChannel
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.qualifier.named
import portal.user.configuration.NotificationEmailConfig
import portal.user.koin.NOTIFICATION_SERVICE_CHANNEL
import portal.user.pojo.ResetPasswordEmail
import portal.user.pojo.VerificationEmailData
import proto.portal.notification.NotificationMessages
import proto.portal.notification.NotificationServiceGrpcKt

@Singleton
class NotificationServiceGateway constructor(
    private val emailConfig: NotificationEmailConfig,
    @Named(NOTIFICATION_SERVICE_CHANNEL) private val notificationChannel: ManagedChannel,
    private val mapper: ObjectMapper,
) : Logging, KoinComponent {

    suspend fun sendVerificationEmail(
        email: String,
        data: VerificationEmailData
    ): NotificationMessages.SendEmailResponse {
        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(notificationChannel)
        val jsonData = mapper.writeValueAsString(data)

        val req = NotificationMessages.SendEmailRequest.newBuilder()
            .setToAddress(email)
            .setToName(data.username)
            .setSubject(emailConfig.sendVerification.subject)
            .setTemplateId(emailConfig.sendVerification.templateId)
            .setJsonData(jsonData)
            .build()

        return stub.sendEmail(req)
    }

    suspend fun sendResetPasswordEmail(
        email: String,
        data: ResetPasswordEmail
    ): NotificationMessages.SendEmailResponse {
        val stub = NotificationServiceGrpcKt.NotificationServiceCoroutineStub(notificationChannel)
        val jsonData = mapper.writeValueAsString(data)

        val req = NotificationMessages.SendEmailRequest.newBuilder()
            .setToAddress(email)
            .setToName(data.username)
            .setSubject(emailConfig.sendResetPassword.subject)
            .setTemplateId(emailConfig.sendResetPassword.templateId)
            .setJsonData(jsonData)
            .build()

        return stub.sendEmail(req)
    }
}