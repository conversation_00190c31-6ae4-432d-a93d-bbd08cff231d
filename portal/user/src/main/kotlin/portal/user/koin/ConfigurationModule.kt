package portal.user.koin

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import common.libs.jwt.JwtConfig
import common.libs.jwt.JwtUtil
import common.libs.logger.Logging
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import portal.user.configuration.*
import java.io.File
import kotlin.system.exitProcess


/**
 *
 * <AUTHOR>
 */
@Module
class ConfigurationModule : Logging {
    @Singleton
    fun provideConfiguration(mapper: ObjectMapper): Configuration {
        val config: Configuration
        try {
            val path = "conf/config.json"
            val jsonString: String = File(path).readText(Charsets.UTF_8)
            config = mapper.readValue(jsonString, Configuration::class.java)
        } catch (t: Throwable) {
            logger.error("Exception when load configurations... ", t)
            exitProcess(1)
        }
        return config
    }

    @Singleton
    fun provideObjectMapper(): ObjectMapper {
        return jacksonObjectMapper()
    }

    @Singleton
    fun provideServerConfiguration(config: Configuration): ServerConfig {
        return config.serverConf
    }

    @Singleton
    fun provideDatabaseConfiguration(config: Configuration): DatabaseConfig {
        return config.dbConf
    }

    @Singleton
    fun provideServiceExplorerConfiguration(config: Configuration): ServiceExplorerConfig {
        return config.seConf
    }

    @Singleton
    fun provideNotificationEmailConfiguration(config: Configuration): NotificationEmailConfig {
        return config.emailConf
    }

    @Singleton
    fun provideSocialLoginConfiguration(config: Configuration): SocialLoginConfig {
        return config.socialLoginConf
    }

    @Singleton
    fun provideJwtConfiguration(config: Configuration): JwtConfig {
        return config.jwtConf
    }

    @Singleton
    fun provideJwtUtilConfiguration(config: JwtConfig): JwtUtil {
        return JwtUtil(config)
    }

    @Singleton
    fun provideResetPasswordConfiguration(config: Configuration): ResetPasswordConfig {
        return config.resetPasswordConf
    }

    @Singleton
    fun provideCacheServiceConf(config: Configuration): CacheServiceConf {
        return config.cacheServiceConf
    }
}
