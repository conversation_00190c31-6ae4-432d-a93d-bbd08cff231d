package portal.user.koin

import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import io.lettuce.core.RedisClient
import io.lettuce.core.api.sync.RedisCommands
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import portal.user.configuration.CacheServiceConf
import portal.user.configuration.DatabaseConfig
import portal.user.pojo.*


/**
 *
 * <AUTHOR>
 */
@Module
@ComponentScan("portal.user.dbgateway")
class DatabaseModule {
    companion object {
        const val MONGO_COLLECTION_USER_REGISTRATION_POJO = "MongoCollectionUserRegistrationPojo"
        const val MONGO_COLLECTION_USER_PROFILE_POJO = "MongoCollectionUserProfilePojo"
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DatabaseConfig): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        val pojoCodecRegistry: CodecRegistry = CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                PojoCodecProvider.builder()
                    .automatic(true)
                    .register(UserRegistrationPojo::class.java)
                    .register(GoogleRegistrationPojo::class.java)
                    .register(FacebookRegistrationPojo::class.java)
                    .register(EmailRegistrationPojo::class.java)
                    .register(Address::class.java)
                    .register(FeedbackPojo::class.java)
                    .build()
            )
        )
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(pojoCodecRegistry)
    }

    @Singleton
    @Named(MONGO_COLLECTION_USER_REGISTRATION_POJO)
    fun provideUserCollection(db: MongoDatabase): MongoCollection<UserRegistrationPojo> {
        return db.getCollection("user-registration", UserRegistrationPojo::class.java)
    }

    @Singleton
    @Named(MONGO_COLLECTION_USER_PROFILE_POJO)
    fun provideUserProfileCollection(db: MongoDatabase): MongoCollection<UserProfilePojo> {
        return db.getCollection("user-profile", UserProfilePojo::class.java)
    }

    @Singleton
    fun provideRedisCommands(cacheServiceConf: CacheServiceConf): RedisCommands<String, String> {
        return RedisClient.create("${cacheServiceConf.host}:${cacheServiceConf.port}").connect().sync()
    }
}
