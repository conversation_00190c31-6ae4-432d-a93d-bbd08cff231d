package portal.user.koin

import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import org.koin.core.annotation.*
import portal.user.configuration.ServiceExplorerConfig

@Module
@ComponentScan("portal.user.gateway")
class GatewayModule {

    @Singleton
    @Named(NOTIFICATION_SERVICE_CHANNEL)
    fun provideNotificationChannel(config: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(config.notificationService.host, config.notificationService.port)
            .usePlaintext().build()
    }
}