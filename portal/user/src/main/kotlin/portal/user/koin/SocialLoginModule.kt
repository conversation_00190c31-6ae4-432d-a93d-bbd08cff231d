package portal.user.koin

import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.JsonFactory
import com.google.api.client.json.gson.GsonFactory
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import portal.user.configuration.SocialLoginConfig

@Module
class SocialLoginModule {
    @Singleton
    fun provideGoogleVerifier(config: SocialLoginConfig): GoogleIdTokenVerifier {
        val httpTransport = NetHttpTransport()
        val jsonFactory: JsonFactory = GsonFactory.getDefaultInstance()
        val verifier = GoogleIdTokenVerifier.Builder(
            httpTransport,
            jsonFactory
        ) // Specify the CLIENT_ID of the app that accesses the backend:
            .setAudience(listOf(config.google.clientId))
            .build()
        return verifier
    }
}