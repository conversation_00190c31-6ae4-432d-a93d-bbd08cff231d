package portal.user.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.types.ObjectId
import java.util.Date

/**
 * Represents a feedback submission from users
 */
data class FeedbackPojo @BsonCreator constructor(
    @BsonId val id: String = ObjectId().toHexString(),
    @BsonProperty("userType") val userType: String,
    @BsonProperty("name") val name: String?,
    @BsonProperty("phone") val phone: String?,
    @BsonProperty("email") val email: String,
    @BsonProperty("message") val message: String,
    @BsonProperty("createdAt") val createdAt: Date = Date()
)
