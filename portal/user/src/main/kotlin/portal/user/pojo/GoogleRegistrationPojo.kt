package portal.user.pojo

import org.bson.codecs.pojo.annotations.*
import org.bson.types.ObjectId
import java.util.*

/**
 *  Model for the Google registration flow
 */
@BsonDiscriminator(key = "regType", value = "GOOGLE")
class GoogleRegistrationPojo @BsonCreator constructor(
    @BsonId id: String = ObjectId().toHexString(),
    @BsonProperty("email") email: String,
    @BsonProperty("username") username: String = "",
    @BsonProperty("phone") phone: String = "",

    @BsonProperty("socialId") val socialId: String,

    @BsonProperty("verified") val verified: Boolean,
    @BsonProperty("verificationCode") val verificationCode: String? = null,
    @BsonProperty("lastSent") var lastSent: Date? = null,
) : UserRegistrationPojo(RegistrationType.GOOGLE, id, email, username, phone)


