package portal.user.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.*
import org.bson.types.ObjectId
import java.util.Date


/**
 *  Base model for all user registration flows
 *
 *  !!! IMPORTANT: all class inherited from this model must:
 *  - have a BsonDiscriminator of key "regType" with value from `RegistrationType` enum
 *  - register the class type with the `PojoCodecProvider` in `DatabaseModule`
 *  (so the codec registry can cache it before pulling any object from MongoDB)
 *  - properties with complex nested object types are not encouraged.
 *  Because when retrieve documents with collection<UserRegistrationPojo>,
 *  the MongoDb driver might not deserialize the nested object properly
 *
 *  @see EmailRegistrationPojo for example
 */
@BsonDiscriminator(key = "regType", value = "OTHER")
abstract class UserRegistrationPojo @BsonCreator constructor(
    @BsonProperty("regType") val regType: RegistrationType = RegistrationType.OTHER,
    @BsonId() @BsonRepresentation(BsonType.OBJECT_ID) val id: String = ObjectId().toHexString(),
    @BsonProperty("email") val email: String,
    @BsonProperty("username") val username: String = "",
    @BsonProperty("phone") val phone: String = "",

    @BsonProperty("createdAt") var createdAt: Date? = null,
    @BsonProperty("updatedAt") var updatedAt: Date? = null,
)