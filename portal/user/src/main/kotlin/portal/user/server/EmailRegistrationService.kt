package portal.user.server

import com.google.protobuf.Timestamp
import common.libs.jwt.JwtUtil
import common.libs.logger.Logging
import io.grpc.Status
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import portal.user.configuration.NotificationEmailConfig
import portal.user.configuration.ResetPasswordConfig
import portal.user.dbgateway.*
import portal.user.gateway.NotificationServiceGateway
import portal.user.pojo.*
import portal.user.utils.getRegLastSent
import portal.user.utils.isRegVerified
import proto.portal.user.UserMessage
import java.security.SecureRandom
import java.util.*

@Singleton
class EmailRegistrationService constructor(
    private val userRegistrationGateway: UserRegistrationGateway,
    private val userProfileGateway: UserProfileGateway,
    private val emailVerificationGateway: EmailVerificationGateway,
    private val socialEmailRegistrationGateway: SocialEmailVerificationGateway,
    private val notificationServiceGateway: NotificationServiceGateway,
    private val emailConfig: NotificationEmailConfig,
    private val jwtUtil: JwtUtil,
    private val resetPasswordConfig: ResetPasswordConfig
) : Logging {

    suspend fun checkRegistrationEmailExist(request: UserMessage.CheckRegistrationEmailExistRequest): UserMessage.CheckRegistrationEmailExistResponse {
        val builder = UserMessage.CheckRegistrationEmailExistResponse.newBuilder()

        if (request.email.isNullOrEmpty()) {
            return builder.setStatus(
                com.google.rpc.Status.newBuilder().setCode(Status.Code.INVALID_ARGUMENT.value())
                    .setMessage("email is empty")
            ).build()
        }

        val registrationType: RegistrationType;
        try {
            registrationType = RegistrationType.from(request.registrationType)
            if (registrationType == RegistrationType.OTHER) throw IllegalArgumentException("registration type not supported")
        } catch (t: Throwable) {
            return builder.setStatus(
                com.google.rpc.Status.newBuilder().setCode(Status.Code.INVALID_ARGUMENT.value())
                    .setMessage("registration type ${request.registrationType} not supported")
            ).build()
        }

        var code = Status.Code.INTERNAL
        var message = ""

        try {
            val registration = userRegistrationGateway.getRegistrationByEmail(registrationType, request.email)

            code = Status.Code.OK
            builder.setIsExist(registration != null)
        } catch (t: Throwable) {
            logger.error("check exist for email {} error...", request.email, t)
            message = t.message ?: "check registration exist failed"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Register user with the email flow.
     * The new registration will need to be verified before use
     */
    suspend fun registerUserByEmail(request: UserMessage.EmailRegistrationRequest): UserMessage.EmailRegistrationResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = UserMessage.EmailRegistrationResponse.newBuilder()

        // Unique check
        var isUnique = false
        try {
            isUnique = userRegistrationGateway.isEmailRegistrationUnique(request.email, request.username).await()
            if (!isUnique) {
                code = Status.Code.ALREADY_EXISTS
                message = "Email ${request.email} or username ${request.username} registration already exists"
            }
        } catch (t: Throwable) {
            logger.error("check unique for email {} and username {} error...", request.email, request.username, t)
            message = t.message ?: "check registration unique failed"
        }
        if (!isUnique) {
            return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message))
                .build()
        }

        val user = EmailRegistrationPojo(
            username = request.username,
            email = request.email,
            phone = request.phone,
            password = request.password,
            status = if (request.isVerified) EmailRegistrationStatus.VERIFIED else EmailRegistrationStatus.NEWLY_CREATED,
        )
        try {
            val insUser = userRegistrationGateway.insertOne(user).await()
            val insId = insUser.insertedId
            if (insId == null) {
                code = Status.Code.INTERNAL
                message = "Add registration failed"
            } else {
                code = Status.Code.OK
                message = "Add registration success"
                builder.setUserId(insId.asObjectId().value.toHexString())
            }
        } catch (t: Throwable) {
            logger.error("save user {} failed...", user, t)
            message = t.message ?: ""
            if (message.startsWith("E11000")) code = Status.Code.ALREADY_EXISTS
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Send verification email with a one-time password to the user.
     * Will only send if the last verification email to the same user was before 3 minutes ago
     */
    suspend fun sendVerificationEmail(request: UserMessage.SendVerificationEmailRequest): UserMessage.SendVerificationEmailResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = UserMessage.SendVerificationEmailResponse.newBuilder()

        val regId = request.registrationId

        // check if we should send it
        var shouldSend = false
        var registration: UserRegistrationPojo? = null;
        try {
            registration = userRegistrationGateway.findRegistrationById(regId).await()
            val lastSent = registration.getRegLastSent()
            if (isNullOrBeforeSecondsAgo(
                    lastSent, emailConfig.sendVerification.codeResendIntervalSeconds
                )
            ) {
                shouldSend = true
            } else {
                // verification email already sent a short time ago, so we don't have to do it again
                code = Status.Code.OK
                builder.setIsSent(false)
                builder.setLastSent(toTimestamp(lastSent))
            }
        } catch (t: NoSuchElementException) {
            code = Status.Code.NOT_FOUND
            message = t.message ?: "invalid registration id or user already verified"
        } catch (t: Throwable) {
            message = t.message ?: "find unverified registration failed"
        }


        // sending email and save verification code to mongodb
        if (shouldSend && registration != null) {
            val verificationCode = generateOneTimeVerificationCode()
            try {
                val sendResult = notificationServiceGateway.sendVerificationEmail(
                    registration.email, VerificationEmailData(
                        registration.username, verificationCode
                    )
                )

                if (sendResult.status.code != Status.Code.OK.value()) {
                    code = Status.fromCodeValue(sendResult.status.code).code
                    message = "send verification email failed"
                } else {
                    val gateway = getVerificationGateWay(registration.regType)
                    val updatedReg: UserRegistrationPojo = gateway.setVerificationCode(regId, verificationCode).await()
                    code = Status.Code.OK
                    builder.setIsSent(true)
                    builder.setLastSent(toTimestamp(updatedReg.getRegLastSent()))
                }
            } catch (t: Throwable) {
                message = t.message ?: "error create verification code"
            }
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Check if the verification email is allowed to send
     */
    suspend fun verifyEmail(request: UserMessage.EmailVerificationRequest): UserMessage.EmailVerificationResponse {
        var code = Status.Code.INTERNAL
        var message = ""
        val builder = UserMessage.EmailVerificationResponse.newBuilder()

        val regId = request.registrationId

        var registration: UserRegistrationPojo? = null;
        try {
            registration = userRegistrationGateway.findRegistrationById(regId).await()
            if (registration.isRegVerified()) throw NoSuchElementException("user already verified")
            if (registration.email.isEmpty()) throw NoSuchElementException("email not set")
        } catch (t: NoSuchElementException) {
            code = Status.Code.NOT_FOUND
            message = t.message ?: "invalid registration id or user already verified"
        } catch (t: Throwable) {
            message = t.message ?: "find unverified registration failed"
        }

        if (registration == null) {
            return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message))
                .build()
        }

        try {
            val gateway = getVerificationGateWay(registration.regType)
            registration = if (request.skipVerification) gateway.forceVerify(request.registrationId)
                .await() else gateway.verifyEmail(request.registrationId, request.verificationCode).await()

            code = Status.Code.OK
            builder.setRegistrationId(registration.id)
        } catch (e: NoSuchElementException) {
            code = Status.Code.NOT_FOUND
            message = "invalid/expired verification code, please try send a new one"
        } catch (t: Throwable) {
            code = Status.Code.INTERNAL
            message = "verification failed cause ${t.message}"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    private fun generateOneTimeVerificationCode(length: Int = 9): String {
        val allowedCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
        val secureRandom = SecureRandom()
        val otp = StringBuilder()

        for (i in 0 until length) {
            val randomIndex: Int = secureRandom.nextInt(allowedCharacters.length)
            otp.append(allowedCharacters[randomIndex])
        }
        return otp.toString()
    }

    private fun isNullOrBeforeSecondsAgo(lastSent: Date?, secondsAgo: Int = 180): Boolean {
        if (lastSent == null) return true

        val threeMinutesAgo = Date(System.currentTimeMillis() - (secondsAgo * 1000))
        return lastSent.before(threeMinutesAgo)
    }

    private fun toTimestamp(date: Date?): Timestamp? {
        return if (date == null) null else Timestamp.newBuilder().setSeconds(date.time / 1000).build()
    }

    /**
     * Get the verification gateway based on the registration type
     */
    private fun getVerificationGateWay(registrationType: RegistrationType): IEmailVerificationGateway {
        return if (registrationType == RegistrationType.EMAIL) emailVerificationGateway
        else socialEmailRegistrationGateway
    }

    /**
     * send email for reset password
     */
    suspend fun sendEmailResetPassword(request: UserMessage.SendEmailResetPasswordRequest): UserMessage.SendEmailResetPasswordResponse {
        val builder = UserMessage.SendEmailResetPasswordResponse.newBuilder()
        var code: Status.Code = Status.Code.INTERNAL
        var message = "Send reset password fail"

        try {
            val regUser = userRegistrationGateway.getRegistrationByEmail(RegistrationType.EMAIL, request.email)
            val profileUser = userProfileGateway.findUserProfileByEmail(request.email)

            if (regUser != null) {
                val expirationPattern = "HH:mm 'ngày' dd/MM/yyyy"

                val resetPasswordToken: String = jwtUtil.create(request.email)
                val resetPasswordExpirationTime = jwtUtil.getExpirationTimeFormat(resetPasswordToken, expirationPattern)

                // send email
                val result = notificationServiceGateway.sendResetPasswordEmail(
                    request.email, ResetPasswordEmail(
                        profileUser?.username ?: regUser.username,
                        resetPasswordExpirationTime,
                        "${resetPasswordConfig.baseUrl}/${resetPasswordToken}"
                    )
                )

                if (result.status.code == Status.Code.OK.value()) {
                    builder.setJwtToken(resetPasswordToken)
                    code = Status.fromCodeValue(result.status.code).code
                    message = "Send reset password success"
                }
            } else {
                code = Status.Code.NOT_FOUND
                message = "Invalid user"
            }
        } catch (e: Throwable) {
            code = Status.Code.INTERNAL
            message = e.message.toString()
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * reset password by jwtToken
     */
    suspend fun resetPassword(request: UserMessage.ResetPasswordRequest): UserMessage.ResetPasswordResponse {
        val builder = UserMessage.ResetPasswordResponse.newBuilder()
        var code: Status.Code = Status.Code.INTERNAL
        var message = "Reset password fail"

        try {
            val jwtVerify = jwtUtil.verify(request.jwtToken)

            if (jwtVerify.expiresAt.before(Date())) {
                code = Status.Code.UNAUTHENTICATED
                message = "Tokens are expired"
            } else {
                val email: String = jwtUtil.getClaimValueFromToken(request.jwtToken)
                val newPassword: String = request.newPassword

                // change password
                val result = userRegistrationGateway.setPasswordForEmailRegistration(email, newPassword)

                if (result != null) {
                    code = Status.Code.OK
                    message = "Reset password success"
                }
            }
        } catch (e: Throwable) {
            logger.error("exception when reset password... ", e)
            code = Status.Code.INTERNAL
            message = e.message.toString()
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * check email and password
     */
    suspend fun checkPassword(request: UserMessage.CheckPasswordRequest): UserMessage.CheckPasswordResponse {
        val builder = UserMessage.CheckPasswordResponse.newBuilder()
        var code: Status.Code = Status.Code.UNAUTHENTICATED
        var message = "incorrect password"

        try {
            val userReg = userRegistrationGateway.findEmailRegistrationByUsernameOrEmail(request.email).await()
            if (userReg.password == request.password) {
                code = Status.Code.OK
                message = "correct password"
            }
        } catch (e: Throwable) {
            code = Status.Code.INTERNAL
            message = e.message.toString()
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    suspend fun updateUserEmailReg(request: UserMessage.UpdateUserEmailRegRequest): UserMessage.UpdateUserEmailRegResponse {
        val builder = UserMessage.UpdateUserEmailRegResponse.newBuilder()
        var code: Status.Code = Status.Code.OK
        var message = "update success"

        try {
            userRegistrationGateway.updateUserEmailRegByEmail(request)
        } catch (t: Throwable) {
            code = Status.Code.INTERNAL
            message = "update fail"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Get email registration information for the security page.
     * Given a username or email, it retrieves the corresponding email registration information.
     *
     * @param request The request containing the username or email of the user whose registration information is to be retrieved.
     * @return A EmailRegInfoResponse containing the email registration information for the specified username or email
     *         or an error status if the user is not found.
     */
    suspend fun getEmailRegInfo(request: UserMessage.EmailRegInfoRequest): UserMessage.EmailRegInfoResponse {
        val builder = UserMessage.EmailRegInfoResponse.newBuilder()
        var code: Status.Code = Status.Code.OK
        var message = "get success"

        try {
            val userReg =
                userRegistrationGateway.findEmailRegistrationByUsernameOrEmail(request.usernameOrEmail).await()
            builder.setEmail(userReg.email).setUsername(userReg.username)
        } catch (e: NoSuchElementException) {
            logger.error("get user by id failed, not found user {}", request.usernameOrEmail)
            code = Status.Code.NOT_FOUND
            message = "Not found user ${request.usernameOrEmail}"
        } catch (t: Throwable) {
            logger.error("get user by id {} failed...", request.usernameOrEmail, t)
            message = t.message ?: "Unknown error"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }
}