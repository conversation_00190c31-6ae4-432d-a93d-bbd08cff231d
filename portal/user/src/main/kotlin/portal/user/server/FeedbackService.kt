package portal.user.server

import com.google.rpc.Status
import common.libs.logger.Logging
import io.grpc.Status.Code
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import portal.user.dbgateway.IFeedbackGateway
import portal.user.pojo.FeedbackPojo
import proto.portal.user.UserMessage

/**
 * Service for handling feedback-related operations
 */
@Singleton
class FeedbackService(
    private val feedbackGateway: IFeedbackGateway
) : Logging {

    /**
     * Saves a feedback submission to the database
     * 
     * @param request The SaveFeedbackRequest containing the feedback data
     * @return A SaveFeedbackResponse indicating success or failure
     */
    suspend fun saveFeedback(request: UserMessage.SaveFeedbackRequest): UserMessage.SaveFeedbackResponse {
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.SaveFeedbackResponse.newBuilder()

        try {
            // Create feedback pojo from the request
            val feedback = FeedbackPojo(
                userType = request.userType,
                name = if (request.hasName()) request.name else null,
                phone = if (request.hasPhone()) request.phone else null,
                email = request.email,
                message = request.message
            )

            // Save feedback to a database
            feedbackGateway.saveFeedback(feedback).await()
            
            code = Code.OK
            builder.setSuccess(true)
        } catch (t: Throwable) {
            logger.error("Failed to save feedback from ${request.email}", t)
            message = t.message ?: "Unknown error occurred while saving feedback"
            builder.setSuccess(false)
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }
}
