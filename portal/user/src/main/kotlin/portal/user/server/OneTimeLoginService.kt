package portal.user.server

import io.lettuce.core.api.sync.RedisCommands
import org.koin.core.annotation.Singleton
import java.util.UUID

@Singleton
class OneTimeLoginService(
    private val redisCommands: RedisCommands<String, String>,
) {
    /**
     * Expiration time in seconds
     */
    private val expiration: Long = 60 * 5 // 5 minutes

    /**
     * Get the cache key to be used in Redis
     */
    private fun getCacheKey(key: String): String {
        return "onetime:$key"
    }

    private fun setToken(key: String) {
        redisCommands.setex(getCacheKey(key), expiration, "1")
    }

    private fun getToken(key: String): String? {
        return redisCommands.get(getCacheKey(key))
    }

    private fun deleteToken(key: String) {
        redisCommands.del(getCacheKey(key))
    }

    /**
     * Generate one-time login data with a key combining registration ID and UUID v4
     */
    fun generateLoginDataWithRegistrationId(registrationId: String): String {
        val uuid = UUID.randomUUID().toString()
        val key = "$registrationId:$uuid"

        setToken(key)
        return key
    }

    fun verifyAndConsumeToken(token: String): Bo<PERSON>an {
        getToken(token) ?: return false
        deleteToken(token)
        return true
    }

    fun getRegistrationIdFromToken(token: String): String? {
        return token.split(":").firstOrNull()
    }
}
