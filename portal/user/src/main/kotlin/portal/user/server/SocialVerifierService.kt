package portal.user.server

import common.libs.logger.Logging
import org.apache.http.auth.AuthenticationException
import org.koin.core.annotation.Singleton
import portal.user.pojo.RegistrationType
import portal.user.pojo.SocialLoginInfo
import portal.user.socialverifier.FacebookVerifier
import portal.user.socialverifier.GoogleVerifier

@Singleton
class SocialVerifierService constructor(
    private val googleVerifier: GoogleVerifier,
    private val facebookVerifier: FacebookVerifier,
) : Logging {
    private val verifiers = mapOf(
        RegistrationType.GOOGLE.name to googleVerifier,
        RegistrationType.FACEBOOK.name to facebookVerifier,
    )

    fun verifyUser(provider: String, token: String): SocialLoginInfo {
        val verifier = verifiers[provider] ?: throw IllegalArgumentException("Unknown provider")

        try {
            return verifier.verifyUser(token)
        } catch (e: Throwable) {
            logger.warn("[${provider}] Failed to verify user: ", e)
            throw AuthenticationException("[${provider}] Failed to verify token", e)
        }
    }
}