package portal.user.server

import common.libs.logger.Logging
import io.grpc.Status
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import portal.user.dbgateway.UserProfileGateway
import portal.user.dbgateway.UserRegistrationGateway
import portal.user.pojo.*
import portal.user.utils.buildWithUserData
import portal.user.utils.isRegVerified
import proto.portal.user.UserMessage
import java.text.SimpleDateFormat
import java.util.*

@Singleton
class UserProfileService constructor(
    private val userRegistrationGateway: UserRegistrationGateway,
    private val userProfileGateway: UserProfileGateway,
) : Logging {
    suspend fun createOrMergeUserProfile(request: UserMessage.CreateOrMergeUserProfileRequest): UserMessage.CreateOrMergeUserProfileResponse {
        val builder = UserMessage.CreateOrMergeUserProfileResponse.newBuilder()

        val tokenInfo: UserMessage.TokenInfo? = if (request.hasTokenInfo()) request.tokenInfo else null

        val registration = userRegistrationGateway.findRegistrationById(request.registrationId).await()
        if (!registration.isRegVerified()) {
            return builder.buildWithStatus("User not verified", Status.Code.INVALID_ARGUMENT)
        }
        if (registration.email.isEmpty()) {
            return builder.buildWithStatus("email not set", Status.Code.INVALID_ARGUMENT)
        }

        try {
            var profile = userProfileGateway.findUserProfileByEmail(registration.email)

            if (profile == null) {
                val currentDate = Date()
                // User not yet registered -> create new profile
                val insertRes = userProfileGateway.insertOne(
                    UserProfilePojo(
                        email = registration.email,
                        phone = registration.phone,
                        username = tokenInfo?.username ?: registration.username,
                        avatarUrl = tokenInfo?.avatarUrl ?: "",
                        createdAt = currentDate,
                        updatedAt = currentDate,
                    )
                ).await()
                val profileId = insertRes.insertedId?.asObjectId()?.value?.toHexString()
                    ?: return builder.buildWithStatus("insert user profile failed")

                profile = userProfileGateway.findUserProfileById(profileId).await()
            } else {
                // merge user profile
                userProfileGateway.updateUsernameAndAvatarUrl(
                    registration.email,
                    tokenInfo?.username ?: registration.username,
                    tokenInfo?.avatarUrl ?: profile.avatarUrl
                )
            }

            return builder
                .setUser(UserMessage.UserProto.newBuilder().buildWithUserData(registration, profile))
                .buildWithStatus("verify email success", Status.Code.OK)
        } catch (t: Throwable) {
            logger.error("save user profile {} failed... ", registration, t)
            return builder.buildWithStatus("save user profile failed cause ${t.message}")
        }
    }

    suspend fun updateProfile(request: UserMessage.UpdateProfileRequest): UserMessage.UpdateProfileResponse {
        val profile =
            userProfileGateway.findUserProfileByEmail(request.email) ?: throw RuntimeException("profile not found")
        val builder = UserMessage.UpdateProfileResponse.newBuilder()

        try {
            val updatedProfile = profile.copy(
                name = request.name.takeIf { request.hasName() } ?: profile.name,
                dateOfBirth = Date(request.dateOfBirth).takeIf { request.hasDateOfBirth() } ?: profile.dateOfBirth,
                gender = Gender.valueOf(request.gender.toString()).takeIf { request.hasGender() } ?: profile.gender,
                address = profile.address.merge(Address.fromAddressProto(request.address))
                    .takeIf { request.hasAddress() } ?: profile.address,
                phone = request.phone.takeIf { request.hasPhone() } ?: profile.phone,
            )

            userProfileGateway.updateProfile(updatedProfile)

            return builder.setProfile(updatedProfile.toProfileProto())
                .buildWithStatus("update profile success", Status.Code.OK)
        } catch (t: Throwable) {
            logger.error("update user profile {} failed... ", profile, t)
            return builder.buildWithStatus("update profile failed cause ${t.message}")
        }
    }

    suspend fun updateAvatar(request: UserMessage.UpdateAvatarRequest): UserMessage.UpdateAvatarResponse {
        val builder = UserMessage.UpdateAvatarResponse.newBuilder()

        try {
            val result = userProfileGateway.updateAvatar(request.userId, request.avatarUrl)
                ?: throw RuntimeException("update avatar failed")

            if (result.matchedCount == 0L)
                return builder.buildWithStatus("user not found", Status.Code.NOT_FOUND)

            return builder.buildWithStatus("update avatar success", Status.Code.OK)
        } catch (t: Throwable) {
            logger.error("update avatar {} failed... ", request, t)
            return builder.buildWithStatus("update avatar failed cause ${t.message}")
        }
    }

    suspend fun getUserLoginInformation(request: UserMessage.GetUserLoginInformationRequest): UserMessage.GetUserLoginInformationResponse {
        val builder = UserMessage.GetUserLoginInformationResponse.newBuilder()
        var code: Status.Code = Status.Code.INTERNAL
        var message = "Fail get user security information"

        try {

            val userProfile = userProfileGateway.findUserProfileByEmail(request.email)
            if (userProfile != null) {
                val sdf = SimpleDateFormat("dd/MM/yyyy")
                sdf.timeZone = TimeZone.getDefault()
                val dateCreated = sdf.format(userProfile.createdAt)
                val lastLogin = if (userProfile.lastLogin == null) "" else { sdf.format(userProfile.lastLogin) }

                builder.setEmail(userProfile.email)
                    .setDateCreated(dateCreated)
                    .setLastLogin(lastLogin)
            }

        } catch (e: Throwable) {
            code = Status.Code.INTERNAL
            message = e.message.toString()
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    suspend fun updateLastLoginTime(request: UserMessage.UpdateLastLoginTimeRequest): UserMessage.UpdateLastLoginTimeResponse {
        val builder = UserMessage.UpdateLastLoginTimeResponse.newBuilder()
        var code: Status.Code
        var message: String

        try {
            userProfileGateway.updateLastLogin(request.email)

            code = Status.Code.OK
            message = "Success update last login"
        } catch (e: Throwable) {
            code = Status.Code.INTERNAL
            message = e.message.toString()
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    suspend fun updateUserProfile(request: UserMessage.UpdateUserProfileRequest): UserMessage.UpdateUserProfileResponse {
        val builder = UserMessage.UpdateUserProfileResponse.newBuilder()
        var code: Status.Code = Status.Code.OK
        var message = "update success"

        try {
            userProfileGateway.updateByEmail(request)
        } catch (t: Throwable) {
            code = Status.Code.INTERNAL
            message = "update fail"
        }

        return builder.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }
}

fun UserMessage.UpdateProfileResponse.Builder.buildWithStatus(
    message: String,
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.UpdateProfileResponse {
    return this.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
}

fun UserMessage.CreateOrMergeUserProfileResponse.Builder.buildWithStatus(
    message: String,
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.CreateOrMergeUserProfileResponse {
    return this.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
}

fun UserMessage.UpdateAvatarResponse.Builder.buildWithStatus(
    message: String,
    code: Status.Code = Status.Code.INTERNAL,
): UserMessage.UpdateAvatarResponse {
    return this.setStatus(com.google.rpc.Status.newBuilder().setCode(code.value()).setMessage(message)).build()
}