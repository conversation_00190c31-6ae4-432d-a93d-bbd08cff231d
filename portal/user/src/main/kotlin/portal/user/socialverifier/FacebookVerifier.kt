package portal.user.socialverifier

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.api.client.http.GenericUrl
import com.google.api.client.http.javanet.NetHttpTransport
import portal.user.pojo.RegistrationType
import portal.user.pojo.SocialLoginInfo

data class FacebookResponseData(
    val id: String,
    val email: String?,
    val name: String,
    val first_name: String,
    val last_name: String,
    val picture: FacebookPicture?,
) {

    data class FacebookPicture(
        val data: FacebookPictureData?,
    )

    data class FacebookPictureData(
        val width: Int,
        val height: Int,
        val is_silhouette: Boolean,
        val url: String,
    )
}

@org.koin.core.annotation.Singleton
class FacebookVerifier : IBaseVerifier {
    private val requestFields = listOf("name", "email", "picture", "first_name", "last_name")

    override fun verifyUser(token: String): SocialLoginInfo {
        if (token.isEmpty()) throw IllegalArgumentException("token is empty")

        val fields = requestFields.joinToString("%2C") // URL encoded ','
        val url = GenericUrl("https://graph.facebook.com/v22.0/me?fields=${fields}&access_token=${token}")

        val requestFactory = NetHttpTransport().createRequestFactory()
        val response = requestFactory.buildGetRequest(url).execute()
        if (!response.isSuccessStatusCode) {
            throw RuntimeException("Facebook request failed with code ${response.statusCode}: ${response.statusMessage}")
        }

        val dataStr = response.parseAsString()
        val data = jacksonObjectMapper().readValue(dataStr, FacebookResponseData::class.java)

        return SocialLoginInfo(
            provider = RegistrationType.FACEBOOK.name,
            socialId = data.id,
            email = data.email,
            avatarUrl = data.picture?.data?.url ?: "https://graph.facebook.com/${data.id}/picture?type=normal", //
            username = data.name,
            firstName = data.first_name,
            lastName = data.last_name,
            emailVerified = data.email != null
        )
    }
}