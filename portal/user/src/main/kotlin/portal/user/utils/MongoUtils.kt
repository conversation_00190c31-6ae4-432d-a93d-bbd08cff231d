package portal.user.utils

import com.mongodb.client.model.Updates
import org.bson.conversions.Bson
import java.util.*

object MongoUtils {
    fun updatesWithTimestamp(vararg updates: Bson): Bson {
        return Updates.combine(
            listOf(
                Updates.setOnInsert("createdAt", Date()), // update on an unsert that insert new doc
                Updates.currentDate("updatedAt"),
                *updates
            )
        )
    }
}