package portal.user.utils

import portal.user.pojo.*
import proto.portal.user.UserMessage
import java.util.Date

fun UserMessage.UserProto.Builder.buildWithUserData(
    registration: UserRegistrationPojo,
    profile: UserProfilePojo?,
): UserMessage.UserProto.Builder {
    return this.setId(profile?.id ?: "")
        .setUsername(profile?.username ?: registration.username)
        .setEmail(profile?.email ?: registration.email)
        .setPhone(profile?.phone ?: registration.phone)
        .setRegistrationId(registration.id)
        .setVerified(registration.isRegVerified())
}

fun UserRegistrationPojo.isRegVerified(): Boolean {
    return when (this.regType) {
        RegistrationType.EMAIL -> {
            (this as EmailRegistrationPojo).status == EmailRegistrationStatus.VERIFIED
        }

        RegistrationType.GOOGLE -> {
            val reg = (this as GoogleRegistrationPojo)
            reg.email.isNotEmpty() && reg.verified
        }

        RegistrationType.FACEBOOK -> {
            val reg = (this as FacebookRegistrationPojo)
            reg.email.isNotEmpty() && reg.verified
        }

        else -> true
    }
}

fun UserRegistrationPojo.getRegLastSent(): Date? {
    return when (this.regType) {
        RegistrationType.EMAIL -> {
            (this as EmailRegistrationPojo).lastSent
        }

        RegistrationType.GOOGLE -> {
            (this as GoogleRegistrationPojo).lastSent
        }

        RegistrationType.FACEBOOK -> {
            (this as FacebookRegistrationPojo).lastSent
        }

        else -> throw Exception("Unsupported registration type for lastSent")
    }
}