package portal.user.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty
import proto.portal.user.UserMessage

data class Address @BsonCreator constructor(
    @BsonProperty("country") val country: String?,
    @BsonProperty("city") val city: String? = null,
    @BsonProperty("district") val district: String? = null,
    @BsonProperty("ward") val ward: String? = null,
    @BsonProperty("street") val street: String? = null,
) {
    companion object {}
}

fun Address.Companion.fromAddressProto(addressProto: UserMessage.Address) = Address(
    country = addressProto.country.takeIf { addressProto.hasCountry() },
    city = addressProto.city.takeIf { addressProto.hasCity() },
    district = addressProto.district.takeIf { addressProto.hasDistrict() },
    ward = addressProto.ward.takeIf { addressProto.hasWard() },
    street = addressProto.street.takeIf { addressProto.hasStreet() }
)

fun Address.merge(another: Address) = Address(
    country = another.country.takeIf { it != null } ?: country,
    city = another.city.takeIf { it != null } ?: city,
    district = another.district.takeIf { it != null } ?: district,
    ward = another.ward.takeIf { it != null } ?: ward,
    street = another.street.takeIf { it != null } ?: street
)

fun Address.toAddressProto(): UserMessage.Address {
    val builder = UserMessage.Address.newBuilder()

    country?.let { builder.setCountry(it) }
    city?.let { builder.setCity(it) }
    district?.let { builder.setDistrict(it) }
    ward?.let { builder.setWard(it) }
    street?.let { builder.setStreet(it) }

    return builder.build()
}