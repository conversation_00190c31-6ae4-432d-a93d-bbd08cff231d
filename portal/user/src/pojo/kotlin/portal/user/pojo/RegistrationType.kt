package portal.user.pojo

/**
 * Type of registration, used as value for @BsonDiscriminator in child class of `UserRegistrationPojo`
 */
enum class RegistrationType {
    OTHER,
    EMAIL,
    GOOGLE,
    FACEBOOK;

    companion object {
        fun from(value: String): RegistrationType =
            valueOf(value.uppercase())

        fun fromSocial(value: String): RegistrationType? {
            val type = valueOf(value.uppercase())

            // Check is social
            if (
                arrayOf(
                    GOOGLE,
                    FACEBOOK,
                ).none { it === type }
            ) return null

            return type
        }
    }
}
