plugins {
    id "hnct.build"
    id "kotlin"
    id "application"
    id "kotlinx-serialization"
    id "com.google.protobuf"
    id "com.google.devtools.ksp"
}

version = "1.0.0"

application {
    mainClass = 'vinet.ccs.Launcher'
}

run {
    classpath += files("conf")
}

sourceSets {
    main {
        resources {
            srcDirs += [files("conf")]
        }
    }
}

dependencies {
     sourceSets {
         implementation metadata
     }
    internal {
        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
        implementation([id: ':common.libs', src: ['codec']], "viclass:common.libs-codec:1.0.0", true)
        implementation ":portal.kafka", "viclass:portal.kafka:1.0.0", true
        implementation([id: ':portal.kafka', src: ['events']], "viclass:portal.kafka-events:1.0.0", true)

        implementation([id: ':portal.grpc', src: ['user']], "viclass:portal.grpc-user:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['lsession']], "viclass:portal.grpc-lsession:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['classroom']], "viclass:portal.grpc-classroom:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['notification']], "viclass:portal.grpc-notification:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['metadataDoc']], "viclass:portal.grpc-metadataDoc:1.0.0", true)
        implementation([id: ':portal.lsession', src: ['pojo']], "viclass:portal.lsession-pojo:1.0.0", true)
        implementation([id: ':portal.datastructures', src: ['lsession']], "viclass:portal.datastructures-lsession:1.0.0", true)
        implementation([id: ':portal.notification', src: ['pojo']], "viclass:portal.notification-pojo:1.0.0", true)
    }

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation("io.insert-koin:koin-ktor:$koinVs")
    implementation("io.insert-koin:koin-logger-slf4j:$koinVs")
    // implementation "org.koin:koin-java:2.0.1"

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-jdk8:$coroutineVs"
    implementation 'org.jetbrains.kotlinx:kotlinx-serialization-protobuf:1.3.1'
    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"

    implementation "jayeson:jayeson.lib.utility:2.1.0"
    implementation "io.grpc:grpc-okhttp:${grpcVs}"

    implementation "io.ktor:ktor-server-core:$ktorVs"
    implementation "io.ktor:ktor-server-content-negotiation:$ktorVs"
    implementation "io.ktor:ktor-serialization-jackson:$ktorVs"
    implementation "io.ktor:ktor-server-netty:$ktorVs"
    implementation "io.ktor:ktor-server-cors:$ktorVs"
    implementation "io.ktor:ktor-server-status-pages:$ktorVs"
    implementation "io.ktor:ktor-server-jvm:$ktorVs"

    implementation "io.ktor:ktor-client-core:$ktorVs"
    implementation "io.ktor:ktor-client-cio:$ktorVs"
    implementation "io.ktor:ktor-client-content-negotiation:$ktorVs"

    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jacksonVs"

    implementation "com.google.guava:guava:$guavaVs"

    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    implementation("com.auth0:java-jwt:$javaJwtVs")

    // grpc dependencies
    implementation "com.google.protobuf:protobuf-java:$protobufVs"
    implementation "io.grpc:grpc-protobuf:$grpcVs"

    testImplementation 'junit:junit:4.13.2'
    

}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:$protobufVs"
    }
}

tasks.withType(Tar).configureEach {
    duplicatesStrategy = DuplicatesStrategy.WARN
}

tasks.withType(Zip).configureEach {
    duplicatesStrategy = DuplicatesStrategy.WARN
}