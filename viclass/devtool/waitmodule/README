Incremental watch builds using watch flag in angular or webpack would fail if a dependency within the workspace is not yet present. Once the watch builds failed for this reason, it is not able to recover, even if the dependency folder became fully built and present.

When the watch builds failed for this reason, it is often comes with the error saying that a module is not found.

This waitmodule does the following:

- Receive a build command from the command line and run the command
- Intercept the output of the watch build and look for `TS2307: Cannot find module '[module name]'`
- If a preconfigured mapping `[module name] => [folder]` is found,
    - Terminate the command
    - Use wait on and wait for the folder to present with a `package.json` inside
    - Restart the build command
- Else do nothing
