#!/usr/bin/env node

import yargs from 'yargs';
import path from 'path';
import { spawn } from 'child_process';

const argv = yargs(process.argv.slice(2))
    .scriptName('@viclass/waitmodule')
    .positional('[cmd]', {
        describe: 'Command to be run',
        demandOption: true,
        type: 'string',
    })
    .option('m', {
        alias: 'mapping',
        demandOption: true,
        default: './workspace-mapping.js',
        type: 'string',
        describe: 'workspace mapping contains list of modules and their folders',
    }).argv;

const mappings = require(path.join(process.cwd(), argv['mapping']));

const command: string = argv['_'][0];

const cmd: string[] = command.split(' ');

async function runCommand(command): Promise<number> {
    const sp = spawn(cmd[0], cmd.slice(1), {
        stdio: 'inherit',
        env: { ...process.env },
    });

    return new Promise((resolve, reject) => {
        // sp.on('close', (code) => {
        //     resolve(code)
        // });
        // sp.stdin.on('data', data => {
        //     let d : string = `${data}`
        //     if (d.includes("TS2307")) {
        //         sp.kill()
        //         resolve(1)
        //     }
        // })
        // sp.stderr.on('data', data => {
        //     let d : string = `${data}`
        //     if (d.includes("TS2307")) {
        //         sp.
        //         resolve(1)
        //     }
        // })
    });
}

runCommand(command);
