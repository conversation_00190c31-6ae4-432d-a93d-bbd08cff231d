# syntax=docker/dockerfile:1

from node:20-alpine as workspace_deps

ARG WORKSPACE_PATH=/viclass
ARG PROJECT_PATH=packages/backend/viclass/eb.word

WORKDIR ${WORKSPACE_PATH}

# includes all project dependency
COPY ./dist ./dist
# include workspace level configuration
# only copy these lock files so that we don't have to re-install dependencies 
# when source code changed
COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* tsconfig.json angular.json tailwind.config.js ./

RUN \
# run installer depending on the lock file availablilty
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild eb.word the source code only when needed
FROM workspace_deps AS builder
WORKDIR ${WORKSPACE_PATH}
COPY --from=workspace_deps ${WORKSPACE_PATH}/dist ${WORKSPACE_PATH}/dist
# first copy all installed dependencies which has been linked
COPY --from=workspace_deps ${WORKSPACE_PATH}/node_modules ${WORKSPACE_PATH}/node_modules

# Copy eb.word source from the context
COPY ./${PROJECT_PATH} ./${PROJECT_PATH}

# install the dependency inside the project folder
RUN cd ./${PROJECT_PATH} && yarn && yarn build

# Production image, copy all the files and run next
FROM node:20-alpine AS runner

ARG PORT=8012
ARG WORKSPACE_PATH=/viclass
ARG PROJECT_PATH=packages/backend/viclass/eb.word

WORKDIR ${WORKSPACE_PATH}
COPY --from=builder ${WORKSPACE_PATH}/dist ./dist
COPY --from=builder ${WORKSPACE_PATH}/node_modules ./node_modules
COPY --from=builder ${WORKSPACE_PATH}/${PROJECT_PATH}/node_modules ./${PROJECT_PATH}/node_modules
COPY --from=builder ${WORKSPACE_PATH}/${PROJECT_PATH}/dist ./${PROJECT_PATH}/dist
COPY --from=builder ${WORKSPACE_PATH}/${PROJECT_PATH}/*.json ./${PROJECT_PATH}/

WORKDIR ${WORKSPACE_PATH}/${PROJECT_PATH}

EXPOSE ${PORT}

ENV NODE_ENV=production
ENV PORT ${PORT}
ENV HOSTNAME localhost

CMD ["node", "dist/main.js"]