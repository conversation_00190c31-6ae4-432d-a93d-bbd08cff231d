/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
    "extends": "../../../../tsconfig.json",
    "compilerOptions": {
        "outDir": "../../../../out-tsc/app",
        "types": []
    },
    "files": [
        "src/main.ts",
        "src/polyfills.ts",
        "src/coordinator.docloader.ts",
        "src/coordinator.embed.ts",
        "src/editor.freedrawing.ts",
        "src/editor.geo.ts",
        "src/editor.word.ts",
        "src/editor.math.ts",
        "src/editor.magh.ts",
        "src/editorui.freedrawing.ts",
        "src/editorui.geo.ts",
        "src/editorui.word.ts",
        "src/editorui.math.ts",
        "src/editorui.magh.ts",
        "src/editorui.loader.webcomp.ts",
        "src/editorui.commontools.ts",
        "src/editorui.zoomtools.ts",
        "src/editorui.embedtools.ts",
        "src/editorui.contextmenutool.ts",
        "src/editorui.classroomtools.ts"
    ],
    "include": ["src/**/*.d.ts"]
}
