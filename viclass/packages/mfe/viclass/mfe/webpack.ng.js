const abs = require('../../../webpack/helpers');
const { merge } = require('webpack-merge');
const mfeConf = require('./webpack.mfe');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

function projPath(relative) {
    return abs(`packages/mfe/viclass/mfe/${relative}`);
}

module.exports = merge(mfeConf, {
    output: {
        uniqueName: '@viclass/mfe',
        publicPath: 'auto',
    },
});
