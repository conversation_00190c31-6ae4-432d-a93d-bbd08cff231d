const abs = require('../../../webpack/helpers');
const { merge } = require('webpack-merge');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');

function projPath(relative) {
    return abs(`packages/mfe/viclass/mfe/${relative}`);
}

module.exports = merge(
    {
        devtool: 'source-map',
        output: {
            uniqueName: '@viclass/style',
            publicPath: 'auto',
            path: abs('dist/viclass/themes'),
            filename: '[name].[contenthash:9].js',
        },
        mode: 'development',
        entry: {
            main: projPath('src/styles.sass'),
        },

        module: {
            rules: [
                {
                    test: /\.s[ca]ss$/,
                    use: [
                        //MiniCssExtractPlugin.loader,
                        {
                            loader: 'css-loader',
                            options: {
                                exportType: 'css-style-sheet',
                                import: true,
                                url: true,
                                importLoaders: 1,
                            },
                        },
                        {
                            loader: 'resolve-url-loader',
                        },
                        {
                            loader: 'sass-loader',
                            options: {
                                sourceMap: true,
                            },
                        },
                    ],
                },
            ],
        },
        resolve: {
            extensions: ['.tsx', '.ts', '.js', '.sass', '.scss', '.css'],
        },
        plugins: [
            new MiniCssExtractPlugin({
                filename: '[name].[contenthash:9].css',
                chunkFilename: '[id].[contenthash:9].css',
            }),
        ],
    },
    {
        experiments: {
            outputModule: true,
        },

        plugins: [
            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.freedrawing.style',
                filename: 'editorui.freedrawing.style.js',
                exposes: {
                    './editorui.freedrawing.style': projPath(`src/editorui.freedrawing.style.sass`),
                },
                shared: {},
            }),
            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.geo.style',
                filename: 'editorui.geo.style.js',
                exposes: {
                    './editorui.geo.style': projPath(`src/editorui.geo.style.sass`),
                },
                shared: {},
            }),
            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.word.style',
                filename: 'editorui.word.style.js',
                exposes: {
                    './editorui.word.style': projPath(`src/editorui.word.style.sass`),
                },
                shared: {},
            }),
            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.math.style',
                filename: 'editorui.math.style.js',
                exposes: {
                    './editorui.math.style': projPath(`src/editorui.math.style.sass`),
                },
                shared: {},
            }),
            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.magh.style',
                filename: 'editorui.magh.style.js',
                exposes: {
                    './editorui.magh.style': projPath(`src/editorui.magh.style.sass`),
                },
                shared: {},
            }),

            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.commontools.style',
                filename: 'editorui.commontools.style.js',
                exposes: {
                    './editorui.commontools.style': projPath(`src/editorui.commontools.style.sass`),
                },
                shared: {},
            }),

            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.classroomtools.style',
                filename: 'editorui.classroomtools.style.js',
                exposes: {
                    './editorui.classroomtools.style': projPath(`src/editorui.classroomtools.style.sass`),
                },
                shared: {},
            }),

            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'editorui.theme',
                filename: 'editorui.theme.js',
                exposes: {
                    './editorui.theme': projPath(`src/editorui.theme.sass`),
                },
                shared: {},
            }),

            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'viclass.theme.cursor',
                filename: 'viclass.theme.cursor.js',
                exposes: {
                    './viclass.theme.cursor': projPath(`src/viclass.theme.cursor.sass`),
                },
                shared: {},
            }),

            // new ModuleFederationPlugin({
            //     runtime: false,
            //     library: { type: 'module' },
            //     name: 'viclass.theme',
            //     filename: 'viclass.theme.js',
            //     exposes: {
            //         './viclass.theme': projPath(`src/viclass.theme.sass`),
            //     },
            //     shared: {},
            // }),

            new ModuleFederationPlugin({
                runtime: false,
                library: { type: 'module' },
                name: 'viclass.theme.embed',
                filename: 'viclass.theme.embed.js',
                exposes: {
                    './viclass.theme.embed': projPath(`src/viclass.theme.embed.sass`),
                },
                shared: {},
            }),
        ],
    }
);
