import { LoadRemoteModuleOptions } from '@angular-architects/module-federation-runtime';
import editorConfSpecs from './module.conf.spec';

export function envBase(domain: string) {
    const docLoader: LoadRemoteModuleOptions = {
        type: 'module',
        remoteEntry: `https://${domain}/modules/coordinator.docloader/coordinator.docloader.js`,
        exposedModule: './coordinator.docloader',
    };

    const base = {
        production: false, // default value
        confSpecs: editorConfSpecs,
        confEnv: 'dev', // the environment key to use with configuration service, default value
        moduleLookups: {
            coordinatorDocLoader: docLoader,
        },
        apiUri: `https://${domain}/conf/editors`,
    };

    return base;
}
