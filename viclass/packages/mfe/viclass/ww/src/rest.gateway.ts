import axios, { AxiosResponse } from 'axios';
import { environment } from './environments/environment';
import { MFEConfResponse } from '@viclass/config.server';

const e = environment.confEnv;

export class RestGateway {
    constructor() {}

    getEditorLookup(specs: object[]): Promise<AxiosResponse<MFEConfResponse>> {
        return axios.post<MFEConfResponse>(`${environment.apiUri}/${e}`, {
            specs: specs,
        });
    }
}
