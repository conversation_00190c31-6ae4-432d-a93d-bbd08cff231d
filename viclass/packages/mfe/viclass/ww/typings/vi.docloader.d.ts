declare global {
    namespace viclass {
        namespace ViDocLoader {
            type RenderOption = {
                vpType?: BoardType;
                lookAtX?: number;
                lookAtY?: number;
                zoom?: number;
                width?: number;
                height?: number;
                /**
                 * The element selector. All element with this selector will be
                 * checked for potential documents data to be rendered.
                 */
                docSelector?: string;
                edType?: EditorType;
                showTool?: boolean;
                tools?: CommonToolType[];
                toolVAlign?: string; // v-align toolbar
                toolHAlign?: string; // h-align toolbar
                toolDirection?: string; // toolbar direction
            };

            type RenderAllOption = RenderOption & {
                /**
                 * The element selector. All element with this selector will be
                 * checked for potential documents to be rendered.
                 */
                vpSelector?: string;
            };

            type ViewportEventType = 'viewport-pan' | 'viewport-zoom';

            interface IViewportEvent<ViewportEventType, S> {
                eventType: ViewportEventType;
                viewportId: string;
                state: S;
            }

            type ViewportEvent =
                | IViewportEvent<'viewport-pan', { x: number; y: number }>
                | IViewportEvent<'viewport-zoom', number>;

            type RemoveEventListenerFunc = () => void;

            var initialize: () => Promise<boolean>;
            var renderAll: (option?: RenderAllOption) => Promise<string[]>;
            var renderEl: (el: HTMLElement, option?: RenderOption) => Promise<string>;

            var addViewportEventListener: (
                vpId: string,
                vpType: BoardType,
                eventType: ViewportEventType,
                handler: (event: ViewportEvent) => void | Promise<void>
            ) => Promise<RemoveEventListenerFunc | null>;
        }
    }
}

export {};
