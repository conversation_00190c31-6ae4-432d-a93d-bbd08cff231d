const abs = require('../../../webpack/helpers');
const CopyPlugin = require('copy-webpack-plugin');

const { CleanWebpackPlugin } = require('clean-webpack-plugin');

function projPath(relative) {
    return abs(`packages/mfe/viclass/ww/${relative}`);
}

module.exports = {
    mode: 'development',
    devtool: 'eval-cheap-source-map',
    entry: {
        'docloader.coordinator': projPath('src/docloader.coordinator.wrapper.ts'),
        'vi.docloader': projPath('src/vi.docloader.ts'),
        // wrap in format that can be used directly on the website without additional async loading
        'mfe.webrt': projPath('src/mfe.runtime.js'),
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                use: [
                    {
                        loader: 'ts-loader',
                        options: {
                            compilerOptions: {
                                // for some endpoints, we need to generate declaration by hand
                                declaration: false,
                            },
                        },
                    },
                ],
                exclude: /node_modules/,
            },
        ],
    },
    resolve: {
        extensions: ['.tsx', '.ts', '.js'],
    },
    output: {
        path: abs('dist/viclass/ww'),
        publicPath: '',
        filename: '[name].js',
        scriptType: 'text/javascript',
        library: {
            name: 'viclass',
            type: 'assign-properties',
        },
        uniqueName: '@viclass/ww',
    },
    plugins: [
        new CopyPlugin({
            patterns: [
                {
                    from: projPath('typings/*.*').replace(/\\/g, '/'),
                    to: abs('dist/viclass/ww/'),
                },
                {
                    from: projPath('package.json'),
                    to: abs('dist/viclass/ww/package.json'),
                },
                {
                    from: projPath('src/mfe.runtime.js'),
                    to: abs('dist/viclass/ww/mfe.runtime.js'),
                },
            ],
        }),
        new CleanWebpackPlugin(),
    ],
};
