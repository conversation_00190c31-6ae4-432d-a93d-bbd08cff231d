const devConf = require('./webpack.dev');
const { merge } = require('webpack-merge');
const TerserJSPlugin = require('terser-webpack-plugin');
const webpack = require('webpack');
const abs = require('../../../webpack/helpers');

function projPath(relative) {
    return abs(`packages/mfe/viclass/ww/${relative}`);
}

module.exports = merge(
    {
        plugins: [
            // the environment replacement need to be the first one in plugins array
            new webpack.NormalModuleReplacementPlugin(
                /src[\\/]environments[\\/]environment\.ts/,
                projPath('src/environments/environment.prod.ts')
            ),
        ],
    },
    devConf,
    {
        mode: 'production',
        devtool: false,
        optimization: {
            minimizer: [
                new TerserJSPlugin({
                    terserOptions: {
                        format: {
                            comments: /webpackIgnore:true/,
                        },
                    },
                }),
            ],
        },
    }
);
