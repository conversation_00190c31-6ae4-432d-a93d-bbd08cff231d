import { Routes } from '@angular/router';
import {
    createRequireLoginFn,
    loginRequiredFn,
    LSessionService,
    LsRegistrationService,
    NotFoundPageComponent,
    profileResolveFn,
} from '@viclass/portal.common';
import { AppStateService } from './app.state.service';
import { CreatePageComponent } from './create-page/create-page.component';
import { ApiGateway } from './gateways/api.gateway';
import { CcsGateway } from './gateways/ccs.gateway';
import { ActivityStateService } from './online-page/activity.state.service';
import { BoardActionListener } from './online-page/board.action.listener';
import { ClassroomMarkerService } from './online-page/classroom.marker.service';
import { ClassroomPresenterService } from './online-page/classroom.presenter.service';
import { ClassroomSettingService } from './online-page/classroom.setting.service';
import { ClassroomConferenceService } from './online-page/conference/classroom.conference.service';
import { CoordinatorEventListener } from './online-page/coord.event.listener';
import { CoordStatesService } from './online-page/coord.state.service';
import { DocinfoStateService } from './online-page/docinfo.state.service';
import { DocumentActionListener } from './online-page/document.action.listener';
import { ClassroomErrorHandlerListener } from './online-page/error-handler';
import { EventNotiService } from './online-page/event-notification.component/event.service';
import { MemberActionListener } from './online-page/member.action.listener';
import { MemberStateService } from './online-page/member.state.service';
import { NotificationProcessor } from './online-page/notification.processor';
import { NotificationStateService } from './online-page/notification.state.service';
import { OnlineStateService } from './online-page/online.state.service';
import { ShareScreenService } from './online-page/share-screen-editor/share-screen.service';
import { SignalClassroomListener } from './online-page/signal.classroom.listener';
import { SignalCoordinatorListener } from './online-page/signal.coordinator.listener';
import { SignalProcessor } from './online-page/signal.processor';
import { UserActionListener } from './online-page/user.action.listener';
import { UserStatusDetector } from './online-page/user.status.detector';
import { ViewportContentEventListener } from './online-page/viewport.content.event.listener';
import {
    basicSessionInfoResolveFn,
    initializeStateFn,
    loggedInMemberInfoResolveFn,
    requireMemberFn,
} from './resolvers';

export const routes: Routes = [
    {
        path: 'create',
        component: CreatePageComponent,
        resolve: {
            profile: profileResolveFn,
        },
        canActivate: [createRequireLoginFn('tạo lớp học')],
    },
    {
        path: ':id',
        children: [
            { path: '', pathMatch: 'full', redirectTo: 'waiting' },
            {
                path: 'waiting',
                canActivate: [createRequireLoginFn('trang chờ')],
                loadComponent: () => import('./waiting-page/waiting-page.component').then(m => m.WaitingPageComponent),
                pathMatch: 'full',
            },
            {
                path: 'online',
                canActivate: [createRequireLoginFn('lớp học')],
                loadComponent: () => import('./online-page/online-page.component').then(m => m.OnlinePageComponent),
                pathMatch: 'full',
                providers: [
                    // services
                    ActivityStateService,
                    NotificationStateService,
                    OnlineStateService,
                    MemberStateService,
                    CoordStatesService,
                    DocinfoStateService,
                    // listeners
                    CoordinatorEventListener,
                    SignalClassroomListener,
                    UserActionListener,
                    MemberActionListener,
                    BoardActionListener,
                    SignalCoordinatorListener,
                    ViewportContentEventListener,
                    DocumentActionListener,
                    NotificationProcessor,
                    UserStatusDetector,
                    SignalProcessor,
                    ClassroomSettingService,
                    ClassroomPresenterService,
                    ClassroomMarkerService,
                    ClassroomConferenceService,
                    EventNotiService,
                    ShareScreenService,
                    ClassroomErrorHandlerListener,
                ],
                resolve: {
                    // this is not really ideal since what we want here is a guard instead of resolve, but guard runs before parent route resolve so....
                    hasMemberInfo: requireMemberFn,
                },
            },
        ],
        providers: [LsRegistrationService, LSessionService, AppStateService, ApiGateway, CcsGateway],
        canActivate: [loginRequiredFn, initializeStateFn],
        resolve: {
            profile: profileResolveFn,
            sessionInfo: basicSessionInfoResolveFn, // resolve details and settings, and combine with the profile of the creator
            memberInfo: loggedInMemberInfoResolveFn, // resolve the member information of the current user
        },
    },
    { path: '**', component: NotFoundPageComponent },
];
