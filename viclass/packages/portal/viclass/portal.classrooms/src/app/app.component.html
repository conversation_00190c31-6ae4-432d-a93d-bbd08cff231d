<!-- App-level loading indicator -->
<!-- Displayed when isLoading$ is true -->
<div
    *ngIf="isLoading$ | async"
    style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: rgba(51, 51, 51, 0.8); /* Should be synchronized with your theme, e.g., BW2/70 */
        z-index: 9998; /* High z-index, but potentially lower than specific page loaders if needed */
        font-family: sans-serif;
    ">
    <img src="assets/img/loading-icon.svg" style="width: 200px; margin-bottom: 15px" alt="Loading..." />
    <p style="font-size: 16px; color: #f0f0f0">Loading...</p>
    <!-- Text color should be synchronized with your theme (e.g., BW7) -->
</div>
<!-- Router outlet for displaying routed components -->
<router-outlet></router-outlet>
