import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import {
    Event as RouterEvent,
    NavigationCancel,
    NavigationEnd,
    NavigationError,
    NavigationStart,
    Router,
    RouterModule,
} from '@angular/router';
import { commonErrorHandlerEmitter, CommonErrorHandlerListener } from '@viclass/portal.common';
import { BehaviorSubject, Subscription } from 'rxjs'; // Import BehaviorSubject and Subscription

/**
 * The main application component.
 * It sets up a global loader that shows during initial app bootstrap and route transitions.
 */
@Component({
    imports: [CommonModule, RouterModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    selector: '[app-root]', // The component's selector, used as an attribute on the body tag.
    // The template includes a loading indicator and the main router outlet.
    templateUrl: 'app.component.html',
})
export class AppComponent implements OnInit, OnDestroy {
    // BehaviorSubject to control the visibility of the app-level loading indicator.
    // Starts with true to show loader immediately.
    public isLoading$ = new BehaviorSubject<boolean>(true);
    private routerSubscription: Subscription;

    constructor(
        private readonly commonErrorHandlerListener: CommonErrorHandlerListener,
        private router: Router
    ) {}

    ngOnInit(): void {
        // Subscribe to the common error handler.
        this.commonErrorHandlerListener.subscribe([commonErrorHandlerEmitter]);

        // Subscribe to router events to manage the loading indicator.
        this.routerSubscription = this.router.events.subscribe((event: RouterEvent) => {
            if (event instanceof NavigationStart) {
                // Show loader when navigation starts.
                this.isLoading$.next(true);
            } else if (
                event instanceof NavigationEnd || // Navigation finished successfully.
                event instanceof NavigationCancel || // Navigation was cancelled.
                event instanceof NavigationError // Navigation failed.
            ) {
                // Hide app-level loader when navigation ends (successfully, cancelled, or with error).
                // At this point, the target page/component should manage its own loading state if necessary.
                this.isLoading$.next(false);
            }
        });
    }

    ngOnDestroy(): void {
        this.commonErrorHandlerListener.unsubscribe();
        // Unsubscribe from router events to prevent memory leaks.
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }
}
