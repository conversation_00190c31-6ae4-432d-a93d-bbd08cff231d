import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ClassroomActivity } from '../activities';
import { map, take } from 'rxjs/operators';
import { ClassroomController, createRPC, NotificationController } from '@viclass/portal.common';
import { GetClassroomActivityRequest } from './api.model';

import { ClassroomNotification } from '../notifications';

@Injectable()
export class ApiGateway {
    private classroomApiServer = new ClassroomController();
    private notificationApiServer = new NotificationController();

    constructor(private http: HttpClient) {}

    loadClassroomActivities(lsId: string): Observable<ClassroomActivity[]> {
        return createRPC<ClassroomActivity[]>(this.classroomApiServer.loadClassroomActivities)(
            this.http,
            {},
            lsId
        ).pipe(
            take(1),
            map(res => {
                return res.body;
            })
        );
    }

    getClassroomActivity(data: GetClassroomActivityRequest): Observable<ClassroomActivity> {
        return createRPC<ClassroomActivity>(this.classroomApiServer.getClassroomActivity)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    loadNotificationById(notiId: string): Observable<ClassroomNotification> {
        return createRPC<ClassroomNotification>(this.notificationApiServer.loadNotificationById)(
            this.http,
            {},
            notiId
        ).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }

    loadClassroomNotification(lsId: string): Observable<ClassroomNotification[]> {
        return createRPC<ClassroomNotification[]>(this.notificationApiServer.loadClassroomNotification)(this.http, {
            body: JSON.stringify({ lsId: lsId }),
        }).pipe(
            take(1),
            map(response => {
                return response.body;
            })
        );
    }
}
