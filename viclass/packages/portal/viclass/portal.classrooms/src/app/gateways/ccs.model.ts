import { DocLocalId, DocumentId, EditorType, ViewportId } from '@viclass/editor.core';

export interface CcsBasicRequest {
    lsId: string;
}

export interface StartClassRequest extends CcsBasicRequest {
    callingUserId: string;
}

export interface StopClassRequest extends CcsBasicRequest {
    callingUserId: string;
}

export interface LeaveClassRequest extends CcsBasicRequest {
    callingPeerId: string;
}

export interface NewQuestionRequest extends CcsBasicRequest {
    callingPeerId: string;
    question: string;
}

export interface StopQuestionRequest extends CcsBasicRequest {
    callingPeerId: string;
    activityId: string;
}

/**
 * classroom owner send a request presentation to member
 */
export interface RequestPresentationRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
}

/**
 * classroom owner cancel request presentation of a member
 */
export interface CancelRequestPresentationRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
    activityId: string;
}

/**
 * member accept request presentation from classroom owner
 */
export interface AcceptRequestPresentationRequest extends CcsBasicRequest {
    callingPeerId: string;
    activityId: string;
}

/**
 * member reject request presentation from classroom owner
 */
export interface RejectRequestPresentationRequest extends CcsBasicRequest {
    callingPeerId: string;
    activityId: string;
}

/**
 * member end his presentation
 */
export interface EndPresentationRequest extends CcsBasicRequest {
    callingPeerId: string;
}

/**
 * classroom owner stop presentation of a member
 */
export interface StopPresentationRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
}

export interface RaiseHandRequest extends CcsBasicRequest {
    callingPeerId: string;
}

export interface ShareScreenRequest extends CcsBasicRequest {
    callingPeerId: string;
}

export interface RejectShareScreenRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
}

/**
 * Request interface for canceling a screen-sharing session.
 */
export interface CancelShareScreenRequest extends CcsBasicRequest {
    callingPeerId: string;
}

/**
 * member cancel raising hand
 */
export interface CancelRaiseHandRequest extends CcsBasicRequest {
    callingPeerId: string;
}

/**
 * classroom owner reject raising hand of a member
 */
export interface RejectRaiseHandRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
}

/**
 * classroom owner accept raising hand of a member
 */
export interface AcceptRaiseHandRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
}

export interface AcceptShareScreenRequest extends CcsBasicRequest {
    callingPeerId: string;
    targetUserId: string;
}

export interface DocumentInfoDetail {
    classroomId: string;
    coordStateId: ViewportId;
    docGlobalId: DocumentId;
    docLocalId: DocLocalId;
    docName?: string;
    ownerUserId: string;
    ownerUserName: string;
    ownerRegId: string;
    isValid?: boolean;
}

export interface DocumentInfo {
    docGlobalId: DocumentId;
    editorType: EditorType;
    details: DocumentInfoDetail;
}

export interface CreateDocumentInfoRequest {
    peerId: string;
    coordStateId: ViewportId;
    docs: DocumentInfo[];
}

export interface MarkValidDocumentRequest {
    peerId: string;
    coordStateId: ViewportId;
    docGlobalId: DocumentId;
    isValid: boolean;
    editorType: EditorType;
}

export interface MarkValidMultiDocumentRequest {
    peerId: string;
    coordStateId: ViewportId;
    docs: MarkValidDocument[];
}

export interface MarkValidDocument {
    docGlobalId: DocumentId;
    isValid: boolean;
    editorType: EditorType;
}

export interface MarkSelectedDocumentRequest {
    peerId: string;
    coordStateId: ViewportId;
    docGlobalId: DocumentId;
    selected: string[];
    deselected: string[];
}

export interface UpdateDocumentInfoRequest {
    peerId: string;
    coordStateId: ViewportId;
    docGlobalId: DocumentId;
    details: DocumentInfoDetail;
    editorType: EditorType;
}

export interface LoadDocumentInfoRequest {
    peerId: string;
    coordStateId: ViewportId;
    docGlobalId?: DocumentId;
    editorType?: EditorType;
}

export interface CreateJitsiJwtTokenRequest {
    peerId: string;
    userId: string;
    roomId: string;
}

export interface JitsiTokenResponse {
    token: string;
}
