import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { createRPC, ShortURLController } from '@viclass/portal.common';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';

@Injectable()
export class ShortURLGateway {
    private shortURLApi = new ShortURLController();

    constructor(private http: HttpClient) {}

    shortenURL(url: string): Observable<string> {
        // assuming the shortenURL endpoint expects { url: string } in the body and returns the shortened URL string
        return createRPC<{ shortUrl: string }>(this.shortURLApi.shortenURL)(this.http, {
            body: JSON.stringify({ url }),
        }).pipe(
            take(1),
            map(res => res.body.shortUrl)
        );
    }

    getStats(shortUrlId: string): Observable<any> {
        return createRPC<any>(this.shortURLApi.getStats)(this.http, {}, shortUrlId).pipe(
            take(1),
            map(res => res.body)
        );
    }

    getAllURLs(): Observable<any[]> {
        return createRPC<any[]>(this.shortURLApi.getAllURLs)(this.http).pipe(
            take(1),
            map(res => res.body)
        );
    }

    searchURLs(query: string): Observable<any[]> {
        return createRPC<any[]>(this.shortURLApi.searchURLs)(this.http, { params: { q: query } }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    deleteURL(shortUrlId: string): Observable<void> {
        return createRPC<void>(this.shortURLApi.deleteURL)(this.http, {}, shortUrlId).pipe(
            take(1),
            map(() => undefined)
        );
    }

    getAnalytics(): Observable<any> {
        return createRPC<any>(this.shortURLApi.getAnalytics)(this.http).pipe(
            take(1),
            map(res => res.body)
        );
    }
}
