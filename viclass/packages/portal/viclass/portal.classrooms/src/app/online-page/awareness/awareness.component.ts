import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { ClassroomCoordinator } from '@viclass/editor.coordinator/classroom';
import { Awareness, ViewportId } from '@viclass/editor.core';
import { NotificationService } from '@viclass/portal.common';
import { combineLatest, map, Observable, of, switchMap, tap } from 'rxjs';

const AW_TYPES_TO_SHOW = ['aw-document', 'aw-loading'];

@Component({
    standalone: true,
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'awareness',
    templateUrl: './awareness.component.html',
    styles: [],
})
export class AwarenessComponent implements AfterViewInit, OnInit, OnDestroy {
    @Input() coord$: Observable<ClassroomCoordinator>;
    @Input() vpId$: Observable<ViewportId>;

    awareness$: Observable<Awareness[]>;

    constructor(private notificationService: NotificationService) {
        this.awareness$ = of([]);
    }

    ngOnInit(): void {
        this.awareness$ = combineLatest([this.coord$, this.vpId$]).pipe(
            switchMap(([coord, vpId]) =>
                coord && vpId
                    ? (coord.awarenessFeature?.getReceivedAwareness(vpId)?.pipe(
                          tap(this.showNotifications),
                          map(awareness => awareness.filter(a => AW_TYPES_TO_SHOW.includes(a.options.type)))
                      ) ?? of([]))
                    : of([])
            )
        );
    }

    ngAfterViewInit(): void {}

    ngOnDestroy(): void {}

    private showNotifications = (awareness: Awareness[]) => {
        const notiMessages = awareness.filter(a => a.options.type === 'aw-notification');
        if (notiMessages.length > 0) {
            // should only show the last notification
            const lastNoti = notiMessages[notiMessages.length - 1];
            this.notificationService.showNotification({
                message: lastNoti.message,
                status: lastNoti.options.payload?.msgType ?? 'info',
                duration: 2000,
            });
        }
    };
}
