::ng-deep {
    .mat-mdc-menu-panel {
        border-radius: 10px !important;
        margin-top: 10px !important;
        --mat-menu-item-label-text-font: Montser<PERSON>;
        --mat-menu-item-label-text-line-height: 18px;
        --mat-menu-item-label-text-size: 12px;
    }
    .mat-mdc-menu-item {
        padding: 0px 10px;
        height: 38px;
        min-height: 38px !important;

        &:not([disabled]):hover {
            @apply bg-P3 w-full #{!important};
        }
    }
}

.board-tab {
    .selected {
        @apply bg-P2;
    }

    .board-tab-action {
        display: none;
    }

    &:hover {
        .board-tab-action {
            display: flex;
        }
    }
}

.v-tool-label-btn {
    &.hover {
        @apply bg-P3 #{!important};
        @apply text-BW1 #{!important};
    }
}
