import { Injectable, OnDestroy } from '@angular/core';
import { ClassroomMarkerState, ClassroomMarkerTool, ClassroomToolbar } from '@viclass/editor.coordinator/classroom';
import { DefaultToolBar, ToolEventData, ToolEventListener } from '@viclass/editor.core';
import {
    BehaviorSubject,
    combineLatest,
    distinctUntilChanged,
    firstValueFrom,
    map,
    Observable,
    pairwise,
    startWith,
    Subscription,
} from 'rxjs';

import { CoordStatesService } from './coord.state.service';
import { MarkerFieldChangeEmitterData } from './marker-tool';
import { OnlineStateService } from './online.state.service';

@Injectable()
export class ClassroomMarkerService implements OnDestroy {
    /**
     * observer for current and previous classroom toolbar
     */
    private readonly _classroomToolbar$: Observable<{ prev: ClassroomToolbar | null; curr: ClassroomToolbar | null }> =
        combineLatest([this.onlStateS.coordinator$, this.coordStateS.selected$]).pipe(
            startWith([null, null]), // start with null for initial state of pairwise()
            map(([coord, selectedVp]) => (coord && selectedVp ? coord.getClassroomToolbar(selectedVp) : null)),
            distinctUntilChanged(),
            pairwise(), // get both previous selected and current toolbar
            map(([prev, curr]) => ({
                prev: prev ?? null,
                curr: curr ?? null,
            }))
        );

    /**
     * observer for current presenter tool
     */
    private readonly _markerTool$: Observable<ClassroomMarkerTool | null> = this._classroomToolbar$.pipe(
        map(({ curr: curTb }) => (curTb?.getTool('markertool') as ClassroomMarkerTool) || null)
    );

    private readonly _classroomMarkerState$ = new BehaviorSubject<ClassroomMarkerState>(null);

    private toolListener = ClassroomMarkerService.ToolListener(this);

    private toolbarSuscribtion: Subscription;

    public isActiveMarkerTool$ = new BehaviorSubject<boolean>(false);

    get classroomMarkerState$(): Observable<ClassroomMarkerState> {
        return this._classroomMarkerState$.asObservable();
    }

    constructor(
        public onlStateS: OnlineStateService,
        public coordStateS: CoordStatesService
    ) {
        this.toolbarSuscribtion = this._classroomToolbar$.subscribe(({ prev, curr }) => {
            if (prev) {
                prev.unregisterToolListener(this.toolListener);
            }

            if (curr) {
                curr.registerToolListener(this.toolListener);
                this._classroomMarkerState$.next(curr.toolState('markertool'));
            }
        });
    }

    ngOnDestroy(): void {
        this.toolbarSuscribtion?.unsubscribe();
    }

    async getMarkerTool(): Promise<ClassroomMarkerTool | null> {
        return await firstValueFrom(this._markerTool$);
    }

    async onMarkerFieldChange(ev: MarkerFieldChangeEmitterData) {
        const markerTool = await this.getMarkerTool();

        if (markerTool) {
            await markerTool.adjustSettings({
                [ev.field]: ev.value,
            });
        }
    }

    async onEraseMyDrawing() {
        const markerTool = await this.getMarkerTool();

        if (markerTool) {
            await markerTool.eraseMyDrawing();
        }
    }

    async onEraseOtherDrawing() {
        const markerTool = await this.getMarkerTool();

        if (markerTool) {
            await markerTool.eraseAllDrawing();
        }
    }

    async toggleActiveMarkerTool() {
        const coord = await firstValueFrom(this.onlStateS.coordinator$);
        const vpId = await firstValueFrom(this.coordStateS.selected$);
        if (!vpId) return;

        const isActiveMarker = coord.getClassroomToolbar(vpId)?.isToolActive('markertool') ?? false;

        if (!isActiveMarker) coord.getClassroomToolbar(vpId).focus('markertool');
        else coord.getClassroomToolbar(vpId).blur('markertool');
    }

    private static ToolListener(_p: ClassroomMarkerService): ToolEventListener<DefaultToolBar<any, any>, any> {
        return new (class implements ToolEventListener<DefaultToolBar<any, any>, any> {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                if (eventData.toolType != 'markertool') return eventData;
                switch (eventData.eventType) {
                    case 'focus':
                        _p.isActiveMarkerTool$.next(true);

                        break;
                    case 'blur':
                        _p.isActiveMarkerTool$.next(false);

                        break;
                    case 'change': {
                        const toolState = eventData.state as ClassroomMarkerState;
                        _p._classroomMarkerState$.next(toolState);

                        break;
                    }
                    default:
                        break;
                }

                return eventData;
            }
        })();
    }
}
