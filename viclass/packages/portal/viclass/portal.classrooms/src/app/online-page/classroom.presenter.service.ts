import { Injectable, OnDestroy } from '@angular/core';
import {
    ClassroomPresenterState,
    ClassroomPresenterTool,
    ClassroomToolbar,
} from '@viclass/editor.coordinator/classroom';
import { DefaultToolBar, ToolEventData, ToolEventListener } from '@viclass/editor.core';
import {
    BehaviorSubject,
    combineLatest,
    distinctUntilChanged,
    firstValueFrom,
    map,
    Observable,
    pairwise,
    startWith,
    Subscription,
} from 'rxjs';

import { CoordStatesService } from './coord.state.service';
import { OnlineStateService } from './online.state.service';
import { SettingFieldChangeEmitterData } from './setting-tool';

@Injectable()
export class ClassroomPresenterService implements OnDestroy {
    /**
     * observer for current and previous classroom toolbar
     */
    private readonly _classroomToolbar$: Observable<{ prev: ClassroomToolbar | null; curr: ClassroomToolbar | null }> =
        combineLatest([this.onlStateS.coordinator$, this.coordStateS.selected$]).pipe(
            startWith([null, null]), // start with null for initial state of pairwise()
            map(([coord, selectedVp]) => (coord && selectedVp ? coord.getClassroomToolbar(selectedVp) : null)),
            distinctUntilChanged(),
            pairwise(), // get both previous selected and current toolbar
            map(([prev, curr]) => ({
                prev: prev ?? null,
                curr: curr ?? null,
            }))
        );

    /**
     * observer for current presenter tool
     */
    private readonly _presenterTool$: Observable<ClassroomPresenterTool | null> = this._classroomToolbar$.pipe(
        map(({ curr: curTb }) => (curTb?.getTool('presentertool') as ClassroomPresenterTool) || null)
    );

    /**
     * observer for doc presenter state of current viewport, include setting of all selected docs and default setting
     */
    private readonly _classroomPresenterState$ = new BehaviorSubject<ClassroomPresenterState>(null);

    private toolListener = ClassroomPresenterService.ToolListener(this);

    private toolbarSuscribtion: Subscription;

    get classroomPresenterState$(): Observable<ClassroomPresenterState> {
        return this._classroomPresenterState$.asObservable();
    }

    constructor(
        public onlStateS: OnlineStateService,
        public coordStateS: CoordStatesService
    ) {
        this.toolbarSuscribtion = this._classroomToolbar$.subscribe(({ prev, curr }) => {
            if (prev) {
                prev.unregisterToolListener(this.toolListener);
            }

            if (curr) {
                curr.registerToolListener(this.toolListener);
                this._classroomPresenterState$.next(curr.toolState('presentertool'));
            }
        });
    }

    ngOnDestroy(): void {
        this.toolbarSuscribtion?.unsubscribe();
    }

    async getPresenterTool(): Promise<ClassroomPresenterTool | null> {
        return await firstValueFrom(this._presenterTool$);
    }

    async onSettingFieldChange(ev: SettingFieldChangeEmitterData) {
        const presenterTool = await this.getPresenterTool();

        if (presenterTool) {
            await presenterTool.adjustSettings({
                [ev.field]: ev.value,
            });
        }
    }

    private static ToolListener(_p: ClassroomPresenterService): ToolEventListener<DefaultToolBar<any, any>, any> {
        return new (class implements ToolEventListener<DefaultToolBar<any, any>, any> {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                switch (eventData.eventType) {
                    case 'change': {
                        switch (eventData.toolType) {
                            case 'presentertool': {
                                const toolState = eventData.state as ClassroomPresenterState;

                                _p._classroomPresenterState$.next(toolState);
                                break;
                            }
                        }

                        break;
                    }
                    default:
                        break;
                }

                return eventData;
            }
        })();
    }
}
