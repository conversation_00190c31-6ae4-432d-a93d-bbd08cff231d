import { Injectable, OnDestroy } from '@angular/core';
import {
    ClassroomDocSettingState,
    ClassroomDocSettingTool,
    ClassroomToolbar,
} from '@viclass/editor.coordinator/classroom';
import { DefaultToolBar, ToolEventData, ToolEventListener } from '@viclass/editor.core';
import {
    BehaviorSubject,
    combineLatest,
    distinctUntilChanged,
    firstValueFrom,
    map,
    Observable,
    pairwise,
    startWith,
    Subject,
    Subscription,
} from 'rxjs';
import { CoordStatesService } from './coord.state.service';
import { OnlineStateService } from './online.state.service';
import { SettingFieldChangeEmitterData } from './setting-tool';

@Injectable()
export class ClassroomSettingService implements OnDestroy {
    /**
     * observer for current and previous classroom toolbar
     */
    private readonly _classroomToolbar$: Observable<{ prev: ClassroomToolbar | null; curr: ClassroomToolbar | null }> =
        combineLatest([this.onlStateS.coordinator$, this.coordStateS.selected$]).pipe(
            startWith([null, null]), // start with null for initial state of pairwise()
            map(([coord, selectedVp]) => (coord && selectedVp ? coord.getClassroomToolbar(selectedVp) : null)),
            distinctUntilChanged(),
            pairwise(), // get both previous selected and current toolbar
            map(([prev, curr]) => ({
                prev: prev ?? null,
                curr: curr ?? null,
            }))
        );

    /**
     * observer for current setting tool
     */
    private readonly _settingTool$: Observable<ClassroomDocSettingTool | null> = this._classroomToolbar$.pipe(
        map(({ curr: curTb }) => (curTb?.getTool('docsettingtool') as ClassroomDocSettingTool) || null)
    );

    /**
     * observer for doc settings state of current viewport, include setting of all selected docs and default setting
     */
    private readonly _docSettings$ = new BehaviorSubject<ClassroomDocSettingState>(null);

    private toolListener = ClassroomSettingService.ToolListener(this);

    private toolbarSuscribtion: Subscription;

    get docSetting$(): Observable<ClassroomDocSettingState> {
        return this._docSettings$.asObservable();
    }

    readonly settingToolToggle$ = new Subject<void>();

    constructor(
        public onlStateS: OnlineStateService,
        public coordStateS: CoordStatesService
    ) {
        this.toolbarSuscribtion = this._classroomToolbar$.subscribe(({ prev, curr }) => {
            if (prev) {
                prev.unregisterToolListener(this.toolListener);
            }

            if (curr) {
                curr.registerToolListener(this.toolListener);
                this._docSettings$.next(curr.toolState('docsettingtool'));
            }
        });
    }

    ngOnDestroy(): void {
        this.toolbarSuscribtion?.unsubscribe();
    }

    async getDocSettingTool(): Promise<ClassroomDocSettingTool | null> {
        return await firstValueFrom(this._settingTool$);
    }

    async onSettingFieldChange(ev: SettingFieldChangeEmitterData) {
        const docSettingTool = await this.getDocSettingTool();

        if (docSettingTool) {
            await docSettingTool.adjustSettings({
                [ev.field]: ev.value,
            });
        }
    }

    private static ToolListener(_p: ClassroomSettingService): ToolEventListener<DefaultToolBar<any, any>, any> {
        return new (class implements ToolEventListener<DefaultToolBar<any, any>, any> {
            onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
                switch (eventData.eventType) {
                    case 'change': {
                        switch (eventData.toolType) {
                            case 'docsettingtool': {
                                const toolState = eventData.state as ClassroomDocSettingState;

                                _p._docSettings$.next(toolState);
                                break;
                            }
                        }

                        break;
                    }
                    case 'toggle': {
                        if (eventData.toolType === 'docsettingtool') {
                            _p.settingToolToggle$.next();
                        }
                        break;
                    }
                    default:
                        break;
                }

                return eventData;
            }
        })();
    }
}
