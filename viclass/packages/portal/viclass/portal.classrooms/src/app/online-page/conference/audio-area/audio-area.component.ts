import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Observable } from 'rxjs';
import { AudioTileComponent } from '../audio-tile/audio-tile.component';
import { ClassroomConferenceService, ParticipantStream } from '../classroom.conference.service';

@Component({
    standalone: true,
    imports: [CommonModule, AudioTileComponent],
    selector: '[audio-area]',
    templateUrl: './audio-area.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AudioAreaComponent {
    constructor(public confS: ClassroomConferenceService) {}

    get streams$(): Observable<ParticipantStream[]> {
        return this.confS.remoteStreams;
    }
}
