import { classroom<PERSON><PERSON>r<PERSON>and<PERSON> } from '../error-handler';
import { Inject, Injectable, InjectionToken, NgZone, Optional } from '@angular/core';
import { DefaultEventEmitter, ErrorHandlerDecorator, Position, VEventData, VEventListener } from '@viclass/editor.core';
import { LSessionRegistrationModel, LsRegistrationService } from '@viclass/portal.common';
import { CreateLocalTracksOptions, JitsiMeetJSType } from 'lib-jitsi-meet';
import type JitsiConference from 'lib-jitsi-meet/esm/JitsiConference';
import type JitsiConnection from 'lib-jitsi-meet/esm/JitsiConnection';
import JitsiParticipant from 'lib-jitsi-meet/esm/JitsiParticipant';
import JitsiLocalTrack from 'lib-jitsi-meet/esm/modules/RTC/JitsiLocalTrack';
import JitsiRemoteTrack from 'lib-jitsi-meet/esm/modules/RTC/JitsiRemoteTrack';
import JitsiTrack from 'lib-jitsi-meet/esm/modules/RTC/JitsiTrack';
import { MediaType } from 'lib-jitsi-meet/esm/service/RTC/MediaType';
import { VideoType } from 'lib-jitsi-meet/esm/service/RTC/VideoType';
import { BehaviorSubject, firstValueFrom, map, Observable, Subject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AppStateService } from '../../app.state.service';
import { CcsGateway } from '../../gateways/ccs.gateway';
import { OnlineStateService } from '../online.state.service';

declare var JitsiMeetJS: JitsiMeetJSType;

export enum CallStatus {
    NOT_JOINED,
    JOINING,
    JOINED,
}

export enum ParticipantProperty {
    VICLASS_ID = 'vid',
    SCREEN_SHARE_DATA = 'ssdata',
}

export type ParticipantStream = {
    videoTrack: BehaviorSubject<JitsiTrack | undefined>;
    screenTrack: BehaviorSubject<JitsiTrack | undefined>;
    audioTrack: BehaviorSubject<JitsiTrack | undefined>;
    userId: string;
    jUserId: string; // id of the jitsi user
    hasAudio: BehaviorSubject<boolean | undefined>;
    hasVideo: BehaviorSubject<boolean | undefined>;
    hasScreen: BehaviorSubject<boolean | undefined>;
    showVideo: BehaviorSubject<boolean | undefined>;
    showAudio: BehaviorSubject<boolean | undefined>;
    showScreen: BehaviorSubject<boolean | undefined>; // True after screen sharing permission is granted, before setup is complete.
};

/**
 * Event to emit when an user is speaking or not speaking
 */
export type UserSpeakingEvent = {
    userId: string; // user id of the user who is speaking or not speaking
    audioLevel: number;
    speaking: boolean;
};

export type ConferenceConfig = {
    speakingAudioThreshold: number;
};

export const CONFERENCE_CONFIG_TOKEN = new InjectionToken('conference_config');

/**
 * Each user whose screenshare is shown inside the conference will have a piece of
 * data associated. This data is saved on JVB and loaded whenever an user join the conference.
 */
export type ScreenShareData = {
    uId: string; // the user id whose screen is being shown
    p: Position; // board position of the top left corner
    w: number;
    h: number;
};

export type DeviceConfig = { micId: string | null; cameraId: string | null; speakerId: string | null };

interface CustomCommandPayload<T = any> {
    type: 'custom-command';
    command: CustomCommands;
    data: T;
}

enum CustomCommands {
    CAMERA_PERMISSION_STOPPED = 'camera-permission-stopped',
}

interface CameraPermissionStoppedData {
    userId: string;
}

export type ClassroomConferenceServiceEvent = VEventData<
    | 'stop-share-screen-local-from-browser'
    | 'screen-sharing-has-been-stopped'
    | 'stop-camera-local-from-browser'
    | 'stop-audio-local-from-browser'
    | 'show-screen'
    | 'hidden-screen',
    ClassroomConferenceService,
    ParticipantStream
>;

/**
 * The conference service is responsible for managing video / audio state of the classroom.
 * Specifically:
 * - It provides API to show a video track of a user (requesting the respective stream source from the JVB)
 * - It integrate with lib-jitsi-meet (low level) to know who joins the video / audio conference and their
 * video / audio / screen tracks
 * - It provide API to save screenshare locations on to the JVB / Prosody
 */
@Injectable()
export class ClassroomConferenceService {
    private _conferenceEventEmitter: DefaultEventEmitter<ClassroomConferenceServiceEvent> = new DefaultEventEmitter();
    private _callStatus: BehaviorSubject<CallStatus> = new BehaviorSubject(CallStatus.NOT_JOINED);
    private jitsiConn: JitsiConnection;
    private conference: JitsiConference;
    private requestedSources = [];

    /**
     * My own stream
     */
    private _localStream: BehaviorSubject<ParticipantStream> = new BehaviorSubject(undefined);

    /**
     * Streams from remote users.
     *
     * IMPORTANT NOTE: any participant stream to be put into this list
     * must first be put into staging streams, and moved over using method moveOrReplaceParticipantStream
     * which would do proper checking and disposing of any existing stream
     */
    private _remoteStreams: BehaviorSubject<ParticipantStream[]> = new BehaviorSubject([]);

    /**
     * Because when user join the conference, his information is not yet complete
     * His partial data will be put in here until complete data is gathered
     * he will be put into the remote streams
     */
    private stagingStreams: Partial<ParticipantStream>[] = [];
    private registrationModel: LSessionRegistrationModel;

    /**
     * The stream of speaking change detected. When an user speak or
     * stop speaking, an event will be emit through here.
     */
    private _speakingChange: Subject<UserSpeakingEvent> = new Subject();
    private _config: ConferenceConfig;

    private _deviceConfig: DeviceConfig;
    private _listShowCamera: BehaviorSubject<string[]> = new BehaviorSubject([]);

    // Stores video tracks per participant
    // Used to track and update video type in case the initial type is incorrect when the track is first received
    private videoTrackMap = new Map<string, JitsiRemoteTrack[]>();

    public cameraGranted$ = new BehaviorSubject<boolean>(false);
    public micGranted$ = new BehaviorSubject<boolean>(false);

    registerConferenceEventListener(listener: VEventListener<ClassroomConferenceServiceEvent>) {
        this._conferenceEventEmitter.registerListener(listener);
    }

    unregisterConferenceEventListener(listener: VEventListener<ClassroomConferenceServiceEvent>) {
        this._conferenceEventEmitter.unregisterListener(listener);
    }

    constructor(
        private ccsGw: CcsGateway, // used for calling ccs
        private lsRegS: LsRegistrationService, // used to get registration information
        private onlS: OnlineStateService, // used to get peer information
        private as: AppStateService,
        private _ngZone: NgZone,
        @Optional() @Inject(CONFERENCE_CONFIG_TOKEN) config?: ConferenceConfig
    ) {
        this._config = config || {
            speakingAudioThreshold: 0.05,
        };

        this._deviceConfig = { micId: null, cameraId: null, speakerId: null };
        this.registerConferenceEventListener(new this.conferenceEventHandler(this));
        this.initPermissions();
    }

    setDeviceConfig(config: DeviceConfig): void {
        this._deviceConfig = config;
    }

    getDeviceConfig(): DeviceConfig {
        return this._deviceConfig;
    }

    get remoteStreams(): BehaviorSubject<ParticipantStream[]> {
        return this._remoteStreams;
    }

    get listShowCamera(): BehaviorSubject<string[]> {
        return this._listShowCamera;
    }

    get localStream(): BehaviorSubject<ParticipantStream> {
        return this._localStream;
    }

    get speakingEvent(): Observable<UserSpeakingEvent> {
        return this._speakingChange;
    }

    get callNotJoined(): Observable<boolean> {
        return this._callStatus.pipe(map(s => s != CallStatus.JOINED));
    }

    /**
     * Initializes permissions for camera and microphone.
     * - Requests access to media devices to prompt permission dialog if needed.
     * - Queries current permission states for camera and microphone.
     * - Sets up listeners to reactively track permission changes.
     */
    private async initPermissions() {
        try {
            // Request user permission for camera and microphone
            navigator.mediaDevices
                .getUserMedia({ video: true, audio: true })
                .then(stream => {
                    // Permission granted, stop tracks immediately since we don’t need them now
                    stream.getTracks().forEach(track => track.stop());
                })
                .catch(err => {
                    // Permission denied or error occurred
                    console.log('Media permission error:', err);
                });

            // Check current camera permission status
            const camPerm = await navigator.permissions.query({ name: 'camera' as PermissionName });
            this.cameraGranted$.next(camPerm.state === 'granted');

            // Listen for changes in camera permission
            camPerm.onchange = () => {
                this._ngZone.run(() => {
                    this.cameraGranted$.next(camPerm.state === 'granted');
                });
            };

            // Check current microphone permission status
            const micPerm = await navigator.permissions.query({ name: 'microphone' as PermissionName });
            this.micGranted$.next(micPerm.state === 'granted');

            // Listen for changes in microphone permission
            micPerm.onchange = () => {
                this._ngZone.run(() => {
                    this.micGranted$.next(micPerm.state === 'granted');
                });
            };
        } catch (err) {
            // Error during permission querying
            console.error('Lỗi khi truy vấn quyền truy cập:', err);
        }
    }

    /**
     * Join the conference room. The user doesn't need to turn on the camera to join the
     * conference.
     */
    @ErrorHandlerDecorator([classroomErrorHandler], 'JOIN_CALL')
    async joinCall(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this._ngZone.runOutsideAngular(async () => {
                if (this._callStatus.value !== CallStatus.NOT_JOINED) {
                    resolve(); // Already joined or joining, resolve the promise
                    return;
                }

                try {
                    const roomId = this.as.lsId;
                    this.registrationModel = await firstValueFrom(this.lsRegS.loggedInMI$.get(roomId));
                    const coordinator = await firstValueFrom(this.onlS.coordinator$);

                    const resp = await firstValueFrom(
                        this.ccsGw.generateJitsiJwt({
                            peerId: coordinator.syncConfig().peerId!,
                            userId: this.registrationModel.profile.id!,
                            roomId: roomId,
                        })
                    );

                    JitsiMeetJS.init({ disableSimulcast: true });
                    JitsiMeetJS.setLogLevel(JitsiMeetJS.logLevels.ERROR);

                    const options: any = environment.callConfig;
                    this.jitsiConn = new JitsiMeetJS.JitsiConnection(null, resp.token, options);
                    this._callStatus.next(CallStatus.JOINING);

                    // Event listener for connection success
                    this.jitsiConn.addEventListener(JitsiMeetJS.events.connection.CONNECTION_ESTABLISHED, async () => {
                        try {
                            await this.onConnectionSuccess();
                            resolve(); // Resolve the promise on successful connection
                        } catch (error) {
                            console.error('Error during onConnectionSuccess:', error);
                            reject(new Error('Error during connection success handling'));
                        }
                    });

                    // Event listener for connection failure
                    this.jitsiConn.addEventListener(JitsiMeetJS.events.connection.CONNECTION_FAILED, error => {
                        this.onConnectionFailed(error);
                        reject(new Error('Connection failed')); // Reject the promise on connection failure
                    });

                    this.jitsiConn.addEventListener(JitsiMeetJS.events.connection.CONNECTION_DISCONNECTED, async () => {
                        console.log('CONNECTION DISCONNECTED');
                    });

                    // Initiate the connection
                    this.jitsiConn.connect({});
                } catch (error) {
                    console.error('Error joining call:', error);
                    reject(error); // Reject the promise on any other error
                }
            });
        });
    }

    /**
     * Emits an event when the local microphone track is stopped directly from the browser
     * (e.g., via browser permission prompt or system-level action).
     *
     * Used to notify the conference system so it can update UI or participant state accordingly.
     */
    private onLocalMicTrackStoppedFromBrowser() {
        this._conferenceEventEmitter.emit({
            eventType: 'stop-audio-local-from-browser',
            state: this._localStream.value,
            source: this,
        });
    }

    /**
     * Emits an event when the local screen sharing track is stopped from the browser
     * (e.g., user clicks "Stop sharing" in browser UI).
     *
     * Ensures the system reacts properly by stopping screen sharing state and cleaning up if needed.
     */
    private onLocalShareScreenTrackStoppedFromBrowser() {
        this._conferenceEventEmitter.emit({
            eventType: 'stop-share-screen-local-from-browser',
            state: this._localStream.value,
            source: this,
        });
    }

    /**
     * Emits an event when the local camera track is stopped from the browser
     * (e.g., browser permissions revoked or camera manually disabled).
     *
     * Allows the app to detect and handle camera deactivation outside of in-app controls.
     */
    private onLocalCameraTrackStoppedFromBrowser() {
        this._conferenceEventEmitter.emit({
            eventType: 'stop-camera-local-from-browser',
            state: this._localStream.value,
            source: this,
        });
    }

    /**
     * Emits an event to notify that local screen sharing has been stopped.
     * Includes the current local stream state and the source object in the event payload.
     */
    private onLocalScreenSharingHasBeenStopped() {
        this._conferenceEventEmitter.emit({
            eventType: 'screen-sharing-has-been-stopped',
            state: this._localStream.value,
            source: this,
        });
    }

    /**
     * Enables the local video or screen-sharing track and adds it to the conference.
     *
     * @param type - Specifies whether to enable the 'video' (camera) or 'screen' (screen sharing) track.
     */
    async turnOnVideoTrackLocal(type: 'video' | 'screen') {
        const turnOnVideo = async () => {
            const isCamera = type === 'video';
            const streamValue = this._localStream.value;
            const trackKey = isCamera ? 'videoTrack' : 'screenTrack';
            const hasKey = isCamera ? 'hasVideo' : 'hasScreen';

            let stream;

            /**
             * Requests camera or screen stream based on `isCamera` flag.
             * - On failure, emits the appropriate stop event to keep conference state in sync.
             */
            if (isCamera) {
                try {
                    const cameraId = this._deviceConfig.cameraId;
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: cameraId ? { deviceId: { exact: cameraId } } : true,
                        audio: false,
                    });
                } catch (error) {
                    this.onLocalCameraTrackStoppedFromBrowser();
                    return;
                }
            } else {
                try {
                    stream = await navigator.mediaDevices.getDisplayMedia({ video: true, audio: false });
                } catch (error) {
                    this.onLocalShareScreenTrackStoppedFromBrowser();
                    return;
                }
            }

            const videoType = isCamera ? VideoType.CAMERA : VideoType.DESKTOP;
            /**
             * Object containing information about the media track. create by browser
             */
            const trackInfo = {
                stream: stream, // The media stream containing the track
                mediaType: 'video', // Specifies the type of media (video)
                videoType: videoType, // Indicates whether it's a camera or screen-sharing video
            };

            // If the stream is not a camera stream (i.e., it is a screen sharing stream)
            // and the showScreen flag is false, it means the screen sharing document
            // was either not created successfully or has already been deleted (e.g.,
            // the teacher deleted the shared screen document).
            // In that case, we stop all tracks from the stream and trigger the handler
            // to notify that local screen sharing has been stopped.
            if (!isCamera && !streamValue.showScreen?.value) {
                stream.getTracks().forEach(track => track.stop());
                this.onLocalScreenSharingHasBeenStopped();
                return;
            }
            const localTracks = await JitsiMeetJS.createLocalTracksFromMediaStreams([trackInfo]);
            for (const track of localTracks) {
                if (track && typeof track !== 'string' && track.getType() === MediaType.VIDEO) {
                    if (this.conference) {
                        if (streamValue[trackKey].value) {
                            if (!(streamValue[trackKey].value as JitsiLocalTrack).isMuted())
                                (streamValue[trackKey].value as JitsiLocalTrack).mute();

                            this.conference.replaceTrack(streamValue[trackKey].value as JitsiLocalTrack, track);
                        } else await this.conference.addTrack(track);

                        streamValue[trackKey].next(track);
                        // Mute the local camera track if it is currently unmuted and the stream is a camera track.
                        if (!(streamValue[trackKey].value as JitsiLocalTrack).isMuted() && isCamera)
                            (streamValue[trackKey].value as JitsiLocalTrack).mute();

                        (streamValue[trackKey].value as JitsiLocalTrack)?.unmute();
                    }

                    if (!isCamera)
                        // Listen for the event when the local screen-sharing track is stopped
                        track.addEventListener(
                            JitsiMeetJS.events.track.LOCAL_TRACK_STOPPED,
                            this.onLocalShareScreenTrackStoppedFromBrowser.bind(this)
                        );
                    else
                        // Listen for the event when the local camera track is stopped
                        track.addEventListener(
                            JitsiMeetJS.events.track.LOCAL_TRACK_STOPPED,
                            this.onLocalCameraTrackStoppedFromBrowser.bind(this)
                        );

                    streamValue[hasKey].next(true);
                    streamValue[trackKey].next(track);
                    this._localStream.next(streamValue);
                }
            }
        };

        await new Promise((resolve, reject) =>
            this._ngZone.runOutsideAngular(() => turnOnVideo().then(resolve).catch(reject))
        );
    }

    /**
     * Turns off the local video or screen-sharing track by muting it and updating its state.
     *
     * @param type - Specifies whether to turn off the 'video' (camera) or 'screen' (screen sharing) track.
     */
    private async turnOffVideoTrackLocal(type: 'video' | 'screen') {
        const turnOffVideo = async () => {
            const isCamera = type === 'video';
            const streamValue = this._localStream.value;
            const trackKey = isCamera ? 'videoTrack' : 'screenTrack';
            const hasKey = isCamera ? 'hasVideo' : 'hasScreen';
            const showKey = isCamera ? 'showVideo' : 'showScreen';

            const track = streamValue[trackKey]?.value as JitsiLocalTrack | undefined;
            if (track) {
                const nativeTrack = track.getTrack();
                // If the native track is still active, mute it to trigger proper signaling to remote peers.
                // If the track has ended (e.g., due to permission revoke), mute won't emit events,
                // so manually notify peers that camera permission was stopped.
                if (nativeTrack && nativeTrack.readyState !== 'ended') await track.mute();
                else this.sendCameraPermissionStopped(streamValue.userId);

                streamValue[hasKey].next(false);
                streamValue[showKey].next(false);
                this._localStream.next(streamValue);
            }
        };
        await new Promise((resolve, reject) =>
            this._ngZone.runOutsideAngular(() => turnOffVideo().then(resolve).catch(reject))
        );
    }

    /**
     * Create the local video track, and store it
     * @returns
     */
    async turnOnCamera() {
        await this.turnOnVideoTrackLocal('video');
        if (this._localStream.value.videoTrack.value && this._localStream.value.hasVideo.value) {
            this._localStream.value.showVideo.next(true);
            const userId = this.localStream?.value.userId;
            this.addListShowCamera(userId, true);
        }
    }

    /**
     * Remove the local video track, and also remove it from the conference
     */
    async turnOffCamera() {
        await this.turnOffVideoTrackLocal('video');
        const userId = this.localStream?.value.userId;
        this.removeListShowCamera(userId);
    }

    /**
     * Starts screen sharing by enabling the screen-sharing track.
     */
    async turnOnShareScreen() {
        await this.turnOnVideoTrackLocal('screen');
    }

    /**
     * Stops screen sharing by disabling the screen-sharing track.
     */
    async turnOffShareScreen() {
        await this.turnOffVideoTrackLocal('screen');
    }

    /**
     * Enables the local microphone and adds it to the conference.
     * Also sets the selected speaker (audio output device) if supported.
     */
    async turnOnMicrophone() {
        const options: CreateLocalTracksOptions = {
            devices: ['audio'],
        };

        if (this._deviceConfig.micId) options.micDeviceId = this._deviceConfig.micId;

        const turnOnMic = async () => {
            try {
                let localTracks;
                // Attempt to create local media tracks (e.g., mic, camera) with the given options.
                // If the user denies permission or an error occurs, trigger a mic stop handler
                // to update the app state and notify peers if necessary.
                try {
                    localTracks = await JitsiMeetJS.createLocalTracks(options);
                } catch (error) {
                    this.onLocalMicTrackStoppedFromBrowser();
                    return;
                }
                // Create local audio track

                for (const track of localTracks) {
                    if (track && typeof track !== 'string' && track.getType() == MediaType.AUDIO) {
                        if (this.conference) {
                            if (this._localStream.value.audioTrack.value) {
                                if (!(this._localStream.value.audioTrack.value as JitsiLocalTrack).isMuted())
                                    (this._localStream.value.audioTrack.value as JitsiLocalTrack).mute();

                                await this.conference.replaceTrack(
                                    this._localStream.value.audioTrack.value as JitsiLocalTrack,
                                    track
                                );
                            } else await this.conference.addTrack(track);

                            this._localStream.value.audioTrack.next(track);
                            this._localStream.value.hasAudio.next(true);
                            (this._localStream.value.audioTrack.value as JitsiLocalTrack)?.unmute();

                            // Listen for the event when the local mic track is stopped (e.g., browser permission revoked).
                            // Triggers handler to update local state and notify other participants.
                            track.addEventListener(
                                JitsiMeetJS.events.track.LOCAL_TRACK_STOPPED,
                                this.onLocalMicTrackStoppedFromBrowser.bind(this)
                            );

                            this._localStream.next(this._localStream.value);
                            // Now that the track is added, set the speaker (audio output device)
                            const speakerId = this._deviceConfig.speakerId; // Assuming this._deviceConfig.speakerId holds the selected speaker ID
                            if (speakerId) {
                                const isOutputChangeAvailable =
                                    await JitsiMeetJS.mediaDevices.isDeviceChangeAvailable('output');
                                if (isOutputChangeAvailable) {
                                    JitsiMeetJS.mediaDevices
                                        .setAudioOutputDevice(speakerId)
                                        .then(() => {
                                            console.log('Speaker set to:', speakerId);
                                        })
                                        .catch(error => {
                                            console.error('Failed to set speaker:', error);
                                        });
                                } else {
                                    console.log('Audio output device change is not supported.');
                                }
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Error turning on microphone or setting speaker:', error);
                throw error;
            }
        };

        await new Promise<void>((resolve, reject) => {
            this._ngZone.runOutsideAngular(() => turnOnMic().then(resolve).catch(reject));
        });
    }

    /**
     * Turns off the local microphone by muting it and updating the stream state.
     * Ensures the microphone is properly deactivated in the conference.
     */
    async turnOffMicrophone() {
        const turnOffMic = async () => {
            if (this._localStream.value.audioTrack?.value) {
                this._localStream.value.hasAudio.next(false);
                if (this.conference && this._localStream.value.audioTrack.value)
                    (this._localStream.value.audioTrack.value as JitsiLocalTrack).mute();

                this._localStream.next(this._localStream.value);
            }
        };
        await new Promise((resolve, reject) =>
            this._ngZone.runOutsideAngular(() => turnOffMic().then(resolve).catch(reject))
        );
    }
    /**
     * Toggles the user's camera on or off.
     * If the camera is active, it turns it off; otherwise, it turns it on.
     */
    async toggleCamera() {
        if (this._localStream.value.hasVideo.value) {
            await this.turnOffCamera(); // Turn off the camera if it's currently on
        } else {
            await this.turnOnCamera(); // Turn on the camera if it's currently off
        }
    }

    /**
     * Toggles screen sharing on or off.
     * If screen sharing is active, it turns it off; otherwise, it turns it on.
     */
    async toggleShareScreen() {
        this._ngZone.runOutsideAngular(async () => {
            if (this._localStream.value.hasScreen.value) {
                await this.turnOffShareScreen(); // Stop screen sharing if it's currently active
            } else {
                await this.turnOnShareScreen(); // Start screen sharing if it's currently inactive
            }
        });
    }

    /**
     * Toggles the microphone on or off.
     * If the microphone is active, it turns it off; otherwise, it turns it on.
     */
    async toggleMic() {
        if (this._localStream.value.hasAudio?.value) {
            await this.turnOffMicrophone(); // Mute the microphone if it's currently on
        } else {
            await this.turnOnMicrophone(); // Unmute the microphone if it's currently off
        }
    }

    /**
     * Mutes the microphone of all participants **except** the one with the given `userIdToKeepUnmuted`.
     *
     * @param userIdToKeepUnmuted - The user ID (VID) of the participant who should remain unmuted.
     */
    async muteAllMics(userIdToKeepUnmuted: string): Promise<void> {
        const keepUnmutedJid = this.getStreamByVid(userIdToKeepUnmuted)?.jUserId;

        this.conference.getParticipants().forEach(participant => {
            const participantJid = participant.getId();
            const shouldMute = !keepUnmutedJid || participantJid !== keepUnmutedJid;

            if (shouldMute) {
                this.conference.muteParticipant(participantJid, MediaType.AUDIO);
            }
        });
    }

    /**
     * Mutes the microphone of a specific user if they currently have audio enabled.
     *
     * @param userId - The user ID (VID) of the participant to be muted.
     */
    async muteMic(userId: string): Promise<void> {
        const stream = this.getStreamByVid(userId);
        const jitsiId = stream?.jUserId;

        if (stream?.hasAudio.value && jitsiId) {
            this.conference.muteParticipant(jitsiId, MediaType.AUDIO);
        }
    }

    /**
     * Handles the successful connection to the Jitsi conference.
     * Initializes the conference settings, registers event listeners, and joins the conference.
     *
     * @param event - Optional event parameter.
     * @returns A Promise that resolves when the conference is successfully joined.
     */
    onConnectionSuccess(event?: any): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            try {
                // Initialize the Jitsi conference
                this.conference = this.jitsiConn.initJitsiConference(this.as.lsId, {
                    p2p: {
                        enabled: false,
                        stunServers: [
                            { urls: 'stun:stun.l.google.com:19302' },
                            { urls: 'stun:stun1.l.google.com:19302' },
                        ],
                    },
                    resolution: 480, // Set the resolution for video streams
                    constraints: {
                        video: {
                            height: {
                                ideal: 480, // Set ideal height for the video
                                max: 480, // Set max height for the video
                                min: 180, // Set min height for the video
                            },
                        },
                    },
                    disableSimulcast: true, // Disable simulcast to prevent multiple streams with different resolutions (increased stability)

                    enableLayerSuspension: false, // Disable layer suspension to prevent dropping video layers based on available bandwidth

                    // Codec preferences
                    preferH264: true, // Prioritize H264 codec for video encoding, which provides better quality and compression
                    disableH264: false, // Ensure that H264 codec is not disabled, enabling better compatibility

                    // RTX (Retransmission) settings
                    disableRtx: true, // Disable RTX (to optimize performance by not requiring retransmission)

                    enableTcc: true, // Enable TCC (Transport-wide Congestion Control) to manage bandwidth and improve video quality in fluctuating network conditions

                    // Desktop sharing settings
                    desktopSharingFrameRate: {
                        min: 25, // Set minimum frame rate for screen sharing (lower frame rate for better performance)
                        max: 25, // Set maximum frame rate for screen sharing (ensure consistent frame rate)
                    },
                });

                // Set display name
                this.conference.setDisplayName(this.registrationModel.profile.username);

                // Initialize local user property
                this.conference.setLocalParticipantProperty(
                    ParticipantProperty.VICLASS_ID,
                    `${this.registrationModel.profile.id}`
                );

                // Set receiver constraints
                this.conference.setReceiverConstraints({
                    //lastN: this.requestedSources.length
                    selectedSources: ['-', ...this.requestedSources],
                });
                this.conference.setSenderVideoConstraint(180);

                // Event listeners for conference events
                this.conference.on(JitsiMeetJS.events.conference.CONFERENCE_JOINED, () => {
                    try {
                        this.onConferenceJoined();
                        resolve(); // Resolve the promise when the conference is joined
                    } catch (error) {
                        console.error('Error during conference joined handling:', error);
                        reject(new Error('Error during conference joined handling'));
                    }
                });
                this.conference.on(JitsiMeetJS.events.conference.USER_JOINED, this.onUserJoined.bind(this));
                this.conference.on(JitsiMeetJS.events.conference.USER_LEFT, this.onUserLeft.bind(this));
                this.conference.on(
                    JitsiMeetJS.events.conference.PARTICIPANT_PROPERTY_CHANGED,
                    this.onParticipantPropertyChange.bind(this)
                );
                this.conference.on(
                    JitsiMeetJS.events.conference.PROPERTIES_CHANGED,
                    this.onConferencePropertyChange.bind(this)
                );
                this.conference.on(JitsiMeetJS.events.conference.TRACK_ADDED, this.onRemoteTrackAdded.bind(this));
                this.conference.on(
                    JitsiMeetJS.events.conference.TRACK_MUTE_CHANGED,
                    this.onTrackMuteChanged.bind(this)
                );
                this.conference.on(JitsiMeetJS.events.conference.TRACK_REMOVED, this.onRemoteTrackRemoved.bind(this));
                this.conference.on(
                    JitsiMeetJS.events.conference.TRACK_AUDIO_LEVEL_CHANGED,
                    this.onAudioLevelChange.bind(this)
                );
                this.conference.on(JitsiMeetJS.events.conference.KICKED, this.onKicked.bind(this));
                this.conference.on(JitsiMeetJS.events.conference.MESSAGE_RECEIVED, this.onMessageReceived.bind(this));

                // Attempt to join the conference
                this.conference.join('');
            } catch (error) {
                console.error('Error during onConnectionSuccess execution:', error);
                reject(new Error('Error during onConnectionSuccess execution'));
            }
        });
    }
    // The main message handler that is called when a message is received.
    onMessageReceived(from: string, text: string) {
        try {
            const payload = JSON.parse(text) as CustomCommandPayload;

            if (payload.type === 'custom-command') {
                this.handleCustomCommand(from, payload);
            } else {
                this.handleTextMessage(from, text);
            }
        } catch (e) {
            // If parsing fails, treat the message as a regular chat message.
            this.handleTextMessage(from, text);
        }
    }

    /**
     * Handles regular chat messages.
     * @param from The sender's identifier.
     * @param text The message text.
     */
    handleTextMessage(from: string, text: string) {
        console.log(`Chat message from ${from}: ${text}`);
    }

    /**
     * Handles custom command payloads.
     * @param from The sender's identifier.
     * @param payload The parsed custom command payload.
     */
    handleCustomCommand(from: string, payload: CustomCommandPayload) {
        switch (payload.command) {
            case CustomCommands.CAMERA_PERMISSION_STOPPED: {
                // Cast the data to the expected type.
                const data = payload.data as CameraPermissionStoppedData;
                const stream = this.getStreamByVid(data.userId);
                if (
                    stream &&
                    stream.videoTrack &&
                    stream.videoTrack.value &&
                    data.userId != this.localStream.value.userId
                ) {
                    const track = stream.videoTrack.value as JitsiRemoteTrack;
                    track.setMute(true);
                } else {
                    console.warn(`Stream or video track for user ${data.userId} not found.`);
                }
                break;
            }
            default:
                console.warn(`Received unknown custom command: ${payload.command}`);
                break;
        }
    }

    onKicked() {
        this._localStream.next(null);
    }

    onConnectionFailed(event: any) {
        console.log('Connection failed', event);
    }

    /**
     * Handles actions when the user successfully joins the Jitsi conference.
     * Initializes the local media stream state and updates the call status.
     */
    onConferenceJoined() {
        // initialize local stream
        // console.log(
        //     '[CONFS] Create local stream. Am I moderator? ',
        //     this.conference.isModerator(),
        //     this.conference.getRole()
        // );
        this._localStream.next({
            videoTrack: new BehaviorSubject(undefined),
            audioTrack: new BehaviorSubject(undefined),
            screenTrack: new BehaviorSubject(undefined),
            userId: this.registrationModel.profile.id, // vid
            jUserId: this.conference.myUserId(), // jid
            hasAudio: new BehaviorSubject(false),
            hasVideo: new BehaviorSubject(false),
            hasScreen: new BehaviorSubject(false),
            showAudio: new BehaviorSubject(false),
            showVideo: new BehaviorSubject(true),
            showScreen: new BehaviorSubject(false),
        });

        this._callStatus.next(CallStatus.JOINED);
    }

    /**
     * check if a staging stream can be moved to the remote streams array for displaying
     **/
    private validateStagingStream(s: Partial<ParticipantStream>): boolean {
        if (s.userId !== undefined && s.jUserId !== undefined) return true;

        return false;
    }

    /**
     * Retrieves the media stream associated with a specific Jitsi user ID (`jid`).
     * It searches in staging streams and remote streams.
     *
     * @param jid - The Jitsi user ID of the participant whose stream is being retrieved.
     * @param nonStaging - If `true`, skips checking staging streams.
     * @returns A `Partial<ParticipantStream>` if the stream is found, otherwise `undefined`.
     */
    private getStreamByJid(jid: string, nonStaging: boolean = false): Partial<ParticipantStream> | undefined {
        if (!nonStaging) {
            const idx = this.stagingStreams.findIndex(v => v.jUserId == jid);

            if (idx >= 0) return this.stagingStreams[idx];
        }

        const idx1 = this._remoteStreams.value.findIndex(v => v.jUserId == jid);

        if (idx1 >= 0) return this._remoteStreams.value[idx1];

        return undefined;
    }

    /**
     * Retrieves the media stream associated with a specific user ID (`vid`).
     * It searches in staging streams, the local stream, and remote streams.
     *
     * @param vid - The user ID of the participant whose stream is being retrieved.
     * @param nonStaging - If `true`, skips checking staging streams.
     * @returns A `Partial<ParticipantStream>` if the stream is found, otherwise `undefined`.
     */
    getStreamByVid(vid: string, nonStaging: boolean = false): Partial<ParticipantStream> | undefined {
        if (!nonStaging) {
            const idx = this.stagingStreams.findIndex(v => v.userId == vid);

            if (idx >= 0) return this.stagingStreams[idx];
        }

        if (this.localStream?.value && vid === this.localStream?.value.userId) return this.localStream.value;

        const idx1 = this._remoteStreams.value.findIndex(v => v.userId == vid);

        if (idx1 >= 0) return this._remoteStreams.value[idx1];

        return undefined;
    }

    /**
     * Handles changes in a participant's properties within the Jitsi conference.
     * Primarily checks for changes in the VICLASS_ID property and updates the corresponding stream.
     *
     * @param participant - The Jitsi participant whose property changed.
     * @param fieldName - The name of the property that changed.
     * @param oldValue - The previous value of the property.
     * @param newValue - The new value of the property.
     */
    onParticipantPropertyChange(participant: JitsiParticipant, fieldName: string, oldValue: any, newValue: any) {
        if (fieldName == ParticipantProperty.VICLASS_ID) {
            // search staging data
            const idx = this.stagingStreams.findIndex(v => v.jUserId == participant.getId());

            if (idx >= 0) {
                this.stagingStreams[idx].userId = newValue;

                // if the staging stream is okay for viewing, move it to the
                // remote participant stream, replace any existing track
                if (this.validateStagingStream(this.stagingStreams[idx])) this.moveStagingStreamToRemote(idx);
            }
        }
    }

    /**
     * This method move the participant stream from staging array to the remote stream.
     * Because each user must only have one remote participant stream, it will replace the participant stream inside
     * the remote stream array.
     *
     * If replacement is needed, checks if the current remote stream has any existing media stream
     * and dispose of those stream before replacing.
     *
     * @param idx Index of the participant stream in staging area to be move
     */
    private moveStagingStreamToRemote(idx: number) {
        const userId = this.stagingStreams[idx].userId;
        const pStreams = this._remoteStreams.value;

        const curIdx = pStreams.findIndex(v => v.userId == userId);

        if (curIdx >= 0) this.removeRemoteParticipantStream(pStreams, curIdx);

        pStreams.splice(curIdx, 0, this.stagingStreams[idx] as ParticipantStream);
        this._remoteStreams.next(pStreams);

        this.removeStagingParticipantStream(idx);
    }

    /**
     * Handles a new user joining the Jitsi conference.
     * Creates a placeholder for the participant's media streams in the staging area.
     *
     * @param id - The unique Jitsi-assigned user ID for the participant.
     * @param user - The Jitsi participant object representing the new user.
     */
    onUserJoined(id: string, user: JitsiParticipant) {
        // add place holder for the participant stream
        this.stagingStreams.push({
            videoTrack: new BehaviorSubject(undefined),
            audioTrack: new BehaviorSubject(undefined),
            screenTrack: new BehaviorSubject(undefined),
            jUserId: id,
            hasAudio: new BehaviorSubject(false),
            hasVideo: new BehaviorSubject(false),
            hasScreen: new BehaviorSubject(false),
            showAudio: new BehaviorSubject(true), // by default, if the user turn on mic, the audio of the user is turned on
            showVideo: new BehaviorSubject(false), // by default, the user's video is not shown on my screen
            showScreen: new BehaviorSubject(false), // by default doesn't show user screen
        });
    }

    private removeStagingParticipantStream(idx: number) {
        this.stagingStreams.splice(idx, 1);
    }

    private removeRemoteParticipantStream(pStreams: ParticipantStream[], pIdx: number) {
        const stream = pStreams[pIdx];
        if (stream.videoTrack.value) {
            this.onTurnOffMute(stream.videoTrack.value as JitsiRemoteTrack);
        }

        if (stream.audioTrack.value) {
            this.onTurnOffMute(stream.videoTrack.value as JitsiRemoteTrack);
        }

        if (stream.screenTrack.value) {
            this.onTurnOffMute(stream.screenTrack.value as JitsiRemoteTrack);
        }

        if (this.videoTrackMap.has(stream.jUserId)) {
            const tracks = this.videoTrackMap.get(stream.jUserId);
            for (const t of tracks) {
                (t as any).off(
                    JitsiMeetJS.events.track.TRACK_VIDEOTYPE_CHANGED,
                    this.handleVideoTypeChange.bind(this, t)
                );
            }
            this.videoTrackMap.delete(stream.jUserId);
        }

        pStreams.splice(pIdx, 1); // remove the stream
        this._remoteStreams.next(pStreams);
    }

    onUserLeft(id: string) {
        const sIdx = this.stagingStreams.findIndex(v => v.jUserId == id);
        if (sIdx >= 0) this.removeStagingParticipantStream(sIdx);
        else {
            const pStreams = this._remoteStreams.value;
            const pIdx = pStreams.findIndex(v => v.jUserId == id);
            if (pIdx >= 0) {
                this.removeRemoteParticipantStream(pStreams, pIdx);
            }
        }
    }

    /**
     * Handles mute state changes for remote or local Jitsi tracks.
     *
     * - For remote tracks:
     *   - Retrieves the associated stream using the participant ID.
     *   - If it's a video track:
     *     - Calls `onTurnOnMute` if unmuted.
     *     - Calls `onTurnOffMute` if muted.
     *   - If it's an audio track:
     *     - Updates the stream's `hasAudio` observable to reflect the mute state.
     *
     * - For local audio tracks:
     *   - This typically occurs when the teacher mutes or unmutes the student.
     *   - Updates the local stream's `hasAudio` observable accordingly.
     *
     * @param track The JitsiRemoteTrack object whose mute state has changed.
     */
    onTrackMuteChanged(track: JitsiRemoteTrack) {
        if (!track.isLocal()) {
            const jUid = track.getParticipantId();
            const s = this.getStreamByJid(jUid);
            if (s) {
                if (track.getType() == MediaType.VIDEO) {
                    if (!track.isMuted()) this.onTurnOnMute(track);
                    else this.onTurnOffMute(track);
                } else {
                    s.hasAudio.next(!track.isMuted());
                }
            }
        } else {
            if (track.getType() == MediaType.AUDIO) this.localStream.value.hasAudio.next(!track.isMuted());
        }
    }

    onRemoteTrackAdded(track) {
        this.onTurnOnMute(track);
    }

    onRemoteTrackRemoved(track) {
        this.onTurnOffMute(track);
    }

    onTurnOffMute(track: JitsiRemoteTrack) {
        if (!track.isLocal()) {
            const jUid = track.getParticipantId();
            const s = this.getStreamByJid(jUid);

            if (!s) return;

            if (track.getType() == 'video') {
                if (track.getVideoType() == VideoType.CAMERA) {
                    s.videoTrack.next(undefined);
                    s.hasVideo.next(false);
                    s.showVideo.next(false);
                } else {
                    s.screenTrack.next(undefined);
                    s.hasScreen.next(false);
                    s.showScreen.next(false);
                }

                // Check if the track exists in videoTrackMap for this participant
                // If found, remove the video type change listener and delete the track from the list
                if (this.videoTrackMap.has(jUid)) {
                    const index = this.videoTrackMap.get(jUid).findIndex(item => item.getId() === track.getId());

                    if (index !== -1) {
                        (track as any).off(
                            JitsiMeetJS.events.track.TRACK_VIDEOTYPE_CHANGED,
                            this.handleVideoTypeChange.bind(this, track)
                        );

                        this.videoTrackMap.get(jUid).splice(index, 1);
                    }
                }
            } else {
                s.audioTrack.next(undefined);
                s.hasAudio.next(false);
            }
        }
    }

    onTurnOnMute(track: JitsiRemoteTrack, videoChangeType = false) {
        if (!track.isLocal()) {
            const jUid = track.getParticipantId();
            const participant = this.conference.getParticipantById(jUid);

            if (!participant) {
                console.log(
                    '[CONFS] Inconsistency detected, receiving a track without participant. Discard the track.'
                );
                return;
            }

            const vid = participant.getProperty(ParticipantProperty.VICLASS_ID);
            if (!vid) {
                console.log('[CONFS] Inconsistency detected, receiving a track with participant without viclass id.');
                return;
            }

            const s = this.getStreamByJid(jUid);

            if (s) {
                if (track.getType() === 'video') {
                    if (
                        track.getVideoType() === VideoType.CAMERA &&
                        (!s.videoTrack.value || s.videoTrack.value.getId() == track.getId())
                    ) {
                        s.videoTrack.next(track);
                        s.hasVideo.next(!track.isMuted());
                    } else if (
                        track.getVideoType() === VideoType.DESKTOP &&
                        (!s.screenTrack.value || s.screenTrack.value.getId() == track.getId())
                    ) {
                        s.screenTrack.next(track);
                        s.hasScreen.next(!track.isMuted());
                    }

                    // Add track to videoTrackMap if not already added
                    // Then listen for video type change on this track
                    if (!videoChangeType) {
                        if (
                            this.videoTrackMap.has(jUid) &&
                            !this.videoTrackMap.get(jUid).find(t => t.getId() === track.getId())
                        ) {
                            this.videoTrackMap.get(jUid).push(track);
                        } else {
                            this.videoTrackMap.set(jUid, [track]);
                        }

                        (track as any).on(
                            JitsiMeetJS.events.track.TRACK_VIDEOTYPE_CHANGED,
                            this.handleVideoTypeChange.bind(this, track)
                        );
                    }
                } else {
                    if (!s.audioTrack.value || s.audioTrack.value.getId() !== track.getId()) {
                        s.audioTrack.next(track);
                        s.hasAudio.next(!track.isMuted());
                    }
                }
            }
            this._remoteStreams.next(this._remoteStreams.value);
        }
    }

    // Handle video type change and update stream state
    // Remove listener after handling to avoid duplicate calls
    // Needed because initial video type might be incorrect when track is first received
    private handleVideoTypeChange(track: JitsiRemoteTrack) {
        console.log('Video type changed', track);
        const jUid = track.getParticipantId();

        const tracks: JitsiRemoteTrack[] = this.videoTrackMap.get(jUid);
        if (!tracks || !tracks.length) return;

        // If only one track, remove the incorrectly assigned track and update stream state
        // If multiple tracks, just mute all of them
        if (tracks.length == 1) {
            const t = tracks[0];
            const s = this.getStreamByJid(jUid);
            if (track.getVideoType() != VideoType.CAMERA) {
                s.videoTrack.next(undefined);
                s.hasVideo.next(false);
                s.showVideo.next(false);
            } else {
                s.screenTrack.next(undefined);
                s.hasScreen.next(false);
                s.showScreen.next(false);
            }
            this.onTurnOnMute(t, true);
        } else {
            for (const t of tracks) {
                this.onTurnOnMute(t as JitsiRemoteTrack, true);
            }
        }

        (track as any).off(
            JitsiMeetJS.events.track.TRACK_VIDEOTYPE_CHANGED,
            this.handleVideoTypeChange.bind(this, track)
        );
    }

    private onAudioLevelChange(jid: string, audioLevel: number) {
        const s = this.getStreamByJid(jid, true);
        if (s)
            this._speakingChange.next({
                userId: s.userId!,
                audioLevel: audioLevel,
                speaking: audioLevel > this._config.speakingAudioThreshold,
            });
    }

    private addListShowCamera(userId: string, isFirstAdd: boolean = false) {
        const lCamera = this._listShowCamera.value;
        if (lCamera.indexOf(userId) < 0 && !isFirstAdd) lCamera.push(userId);
        else lCamera.unshift(userId);
        this._listShowCamera.next(lCamera.sort());
    }

    private removeListShowCamera(userRegId: string) {
        const lCamera = this._listShowCamera.value;
        this._listShowCamera.next([...lCamera.filter(v => v != userRegId)]);
    }

    showUserTrack(userId: string, type: VideoType = VideoType.CAMERA) {
        if (userId === this.localStream?.value?.userId && type == VideoType.CAMERA) {
            this.turnOnCamera();
            return;
        }
        const s = this.getStreamByVid(userId);

        if (s) {
            let t = s.videoTrack.value;
            if (type == VideoType.DESKTOP) t = s.screenTrack.value;
            if (t) {
                const sourceName = (t as JitsiRemoteTrack).getSourceName();
                if (this.requestedSources.indexOf(sourceName) < 0) {
                    this.requestedSources.push(sourceName);
                    this.conference.setReceiverConstraints({
                        selectedSources: ['-', ...this.requestedSources],
                    });
                    if (type == VideoType.CAMERA) {
                        if (!s.showVideo.value) s.showVideo.next(true);
                        this.addListShowCamera(s.userId);
                    }
                }
            }
        }
    }

    hideUserTrack(userId: string, type: VideoType = VideoType.CAMERA) {
        if (userId === this.localStream?.value.userId && type == VideoType.CAMERA) {
            this.turnOffCamera();
            return;
        }
        const s = this.getStreamByVid(userId);

        if (s) {
            let t = s.videoTrack.value;
            if (type == VideoType.DESKTOP) t = s.screenTrack.value;
            if (t) {
                const sourceName = (t as JitsiRemoteTrack).getSourceName();
                const newSources = this.requestedSources.filter(src => src != sourceName);

                if (newSources.length != this.requestedSources.length) {
                    this.requestedSources.splice(0, this.requestedSources.length, ...newSources);
                    this.conference.setReceiverConstraints({
                        selectedSources: ['-', ...this.requestedSources],
                    });

                    console.log('Requesting sources --- ', this.requestedSources);
                }
            }
            if (s.showVideo.value) s.showVideo.next(false);
            this.removeListShowCamera(s.userId);
        }
    }

    private onConferencePropertyChange(fieldName: string, oldValue: any, newValue: any) {
        // emit the screenshare data to the screenshare data behavior subject to inform the user
    }

    /**
     * This method is intended to save the location of the shared screens on the JVB as conference property
     * This is mainly used for the presenter to persist the current state of the screenshare so that
     * another user who join this conference can load the screenshare data.
     * @param data
     */
    saveScreenShareLocation(data: ScreenShareData[]) {
        // TODO: change the conference property to save the screenshare data
        throw new Error('Not implemented yet!');
    }

    /**
     * Kicks out a user from the conference based on their user ID.
     *
     * This function uses the provided userId to lookup the corresponding stream.
     * If a valid stream is found, it retrieves the Jitsi user identifier (jUserId)
     * and then calls the kickParticipant method on the current conference instance.
     *
     * @param userId - The ID of the user to be kicked.
     */
    kickOutUser(userId: string): void {
        const stream = this.getStreamByVid(userId);
        if (!stream || !stream.jUserId) {
            console.error(`Unable to find a valid participant for userId: ${userId}`);
            return;
        }
        const participantId: string = stream.jUserId;

        try {
            this.conference.kickParticipant(participantId);
        } catch (error) {
            console.error(`Failed to kick participant with id ${participantId}:`, error);
        }
    }

    // Handles browser stop events for local camera/mic.
    // If the current user stopped the track, turn off the corresponding media.
    conferenceEventHandler = class implements VEventListener<ClassroomConferenceServiceEvent> {
        constructor(private confService: ClassroomConferenceService) {}
        onEvent(
            eventData: ClassroomConferenceServiceEvent
        ): ClassroomConferenceServiceEvent | Promise<ClassroomConferenceServiceEvent> {
            const state = eventData.state;

            switch (eventData.eventType) {
                case 'stop-camera-local-from-browser': {
                    const userId = state.userId;
                    if (this.confService.localStream?.value?.userId == userId) {
                        this.confService.turnOffCamera();
                    }
                    break;
                }
                case 'stop-audio-local-from-browser': {
                    const userId = state.userId;
                    if (this.confService.localStream?.value?.userId == userId) {
                        this.confService.turnOffMicrophone();
                    }
                    break;
                }
                default:
                    break;
            }
            return eventData;
        }
    };

    // Sends a custom command to all peers in the conference if joined.
    private sendCustomCommand(command: CustomCommands, data: any) {
        if (!this.conference || !this.conference.isJoined()) {
            console.warn('Conference not joined yet');
            return;
        }

        const payload = {
            type: 'custom-command',
            command,
            data,
        };

        this.conference.sendMessage(JSON.stringify(payload));
    }

    // Notifies peers that the user's camera permission was stopped or track ended.
    private sendCameraPermissionStopped(userId: string) {
        this.sendCustomCommand(CustomCommands.CAMERA_PERMISSION_STOPPED, { userId });
    }
}
