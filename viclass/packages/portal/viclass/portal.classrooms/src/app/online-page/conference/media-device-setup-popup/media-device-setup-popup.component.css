/* Tailwind handles the layout. Use these to ensure proportional width: */
.w-7\/10 {
    width: 60%;
}

.w-3\/10 {
    width: 40%;
}

::ng-deep .cdk-overlay-dark-backdrop {
    background-color: rgba(0, 0, 0, 0.8); /* Change the second parameter for opacity */
}

.cdk-overlay-dark-backdrop {
    background-color: rgba(0, 0, 0, 0.8); /* Change the second parameter for opacity */
}

::ng-deep .mat-mdc-dialog-container .mdc-dialog__surface {
    border-radius: 30px !important;
}

select {
    border: 1px solid #a4adb4;
}

select:focus {
    outline: none; /* No outline on focus */
    border: 1px solid #a4adb4;
}
