<div class="h-full w-full flex bg-white p-[20px]">
    <!-- Video Section (70%) -->
    <div class="w-7/10 relative flex items-center justify-center p-1.5" style="padding: 10px">
        <!-- Placeholder video element -->
        <video
            id="video-stream"
            #videoEl
            class="w-full h-full object-cover rounded-[10px]"
            [style.display]="isCameraActive ? 'block' : 'none'"
            autoplay></video>

        <!-- Black overlay when camera is off -->
        <div
            *ngIf="!isCameraActive"
            class="bg-black flex items-center justify-center rounded-[10px] w-full h-full"
            style="z-index: 10">
            <span class="text-white">Camera đang tắt</span>
        </div>

        <!-- Microphone and Camera toggle buttons -->
        <div class="flex absolute bottom-4 right-[20px] space-x-[10px]" style="z-index: 11">
            <!-- Microphone button -->
            <button
                #micButton
                class="bg-white rounded-full w-[30px] h-[30px] flex !justify-center !items-center relative"
                [disabled]="!(micGranted | async)"
                (click)="toggleMicrophone()">
                <ng-container *ngIf="isMicActive && isUserSpeaking; else micIcon">
                    <mic-speaking></mic-speaking>
                </ng-container>

                <ng-template #micIcon>
                    <i class="vcon vcon-onl" [ngClass]="{ vcon_mic_off: !isMicActive, vcon_mic_on: isMicActive }"></i>
                    <span class="absolute top-0 right-0 z-10 scale-50 origin-top-right" *ngIf="!(micGranted | async)">
                        <span class="vcon vcon-general vcon_general_warning">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </span>
                    </span>
                </ng-template>
            </button>
            <lib-tooltip
                [toolTipFor]="micButton"
                [tooltipContent]="
                    !(micGranted | async) ? 'Cấp quyền cho mic' : isMicActive ? 'Tắt Microphone' : 'Bật Microphone'
                "></lib-tooltip>

            <!-- Camera button -->
            <button
                #cameraButton
                class="bg-white rounded-full w-[30px] h-[30px] flex !justify-center !items-center relative"
                [disabled]="!(cameraGranted | async)"
                (click)="toggleCamera()">
                <i
                    class="vcon vcon-onl"
                    [ngClass]="{ vcon_camera_off: !isCameraActive, vcon_camera_on: isCameraActive }"></i>
                <span class="absolute top-0 right-0 z-10 scale-50 origin-top-right" *ngIf="!(cameraGranted | async)">
                    <span class="vcon vcon-general vcon_general_warning">
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                    </span>
                </span>
            </button>

            <lib-tooltip
                [toolTipFor]="cameraButton"
                [tooltipContent]="
                    !(cameraGranted | async) ? 'Cấp quyền cho camera' : isCameraActive ? 'Tắt Camera' : 'Bật Camera'
                "></lib-tooltip>
        </div>
    </div>

    <!-- Camera Selection Section (30%) -->
    <div class="w-3/10 bg-white py-6 pl-[10px] pr-[20px]">
        <h2 class="text-[16px] font-bold mb-[30px]">LỰA CHỌN CAMERA VÀ ÂM THANH</h2>

        <!-- Camera Selection Row -->
        <form [formGroup]="deviceForm">
            <!-- Camera Selection Row -->
            <!-- Camera Selection Row -->
            <div class="flex items-center mb-4">
                <span class="mr-[10px]">
                    <div #cameraSelection class="bg-white rounded-full relative">
                        <i
                            class="vcon vcon-onl"
                            [ngClass]="{ vcon_camera_off: !isCameraActive, vcon_camera_on: isCameraActive }"></i>
                        <span
                            class="absolute top-0 right-0 z-10 scale-50 origin-top-right"
                            *ngIf="!(cameraGranted | async)">
                            <span class="vcon vcon-general vcon_general_warning">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </span>
                        </span>
                    </div>
                    <lib-tooltip
                        [toolTipFor]="cameraSelection"
                        [tooltipContent]="
                            !(cameraGranted | async)
                                ? 'Cấp quyền cho camera'
                                : isCameraActive
                                  ? 'Tắt Camera'
                                  : 'Bật Camera'
                        "></lib-tooltip>
                </span>
                <select
                    formControlName="cameraId"
                    class="border border-gray-300 w-full"
                    [disabled]="!isCameraActive"
                    style="height: 30px; border-radius: 12px; font-size: 14px">
                    <option *ngIf="!(cameraGranted | async) || (cameras$ | async)?.length === 0" disabled selected>
                        Bật camera để hiển thị danh sách
                    </option>
                    <option *ngFor="let camera of cameras$ | async" [value]="camera.deviceId">
                        {{ camera.label }}
                    </option>
                </select>
            </div>

            <!-- Microphone Selection Row -->
            <div #micSelection class="flex items-center mb-4">
                <span class="mr-[10px]">
                    <div class="bg-white rounded-full relative">
                        <i
                            class="vcon vcon-onl"
                            [ngClass]="{ vcon_mic_off: !isMicActive, vcon_mic_on: isMicActive }"></i>
                        <span
                            class="absolute top-0 right-0 z-10 scale-50 origin-top-right"
                            *ngIf="!(micGranted | async)">
                            <span class="vcon vcon-general vcon_general_warning">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </span>
                        </span>
                    </div>
                    <lib-tooltip
                        [toolTipFor]="micSelection"
                        [tooltipContent]="
                            !(micGranted | async)
                                ? 'Cấp quyền cho mic'
                                : isMicActive
                                  ? 'Tắt Microphone'
                                  : 'Bật Microphone'
                        "></lib-tooltip>
                </span>
                <select
                    formControlName="micId"
                    class="border border-gray-300 w-full"
                    style="height: 30px; border-radius: 12px; font-size: 14px">
                    <option *ngIf="!(micGranted | async) || (mics$ | async)?.length === 0" disabled selected>
                        Bật mic để hiển thị danh sách
                    </option>
                    <option *ngFor="let mic of mics$ | async" [value]="mic.deviceId">{{ mic.label }}</option>
                </select>
            </div>

            <!-- Speaker Selection Row -->
            <div class="flex items-center mb-4">
                <span class="mr-[10px]">
                    <div class="bg-white rounded-full">
                        <i class="vcon vcon-onl vcon_speaker"></i>
                    </div>
                </span>
                <select
                    formControlName="speakerId"
                    class="border border-gray-300 w-full"
                    style="height: 30px; border-radius: 12px; font-size: 14px">
                    <option *ngIf="!(micGranted | async) || (speakers$ | async)?.length === 0" disabled selected>
                        Bật loa để hiển thị danh sách
                    </option>
                    <option *ngFor="let speaker of speakers$ | async" [value]="speaker.deviceId">
                        {{ speaker.label }}
                    </option>
                </select>
            </div>
        </form>
        <!-- Complete Button -->
        <button
            (click)="onClose()"
            class="vi-btn vi-btn-normal vi-btn-focus inline-block ml-[30px] mt-[30px] !text-sm !h-[30px] !py-[.5rem]">
            Hoàn thành
        </button>
    </div>
</div>
