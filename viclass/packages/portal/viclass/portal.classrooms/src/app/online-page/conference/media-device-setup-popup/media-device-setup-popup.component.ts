import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, ElementRef, <PERSON><PERSON><PERSON><PERSON>, QueryList, ViewChildren } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { TooltipComponent } from '@viclass/portal.common';
import { BehaviorSubject, firstValueFrom, Observable, Subscription } from 'rxjs';
import { ClassroomConferenceService } from '../classroom.conference.service';
import { MicSpeakingIndicatorComponent } from './mic-speaking-indicator/mic-speaking-indicator.component';
@Component({
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, MicSpeakingIndicatorComponent, TooltipComponent],
    selector: 'camera-review-popup',
    templateUrl: './media-device-setup-popup.component.html',
    styleUrls: ['./media-device-setup-popup.component.css'],
})
export class MediaDeviceSetupPopupComponent implements AfterViewInit, OnD<PERSON>roy {
    @ViewChildren('videoEl') videoElements: QueryList<ElementRef<HTMLVideoElement>>;
    _subWaitJoinComplete: Subscription;

    // Reactive form for device selection
    deviceForm = new FormGroup({
        cameraId: new FormControl(''),
        micId: new FormControl(''),
        speakerId: new FormControl(''),
    });

    // Observables for devices
    cameras$ = new BehaviorSubject<MediaDeviceInfo[]>([]);
    mics$ = new BehaviorSubject<MediaDeviceInfo[]>([]);
    speakers$ = new BehaviorSubject<MediaDeviceInfo[]>([]);

    isMicActive = false; // Indicates if the microphone is active
    isCameraActive = false; // Indicates if the camera is active
    isUserSpeaking = false; // Indicates if the user is currently speaking

    private selectedCameraId: string; // Stores the selected camera ID
    private selectedMicId: string; // Stores the selected microphone ID
    private selectedSpeakerId: string; // Stores the selected speaker ID

    private subscriptions = new Subscription();

    constructor(
        private conferenceService: ClassroomConferenceService,
        private dialogRef: MatDialogRef<MediaDeviceSetupPopupComponent>
    ) {}

    ngAfterViewInit(): void {
        this.initializeFormListeners(); // Initialize listeners for form controls
        this.loadDevicesConfig(); // Load existing device configurations
        this.initializeDeviceLists(); // Populate the device lists for selection
        this.updateFormValues(); // Update form values based on the selected devices

        this.subscriptions.add(
            this.micGranted.subscribe(() => {
                this.loadSpeakerDevices();
                this.loadMicrophoneDevices();
            })
        );

        this.subscriptions.add(
            this.cameraGranted.subscribe(() => {
                this.loadCameraDevices();
            })
        );
    }

    ngOnDestroy(): void {
        this.cleanUpMediaElements(); // Clean up media elements on component destruction
        this.subscriptions.unsubscribe();
    }

    get micGranted(): Observable<boolean> {
        return this.conferenceService.micGranted$;
    }

    get cameraGranted(): Observable<boolean> {
        return this.conferenceService.cameraGranted$;
    }
    /**
     * Initialize listeners for form controls
     *
     * This function subscribes to changes in the form controls and updates the
     * selected device IDs and the form values.
     */
    private initializeFormListeners(): void {
        // Subscribe to changes in the form controls
        this.deviceForm.get('cameraId').valueChanges.subscribe(this.onCameraChange.bind(this));
        // When the camera ID changes, store the selected camera ID
        // and update the form values
        this.deviceForm.get('micId').valueChanges.subscribe(this.onMicChange.bind(this));
        // When the microphone ID changes, store the selected microphone ID
        // and update the form values
        this.deviceForm.get('speakerId').valueChanges.subscribe(this.onSpeakerChange.bind(this));
        // When the speaker ID changes, store the selected speaker ID
        // and update the form values
    }

    private async loadSpeakerDevices(): Promise<MediaDeviceInfo[]> {
        const speakerDevices = await this.getMediaByKind('audiooutput');
        if (speakerDevices.length) this.speakers$.next(speakerDevices);
        return speakerDevices;
    }

    private async loadCameraDevices(): Promise<MediaDeviceInfo[]> {
        const cameraDevices = await this.getMediaByKind('videoinput');
        if (cameraDevices.length) this.cameras$.next(cameraDevices);
        return cameraDevices;
    }

    private async loadMicrophoneDevices(): Promise<MediaDeviceInfo[]> {
        const micDevices = await this.getMediaByKind('audioinput');
        if (micDevices.length) this.mics$.next(micDevices);
        return micDevices;
    }

    private async initializeDeviceLists(): Promise<void> {
        const [speakers, cameras, mics] = await Promise.all([
            this.loadSpeakerDevices(),
            this.loadCameraDevices(),
            this.loadMicrophoneDevices(),
        ]);

        if (this.selectedCameraId) {
            this.onCameraChange(this.selectedCameraId);
        } else if (this.isCameraActive) {
            this.onCameraChange(cameras[0]?.deviceId);
        }

        if (this.selectedMicId) {
            this.onMicChange(this.selectedMicId);
        } else if (this.isMicActive) {
            this.onMicChange(mics[0]?.deviceId);
        }

        if (this.selectedSpeakerId) {
            this.onSpeakerChange(this.selectedSpeakerId);
        } else {
            this.onSpeakerChange(speakers[0]?.deviceId);
        }
    }

    /**
     * Update the form control values based on the selected device IDs
     *
     * This function sets the form control values based on the selected device IDs
     * and disables the form controls if the corresponding device is not active.
     */
    private updateFormValues(): void {
        // Set the form control values based on the selected device IDs
        if (this.selectedMicId) this.deviceForm.get('micId').setValue(this.selectedMicId);
        if (this.selectedCameraId) this.deviceForm.get('cameraId').setValue(this.selectedCameraId);
        if (this.selectedSpeakerId) this.deviceForm.get('speakerId').setValue(this.selectedSpeakerId);
    }

    /**
     * Retrieves a list of available media devices filtered by the specified type (camera, microphone, or speaker).
     *
     * @param kind - The type of media device ('videoinput' for cameras, 'audioinput' for microphones, 'audiooutput' for speakers).
     * @returns A promise that resolves to an array of `MediaDeviceInfo` objects matching the specified kind.
     */
    private async getMediaByKind(kind: MediaDeviceKind): Promise<MediaDeviceInfo[]> {
        // Enumerate devices and filter by the specified kind (audio or video)
        const devices = await navigator.mediaDevices.enumerateDevices();
        return devices.filter(device => device.kind === kind && device.deviceId);
    }

    /**
     * Requests user permission to access audio or video devices and updates the device list accordingly.
     *
     * @param kind - The type of media device ('videoinput' for cameras, 'audioinput' for microphones, 'audiooutput' for speakers).
     */

    private async requestPermissionAndUpdateDeviceList(kind: MediaDeviceKind): Promise<void> {
        // Request user permission for accessing audio or video and update the device list
        if (kind === 'videoinput') {
            await navigator.mediaDevices.getUserMedia({ video: true });
        } else {
            await navigator.mediaDevices.getUserMedia({ audio: true });
            const devices = await this.getMediaByKind('audiooutput');
            this.speakers$.next(devices);
        }
        const devices = await this.getMediaByKind(kind);
        if (devices.length) {
            if (kind === 'videoinput') this.cameras$.next(devices);
            if (kind === 'audioinput') this.mics$.next(devices);
        }
    }

    toggleMicrophone(): void {
        // Toggle the microphone on or off
        this.isMicActive = !this.isMicActive;
        this.isMicActive ? this.turnOnMicrophone() : this.turnOffMicrophone();
    }

    toggleCamera(): void {
        // Toggle the camera on or off
        this.isCameraActive = !this.isCameraActive;
        this.isCameraActive ? this.turnOnCamera() : this.turnOffCamera();
    }

    private turnOffCamera(): void {
        // Turn off the camera and disable the camera form control
        this.isCameraActive = false;
        const videoEl = this.videoElements.first?.nativeElement;
        if (videoEl) this.destroyMediaElement(videoEl); // Clean up the video element
    }

    private turnOffMicrophone(): void {
        // Turn off the microphone and disable the microphone form control
        this.isMicActive = false;
    }

    private async turnOnMicrophone(): Promise<void> {
        // Turn on the microphone and enable the microphone form control
        if (!this.mics$.value.length) {
            await this.requestPermissionAndUpdateDeviceList('audioinput');
        }
        this.deviceForm.get('micId').enable();
        if (!this.deviceForm.get('micId').value) {
            this.deviceForm.get('micId').setValue(this.mics$.value[0].deviceId);
        }
    }

    private async turnOnCamera(): Promise<void> {
        // Turn on the camera and enable the camera form control
        if (!this.cameras$.value.length) {
            await this.requestPermissionAndUpdateDeviceList('videoinput');
        }
        this.deviceForm.get('cameraId').enable();
        if (!this.deviceForm.get('cameraId').value) {
            this.deviceForm.get('cameraId').setValue(this.cameras$.value[0].deviceId);
        }
    }

    async onSpeakerChange(deviceId: string): Promise<void> {
        // Handle speaker selection changes and update the audio output
        if (!deviceId) return;
        this.selectedSpeakerId = deviceId;
    }

    /**
     * Handles camera selection changes and updates the video stream accordingly.
     *
     * @param deviceId - The ID of the newly selected camera device.
     */
    async onCameraChange(deviceId: string): Promise<void> {
        // Handle camera selection changes and update the video stream
        if (!deviceId) return;
        this.selectedCameraId = deviceId;
        const videoEl = this.videoElements.first?.nativeElement;
        if (videoEl && this.selectedCameraId) {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { deviceId: this.selectedCameraId ? { exact: this.selectedCameraId } : undefined },
            });
            videoEl.srcObject = stream; // Set the video stream to the video element
        }
    }

    /**
     * Handles microphone selection changes and updates the audio stream accordingly.
     *
     * @param deviceId - The ID of the newly selected microphone device.
     */

    async onMicChange(deviceId: string): Promise<void> {
        // Handle microphone selection changes and update the audio stream
        if (!deviceId) return;
        this.selectedMicId = deviceId;
        if (this.selectedMicId) {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: { deviceId: this.selectedMicId ? { exact: this.selectedMicId } : undefined },
            });

            this.monitorAudioLevel(stream); // Start monitoring the audio level
        }
    }

    /**
     * Monitors the audio level of a given media stream to detect if the user is speaking.
     * Uses the Web Audio API to analyze real-time audio frequencies.
     *
     * @param stream - The MediaStream containing the user's audio input.
     */
    private monitorAudioLevel(stream: MediaStream): void {
        // Monitor the audio level to detect if the user is speaking
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        microphone.connect(analyser);
        analyser.fftSize = 256;

        let speakingTimeout: any = null;
        const speakingThreshold = 1; // Threshold for detecting speech
        const silenceDuration = 300; // Duration to consider as silence

        const checkLevel = () => {
            analyser.getByteFrequencyData(dataArray);
            const average = dataArray.reduce((acc, value) => acc + value, 0) / dataArray.length;
            if (average > speakingThreshold) {
                this.isUserSpeaking = true; // User is speaking
                if (speakingTimeout) {
                    clearTimeout(speakingTimeout);
                    speakingTimeout = null; // Reset timeout
                }
            } else if (!speakingTimeout) {
                speakingTimeout = setTimeout(() => {
                    this.isUserSpeaking = false; // User is not speaking
                }, silenceDuration);
            }
            requestAnimationFrame(checkLevel); // Continue checking the audio level
        };
        checkLevel();
    }

    /**
     * Saves the current device configuration by updating the microphone and camera states.
     */
    private async saveDeviceConfig(): Promise<void> {
        try {
            await this.updateMicrophoneState();
            await this.updateCameraState();
        } catch (error) {
            console.error('saveDeviceConfig error:', error);
        }
    }

    /**
     * Updates the device configuration by setting the selected microphone, camera, and speaker IDs.
     * This configuration is saved using the conference service.
     */
    private updateDeviceConfig(): void {
        this.conferenceService.setDeviceConfig({
            micId: this.selectedMicId,
            cameraId: this.selectedCameraId,
            speakerId: this.selectedSpeakerId,
        });
    }

    /**
     * Cleans up the subscription that waits for the join completion event.
     */
    private cleanupSubWaitJoinComplete(): void {
        if (this._subWaitJoinComplete) {
            this._subWaitJoinComplete.unsubscribe();
            this._subWaitJoinComplete = null;
        }
    }

    /**
     * Updates the microphone state based on its current activation status.
     * If the microphone is inactive, it is turned off.
     * If the microphone is active, it is restarted to apply any configuration changes.
     */
    private async updateMicrophoneState(): Promise<void> {
        if (!this.isMicActive) {
            await this.conferenceService.turnOffMicrophone();
        } else {
            await this.conferenceService.turnOffMicrophone();
            await this.conferenceService.turnOnMicrophone();
        }
    }

    /**
     * Updates the camera state based on its current activation status.
     * If the camera is inactive, it is turned off.
     * If the camera is active, it is restarted to apply any configuration changes.
     */
    private async updateCameraState(): Promise<void> {
        if (!this.isCameraActive) {
            await this.conferenceService.turnOffCamera();
        } else {
            await this.conferenceService.turnOffCamera();
            await this.conferenceService.turnOnCamera();
        }
    }

    /**
     * Waits for the join process to complete before saving the device configuration.
     * Subscribes to the `callNotJoined` event and triggers `saveDeviceConfig()`
     * once the user has successfully joined the call.
     * Cleans up the subscription afterward to prevent memory leaks.
     */
    private waitJoinCompleteToSaveDeviceConfig(): void {
        this._subWaitJoinComplete = this.conferenceService.callNotJoined.subscribe(async notJoined => {
            if (!notJoined) {
                await this.saveDeviceConfig();
                this.cleanupSubWaitJoinComplete();
            }
        });
    }

    /**
     * Loads the user's previously saved device configuration (microphone, camera, and speaker).
     * Also checks if the microphone and camera are currently active.
     */
    private loadDevicesConfig(): void {
        this.isMicActive = !!this.conferenceService.localStream.value?.audioTrack.value; // Check if microphone is active
        this.isCameraActive = !!this.conferenceService.localStream.value?.videoTrack.value; // Check if camera is active

        // Load existing device configuration from the service
        const deviceConfig = this.conferenceService.getDeviceConfig();
        this.selectedMicId = deviceConfig.micId;
        this.selectedCameraId = deviceConfig.cameraId;
        this.selectedSpeakerId = deviceConfig.speakerId;
    }

    /**
     * Destroys a given media element by stopping all media tracks and clearing its source.
     * This ensures that the media stream is properly released and prevents resource leaks.
     *
     * @param mediaElement - The HTMLMediaElement (audio or video) to be cleaned up.
     */
    private destroyMediaElement(mediaElement: HTMLMediaElement): void {
        // Stop all tracks and clean up the media element
        const stream = mediaElement.srcObject as MediaStream;
        stream?.getTracks().forEach(track => track.stop());
        mediaElement.srcObject = null; // Clear the source object
    }

    /**
     * Cleans up media elements by stopping and releasing their associated media streams.
     * This helps prevent memory leaks and ensures efficient resource management.
     */
    private cleanUpMediaElements(): void {
        // Clean up video and audio elements to prevent memory leaks
        this.destroyMediaElement(this.videoElements.first?.nativeElement);
    }

    /**
     * Handles the logic when the dialog is closed.
     * - Closes the dialog.
     * - Checks if the user has not joined the call.
     * - Updates the device configuration.
     * - If the user has not joined, waits for the join process to complete before saving the configuration.
     * - Otherwise, saves the device configuration immediately.
     */
    async onClose(): Promise<void> {
        this.dialogRef.close(); // Close the dialog after
        const isNotJoined = await firstValueFrom(this.conferenceService.callNotJoined);
        this.updateDeviceConfig();
        // Wait for conference join to complete then run save device configuration
        if (isNotJoined) this.waitJoinCompleteToSaveDeviceConfig();
        else this.saveDeviceConfig();
    }
}
