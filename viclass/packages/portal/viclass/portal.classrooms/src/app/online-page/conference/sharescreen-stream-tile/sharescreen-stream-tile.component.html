<div
    (mouseenter)="mouseEnter()"
    (mouseleave)="mouseLeave()"
    (click)="backViewport()"
    [ngClass]="{ 'pointer-events-none': !(isVisible$ | async) }"
    class="w-[88px] h-[66px] bg-transparent relative rounded-[5px]">
    <div
        class="absolute bottom-[0px] ml-[8px] text-white bg-transparent rounded-md p-1 w-[calc(100%-16px)] overflow-hidden whitespace-nowrap text-ellipsis text-shadow">
        {{ (member$ | async).profile.username }}
    </div>

    <div
        #videoContainerElement
        [ngClass]="[!(isVisible$ | async) ? 'hidden' : '']"
        class="flex justify-center rounded-[5px] w-[88px] h-[66px]"></div>

    <ng-template [ngIf]="!(isVisible$ | async)">
        <div class="z-50 flex justify-center items-center text-center rounded-[5px] bg-black w-[88px] h-[66px]">
            <i class="vcon vcon-onl vcon_share-screen"></i>
        </div>
    </ng-template>
</div>
