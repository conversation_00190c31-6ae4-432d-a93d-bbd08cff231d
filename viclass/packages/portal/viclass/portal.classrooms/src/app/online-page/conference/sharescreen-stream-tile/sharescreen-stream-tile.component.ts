import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { LSessionRegistrationModel } from '@viclass/portal.common';
import { Observable, Subscribable, Subscription } from 'rxjs';
import { BoardActionListener } from '../../board.action.listener';
import { MemberStateService } from '../../member.state.service';
import { ShareScreenService } from '../../share-screen-editor/share-screen.service';
import { ClassroomConferenceService } from '../classroom.conference.service';

/**
 * The component that manage a video element
 */
@Component({
    standalone: true,
    imports: [CommonModule],
    selector: 'sharescreen-stream-tile',
    templateUrl: './sharescreen-stream-tile.component.html',
    styleUrls: ['./sharescreen-stream-tile.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShareScreenStreamTileComponent {
    @Input()
    videoElement!: HTMLVideoElement | null;

    @Input()
    userId: string;

    @Input()
    isVisible$: Observable<boolean>;

    @ViewChild('videoContainerElement', { static: true }) videoContainerElement!: ElementRef<SVGElement>;

    isHovered: boolean = false;

    private isVisibleSub: Subscription;

    constructor(
        public confS: ClassroomConferenceService,
        private memberStateS: MemberStateService,
        private shareScreenS: ShareScreenService,
        private boardTabListener: BoardActionListener
    ) {}

    /**
     * Subscribes to `isVisible$` to dynamically manage the display of the video element.
     *
     * The pipeline:
     * 1. Listens for changes in `isVisible$`, which determines whether the video element should be visible.
     * 2. If `isVisible` is `true`, the `videoElement` is appended to `videoContainerElement`, making it visible in the DOM.
     * 3. If `isVisible` is `false`, the `videoContainerElement` is cleared by setting `innerHTML = ''`, effectively removing the video element.
     *
     * This ensures efficient DOM management by only rendering the video when necessary.
     */
    ngOnInit() {
        this.isVisibleSub = this.isVisible$.subscribe(isVisible => {
            if (isVisible) {
                this.videoContainerElement.nativeElement.appendChild(this.videoElement);
            } else {
                this.videoContainerElement.nativeElement.innerHTML = '';
            }
        });
    }

    /**
     * Navigates back to the user's shared screen viewport in the board interface.
     * This ensures that when a user is sharing their screen, the corresponding viewport is selected.
     */
    backViewport() {
        const vpId = this.shareScreenS.getSharedScreen(this.userId)?.viewportId;
        this.boardTabListener.boardActionEvent$.next({
            type: 'select-board',
            id: vpId,
        });
    }

    ngOnDestroy(): void {
        this.isVisibleSub.unsubscribe();
    }

    get member$(): Observable<LSessionRegistrationModel> {
        return this.memberStateS.memberByUserId$(this.userId);
    }

    mouseEnter(): void {
        this.isHovered = true;
    }

    mouseLeave(): void {
        this.isHovered = false;
    }
}
