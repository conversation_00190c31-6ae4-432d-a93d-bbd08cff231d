<div
    (mouseenter)="mouseEnter()"
    (mouseleave)="mouseLeave()"
    (click)="toggleSize()"
    [ngClass]="videoSizeClass"
    class="bg-transparent relative rounded-[5px]">
    <!-- Close Icon in top-right corner -->
    <button
        *ngIf="showCloseBtn && isHovered && !(isPresenting$ | async)"
        (click)="onCloseVideo($event)"
        class="absolute w-[24px] h-[24px] top-0 right-0 m-[5px] text-black bg-white rounded-full p-[5px] hover:bg-P2 flex items-center justify-center z-10">
        <i class="vcon vcon-onl vcon_delete !text-[14px] !leading-[14px]"></i>
    </button>

    <div
        *ngIf="isPresenting$ | async"
        class="w-[16px] h-[16px] absolute top-0 left-0 ml-[5px] text-black bg-P2 rounded-b-[5px] flex items-center justify-center">
        <img class="w-[14px] h-[14px]" src="assets/img/play-icon.svg" alt="" />
    </div>

    <!-- User Name Display -->
    <div
        *ngIf="isLargeSize"
        class="absolute bottom-[0px] ml-[8px] text-white bg-transparent rounded-md p-1 w-[calc(100%-16px)] overflow-hidden whitespace-nowrap text-ellipsis text-shadow">
        {{ displayName(member$ | async) }}
    </div>

    <ng-template
        [ngIf]="stream && (stream.videoTrack | async) && (stream.hasVideo | async) && (stream.showVideo | async)">
        <div [ngClass]="videoSizeClass" class="flex justify-center rounded-[5px]">
            <video #videoEl class="w-full h-full object-cover rounded-[5px]" autoplay></video>
        </div>
    </ng-template>
    <ng-template
        [ngIf]="!stream || !(stream.videoTrack | async) || !(stream.hasVideo | async) || !(stream.showVideo | async)">
        <div [ngClass]="videoSizeClass" class="flex justify-center items-center text-center rounded-[5px] bg-black">
            <i class="vcon vcon-onl vcon_camera_off"></i>
        </div>
    </ng-template>
</div>
