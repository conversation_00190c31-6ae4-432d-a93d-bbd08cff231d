import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
    QueryList,
    SimpleChanges,
    ViewChildren,
} from '@angular/core';
import { displayName, LSessionRegistrationModel } from '@viclass/portal.common';
import { map, Observable, Subscription } from 'rxjs';
import { MemberStateService } from '../../member.state.service';
import { ClassroomConferenceService, ParticipantStream } from '../classroom.conference.service';

/**
 * The component that manage a video element
 */
@Component({
    standalone: true,
    imports: [CommonModule],
    selector: 'stream-tile',
    templateUrl: './stream-tile.component.html',
    styleUrls: ['./stream-tile.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StreamTileComponent implements AfterViewInit, OnDestroy {
    displayName = displayName;

    @Input()
    isLargeSize: boolean = false;

    @Input()
    userId: string;

    @Input()
    stream: Partial<ParticipantStream>;

    @ViewChildren('videoEl')
    videoQL: QueryList<ElementRef<HTMLVideoElement>>;

    @Input()
    showCloseBtn: boolean = true;
    @Output() onClose = new EventEmitter<boolean>();

    @Input()
    isVisible: boolean = true;

    videoSubscription: Subscription;

    _isHovered: boolean = false;

    pS: Subscription;

    isCurPresenting: boolean = false;
    // marked video not opened yet, because presenting so it is turned on

    private currentVideoElement?: HTMLVideoElement;

    constructor(
        private memberStateS: MemberStateService,
        public confS: ClassroomConferenceService
    ) {}

    ngAfterViewInit() {
        this.videoTrackAttach();
        this.videoSubscription = this.videoQL.changes.subscribe(() => this.videoTrackAttach());

        this.pS = this.isPresenting$.subscribe(isPresenting => {
            if (this.isCurPresenting != isPresenting) {
                this.isCurPresenting = isPresenting;
                this.videoTrackAttach();
                this.videoSubscription = this.videoQL.changes.subscribe(() => this.videoTrackAttach());
            }
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['stream']) {
            this.videoTrackAttach();
        }
    }

    ngOnDestroy(): void {
        this.pS.unsubscribe();
        this.videoTrackDetach();
    }

    get isPresenting$(): Observable<boolean> {
        return this.member$.pipe(map(m => m.regStatus == 'REGISTERED' && m.userState.raiseHandStatus == 'PRESENTING'));
    }

    private videoTrackAttach() {
        if (this.videoQL && this.videoQL.length > 0) {
            const item = this.videoQL.last;
            const t = this.stream?.videoTrack.value;
            if (this.currentVideoElement) {
                if (this.stream && this.stream.videoTrack && this.stream.videoTrack.value)
                    this.stream.videoTrack.value?.detach(this.currentVideoElement);
                delete this.currentVideoElement;
            }

            setTimeout(() => {
                this.currentVideoElement = item.nativeElement;
                if (this.stream && this.stream.videoTrack && this.stream.videoTrack.value) {
                    this.stream.videoTrack.value.attach(item.nativeElement);
                }
            }, 1000);
            console.log(t, t?.isWebRTCTrackMuted());
        }
    }

    private videoTrackDetach() {
        console.log('Destroying stream tile');
        this.videoQL.map(item => {
            console.log('----- Detach stream!!!!');
            this.stream?.videoTrack.value?.detach(item.nativeElement);
        });

        if (this.videoSubscription) this.videoSubscription.unsubscribe();
    }

    get member$(): Observable<LSessionRegistrationModel> {
        return this.memberStateS.memberByUserId$(this.userId);
    }

    onCloseVideo(event: MouseEvent) {
        event.stopPropagation();
        this.onClose.emit(true);
    }

    get videoSizeClass(): string {
        return this.isLargeSize ? 'w-[200px] h-[150px]' : 'w-[88px] h-[66px]';
    }
    get isHovered(): boolean {
        return this._isHovered;
    }

    toggleSize(): void {
        this.isLargeSize = !this.isLargeSize;
    }

    mouseEnter(): void {
        this._isHovered = true;
    }

    mouseLeave(): void {
        this._isHovered = false;
    }
}
