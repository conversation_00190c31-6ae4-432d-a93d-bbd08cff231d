<ng-template [ngIf]="(confS.localStream | async) !== undefined || (confS.remoteStreams | async)?.length > 0">
    <div class="flex max-w-[1000px] h-full items-end">
        <!-- Left Button -->
        <button
            *ngIf="(allItems$ | async).length > maxVisibleItems"
            (click)="slideLeft()"
            [disabled]="!(showLeftButton$ | async)"
            class="custom-shadow z-10 bg-white shadow-lg mb-2.5 mr-0 ml-5 rounded-[15px] w-[40px] h-[40px] flex items-center justify-center hover:shadow-xl transition duration-300 ease-in-out active:scale-90 pointer-events-auto p-[5px] disabled:bg-gray-300 disabled:cursor-not-allowed">
            <span class="vcon vcon-general vcon_arrow_back"></span>
        </button>
        <!-- Slider Container -->
        <div
            #slider
            class="flex-nowrap overflow-x-auto scroll-smooth whitespace-nowrap no-scrollbar bottom-[0px] bg-transparent text-white p-[10px] pointer-events-auto flex items-center">
            <div class="ml-[15px] mr-[5px] flex items-end">
                <!-- Presenting Member -->
                <ng-template [ngIf]="presentingMember$ | async" let-mS="ngIf">
                    <div class="flex flex-col items-end mr-[5px] cursor-pointer">
                        <div
                            (click)="onLargeSize(mS.id, true)"
                            class="border flex justify-center items-end rounded-[5px] min-h-fit"
                            style="box-shadow: 0px 5px 20px 0px #00424b33">
                            <stream-tile
                                [isLargeSize]="isAutoLargeResizePresenter"
                                [stream]="confS.getStreamByVid(mS.profile.id)"
                                [userId]="mS.profile.id"
                                (onClose)="onCloseVideo(confS.getStreamByVid(mS.profile.id))">
                            </stream-tile>
                        </div>
                    </div>
                </ng-template>

                <!-- Cameras -->
                <ng-template [ngIf]="(visibleItems$ | async).length > 0">
                    <div class="flex items-end cursor-pointer">
                        <div
                            *ngFor="let item of visibleItems$ | async"
                            class="mr-[5px] border flex justify-center items-end rounded-[5px] min-h-fit"
                            style="box-shadow: 0px 5px 20px 0px #00424b33">
                            <stream-tile
                                *ngIf="
                                    item.type == 'camera' &&
                                    this.confS.getStreamByVid(item.data) &&
                                    !((this.presentingMember$ | async).profile.id == item.data)
                                "
                                (click)="onLargeSize(item.data)"
                                [isLargeSize]="userRegIdLargeResize == item.data"
                                [stream]="confS.getStreamByVid(item.data)"
                                [userId]="item.data"
                                (onClose)="onCloseVideo(confS.getStreamByVid(item.data))">
                            </stream-tile>
                            <sharescreen-stream-tile
                                *ngIf="
                                    item.type == 'shareScreen' &&
                                    this.confS.getStreamByVid(item.data.userId) &&
                                    (this.confS.getStreamByVid(item.data.userId).showScreen | async)
                                "
                                [userId]="item.data.userId"
                                [isVisible$]="item.data.isShowInCameraArea"
                                [videoElement]="item.data.videoElement">
                            </sharescreen-stream-tile>
                        </div>
                    </div>
                </ng-template>

                <!-- Shared Screens -->
                <ng-template [ngIf]="(listShareScreen$ | async).length > 0">
                    <div class="flex items-end cursor-pointer">
                        <div
                            *ngFor="let sS of listShareScreen$ | async"
                            class="mr-[5px] border flex justify-center items-end min-h-fit"
                            style="box-shadow: 0px 5px 20px 0px #00424b33"></div>
                    </div>
                </ng-template>
            </div>
        </div>

        <!-- Right Button -->
        <button
            *ngIf="(allItems$ | async).length > maxVisibleItems"
            (click)="slideRight()"
            [disabled]="!(showRightButton$ | async)"
            class="custom-shadow mb-2.5 ml-0 mr-5 z-10 bg-white shadow-lg rounded-[15px] w-[40px] h-[40px] flex items-center justify-center hover:shadow-xl transition duration-300 ease-in-out active:scale-90 pointer-events-auto p-[5px] disabled:bg-gray-300 disabled:cursor-not-allowed">
            <span class="vcon vcon-general vcon_arrow_next"></span>
        </button>
    </div>
</ng-template>
