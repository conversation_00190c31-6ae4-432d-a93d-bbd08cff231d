import { Injectable } from '@angular/core';
import { DocumentInfo } from '../model';
import { BehaviorSubject, Observable, ReplaySubject, Subject } from 'rxjs';
import { DocumentId, ViewportId } from '@viclass/editor.core';

@Injectable()
export class DocinfoStateService {
    private docInfosByVP: Map<ViewportId, DocumentInfo[]> = new Map();
    private docInfosByVP$: Map<ViewportId, ReplaySubject<DocumentInfo[]>> = new Map();
    private docInfoObsByVP$: Map<ViewportId, Map<String, BehaviorSubject<DocumentInfo>>> = new Map();

    constructor() {}

    initForViewport(vpId: ViewportId) {
        let s: ReplaySubject<DocumentInfo[]> = this.docInfosByVP$.get(vpId);
        if (!s) {
            s = new ReplaySubject<DocumentInfo[]>();
            this.docInfosByVP$.set(vpId, s);
        }

        s.subscribe(docInfos => {
            this.docInfosByVP.set(vpId, docInfos);
            let curObs = this.docInfoObsByVP$.get(vpId);
            if (!curObs) {
                curObs = new Map();
                this.docInfoObsByVP$.set(vpId, curObs);
            }
            docInfos.forEach(d => {
                let o = curObs.get(d.docGlobalId);
                if (!o) {
                    o = new BehaviorSubject<DocumentInfo>(d);
                    curObs.set(d.docGlobalId, o);
                } else {
                    o.next(d);
                }
            });
        });
    }

    addDocInfos(vpId: ViewportId, docInfos: DocumentInfo[]) {
        let d = this.docInfosByVP.get(vpId);
        if (!d) d = [];
        docInfos.forEach(i => {
            const idx = d.findIndex(j => j.docGlobalId == i.docGlobalId);
            if (idx > -1) d[idx] = i;
            else d = d.concat(docInfos);
        });
        this.docInfosByVP$.get(vpId).next(d);
    }

    getDocInfo(vpId: ViewportId, docGlobalId: DocumentId): DocumentInfo {
        let d = this.docInfosByVP.get(vpId);
        if (!d) d = [];

        const idx = d.findIndex(j => j.docGlobalId == docGlobalId);
        if (idx > -1) return d[idx];
        else return undefined;
    }

    markSelected(vpId: ViewportId, docIds: DocumentId[]) {
        this.docInfosByVP
            .get(vpId)
            ?.filter(d => docIds.includes(d.docGlobalId))
            ?.map(doc => {
                doc.isSelected = true;
                this.obs$(vpId, doc.docGlobalId).next(doc);
            });
    }

    markDeselected(vpId: ViewportId, docIds: DocumentId[]) {
        this.docInfosByVP
            .get(vpId)
            .filter(d => docIds.includes(d.docGlobalId))
            .map(doc => {
                doc.isSelected = false;
                this.obs$(vpId, doc.docGlobalId).next(doc);
            });
    }

    removeDocInfos(vpId: ViewportId, docGlobalIds: DocumentId[]) {
        const d = this.docInfosByVP.get(vpId);
        if (!d) return;
        d.forEach(i => {
            const idx = d.findIndex(j => docGlobalIds.includes(j.docGlobalId));
            if (idx > -1) d.splice(idx, 1);
        });
        this.docInfosByVP$.get(vpId).next(d);
    }

    obsByVP$(vpId: ViewportId): Observable<DocumentInfo[]> {
        return this.docInfosByVP$.get(vpId);
    }

    obs$(vpId: ViewportId, docGlobalId: DocumentId): BehaviorSubject<DocumentInfo> {
        let mapOfDocumentInfo$ = this.docInfoObsByVP$.get(vpId);
        if (!mapOfDocumentInfo$) {
            mapOfDocumentInfo$ = new Map();
            this.docInfoObsByVP$.set(vpId, mapOfDocumentInfo$);
        }
        let documentInfo$ = mapOfDocumentInfo$.get(docGlobalId);
        if (!documentInfo$) {
            documentInfo$ = new BehaviorSubject<DocumentInfo>(undefined);
            mapOfDocumentInfo$.set(docGlobalId, documentInfo$);
        }
        return documentInfo$;
    }
}
