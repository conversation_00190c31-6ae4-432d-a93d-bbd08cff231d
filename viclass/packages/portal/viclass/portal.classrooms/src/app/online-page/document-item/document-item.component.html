<div
    *ngIf="document$ | async as doc"
    class="sidebar-item d-flex align-items-center !relative cursor-pointer click-indicator"
    [class.active]="docItem.isSelected"
    (click)="select($event)">
    <ng-template [ngIf]="processing$ | async" [ngIfElse]="notProcessing">
        <div [spinner]="processing$" class="ml-[1rem]"></div>
    </ng-template>
    <ng-template #notProcessing>
        <div [hidden]="showRenameDialog" class="sidebar-desc ps-[15px] pe-[15px] gap-[5px]">
            <i class="vcon-onl {{ getDocIcon(doc.editorType) }} leading-[20px] pe-[0.5rem]"></i>
            <span class="">{{ doc.details.docName }}</span>
        </div>

        <div [hidden]="!showRenameDialog" class="bg-P2 text-BW2 p-[10px_10px] w-full">
            <div class="d-flex">
                <i class="vcon-onl {{ getDocIcon(doc.editorType) }} leading-[20px] pe-[0.5rem]"></i>
                <input
                    #renameInput
                    class="w-full outline-0 bg-transparent text-xs"
                    tabindex="0"
                    [(ngModel)]="docName"
                    (click)="$event.stopPropagation()"
                    (focusout)="rename()"
                    (keydown)="onKeydown($event)" />
            </div>
        </div>

        <div class="sidebar-item-overlay d-flex" *ngIf="!showRenameDialog">
            <div class="sidebar-desc ps-[15px] gap-2 select-none">
                <i class="vcon-onl {{ getDocIcon(doc.editorType) }} leading-[20px] pe-[0.5rem]"></i>
                <span>{{ doc.details.docName }}</span>
            </div>
            <div class="grow"></div>
            <div class="d-flex flex-row flex-fill pe-[15px]">
                <div class="d-flex flex-fill justify-center">
                    <button #shareDocBtnEl (click)="share($event)">
                        <i class="vcon-general vcon_download"></i>
                    </button>
                    <lib-tooltip [toolTipFor]="shareDocBtnEl" [tooltipContent]="'Lưu lại'"></lib-tooltip>
                </div>
                <ng-template [ngIf]="showActionBtn$ | async">
                    <div class="d-flex flex-fill justify-center" *ngIf="isOwner$ | async">
                        <button #renameDocBtnEl (click)="openRenameDialog($event)">
                            <i class="vcon-onl vcon_edit"></i>
                        </button>
                        <lib-tooltip [toolTipFor]="renameDocBtnEl" [tooltipContent]="'Đổi tên'"></lib-tooltip>
                    </div>
                    <div class="d-flex flex-fill justify-center">
                        <button #guestDeleteBtnEl (click)="delete($event)">
                            <i class="vcon-general vcon_delete"></i>
                        </button>
                        <lib-tooltip [toolTipFor]="guestDeleteBtnEl" [tooltipContent]="'Xóa tài liệu'"></lib-tooltip>
                    </div>
                </ng-template>
            </div>
        </div>
    </ng-template>
</div>
