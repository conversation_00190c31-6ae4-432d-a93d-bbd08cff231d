import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { EditorType, isCtrlOrMeta, SpecialKeyboard } from '@viclass/editor.core';
import {
    LSessionRegistrationModel,
    LsRegistrationService,
    ProcessingRequestManager,
    SpinnerLabelComponent,
    TooltipComponent,
    UserService,
} from '@viclass/portal.common';
import { combineLatest, map, Observable } from 'rxjs';
import { AppStateService } from '../../app.state.service';
import { DocumentInfo, EDITOR_ICONS } from '../../model';
import { CoordStatesService } from '../coord.state.service';
import { DocinfoStateService } from '../docinfo.state.service';
import { DocumentActionListener } from '../document.action.listener';

const DOUBLE_CLICK_DELAY_MS = 300;

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, SpinnerLabelComponent, FormsModule, TooltipComponent],
    templateUrl: './document-item.component.html',
    selector: '[doc-item]',
})
export class DocumentItemComponent implements OnInit {
    @Input('doc-item') docItem: DocumentInfo;
    @Input('classroomOwner$') classroomOwner$: Observable<LSessionRegistrationModel>;

    @ViewChild('renameInput') renameInput: ElementRef<HTMLInputElement>;

    protected docName: string = '';
    protected showRenameDialog: boolean = false;
    private showMenu: boolean;

    private readonly isPresenting$: Observable<boolean> = this.memberS.loggedInMI$
        .get(this.aS.lsId)
        .pipe(map(m => m.userState?.raiseHandStatus === 'PRESENTING'));

    protected document$: Observable<DocumentInfo>;

    protected showActionBtn$: Observable<boolean>;

    protected isOwner$: Observable<boolean>;

    private lastClickTime: number = 0;
    private _selectTimeout: any = null;

    constructor(
        private docInfoStateS: DocinfoStateService,
        private uS: UserService,
        private docActionListener: DocumentActionListener,
        private prm: ProcessingRequestManager,
        public memberS: LsRegistrationService,
        public aS: AppStateService,
        private coordStateService: CoordStatesService
    ) {}

    ngOnInit(): void {
        this.docName = this.docItem.details.docName;

        this.document$ = this.docInfoStateS.obs$(this.docItem.details.coordStateId, this.docItem.docGlobalId);

        this.isOwner$ = this.uS.curUser$.pipe(map(u => u?.id === this.docItem.details.ownerUserId));

        this.showActionBtn$ = combineLatest([this.isPresenting$, this.isOwner$]).pipe(
            map(([presenting, isDocOwner]) => {
                if (presenting) return true;
                const coordState = this.coordStateService.get(this.docItem.details.coordStateId);

                // show actions for doc owner on private coords
                const isPrivateVp = !(coordState?.default || coordState?.pinned || coordState?.presenting);
                return isPrivateVp && isDocOwner;
            })
        );
    }

    protected get processing$(): Observable<boolean> {
        return this.prm.getInprogressObs(`document-${this.docItem.docGlobalId}`);
    }

    protected getDocIcon(edT: EditorType): string {
        return EDITOR_ICONS[edT] || '';
    }

    protected share($event: MouseEvent) {
        const docInfo = structuredClone(this.docItem);
        this.docActionListener.emit({
            type: 'share-doc',
            docGlobalId: this.docItem.docGlobalId,
            data: docInfo,
        });
        $event.stopPropagation();
    }

    protected openRenameDialog($event: MouseEvent) {
        this.showRenameDialog = true;
        this.showMenu = false;
        setTimeout(() => this.renameInput.nativeElement.focus());
        $event.stopPropagation();
    }

    protected rename() {
        this.showRenameDialog = false;
        if (this.docName === this.docItem.details.docName) return;

        const docInfo = structuredClone(this.docItem);
        docInfo.details.docName = this.docName;
        this.docActionListener.emit({
            type: 'update-doc-info',
            docGlobalId: this.docItem.docGlobalId,
            data: docInfo,
        });
    }

    protected copy($event: MouseEvent) {
        const docInfo = structuredClone(this.docItem);
        this.docActionListener.emit({
            type: 'copy-doc',
            docGlobalId: this.docItem.docGlobalId,
            data: docInfo,
        });
        $event.stopPropagation();
    }

    protected duplicate($event: MouseEvent) {
        const docInfo = structuredClone(this.docItem);
        this.docActionListener.emit({
            type: 'duplicate-doc',
            docGlobalId: this.docItem.docGlobalId,
            data: docInfo,
        });
        $event.stopPropagation();
    }

    protected select($event: MouseEvent) {
        $event.preventDefault();
        $event.stopPropagation();

        const docInfo = structuredClone(this.docItem);
        const now = new Date().getTime();
        const isDoubleClick = now - this.lastClickTime < DOUBLE_CLICK_DELAY_MS;
        this.lastClickTime = now;

        if (this._selectTimeout) {
            clearTimeout(this._selectTimeout);
            this._selectTimeout = null;
        }

        if (isDoubleClick) {
            // Handle double click - fit document to view
            this.docActionListener.emit({
                type: 'fit-to-view',
                docGlobalId: this.docItem.docGlobalId,
                data: docInfo,
            });
        } else {
            // Delay single click emit to check for double click
            this._selectTimeout = setTimeout(() => {
                this.docActionListener.emit({
                    type: 'select-doc',
                    keys: <SpecialKeyboard[]>[isCtrlOrMeta($event) ? 'Ctrl' : undefined].filter(k => k),
                    docGlobalId: this.docItem.docGlobalId,
                    data: docInfo,
                });
                this._selectTimeout = null;
            }, DOUBLE_CLICK_DELAY_MS);
        }
    }

    protected delete($event: MouseEvent) {
        const docInfo = structuredClone(this.docItem);
        this.docActionListener.emit({
            type: 'remove-doc',
            docGlobalId: this.docItem.docGlobalId,
            data: docInfo,
        });
        $event.stopPropagation();
    }

    protected showOrHideMenu($event: MouseEvent) {
        this.showMenu = !this.showMenu;
        $event.stopPropagation();
    }

    protected onKeydown($event: KeyboardEvent) {
        switch ($event.key.toLowerCase()) {
            case 'enter': {
                this.rename();
                break;
            }
            case 'escape': {
                this.showRenameDialog = false;
                this.docName = this.docItem.details.docName;
                break;
            }
            default:
                break;
        }
    }
}
