import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ErrorHandlerDecorator } from '@viclass/editor.core';
import {
    DocumentInfoResponse,
    ProcessingRequestManager,
    SharingDialogConfig,
    SharingDocDialogComponent,
} from '@viclass/portal.common';
import { BehaviorSubject, firstValueFrom, from, ReplaySubject } from 'rxjs';

import { ClassroomNonCriticalError } from '@viclass/editor.coordinator/classroom';
import { CcsGateway } from '../gateways/ccs.gateway';
import { DocumentInfoDetail, UpdateDocumentInfoRequest } from '../gateways/ccs.model';
import { DocCtrlEvent, DocCtrlEventType, DocumentInfo } from '../model';
import { DocinfoStateService } from './docinfo.state.service';
import { classroomErrorHandler } from './error-handler';
import { OnlineStateService } from './online.state.service';

@Injectable()
export class DocumentActionListener {
    private readonly actionEvent$: ReplaySubject<DocCtrlEvent> = new ReplaySubject<DocCtrlEvent>();

    constructor(
        private prm: ProcessingRequestManager,
        private onlineStateS: OnlineStateService,
        private ccsGateway: CcsGateway,
        private docinfoStateS: DocinfoStateService,
        private dialog: MatDialog
    ) {}

    start() {
        this.actionEvent$.subscribe(e => this.handleEvent(e));
    }

    emit(event: DocCtrlEvent) {
        this.actionEvent$.next(event);
    }

    actionInProgressName(docId: string, action: DocCtrlEventType | '' = ''): string {
        return `document-${docId}-action-${action}`;
    }

    /**
     * Monitors an asynchronous task related to a document.
     * @template T The type of the result of the task.
     * @param taskCallback A function that executes the task and returns a Promise.
     * @param docId The ID of the document (defaults to 'global' if the action is not tied to a specific document).
     * @param action The name of the action (optional).
     * @returns A Promise that resolves with the result of the task.
     */
    private async monitorDocAction<T>(
        taskCallback: () => Promise<T>,
        docId: string = 'global',
        action: DocCtrlEventType | '' = ''
    ): Promise<T> {
        const monitorKey = this.actionInProgressName(docId, action);
        const task$ = from(taskCallback());
        return await firstValueFrom(this.prm.monitor(monitorKey, task$, { parent: `document-${docId}` }));
    }

    @ErrorHandlerDecorator([classroomErrorHandler])
    private async handleEvent(event: DocCtrlEvent) {
        await this.monitorDocAction(
            async () => {
                const coord = await firstValueFrom(this.onlineStateS.coordinator$);
                switch (event.type) {
                    case 'select-doc': {
                        const docInfo: DocumentInfo = event.data;
                        const details = docInfo.details;
                        const selectMulti = event.keys.find(k => ['ctrl', 'Ctrl', 'Control'].includes(k)) != null;
                        await coord.selectDocByGlobalId(
                            details.coordStateId,
                            docInfo.editorType,
                            details.docGlobalId,
                            selectMulti
                        );
                        break;
                    }
                    case 'share-doc': {
                        this.dialog.open<SharingDocDialogComponent, SharingDialogConfig>(SharingDocDialogComponent, {
                            data: {
                                showTabs: ['share', 'save'],
                                selectedTab: 'save',
                                captureType: 'document',
                                docGlobalId: event.docGlobalId,
                                edType: event.data.editorType,
                                source: 'classroom-doc',
                                docInfo: new BehaviorSubject(event.data as DocumentInfoResponse),
                                docDisplay: {
                                    coord: coord,
                                    viewport: coord.getViewportManager(event.data.details.coordStateId),
                                },
                                useCloneDoc: true,
                            },
                        });
                        break;
                    }
                    case 'update-doc-info': {
                        const data: DocumentInfoDetail = event.data.details;
                        const req: UpdateDocumentInfoRequest = {
                            peerId: coord.peerId,
                            coordStateId: data.coordStateId,
                            docGlobalId: event.docGlobalId,
                            details: data,
                            editorType: event.data.editorType,
                        };
                        const d = await firstValueFrom(this.ccsGateway.updateDocumentInfo(req));
                        this.docinfoStateS.addDocInfos(d.details.coordStateId, [d]);
                        break;
                    }
                    case 'remove-doc': {
                        const docInfo: DocumentInfo = event.data;
                        const details = docInfo.details;
                        await coord.removeDocByLocalId(details.coordStateId, docInfo.editorType, details.docLocalId);
                        break;
                    }
                    case 'duplicate-doc': {
                        const docInfo: DocumentInfo = event.data;
                        const details = docInfo.details;
                        const viewport = coord.getViewportManager(details.coordStateId);

                        try {
                            await coord.duplicateDocument(
                                details.docLocalId,
                                details.docGlobalId,
                                docInfo.editorType,
                                viewport,
                                true,
                                {
                                    x: 20,
                                    y: -20,
                                }
                            );
                            // Optionally handle success such as logging or updating the UI
                        } catch (error) {
                            console.error('Failed to duplicate document:', error);
                            throw new ClassroomNonCriticalError(
                                { type: 'UNCLASSIFIED', data: docInfo },
                                'Failed to duplicate document',
                                error
                            );
                        }
                        break;
                    }
                    case 'fit-to-view': {
                        const docInfo: DocumentInfo = event.data;
                        const details = docInfo.details;
                        await coord.fitDocToView(details.coordStateId, docInfo.editorType, details.docLocalId);
                        break;
                    }
                    default:
                        break;
                }
            },
            event.docGlobalId,
            event.type
        );
    }
}
