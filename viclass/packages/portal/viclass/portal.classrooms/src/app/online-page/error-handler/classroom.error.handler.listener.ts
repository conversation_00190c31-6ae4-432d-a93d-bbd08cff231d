import { ViErr, ViErrEventData } from '@viclass/editor.core';
import { MatDialog } from '@angular/material/dialog';
import {
    CommonErrorHandlerListener,
    ErrorHandlerShowMessageActions,
    LSessionRegistrationModel,
} from '@viclass/portal.common';
import { Injectable } from '@angular/core';
import {
    ClassroomCriticalError,
    ClassroomErrorType,
    ClassroomNonCriticalError,
    DismissClassroomError,
} from '@viclass/editor.coordinator/classroom';
import { BehaviorSubject, ReplaySubject } from 'rxjs';
import { PopupConfirmType } from '../../model';

/**
 * Handles errors related to the classroom environment.
 * This service listens for classroom-related errors and manages error popups accordingly.
 */
@Injectable()
export class ClassroomErrorHandlerListener extends CommonErrorHandlerListener {
    private readonly interruptedCallbacks: Array<() => void | Promise<void>> = [];

    /**
     * Queues for managing different types of classroom errors.
     * - `fatalErrorQueue`: Stores critical errors that require immediate attention.
     * - `nonFatalErrorQueue`: Stores recoverable errors.
     */
    private fatalErrorQueue: ClassroomCriticalError[] = [];
    private nonFatalErrorQueue: ClassroomNonCriticalError[] = [];
    private currError?: ClassroomCriticalError | ClassroomNonCriticalError;

    /**
     * Subjects for managing specific classroom-related error popups.
     */
    readonly presenterOffline$: BehaviorSubject<{
        obs: ReplaySubject<PopupConfirmType>;
        member: LSessionRegistrationModel;
    }> = new BehaviorSubject(undefined);

    readonly ownerOffline$: BehaviorSubject<{
        obs: ReplaySubject<PopupConfirmType>;
        member: LSessionRegistrationModel;
    }> = new BehaviorSubject(undefined);

    readonly classEnded$: BehaviorSubject<any> = new BehaviorSubject(undefined);
    readonly classNotStart$: BehaviorSubject<any> = new BehaviorSubject(undefined);
    readonly connectionError$: BehaviorSubject<any> = new BehaviorSubject(undefined);
    readonly kickedOut$: BehaviorSubject<any> = new BehaviorSubject(undefined);
    readonly offlineUser$: BehaviorSubject<any> = new BehaviorSubject(undefined);
    readonly joinedInAnotherSession$: BehaviorSubject<any> = new BehaviorSubject(undefined);

    /**
     * Constructor to initialize the error handler listener.
     * @param dialog - Material Dialog instance for displaying error popups.
     */
    constructor(dialog: MatDialog) {
        super(dialog);
    }

    /**
     * Handles incoming error events.
     * - If the event contains a `DismissClassroomError`, it dismisses the error.
     * - If the event contains a `ClassroomError`, it enqueues the error for processing.
     * - Otherwise, it passes the event to the parent class.
     *
     * @param data The error event data.
     * @returns The processed event data.
     */
    override onEvent(data: ViErrEventData): ViErrEventData | Promise<ViErrEventData> {
        if (data.state instanceof DismissClassroomError) {
            this.dismissError(data.state.type);
            return data;
        }
        if (data.state instanceof ClassroomNonCriticalError || data.state instanceof ClassroomCriticalError) {
            this.enqueueError(data.state);
            return data;
        }
        return super.onEvent(data);
    }

    /**
     * Enqueues an error into the appropriate queue (fatal or non-fatal)
     * and triggers the processing of errors if no error is currently displayed.
     *
     * @param error The classroom error to enqueue.
     */
    private enqueueError(error: ClassroomCriticalError | ClassroomNonCriticalError) {
        if (error instanceof ClassroomCriticalError) {
            this.fatalErrorQueue.push(error);
        } else if (error instanceof ClassroomNonCriticalError) {
            this.nonFatalErrorQueue.push(error);
        }
        this.processQueue(); // Start processing if no error is currently being displayed
    }

    /**
     * Processes the error queue to display the next error popup.
     * Ensures that only one popup is shown at a time.
     */
    private processQueue() {
        if (this.currError) return; // Ensure only one popup at a time

        let nextError: ClassroomCriticalError | ClassroomNonCriticalError;

        if (this.fatalErrorQueue.length > 0) {
            nextError = this.fatalErrorQueue.shift();
            this.onClassroomInterrupted();
        } else if (this.nonFatalErrorQueue.length > 0) {
            nextError = this.nonFatalErrorQueue.shift();
        }

        if (nextError) {
            this.currError = nextError;
            this.showPopup(nextError);
        }
    }

    /**
     * Displays the appropriate popup based on the error type.
     *
     * @param errorItem The classroom error to display.
     */
    private showPopup(errorItem: ClassroomCriticalError | ClassroomNonCriticalError) {
        const type = errorItem.state.type;
        const data = errorItem.state.data;
        if (type == 'PEER_KICKED_OUT') this.kickedOut$.next(data);
        else if (type == 'OFFLINE_USER') this.offlineUser$.next(data);
        else if (type == 'DUPLICATED_SESSION') this.joinedInAnotherSession$.next(data);
        else if (type == 'CONNECTION_ERROR') this.connectionError$.next(errorItem);
        else if (type == 'CLASS_ENDED') this.classEnded$.next(data);
        else if (type == 'CLASS_NOT_OPENED') this.classNotStart$.next(data);
        else if (type == 'OWNER_OFFLINE') this.ownerOffline$.next(data);
        else if (type == 'PRESENTER_OFFLINE') this.presenterOffline$.next(data);
        else {
            let message = 'Có lỗi xảy ra';
            const actions: ErrorHandlerShowMessageActions = {};
            if (errorItem instanceof ViErr) message = errorItem.message;
            if (errorItem instanceof ClassroomCriticalError) actions.reload = true;

            // using common error popup to show error message
            this.showMessage({ message, actions });
        }
    }

    /**
     * Dismisses a currently displayed error popup and removes it from the queue.
     * If the dismissed error is currently being displayed, it moves to the next error.
     *
     * @param errorType The type of error to dismiss.
     */
    private dismissError(errorType: ClassroomErrorType) {
        // Close the currently displayed popup
        switch (errorType) {
            case 'PEER_KICKED_OUT':
                this.kickedOut$.next(undefined);
                break;
            case 'OFFLINE_USER':
                this.offlineUser$.next(undefined);
                break;
            case 'DUPLICATED_SESSION':
                this.joinedInAnotherSession$.next(undefined);
                break;
            case 'CONNECTION_ERROR':
                this.connectionError$.next(undefined);
                break;
            case 'CLASS_ENDED':
                this.classEnded$.next(undefined);
                break;
            case 'CLASS_NOT_OPENED':
                this.classNotStart$.next(undefined);
                break;
            case 'OWNER_OFFLINE':
                this.ownerOffline$.next(undefined);
                break;
            case 'PRESENTER_OFFLINE':
                this.presenterOffline$.next(undefined);
                break;
            default:
                break;
        }

        // Remove dismissed error from the queue
        this.fatalErrorQueue = this.fatalErrorQueue.filter(error => error.state.type != errorType);
        this.nonFatalErrorQueue = this.nonFatalErrorQueue.filter(error => error.state.type != errorType);

        // If the dismissed error is currently displayed, reset and process the next one
        if (this.currError && errorType === this.currError.state.type) {
            delete this.currError;
            this.processQueue(); // Show the next error if available
        }
    }

    private async onClassroomInterrupted() {
        for (const hook of this.interruptedCallbacks) {
            try {
                await hook();
            } catch (err) {
                console.error('ClassroomErrorHandlerListener: Error executing a stop hook:', err);
            }
        }
    }

    /**
     * Registers a callback function to be executed when a classroom interruption occurs.
     * @param callback The callback function to register. It can be synchronous or asynchronous.
     */
    registerInterruptedHook(callback: () => void | Promise<void>) {
        this.interruptedCallbacks.push(callback);
    }
}
