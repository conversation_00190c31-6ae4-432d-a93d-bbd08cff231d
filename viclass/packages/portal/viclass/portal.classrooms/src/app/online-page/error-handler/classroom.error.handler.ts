import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ViErrEventData, ViErrEventEmitter } from '@viclass/editor.core';
import {
    ClassroomCriticalError,
    ClassroomErrorType,
    ClassroomNonCriticalError,
    DismissClassroomError,
} from '@viclass/editor.coordinator/classroom';
import { ErrorHandler, Injectable } from '@angular/core';

/**
 * **ClassroomErrorHandler**
 * Handles classroom-specific errors by emitting events for further processing.
 */
export class ClassroomErrorHandler extends ViErrorHandler {
    /**
     * Initializes the error handler with a custom event emitter.
     * @param emitter - The event emitter for classroom errors.
     */
    constructor(public override readonly emitter = new ViErrEventEmitter()) {
        super(emitter);
    }

    /**
     * Processes an error event and determines if it should be emitted or returned.
     * - Emits errors related to classroom functionality.
     * - Filters out certain known errors and prevents them from propagating.
     *
     * @param e The error event data.
     * @returns `null` if the error is handled, otherwise returns the event.
     */
    override handle(e: ViErrEventData): ViErrEventData | null {
        if (
            e.state instanceof ClassroomCriticalError ||
            e.state instanceof ClassroomNonCriticalError ||
            e.state instanceof DismissClassroomError
        ) {
            this.emit(e);
            return null;
        }

        return e;
    }

    /**
     * Dismisses a specific classroom error by generating a `DismissClassroomError` event.
     * @param errorType The type of error to dismiss.
     */
    dismissError(errorType: ClassroomErrorType) {
        this.handle((<ViErrEventData>(<unknown>{ state: new DismissClassroomError(errorType) })) as ViErrEventData);
    }
}

export const classroomErrorHandlerEmitter = new ViErrEventEmitter();

export const classroomErrorHandler = new ClassroomErrorHandler(classroomErrorHandlerEmitter);

/**
 * **GlobalErrorHandler**
 * Handles application-wide errors and routes them to the appropriate error handler.
 */
@Injectable()
export class GlobalErrorHandler implements ErrorHandler {
    constructor() {}

    /**
     * Decorator-based error handler that directs errors to the appropriate error handler.
     * @param err The error event data.
     * @throws The original error after processing.
     */
    handleError(err: ViErrEventData) {
        if (
            err instanceof ClassroomNonCriticalError ||
            err instanceof ClassroomCriticalError ||
            err instanceof DismissClassroomError
        ) {
            classroomErrorHandler.handle((<ViErrEventData>(<unknown>{ state: err })) as ViErrEventData);
            return null;
        }
        throw err;
    }
}
