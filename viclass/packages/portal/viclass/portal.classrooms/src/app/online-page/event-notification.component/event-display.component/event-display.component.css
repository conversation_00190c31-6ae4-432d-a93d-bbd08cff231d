@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

.fade-out {
    animation: fadeOut 2s ease-out forwards; /* Adjusted to 2 seconds for a slower effect */
}

@keyframes speaking {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.speaking {
    animation: speaking 0.5s ease-in-out forwards;
}
