<div class="flex items-center space-x-2">
    <div
        *ngIf="overflowCount > 0"
        class="text-center text-black rounded-full w-[30px] h-[30px] flex items-center justify-center transition-all duration-300">
        {{ overflowCount }}+
    </div>

    <div *ngFor="let event of displayEvents">
        <div
            #avatar
            [ngClass]="{ 'fade-out': !event.isShow, speaking: event.speaking }"
            class="d-flex justify-content-end profile-avatar bg-blue-500 rounded-full w-[30px] h-[30px] flex items-center justify-center text-white transition-all duration-300"
            [ngStyle]="
                (event.avatarUrl$ | async) && {
                    'background-image': 'url(' + (event?.avatarUrl$ | async) + ')',
                }
            "></div>
        <lib-tooltip
            class="z-20"
            [toolTipFor]="avatar"
            [tooltipContent]="displayName(event.member$ | async)"
            [placement]="'top'"></lib-tooltip>
    </div>
</div>
