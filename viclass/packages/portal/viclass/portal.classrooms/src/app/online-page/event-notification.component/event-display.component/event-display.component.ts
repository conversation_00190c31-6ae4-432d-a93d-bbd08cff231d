import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { displayName, TooltipComponent } from '@viclass/portal.common';
import { Subscription } from 'rxjs';
import { EventNoti, EventNotiService } from '../event.service';

interface EventWithTimeout extends EventNoti {
    timeoutId?: ReturnType<typeof setTimeout>;
    speaking?: boolean;
    isShow?: boolean;
}

@Component({
    selector: 'app-event-display',
    standalone: true,
    imports: [CommonModule, TooltipComponent],
    templateUrl: './event-display.component.html',
    styleUrls: ['./event-display.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventDisplayComponent implements OnInit, OnDestroy {
    protected displayName = displayName;
    private enSub: Subscription;
    protected events: EventWithTimeout[] = [];
    protected overflowCount = 0;

    constructor(
        private cdr: ChangeDetectorRef,
        public eventNotiService: EventNotiService
    ) {}

    ngOnInit() {
        this.enSub = this.eventNotiService.eventStream$.subscribe(event => this.addEvent(event));
    }

    ngOnDestroy() {
        if (this.enSub) this.enSub.unsubscribe();
    }

    protected get displayEvents() {
        return this.events.slice(0, 3);
    }

    private resetSpeaking(event: EventWithTimeout) {
        setTimeout(() => {
            event.speaking = false;
            this.cdr.markForCheck();
        }, 300);
    }

    private updateOverflowCount() {
        this.overflowCount = this.events.length > 3 ? this.events.length - 3 : 0;
        this.cdr.markForCheck();
    }

    private setRemovalTimeout(event: EventWithTimeout) {
        return setTimeout(() => {
            event.isShow = false;
            this.cdr.markForCheck();
            setTimeout(() => {
                this.events = this.events.filter(e => e !== event);
                this.updateOverflowCount();
            }, 2000);
        }, 3000);
    }

    protected addEvent(event: EventNoti) {
        if (!event) return;

        const existingEvent = this.events.find(e => e.regId === event.regId && e.type === event.type);
        if (existingEvent) {
            clearTimeout(existingEvent.timeoutId);
            existingEvent.timeoutId = this.setRemovalTimeout(existingEvent);
            existingEvent.isShow = true;
            existingEvent.speaking = true;
            this.resetSpeaking(existingEvent);
            this.cdr.markForCheck();
        } else {
            const newEvent: EventWithTimeout = { ...event, speaking: true, isShow: true };
            newEvent.timeoutId = this.setRemovalTimeout(newEvent);
            this.events.unshift(newEvent);
            this.updateOverflowCount();
            this.resetSpeaking(newEvent);
        }
    }
}
