<div class="z-10 fixed bottom-0 right-0 flex flex-col items-end">
    <!-- Top row with the latest items -->
    <div class="flex pt-[10px] pb-[10px]">
        <app-event-display></app-event-display>
    </div>
    <!-- Two EventItem components -->
    <app-noti-event-list
        approveType="approve-present"
        rejectType="reject-present"
        numberColor="#db00ff"
        [items]="memberStates.memberRaisingHand$"
        notiTitle="Giơ tay phát biểu">
        <i icon class="vcon-onl vcon_sidebar_raise-hand text-[20px] mx-[10px]"></i>
    </app-noti-event-list>
    <app-noti-event-list
        approveType="approve-register"
        rejectType="reject-register"
        numberColor="#ff7a00"
        [items]="memberStates.memberWaitingConfirm$"
        notiTitle="Xin vào lớp">
        <i icon class="vcon-onl vcon_user_waiting-student text-[20px] mx-[10px]"></i>
    </app-noti-event-list>
    <app-share-screen-list numberColor="#ff7a00" [items]="reqShareScreens$" notiTitle="Xin chia sẻ màn hình">
        <i icon class="vcon vcon-onl vcon_share-screen text-[20px] mx-[10px]"></i>
    </app-share-screen-list>
    <app-request-pin-board-list [items]="requestPin$">
        <i icon class="vcon vcon-onl vcon_whiteboard_pinned text-[20px] mx-[10px]"></i>
    </app-request-pin-board-list>
</div>
