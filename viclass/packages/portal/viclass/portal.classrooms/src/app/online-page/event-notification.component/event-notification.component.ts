import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { LSessionRegistrationModel } from '@viclass/portal.common';
import { BehaviorSubject, map, Subscription } from 'rxjs';
import { MemberStateService } from '../member.state.service';
import { EventDisplayComponent } from './event-display.component/event-display.component';
import { NotiEventListComponent } from './noti-event-list.component/noti-event-list.component';
import { ShareScreenListComponent } from './share-screen-list.component/share-screen-list.component';
import { RequestPinBoardListComponent } from './request-pin-board-list/request-pin-board-list.component';
import { RequestPinTabModel } from './request-pin-board-list/request-pin-board.model';

@Component({
    selector: 'app-event-notification',
    standalone: true,
    imports: [
        CommonModule,
        NotiEventListComponent,
        EventDisplayComponent,
        ShareScreenListComponent,
        RequestPinBoardListComponent,
    ],
    templateUrl: './event-notification.component.html',
    styleUrls: ['./event-notification.component.css'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EventNotificationComponent {
    private readonly mrhSub: Subscription;
    private readonly mwcSub: Subscription;
    private readonly requestPinSub: Subscription;

    /**
     * Public BehaviorSubject holding the list of members who have pending requests to pin a tab.
     * This is used by the template for app-request-pin-board-list.
     */
    public requestPin$: BehaviorSubject<RequestPinTabModel[]>;

    constructor(
        public memberStates: MemberStateService,
        private cdr: ChangeDetectorRef
    ) {
        this.mrhSub = this.memberStates.memberRaisingHand$.subscribe(() => this.cdr.markForCheck());
        this.mwcSub = this.memberStates.memberWaitingConfirm$.subscribe(() => this.cdr.markForCheck());

        // Initialize requestPin BehaviorSubject
        this.requestPin$ = new BehaviorSubject<RequestPinTabModel[]>([]);

        // Subscribe to memberRequestPinTab$, filter for pending requests, and update this.requestPin
        this.requestPinSub = this.memberStates.memberRequestPinTab$
            .pipe(
                map(members =>
                    members.filter(
                        member =>
                            member.userState &&
                            member.userState.requestPinTabState &&
                            member.userState.requestPinTabState.some(req => req && req.status === 'PENDING')
                    )
                )
            )
            .subscribe(filteredMembers => {
                const a = [];
                filteredMembers.forEach(member => {
                    const b = member.userState.requestPinTabState
                        .filter(req => req && req.status === 'PENDING')
                        .map(
                            req =>
                                new RequestPinTabModel(member.profile.id, member.id, req.tabId, req.status, req.tabName)
                        );
                    a.push(...b);
                });
                this.requestPin$.next(a);
                this.cdr.markForCheck(); // Mark for check if the list update should trigger CD here
            });
    }

    /**
     * Getter for accessing the list of participants who have requested to share their screen.
     * */
    public get reqShareScreens$(): BehaviorSubject<LSessionRegistrationModel[]> {
        return this.memberStates.memberReqShareScreen$;
    }

    ngOnDestroy() {
        // Changed from onDestroy to ngOnDestroy
        if (this.mrhSub) {
            this.mrhSub.unsubscribe();
        }
        if (this.mwcSub) {
            this.mwcSub.unsubscribe();
        }
        if (this.requestPinSub) {
            this.requestPinSub.unsubscribe();
        }
    }
}
