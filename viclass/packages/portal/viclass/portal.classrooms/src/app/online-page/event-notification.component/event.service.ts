import { Injectable } from '@angular/core';
import { LSessionRegistrationModel } from '@viclass/portal.common';
import { combineLatest, map, Observable, take } from 'rxjs';
import { RequestPresentationAD } from '../../activities';
import { MemberActionEvent, MemberAvatarViewModel, MemberItemEventType } from '../../model';
import { ActivityStateService } from '../activity.state.service';
import { ClassroomConferenceService } from '../conference/classroom.conference.service';
import { MemberActionListener } from '../member.action.listener';
import { MemberStateService } from '../member.state.service';

export type EventNotiType = 'speaking';
export type EventNoti = {
    regId: string;
    type: EventNotiType;
    avatarUrl$: Observable<string>;
    member$: Observable<LSessionRegistrationModel>;
};

@Injectable()
export class EventNotiService {
    constructor(
        public confS: ClassroomConferenceService,
        private memberStateS: MemberStateService,
        private activityStateS: ActivityStateService,
        private memberActionListener: MemberActionListener
    ) {}

    eventStream$: Observable<EventNoti> = combineLatest({
        speakingEvent: this.confS.speakingEvent,
    }).pipe(
        map(data => {
            const { speakingEvent } = data;
            if (!speakingEvent.speaking) return null;
            return {
                regId: speakingEvent.userId,
                type: 'speaking',
                avatarUrl$: this.avatarUrl$(speakingEvent.userId),
                member$: this.member$(speakingEvent.userId),
            };
        })
    );

    member$(userId: string): Observable<LSessionRegistrationModel> {
        return this.memberStateS.memberByUserId$(userId);
    }

    private showWaitResponse$(userId: string): Observable<boolean> {
        return this.member$(userId).pipe(
            take(1),
            map(m => {
                const a = this.activityStateS.requestPresentActivities.find(
                    a => (a.data as RequestPresentationAD).requestedTo == m.profile.id
                );
                if (a) {
                    const ad = a.data as RequestPresentationAD;
                    return a.status == 'ON_GOING' && ad.requestedTo == m.profile.id;
                }
                return false;
            })
        );
    }

    avatarModel$(userId: string): Observable<MemberAvatarViewModel> {
        return combineLatest({
            member: this.member$(userId),
            showWaitResponse: this.showWaitResponse$(userId),
        }).pipe(
            take(1),
            map(data => {
                const { member, showWaitResponse } = data;
                return {
                    username: member?.profile.username,
                    avatarUrl: member?.profile.avatarUrl,
                    activityStatus: member?.userState.raiseHandStatus,
                    availableStatus: member?.userState.availableStatus,
                    lsRegStatus: member?.regStatus,
                    showWaitResponse: showWaitResponse,
                };
            })
        );
    }

    avatarUrl$(userId: string): Observable<string> {
        return this.avatarModel$(userId).pipe(map(m => m.avatarUrl));
    }

    onMemberAction(type: MemberItemEventType, regId: string) {
        const event: MemberActionEvent = {
            action: type,
            regId: regId,
            activityId: this.activityStateS.requestPresentationActivity$.value?.id,
        };
        this.memberActionListener.memberActionEvent$.next(event);
    }
}
