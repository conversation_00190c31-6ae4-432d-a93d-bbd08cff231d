import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input } from '@angular/core';
import { map, Observable } from 'rxjs';
import { MemberAvatarViewModel, MemberItemEventType } from '../../../../model';
import { EventNotiService } from '../../event.service';
import {
    displayName,
    LSessionRegistrationModel,
    ProcessingRequestManager,
    SpinnerLabelComponent,
    TooltipComponent,
} from '@viclass/portal.common';
import { OnlineStateService } from '../../../online.state.service';

@Component({
    selector: 'app-event-item',
    standalone: true,
    imports: [CommonModule, TooltipComponent, SpinnerLabelComponent],
    templateUrl: './noti-event-item.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotiEventItemComponent {
    protected displayName = displayName;
    private _accessRejectButtons = false; // Track hover state

    @Input() member: LSessionRegistrationModel;
    @Input() approveType: MemberItemEventType;
    @Input() rejectType: MemberItemEventType;
    @Input('member-avatar') state: Observable<MemberAvatarViewModel>;

    constructor(
        private eventNotiService: EventNotiService,
        private cdr: ChangeDetectorRef,
        private prm: ProcessingRequestManager,
        public onlStateS: OnlineStateService
    ) {}

    protected get actionInProgress$(): Observable<boolean> {
        return this.prm.getInprogressObs(`member-${this.member.id}`);
    }

    protected get avatarUrl$() {
        return this.avatarModel$.pipe(map(m => m.avatarUrl));
    }

    protected get avatarModel$(): Observable<MemberAvatarViewModel> {
        return this.eventNotiService.avatarModel$(this.member.profile.id);
    }

    protected get member$(): Observable<LSessionRegistrationModel> {
        return this.eventNotiService.member$(this.member.profile.id);
    }

    protected onMemberAction(type: MemberItemEventType) {
        this.eventNotiService.onMemberAction(type, this.member.id);
    }

    protected onMouseEnter() {
        this._accessRejectButtons = true;
        this.cdr.markForCheck(); // Trigger change detection on hover
    }

    protected onMouseLeave() {
        this._accessRejectButtons = false;
        this.cdr.markForCheck(); // Trigger change detection on hover out
    }

    protected get accessRejectButtons$(): Observable<boolean> {
        return this.onlStateS.isOwner$.pipe(map(isOwner => this._accessRejectButtons && isOwner));
    }
}
