import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { LSessionRegistrationModel, TooltipComponent } from '@viclass/portal.common';
import { BehaviorSubject, Subscription } from 'rxjs';
import { MemberItemEventType } from '../../../model';
import { NotiEventItemComponent } from './noti-event-item.component/noti-event-item.component';

@Component({
    selector: 'app-noti-event-list',
    standalone: true,
    imports: [CommonModule, NotiEventItemComponent, TooltipComponent],
    templateUrl: './noti-event-list.component.html',
    styleUrls: ['./noti-event-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class NotiEventListComponent implements OnInit, OnDestroy {
    private iSub: Subscription;

    protected showList = false;
    protected isShowScrollUp = false;
    protected isShowScrollDown = false;

    @Input() numberColor: string;
    @Input() notification: string;
    @Input() rejectType: MemberItemEventType;
    @Input() approveType: MemberItemEventType;
    @Input() items: BehaviorSubject<LSessionRegistrationModel[]>;
    @Input() notiTitle: string = '';

    @ViewChild('scrollableList') private scrollableList!: ElementRef<HTMLDivElement>;

    constructor(private cdr: ChangeDetectorRef) {}

    ngOnDestroy(): void {
        if (this.iSub) {
            this.iSub.unsubscribe();
        }
    }

    ngOnInit() {
        this.iSub = this.items.subscribe(() => {
            this.cdr.markForCheck();
        });
    }

    protected get calculatedHeight() {
        const maxItems = Math.min(this.items.value.length, 5);
        return `${maxItems * 45 + (maxItems - 1) * 10}px`;
    }

    protected get isShowScroll() {
        return this.showList && this.items.value.length > 5;
    }

    private updateScrollButtons() {
        if (this.scrollableList) {
            const element = this.scrollableList.nativeElement;
            this.isShowScrollUp = element.scrollTop > 0;
            this.isShowScrollDown = element.scrollHeight > element.clientHeight + element.scrollTop;
        }
    }

    protected scrollUp() {
        if (this.scrollableList) {
            this.scrollableList.nativeElement.scrollTop -= 45; // Scroll up by 45px
            this.updateScrollButtons();
        }
    }

    protected scrollDown() {
        if (this.scrollableList) {
            this.scrollableList.nativeElement.scrollTop += 45; // Scroll down by 45px
            this.updateScrollButtons();
        }
    }

    protected onScroll() {
        this.updateScrollButtons();
    }

    protected toggleList() {
        this.showList = !this.showList;
        this.cdr.markForCheck();

        if (this.showList) {
            setTimeout(() => {
                this.updateScrollButtons();
                this.cdr.markForCheck(); // Trigger check after timeout
            }, 500);
        } else {
            this.scrollableList.nativeElement.scrollTop = 0;
        }
    }
}
