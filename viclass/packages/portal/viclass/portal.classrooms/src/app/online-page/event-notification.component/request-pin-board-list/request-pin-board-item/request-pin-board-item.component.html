<div
    class="relative flex h-[45px] w-[197px]"
    [ngStyle]="{
        width: (accessButtons$ | async) ? '122px' : '197px',
        marginRight: (accessButtons$ | async) ? '0px' : '-75px',
        zIndex: (accessButtons$ | async) ? 100 : 10,
    }"
    (mouseenter)="onMouseEnter()"
    (mouseleave)="onMouseLeave()">
    <!-- Accept/Reject Buttons Group positioned behind the Avatar -->
    <ng-template [ngIf]="onlStateS.isOwner$ | async">
        <div
            #accessButtons
            class="!transition-all !duration-300 absolute right-0 h-[45px] bg-P3 rounded-[20px] flex items-center space-x-[5px] px-[5px]"
            [ngClass]="{
                'opacity-100 translate-x-0': accessButtons$ | async,
                'opacity-0 translate-x-[197px]': !(accessButtons$ | async),
            }"
            style="width: 120px">
            <div [spinner]="actionInProgress$" class="flex">
                <button
                    (click)="onMemberAction('approve-request-pin-board')"
                    class="flex items-center justify-center h-[30px] w-[30px] rounded-full hover:bg-white cursor-pointer">
                    <i class="vcon-onl vcon_sidebar_action_accept"></i>
                </button>
                <button
                    (click)="onMemberAction('reject-request-pin-board')"
                    class="flex items-center justify-center h-[30px] w-[30px] rounded-full hover:bg-white cursor-pointer">
                    <i class="vcon-onl vcon_sidebar_action_denied"></i>
                </button>
            </div>
        </div>
        <lib-tooltip [toolTipFor]="accessButtons" [tooltipContent]="tabName" [placement]="'top-start'"></lib-tooltip>
    </ng-template>

    <!-- Avatar Button -->
    <div class="w-full" [ngStyle]="{ transition: 'none' }">
        <div
            class="flex overflow-x-visible absolute w-auto h-[45px] rounded-full flex items-center justify-center text-white z-10"
            [ngClass]="{ 'right-0': accessButtons$ | async, 'left-0': !(accessButtons$ | async) }"
            [ngStyle]="{ transition: 'none' }">
            <div
                *ngIf="!(accessButtons$ | async)"
                class="mr-auto pr-[5px] text-BW1 w-[76.5px] overflow-hidden custom-marquee-container">
                <span class="custom-marquee-text">
                    <!-- Thêm span với class cho hiệu ứng -->
                    {{ tabName }}
                </span>
            </div>

            <div
                #avatar
                class="d-flex justify-content-end profile-avatar w-[45px] h-[45px] rounded-full ml-auto"
                [ngStyle]="
                    (avatarUrl$ | async) && {
                        'background-image': 'url(' + (avatarUrl$ | async) + ')',
                    }
                "></div>
            <lib-tooltip
                [toolTipFor]="avatar"
                [tooltipContent]="(member$ | async).profile.username"
                [placement]="'top-end'"></lib-tooltip>
        </div>
    </div>
</div>
