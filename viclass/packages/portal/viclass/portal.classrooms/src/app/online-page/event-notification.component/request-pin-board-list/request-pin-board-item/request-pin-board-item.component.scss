/* Inside EventItemComponent styles */
.scrollable-list {
    overflow-y: auto; /* Enable vertical scrolling */
    scrollbar-width: none; /* Hide scrollbar */
}

.font-weight-hover:hover {
    font-weight: 900;
}

.custom-marquee-container {
    .custom-marquee-text {
        display: inline-block;
        white-space: nowrap;
        animation: marqueeEffect 3s linear infinite;
        animation-delay: 0s;
    }

    &:hover .custom-marquee-text {
        //transform: translateX(100%);
        //animation-play-state: paused;
    }
}

@keyframes marqueeEffect {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}
