<div
    *ngIf="items.value.length > 0"
    class="flex border-t-2 pt-[30px] pb-[30px] pr-5 pl-1 transition-all !duration-300 rounded-l-lg bg-BW7"
    style="border-top-color: #ffddb6; border-top-width: 0.5px"
    [ngStyle]="{
        paddingTop: isShowScroll ? '30px' : '10px',
        paddingBottom: isShowScroll ? '30px' : '10px',
    }">
    <div
        #notiIcon
        class="flex items-center t pl-2 cursor-pointer font-weight-hover transition-all !duration-300"
        (click)="toggleList()"
        [style.color]="numberColor">
        <span>{{ items.value.length }}</span>
        <ng-content class="mx-[10px]" select="[icon]"></ng-content>
    </div>
    <lib-tooltip [toolTipFor]="notiIcon" [tooltipContent]="'Xin ghim bảng'"></lib-tooltip>

    <!-- Toggle List with Animation -->
    <div class="relative flex">
        <div
            class="relative flex flex-col items-end space-y-[10px] overflow-y-auto transition-all !duration-300"
            [ngClass]="{ 'scrollable-list': items.value.length }"
            [ngStyle]="{
                height: showList ? calculatedHeight : items.value.length > 0 ? '45px' : '0px',
                maxHeight: showList ? calculatedHeight : items.value.length > 0 ? '45px' : '0px',
                overflowY: items.value.length <= 5 || !showList ? 'visible' : 'auto',
            }"
            (scroll)="onScroll()"
            #scrollableList>
            <ng-template ngFor let-e [ngForOf]="items | async" let-i="index">
                <app-request-pin-board-item
                    class="text-right"
                    [userId]="e.userId"
                    [regId]="e.regId"
                    [coordStateId]="e.tabId"
                    [tabName]="e.tabName"
                    [ngStyle]="{ display: showList || i === 0 ? 'block' : 'none' }">
                </app-request-pin-board-item>
            </ng-template>
        </div>

        <button
            *ngIf="isShowScroll"
            [disabled]="!isShowScrollUp"
            class="absolute right-0 -top-[25px] w-[45px] h-[20px] rounded-[15px] cursor-pointer z-20 flex items-center justify-center"
            [ngClass]="{
                'bg-BW7': isShowScrollUp,
                'bg-BW4 cursor-not-allowed': !isShowScrollUp,
            }"
            style="box-shadow: 0px 5px 20px 0px rgba(0, 66, 75, 0.2)"
            (click)="scrollUp()">
            <i icon class="vcon-general vcon_drop-down-menu_open"></i>
        </button>

        <button
            *ngIf="isShowScroll"
            [disabled]="!isShowScrollDown"
            class="absolute right-0 -bottom-[25px] w-[45px] h-[20px] rounded-[15px] cursor-pointer z-20 flex items-center justify-center"
            [ngClass]="{
                'bg-BW7': isShowScrollDown,
                'bg-BW4 cursor-not-allowed': !isShowScrollDown,
            }"
            style="box-shadow: 0px 5px 20px 0px rgba(0, 66, 75, 0.2)"
            (click)="scrollDown()">
            <i icon class="vcon-general vcon_drop-down-menu_close"></i>
        </button>
    </div>
</div>
