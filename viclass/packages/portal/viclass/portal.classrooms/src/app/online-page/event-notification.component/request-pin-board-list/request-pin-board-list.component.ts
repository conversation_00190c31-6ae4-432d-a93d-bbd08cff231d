import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { ProcessingRequestManager, TooltipComponent } from '@viclass/portal.common';
import { CommonModule } from '@angular/common';
import { RequestPinBoardItemComponent } from './request-pin-board-item/request-pin-board-item.component';
import { RequestPinTabModel } from './request-pin-board.model';

@Component({
    selector: 'app-request-pin-board-list',
    standalone: true,
    imports: [CommonModule, TooltipComponent, RequestPinBoardItemComponent],
    templateUrl: './request-pin-board-list.component.html',
    styleUrls: ['./request-pin-board-list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequestPinBoardListComponent implements OnInit, OnDestroy {
    private iSub: Subscription;

    protected showList = false;
    protected isShowScrollUp = false;
    protected isShowScrollDown = false;

    @Input() numberColor: string;
    @Input() items: BehaviorSubject<RequestPinTabModel[]>;

    @ViewChild('listContainer') protected listContainer!: ElementRef;
    @ViewChild('scrollableList') protected scrollableList!: ElementRef;

    constructor(
        private cdr: ChangeDetectorRef,
        private prm: ProcessingRequestManager
    ) {}

    ngOnDestroy(): void {
        if (this.iSub) {
            this.iSub.unsubscribe();
        }
    }

    ngOnInit() {
        this.iSub = this.items.subscribe(item => {
            this.cdr.markForCheck();
        });
    }

    protected get calculatedHeight() {
        const maxItems = Math.min(this.items.value.length, 5);
        return `${maxItems * 45 + (maxItems - 1) * 10}px`;
    }

    protected get isShowScroll() {
        return this.showList && this.items.value.length > 5;
    }

    private updateScrollButtons() {
        if (this.scrollableList) {
            const element = this.scrollableList.nativeElement;
            this.isShowScrollUp = element.scrollTop > 0;
            this.isShowScrollDown = element.scrollHeight > element.clientHeight + element.scrollTop;
        }
    }

    protected scrollUp() {
        if (this.scrollableList) {
            this.scrollableList.nativeElement.scrollTop -= 45; // Scroll up by 45px
            this.updateScrollButtons();
        }
    }

    protected scrollDown() {
        if (this.scrollableList) {
            this.scrollableList.nativeElement.scrollTop += 45; // Scroll down by 45px
            this.updateScrollButtons();
        }
    }

    protected onScroll() {
        this.updateScrollButtons();
    }

    protected toggleList() {
        this.showList = !this.showList;
        this.cdr.markForCheck();

        if (this.showList) {
            setTimeout(() => {
                this.updateScrollButtons();
                this.cdr.markForCheck(); // Trigger check after timeout
            }, 500);
        } else {
            this.scrollableList.nativeElement.scrollTop = 0;
        }
    }
}
