<div
    class="relative flex h-[45px] w-[120px]"
    [ngStyle]="{
        marginRight: (accessRejectButtons$ | async) ? '0px' : '-75px',
        zIndex: (accessRejectButtons$ | async) ? 100 : 10,
    }"
    (mouseenter)="onMouseEnter()"
    (mouseleave)="onMouseLeave()">
    <!-- Accept/Reject Buttons Group positioned behind the Avatar -->
    <ng-template [ngIf]="onlStateS.isOwner$ | async">
        <div
            [spinner]="actionInProgress$"
            class="!transition-all !duration-300 absolute right-0 h-[45px] bg-P3 rounded-[20px] flex items-center space-x-[5px] px-[5px]"
            [ngClass]="{
                'opacity-100 translate-x-0': accessRejectButtons$ | async,
                'opacity-0 translate-x-[120px]': !(accessRejectButtons$ | async),
            }"
            style="width: 120px">
            <button
                (click)="onMemberAction(true)"
                class="flex items-center justify-center h-[30px] w-[30px] rounded-full hover:bg-white">
                <i class="vcon-onl vcon_sidebar_action_accept"></i>
            </button>
            <button
                (click)="onMemberAction(false)"
                class="flex items-center justify-center h-[30px] w-[30px] rounded-full hover:bg-white">
                <i class="vcon-onl vcon_sidebar_action_denied"></i>
            </button>
        </div>
    </ng-template>

    <!-- Avatar Button -->
    <div
        class="w-full"
        [ngStyle]="{
            transition: 'none',
        }">
        <div
            class="overflow-x-visible absolute w-[45px] h-[45px] rounded-full flex items-center justify-center text-white cursor-pointer z-10"
            [ngClass]="{ 'right-0': accessRejectButtons$ | async, 'left-0': !(accessRejectButtons$ | async) }"
            [ngStyle]="{
                transition: 'none',
            }">
            <div
                #avatar
                class="d-flex justify-content-end profile-avatar h-full w-full rounded-full"
                [ngStyle]="
                    (avatarUrl$ | async) && {
                        'background-image': 'url(' + (avatarUrl$ | async) + ')',
                    }
                "></div>
        </div>

        <lib-tooltip
            [toolTipFor]="avatar"
            [tooltipContent]="member.profile.username"
            [placement]="'top-end'"></lib-tooltip>
    </div>
</div>
