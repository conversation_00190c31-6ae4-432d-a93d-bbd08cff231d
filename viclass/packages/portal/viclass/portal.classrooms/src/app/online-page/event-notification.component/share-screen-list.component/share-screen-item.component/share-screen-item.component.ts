import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input } from '@angular/core';
import {
    LSessionRegistrationModel,
    ProcessingRequestManager,
    SpinnerLabelComponent,
    TooltipComponent,
} from '@viclass/portal.common';
import { map, Observable } from 'rxjs';
import { MemberAvatarViewModel } from '../../../../model';
import { OnlineStateService } from '../../../online.state.service';
import { ShareScreenService } from '../../../share-screen-editor/share-screen.service';
import { EventNotiService } from '../../event.service';

@Component({
    selector: 'app-share-screen-item',
    standalone: true,
    imports: [CommonModule, TooltipComponent, SpinnerLabelComponent],
    templateUrl: './share-screen-item.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ShareScreenItemComponent {
    private _accessRejectButtons = false; // Track hover state

    @Input() member: LSessionRegistrationModel;
    @Input('member-avatar') state: Observable<MemberAvatarViewModel>;

    constructor(
        public onlStateS: OnlineStateService,
        public shareScreenS: ShareScreenService,
        private cdr: ChangeDetectorRef,
        private eventNotiS: EventNotiService,
        private prm: ProcessingRequestManager
    ) {}

    protected get userId(): string {
        return this.member.profile.id;
    }

    protected get actionInProgress$(): Observable<boolean> {
        return this.prm.getInprogressObs(`member-${this.member.id}`);
    }

    protected async acceptShareScreen() {
        this.shareScreenS.acceptShareScreen(this.userId, this.member.id);
    }

    async rejectShareScreen() {
        this.shareScreenS.rejectShareScreen(this.userId, this.member.id);
    }

    protected get avatarUrl$() {
        return this.avatarModel$.pipe(map(m => m.avatarUrl));
    }

    protected get avatarModel$(): Observable<MemberAvatarViewModel> {
        return this.eventNotiS.avatarModel$(this.userId);
    }

    protected onMemberAction(type: boolean) {
        if (type) this.acceptShareScreen();
        else this.rejectShareScreen();
    }

    protected onMouseEnter() {
        this._accessRejectButtons = true;
        this.cdr.markForCheck(); // Trigger change detection on hover
    }

    protected onMouseLeave() {
        this._accessRejectButtons = false;
        this.cdr.markForCheck(); // Trigger change detection on hover out
    }

    protected get accessRejectButtons$(): Observable<boolean> {
        return this.onlStateS.isOwner$.pipe(
            map(isOwner => {
                return isOwner && this._accessRejectButtons;
            })
        );
    }
}
