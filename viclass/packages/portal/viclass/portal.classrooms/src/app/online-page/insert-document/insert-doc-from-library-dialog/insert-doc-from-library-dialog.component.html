<div class="bg-white flex flex-col">
    <div class="flex w-full justify-between items-center bg-BW1 pr-[10px] pl-[30px] py-[5px]">
        <div class="text-BW7 flex items-center gap-2">
            <span class="vcon vcon-onl vcon_library"></span> CHÈN TÀI LIỆU
        </div>
        <button (click)="dialogRef.close()" class="p-[5px] w-[50px] text-center">
            <span class="vcon-general vcon_delete align-middle text-BW7"></span>
        </button>
    </div>
    <div
        class="input-body relative w-[100vw] md:w-[80vw] max-w-full h-[80vh] p-[10px] flex gap-[0.5rem] overflow-hidden pb-[10px] bg-white">
        <app-editor-loading [loading]="insertingDoc$ | async"></app-editor-loading>
        <div class="p-3 pt-0 flex flex-col gap-4 overflow-x-hidden overflow-y-auto w-full">
            <form
                class="vi-form sticky top-[50px] md:top-0 z-[2] p-[20px] bg-white"
                *fflow="let fum; by: buildForm; fflow as f; noNav: true"
                [formGroup]="fum"
                [ferrcoord]="f">
                <div class="flex flex-col sm:flex-row gap-[5px] items-center justify-center">
                    <div class="grow max-w-[720px] min-w-[320px] flex flex-col sm:flex-row gap-[5px] sm:gap-0">
                        <input
                            class="vi-input w-full sm:!rounded-r-none"
                            formControlName="textSearch"
                            placeholder="Nhập từ khóa tìm kiếm" />
                        <div class="flex flex-row justify-between">
                            <common-date-range-picker
                                #dateRangePicker
                                (dateRangeChange)="onDateRangeChange($event)"
                                containerClasses="sm:!rounded-l-none sm:!rounded-r-none click-indicator"
                                [useTriggerButton]="!(smallScreen$ | async)"></common-date-range-picker>
                            <button [matMenuTriggerFor]="editorOptions" class="vi-btn vi-btn-input sm:!rounded-l-none">
                                <span
                                    class="vcon-general vcon_general_filter rounded-full p-[3px]"
                                    [ngClass]="{
                                        'bg-P2': fromEditorType !== '',
                                    }"></span>
                            </button>
                        </div>
                    </div>
                </div>
                <span class="vi-text-error block mt-3" *ngIf="formError$ | async"
                    >! {{ (formError$ | async).msg }}</span
                >
            </form>

            <div class="p-[20px]" scroll-near-end (nearEnd)="onScrollNearEnd()" [threshold]="200">
                <ng-template [ngIf]="savedDocs$ | async" [ngIfElse]="loading" let-savedDocs="ngIf">
                    <div
                        class="grid xl:grid-cols-5 lg:grid-cols-4 sm:grid-cols-3 xs:grid-cols-2 gap-[30px]"
                        *ngIf="(accumulatedDocs$ | async)?.length > 0; else noDocument">
                        <ng-container *ngFor="let doc of accumulatedDocs$ | async">
                            <library-document-item
                                [doc]="doc"
                                [isInserting]="insertingDoc$ | async"
                                (insert)="onInsertDocToViewport($event)"></library-document-item>
                        </ng-container>
                    </div>
                </ng-template>
                <div class="text-center italic text-BW4 p-[20px]" *ngIf="loadingMore$ | async">
                    Đang tải thêm tài liệu...
                </div>
            </div>

            <ng-template #noDocument>
                <div class="w-full h-full flex flex-col gap-2 m-auto py-[30px]">
                    <div class="text-P2 ml-auto mr-auto flex justify-center">
                        <span class="!text-[90px] !leading-[90px] vcon vcon-general vcon_empty-data"></span>
                    </div>
                    <div class="text-center p-[10px]">Chưa có tài liệu</div>
                </div>
            </ng-template>

            <ng-template #loading>
                <div class="text-center italic text-BW4 p-[20px]">Đang tải tài liệu...</div>
            </ng-template>

            <mat-menu #editorOptions>
                <div *ngFor="let edOption of editorTypeOptions" class="px-[10px] py-[5px]">
                    <button
                        mat-menu-item
                        (click)="onEditorTypeChanges(edOption.key)"
                        [ngClass]="{
                            '!bg-P2': fromEditorType === edOption.key,
                        }"
                        class="vi-btn vi-btn-normal vi-btn-menu-item">
                        <span *ngIf="edOption.icon" [class]="'vcon vcon-general ' + edOption.icon"></span>
                        {{ edOption.label }}
                    </button>
                </div>
            </mat-menu>
        </div>
    </div>
</div>
