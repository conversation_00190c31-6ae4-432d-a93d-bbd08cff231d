import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatMomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatTabsModule } from '@angular/material/tabs';
import { CopyPasteTool } from '@viclass/editor.core';
import {
    DateRange,
    DateRangePickerComponent,
    DocInProfileMetadataService,
    DocumentInfoResponse,
    EditorLoadingComponent,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormUtilModule,
    LoadSavedDocsResponse,
    NotificationModule,
    NotificationService,
    ScrollNearEndDirective,
} from '@viclass/portal.common';
import {
    BehaviorSubject,
    debounceTime,
    firstValueFrom,
    map,
    merge,
    scan,
    Subject,
    Subscription,
    switchMap,
    tap,
} from 'rxjs';
import { EDITOR_ICONS } from '../../../model';
import { CoordStatesService } from '../../coord.state.service';
import { OnlineStateService } from '../../online.state.service';
import { LibraryDocumentItemComponent } from './library-document-item/library-document-item.component';

type SupportedEditor = 'FreeDrawingEditor' | 'GeometryEditor' | 'WordEditor' | 'MathEditor' | 'MathGraphEditor';

type EditorOptions = SupportedEditor | '';

export type DocumentFilterData = {
    textSearch: string;
    dateRange: DateRange;
    editorType: EditorOptions;
};

type EditorOptionDefinition = {
    key: EditorOptions;
    label: string;
    icon?: string;
};

const EDITOR_OPTION_DEFS: EditorOptionDefinition[] = [
    {
        key: '',
        label: 'Tất cả',
    },
    {
        key: 'FreeDrawingEditor',
        label: 'Vẽ tự do',
        icon: EDITOR_ICONS['FreeDrawingEditor'],
    },
    {
        key: 'GeometryEditor',
        label: 'Hình học',
        icon: EDITOR_ICONS['GeometryEditor'],
    },
    {
        key: 'MathEditor',
        label: 'Công thức toán',
        icon: EDITOR_ICONS['MathEditor'],
    },
    {
        key: 'MathGraphEditor',
        label: 'Đồ thị hàm số',
        icon: EDITOR_ICONS['MathGraphEditor'],
    },
    {
        key: 'WordEditor',
        label: 'Văn bản',
        icon: EDITOR_ICONS['WordEditor'],
    },
];

@Component({
    selector: 'app-insert-doc-from-library-dialog',
    templateUrl: './insert-doc-from-library-dialog.component.html',
    styleUrls: ['./insert-doc-from-library-dialog.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        LibraryDocumentItemComponent,
        FormUtilModule,
        ReactiveFormsModule,
        ScrollNearEndDirective,
        NotificationModule,
        DateRangePickerComponent,
        MatMenuModule,
        MatTabsModule,
        MatMomentDateModule,
        EditorLoadingComponent,
    ],
    providers: [
        {
            provide: MAT_DATE_FORMATS,
            useValue: {
                parse: {
                    dateInput: 'l',
                },
                display: {
                    dateInput: 'l',
                    monthYearLabel: 'MMM YYYY',
                    dateA11yLabel: 'LL',
                    monthYearA11yLabel: 'MMMM YYYY',
                },
            },
        },
        {
            provide: MAT_DATE_LOCALE,
            useValue: 'vi',
        },
        {
            provide: DateAdapter,
            useClass: MomentDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InsertDocFromLibraryDialogComponent implements OnInit, OnDestroy {
    readonly formError$ = new BehaviorSubject<ErrorModel | null>(null);
    form: UntypedFormGroup;

    savedDocs$ = new BehaviorSubject<LoadSavedDocsResponse | null>(null); // Current load saved docs result

    clearAccumulatedDocs$ = new Subject<void>(); // Subject to trigger clear accumulated docs

    loadingMore$ = new BehaviorSubject<boolean>(false);
    smallScreen$ = new BehaviorSubject<boolean>(false);
    insertingDoc$ = new BehaviorSubject<boolean>(false);

    /**
     *  Accumulated docs for the infinite scroll
     */
    accumulatedDocs$ = merge(
        this.savedDocs$.pipe(
            map(loadDocsRes => (state: DocumentInfoResponse[]) => [...state, ...(loadDocsRes?.documents || [])])
        ),
        this.clearAccumulatedDocs$.pipe(map(() => (_state: DocumentInfoResponse[]) => []))
    ).pipe(scan((acc: DocumentInfoResponse[], fn) => fn(acc), []));

    formChangesSubscriptions: Subscription[] = [];
    editorTypeOptions: EditorOptionDefinition[] = EDITOR_OPTION_DEFS; // Definition for the editor type selection filter

    get fromEditorType(): SupportedEditor | '' {
        return this.form?.get('editorType')?.value || '';
    }

    get fromDateRange(): DateRange {
        return (
            this.form?.get('dateRange')?.value || {
                startDate: null,
                endDate: null,
            }
        );
    }

    get fromTextSearch(): string {
        return this.form?.get('textSearch')?.value || '';
    }

    constructor(
        public fb: UntypedFormBuilder,
        private metadataService: DocInProfileMetadataService,
        private notificationService: NotificationService,
        protected dialogRef: MatDialogRef<InsertDocFromLibraryDialogComponent>,
        private readonly coordStateS: CoordStatesService,
        private readonly onlStateS: OnlineStateService,
        private cdRef: ChangeDetectorRef
    ) {
        this.dialogRef.addPanelClass('insert-from-library-popup');
    }

    ngOnInit(): void {
        firstValueFrom(this.metadataService.loadSavedDocs({}))
            .then(res => {
                this.savedDocs$.next(res);
            })
            .catch(e => {
                console.error(e);
                this.notificationService.showNotification({
                    message: 'Tải tài liệu đã tạo thất bại',
                    status: 'error',
                });
            });

        this.detectSmallScreen();
    }

    ngOnDestroy(): void {
        this.formChangesSubscriptions.forEach(sub => sub.unsubscribe());
    }

    /**
     *  Builds the form for filtering documents
     */
    buildForm = (data?: DocumentFilterData): FormBuildingResult => {
        data =
            data ||
            ({
                textSearch: '',
                dateRange: { startDate: undefined, endDate: undefined } as DateRange,
                editorType: '',
            } as DocumentFilterData);

        const result = new FormCreator(this.fb, data).build();

        this.form = result.control as UntypedFormGroup;

        // clear and reload first page of both docs to get the total number of docs
        const getSearchOption = (formData: any) => ({
            textSearch: formData.textSearch,
            page: 1,
            startDate: formData.dateRange.startDate,
            endDate: formData.dateRange.endDate,
            editorType: formData.editorType,
        });
        const docInProfileSub = this.form.valueChanges
            .pipe(
                debounceTime(500),
                switchMap(data =>
                    this.metadataService
                        .loadSavedDocs(getSearchOption(data))
                        // clear before add new docs result
                        .pipe(tap(() => this.clearAccumulatedDocs$.next()))
                )
            )
            .subscribe(res => this.savedDocs$.next(res));
        this.formChangesSubscriptions.push(docInProfileSub);

        return result;
    };

    onDateRangeChange(dateRange: DateRange) {
        this.form.get('dateRange')?.setValue(dateRange);
    }

    onEditorTypeChanges(editorType: EditorOptions) {
        this.form.get('editorType')?.setValue(editorType);
    }

    /**
     *  Handles load more documents when the user scrolls near the end of the page.
     */
    async onScrollNearEnd() {
        const currLoadResult = this.savedDocs$.value;
        if (!currLoadResult?.hasNext || this.loadingMore$.value) return;

        this.loadingMore$.next(true);

        try {
            const res = await firstValueFrom(
                this.metadataService.loadSavedDocs({
                    textSearch: this.fromTextSearch,
                    page: currLoadResult.page + 1,
                    startDate: this.fromDateRange.startDate,
                    endDate: this.fromDateRange.endDate,
                    editorType: this.fromEditorType,
                })
            );
            this.savedDocs$.next(res);
        } catch (e) {
            console.error(e);
            this.notificationService.showNotification({
                message: 'Tải thêm tài liệu thất bại',
                status: 'error',
            });
        } finally {
            this.loadingMore$.next(false);
        }
    }

    async onInsertDocToViewport(docInfo: DocumentInfoResponse) {
        if (this.insertingDoc$.value) return; // Prevent multiple insertions at the same time

        this.insertingDoc$.next(true);
        this.cdRef.markForCheck(); // Ensure the UI updates to show the inserting state

        const startTime = Date.now();
        try {
            const coord = await firstValueFrom(this.onlStateS.coordinator$);
            const selectedViewport = await firstValueFrom(this.coordStateS.selected$);

            const tool = coord.getCommonToolbar(selectedViewport).getTool('copypaste') as CopyPasteTool;
            await tool.insertDocuments(
                [
                    {
                        editorType: docInfo.editorType,
                        docGlobalId: docInfo.docGlobalId,
                        docLocalId: 1, // This is not a doc of this classroom so any local id is fine
                        docName: docInfo.details.docName,
                    },
                ],
                selectedViewport
            );
        } finally {
            const elapsed = Date.now() - startTime;
            if (elapsed < 1000) {
                await new Promise(resolve => setTimeout(resolve, 1000 - elapsed));
            }
            this.insertingDoc$.next(false);
        }
    }

    @HostListener('window:resize', ['$event'])
    onWindowResize() {
        this.detectSmallScreen();
    }

    /**
     *  Small screen detection.
     *  We use a different layout for the filters in small screen
     */
    private detectSmallScreen() {
        this.smallScreen$.next(window.innerWidth < 568);
    }
}
