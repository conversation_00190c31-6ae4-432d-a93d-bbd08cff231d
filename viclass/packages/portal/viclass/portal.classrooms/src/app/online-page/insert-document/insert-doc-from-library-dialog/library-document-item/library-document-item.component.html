<vi-card>
    <vi-card-header>
        <div class="flex sm:flex-row flex-col-reverse items-center">
            <span class="text-SC1 vcon-general" #editorIconEl [ngClass]="editorIcon"></span>
            <div class="ml-auto flex flex-row gap-[10px]">
                <button
                    class="vi-btn vi-btn-focus rounded-full !p-2"
                    (click)="onInsert()"
                    [disabled]="isInserting"
                    #insertBtnEl>
                    <span class="vcon vcon-general vcon_download"></span>
                </button>
            </div>
        </div>
        <lib-tooltip [toolTipFor]="editorIconEl" [tooltipContent]="editorName"></lib-tooltip>
        <lib-tooltip [toolTipFor]="insertBtnEl" [tooltipContent]="'Chèn tài liệu'"></lib-tooltip>
    </vi-card-header>
    <vi-card-body>
        <div class="flex flex-col gap-[10px]">
            <div
                class="w-full h-[150px] bg-contain bg-no-repeat bg-center"
                [style.background-image]="'url(' + doc.details.previewUrl + ')'"></div>
            <div
                class="text-[18px] leading-[27px] font-[600] overflow-hidden text-ellipsis whitespace-nowrap"
                #docNameEl>
                <span>{{ doc.details.docName }}</span>
            </div>
        </div>
        <lib-tooltip [toolTipFor]="docNameEl" [tooltipContent]="doc.details.docName"></lib-tooltip>
    </vi-card-body>
    <vi-card-footer>
        <div class="flex flex-row gap-[5px]">
            <span class="vcon-general vcon_session_time"></span>
            <span>{{ this.doc.details.savedDate | date: 'dd/MM/yyyy HH:mm' }}</span>
        </div>
    </vi-card-footer>
</vi-card>
