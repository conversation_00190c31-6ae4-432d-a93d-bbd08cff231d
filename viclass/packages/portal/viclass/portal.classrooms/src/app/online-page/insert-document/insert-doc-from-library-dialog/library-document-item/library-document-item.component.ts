import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { DocumentInfoResponse, TooltipComponent } from '@viclass/portal.common';
import { EDITOR_ICONS, EDITOR_NAMES } from '../../../../model';
import {
    ViCardBodyComponent,
    ViCardComponent,
    ViCardFooterComponent,
    ViCardHeaderComponent,
} from './vi-card.component';

@Component({
    selector: 'library-document-item',
    standalone: true,
    imports: [
        CommonModule,
        ViCardComponent,
        ViCardHeaderComponent,
        ViCardBodyComponent,
        ViCardFooterComponent,
        TooltipComponent,
    ],
    templateUrl: './library-document-item.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LibraryDocumentItemComponent {
    @Input() doc: DocumentInfoResponse;
    @Input() isInserting = false;

    @Output() insert = new EventEmitter<DocumentInfoResponse>();

    constructor() {}

    get editorIcon(): string {
        return EDITOR_ICONS[this.doc.editorType];
    }

    get editorName(): string {
        return EDITOR_NAMES[this.doc.editorType] || this.doc.editorType;
    }

    openInNewTab() {
        window.open(`/user/doc/${this.doc.editorType}/${this.doc.docGlobalId}`, '_blank');
    }

    onInsert() {
        this.insert.next(this.doc);
    }
}
