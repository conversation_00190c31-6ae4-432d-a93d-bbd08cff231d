import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';

@Component({
    selector: 'vi-card',
    template: ` <div
        class="border rounded-[15px] bg-BW7 text-BW1 shadow-SH1 relative items-center w-auto flex, flex-col">
        <ng-content></ng-content>
    </div>`,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class ViCardComponent {}

@Component({
    selector: 'vi-card-header',
    template: `<div class="p-[10px_10px_0_10px] w-full mb-auto text-[20px] leading-[20px]">
        <ng-content></ng-content>
    </div> `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class ViCardHeaderComponent {}

@Component({
    selector: 'vi-card-body',
    template: `<div class="p-[10px_10px] w-full mt-auto mb-auto">
        <ng-content></ng-content>
    </div> `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class ViCardBodyComponent {}

@Component({
    selector: 'vi-card-footer',
    template: `<div class="w-full mt-auto bg-BW5">
        <div class="p-[10px]">
            <ng-content></ng-content>
        </div>
    </div> `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class ViCardFooterComponent {}
