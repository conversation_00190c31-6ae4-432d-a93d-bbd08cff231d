import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TooltipComponent } from '@viclass/portal.common';
import { BehaviorSubject, Subscription } from 'rxjs';
import { CoordStatesService } from '../coord.state.service';
import { OnlineStateService } from '../online.state.service';
import { InsertDocFromLibraryDialogComponent } from './insert-doc-from-library-dialog/insert-doc-from-library-dialog.component';

@Component({
    selector: 'app-insert-document',
    standalone: true,
    imports: [CommonModule, TooltipComponent, MatDialogModule],
    templateUrl: './insert-document.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InsertDocumentComponent {
    protected isEditMode$ = new BehaviorSubject<boolean>(true);

    private subscriptions: Subscription[] = [];
    private dialogRef: MatDialogRef<InsertDocFromLibraryDialogComponent, any> | undefined;

    constructor(
        private dialog: MatDialog,
        private readonly coordStateS: CoordStatesService,
        private readonly onlStateS: OnlineStateService
    ) {}

    openInsertFromLibrary() {
        if (this.isEditMode$.value) {
            this.closeCurrentDialog();
            this.dialogRef = this.dialog.open(InsertDocFromLibraryDialogComponent, {});
            this.dialogRef.afterClosed().subscribe(() => (this.dialogRef = undefined));
        }
    }

    ngOnInit(): void {
        // subscribe to the selected state to determine if we are in edit mode
        this.subscriptions.push(
            this.coordStateS.selected$.subscribe(async selected => {
                if (selected) {
                    const mode = await this.onlStateS.calculateViewportMode(selected);
                    this.isEditMode$.next(mode === 'EditMode');
                } else {
                    this.isEditMode$.next(false);
                }
            })
        );

        // automatically close the dialog when we no longer in edit mode
        this.subscriptions.push(
            this.isEditMode$.subscribe(isEditMode => {
                if (!isEditMode) this.closeCurrentDialog();
            })
        );
    }

    ngOnDestroy(): void {
        this.subscriptions?.forEach(sub => sub.unsubscribe());
    }

    private closeCurrentDialog() {
        if (this.dialogRef) {
            this.dialogRef.close();
        }
    }
}
