<div class="pen-popup" cdkDrag>
    <div class="drag-handle" cdkDragHandle>
        <span class="vcon vcon-onl vcon_move-vertical"></span>
    </div>
    <div class="pen-settings-border" (mouseenter)="onMouseEnter()" (mouseleave)="onMouseLeave()">
        <div class="pen-settings-background" [ngStyle]="{ 'background-color': setting.color }">
            <div class="pen-settings-size">{{ setting.size }}</div>
        </div>
    </div>
    <div class="eraser-border">
        <button (click)="eraseMyDrawing()" class="eraser">
            <span class="vcon vcon-onl vcon_eraser"></span>
        </button>
    </div>
    <div *ngIf="isCanClearAll" class="eraser-border">
        <button (click)="eraseOthersDrawing()" class="eraser">
            <span class="vcon vcon-onl vcon_eraser-all"></span>
        </button>
    </div>

    <div class="pen-menu" *ngIf="showMenu | async" (mouseenter)="onMouseEnter()" (mouseleave)="onMouseLeave()">
        <div class="color-menu">
            <div
                class="color-background"
                [ngStyle]="{ 'background-color': setting.color !== color ? 'transparent' : '#8DE4FF' }"
                *ngFor="let color of colorList">
                <div class="color" [ngStyle]="{ 'background-color': color }" (click)="selectColor(color)"></div>
            </div>
        </div>
        <div class="relative">
            <div class="size-slider">
                <tb-width-slider
                    [value]="setting.size"
                    [minValue]="2"
                    [maxValue]="50"
                    [vertical]="true"
                    (valueChange)="onFieldChange({ field: 'size', value: $event })">
                </tb-width-slider>
            </div>
        </div>
    </div>
</div>
