.pen-popup {
    gap: 5px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px;
    border: 1px solid #ccc;
    box-shadow: 0px 5px 20px 0px #00424b33;
    position: fixed;
    top: 70px;
    right: 130px;
    z-index: 100;
    border-radius: 15px;
    background-color: #ffffff;
    user-select: none;
    cursor: pointer;

    .pen-menu {
        width: 85px;
        height: 240px;
        background-color: #ffffff;
        border: 1px solid #ccc;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        position: absolute;
        top: 40px;
        left: 30%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: row;
        z-index: 10;
        border-radius: 15px;
        cursor: pointer;

        .color-menu {
            width: 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            gap: 5px;
            padding: 5px;

            .color {
                width: 20px;
                height: 20px;
                border-radius: 50%;
                cursor: pointer;
                border: 1px solid #121414;
            }
        }
        .size-slider {
            width: calc(85px - 40px);
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 5px;
            box-shadow: 0px 5px 20px 0px #00424b33;
            border-radius: 15px;

            .slider-container {
                width: 190px;
                max-width: 300px;
                transform: rotate(270deg);
            }
            label {
                font-size: 14px;
                margin-bottom: 5px;
            }
        }
    }
    &:hover .pen-menu {
        display: flex;
    }
}
.color-background {
    background: transparent;
    width: 30px;
    height: 30px;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.pen-settings-background {
    background: red;
    font-size: 14px;
    width: 40px;
    height: 20px;
    border-radius: 10px;
    border: solid 1px black;
}

.pen-settings-border {
    width: 50px;
    height: 30px;
    border-radius: 15px;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
}
.pen-settings-border:hover {
    background: #8de4ff;
}

.pen-settings-size {
    text-align: center;
}
.eraser-border {
    width: 30px;
    height: 30px;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 15px;
}
.eraser-border:hover {
    background: #8de4ff;
}
.drag-handle {
    cursor: move;
    display: flex;
    justify-content: center;
    align-items: center;
}
.eraser {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}
