import { DragDropModule } from '@angular/cdk/drag-drop';
import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
    NgZone,
    ChangeDetectorRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ClassroomMarkerState } from '@viclass/editor.coordinator/classroom';
import { Subject } from 'rxjs';

import { MarkerFieldChangeEmitterData } from './marker-tool.models';
import { WidthSliderComponent } from './width-slider/width-slider.component';

@Component({
    standalone: true,
    selector: 'marker-tool',
    templateUrl: './marker-tool.component.html',
    styleUrls: ['./marker-tool.component.scss'],
    imports: [CommonModule, FormsModule, DragDropModule, WidthSliderComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MarkerToolComponent {
    @Input() settings: ClassroomMarkerState;
    @Input() isCanClearAll: boolean = true;

    private mouseLeaveTimeoutHandle: number;

    showMenu: Subject<boolean> = new Subject();

    get setting() {
        return this.settings;
    }

    @Output() onClose = new EventEmitter<boolean>();
    @Output() onChange = new EventEmitter<MarkerFieldChangeEmitterData>();
    @Output() onEraseMyDrawing = new EventEmitter<boolean>();
    @Output() onEraseOthersDrawing = new EventEmitter<boolean>();

    colorList: string[] = ['#EBFF00', '#FFB800', '#F772FF', '#A7FF38', '#4DEED1', '#76ADFF'];

    constructor(private cdr: ChangeDetectorRef) {}

    onFieldChange(data: MarkerFieldChangeEmitterData) {
        this.onChange.emit(data);
        this.cdr.markForCheck();
    }

    eraseMyDrawing() {
        this.onEraseMyDrawing.emit(true);
    }

    eraseOthersDrawing() {
        this.onEraseOthersDrawing.emit(true);
    }

    selectColor(color: string) {
        this.onFieldChange({ field: 'color', value: color });
    }

    onMouseEnter() {
        if (this.mouseLeaveTimeoutHandle) {
            window.clearTimeout(this.mouseLeaveTimeoutHandle);
        }
        this.showMenu.next(true);
    }

    onMouseLeave() {
        if (this.mouseLeaveTimeoutHandle) {
            window.clearTimeout(this.mouseLeaveTimeoutHandle);
        }
        this.mouseLeaveTimeoutHandle = window.setTimeout(() => {
            this.showMenu.next(false);
        }, 200);
    }
}
