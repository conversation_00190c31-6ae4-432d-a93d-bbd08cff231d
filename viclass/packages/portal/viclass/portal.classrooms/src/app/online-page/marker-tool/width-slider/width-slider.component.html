<div
    [class]="vertical ? 'slider-container vertical' : 'slider-container horizontal'"
    (pointerleave)="cancelLineWidthAdjust()"
    [style.--slider-height.px]="height"
    [style.--slider-fill.px]="fill">
    <!-- Plus/Minus buttons and slider layout changes based on orientation -->
    <ng-container *ngIf="vertical">
        <button
            class="v-tool-btn"
            [disabled]="value >= maxValue"
            (pointerdown)="onLineWidthAdjust(1)"
            (pointerup)="cancelLineWidthAdjust()"
            (pointerleave)="cancelLineWidthAdjust()">
            <span class="vcon vcon-common vcon_page-bar_ad"></span>
        </button>

        <div class="slider-track-container">
            <div class="slider-track" (pointerdown)="onSliderTrackClick($event)" [class.dragging]="sliderDragging">
                <div class="slider-fill" [style.height.%]="((value - minValue) / (maxValue - minValue)) * 100"></div>
                <div
                    class="slider-knob"
                    [style.bottom.%]="((value - minValue) / (maxValue - minValue)) * 100"
                    [class.dragging]="sliderDragging"
                    (pointerdown)="startSliderDrag($event)">
                    <span class="knob-value">{{ value }}</span>
                </div>
            </div>
        </div>

        <button
            class="v-tool-btn"
            [disabled]="value <= minValue"
            (pointerdown)="onLineWidthAdjust(-1)"
            (pointerup)="cancelLineWidthAdjust()"
            (pointerleave)="cancelLineWidthAdjust()">
            <span class="vcon vcon-common vcon_page-bar_zoom-out"></span>
        </button>
    </ng-container>

    <!-- Horizontal layout -->
    <ng-container *ngIf="!vertical">
        <button
            class="v-tool-btn"
            [disabled]="value <= minValue"
            (pointerdown)="onLineWidthAdjust(-1)"
            (pointerup)="cancelLineWidthAdjust()"
            (pointerleave)="cancelLineWidthAdjust()">
            <span class="vcon vcon-common vcon_page-bar_zoom-out"></span>
        </button>

        <span class="width-value">{{ value }}</span>

        <button
            class="v-tool-btn"
            [disabled]="value >= maxValue"
            (pointerdown)="onLineWidthAdjust(1)"
            (pointerup)="cancelLineWidthAdjust()"
            (pointerleave)="cancelLineWidthAdjust()">
            <span class="vcon vcon-common vcon_page-bar_ad"></span>
        </button>
    </ng-container>
</div>
