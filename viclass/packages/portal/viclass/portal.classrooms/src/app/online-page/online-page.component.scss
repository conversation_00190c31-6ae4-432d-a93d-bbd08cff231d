.pointer-events-none {
    pointer-events: none;
}

.presenting-user-avatar {
    width: 20px;
    height: 20px;
    background-color: green;
    background-size: cover;
    animation: zoom-in-zoom-out 2s ease-out infinite;
}

@keyframes zoom-in-zoom-out {
    0% {
        transform: scale(1, 1);
    }
    50% {
        transform: scale(1.5, 1.5);
    }
    100% {
        transform: scale(1, 1);
    }
}

.classroom-viewport > .viewport:focus-visible {
    outline: none;
}
