import { ChangeDetectionStrategy, Component } from '@angular/core';
import { interval, lastValueFrom, map, take } from 'rxjs';
import { CommonModule, NgOptimizedImage } from '@angular/common';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: 'class-notstart-popup',
    templateUrl: './class.notstart.popup.component.html',
})
export class ClassNotStartPopupComponent {
    protected readonly counter$ = interval(1000).pipe(
        take(61),
        map(s => 60 - s)
    );

    constructor() {
        lastValueFrom(this.counter$).then(_ => this.gotoDetailPage());
    }

    private gotoDetailPage() {
        window.location.href = `/profile/classrooms`;
    }
}
