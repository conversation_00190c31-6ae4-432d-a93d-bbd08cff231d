import { CommonModule, NgOptimizedImage } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, Input, OnDestroy } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { PopupConfirmType } from '../../../model';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: '[confirm-delete-docs-popup]',
    templateUrl: './confirm.delete.docs.popup.component.html',
})
export class ConfirmDeleteDocsPopupComponent implements AfterViewInit, OnDestroy {
    @Input('confirm-delete-docs-popup')
    confirm$: ReplaySubject<PopupConfirmType>;

    private keyboardHandler = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
            this.action('cancel');
            event.stopPropagation();
        } else if (event.key === 'Enter') {
            this.action('yes');
            event.stopPropagation();
        }
    };

    ngAfterViewInit(): void {
        document.body.addEventListener('keydown', this.keyboardHandler, { capture: true });
    }

    ngOnDestroy(): void {
        document.body.removeEventListener('keydown', this.keyboardHandler, { capture: true });
    }

    protected action(type: PopupConfirmType) {
        this.confirm$.next(type);
        this.confirm$.complete();
    }
}
