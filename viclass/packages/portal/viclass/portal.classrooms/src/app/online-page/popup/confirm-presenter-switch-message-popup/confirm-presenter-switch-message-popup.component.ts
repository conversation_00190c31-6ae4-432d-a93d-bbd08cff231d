import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { PopupConfirmType } from '../../../model';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: '[confirm-presenter-switch-message-popup]',
    templateUrl: './confirm-presenter-switch-message-popup.component.html',
})
export class ConfirmPresenterSwitchMessagePopup {
    @Input('confirm-presenter-switch-message-popup')
    confirm$: ReplaySubject<PopupConfirmType>;

    protected action(type: PopupConfirmType) {
        this.confirm$.next(type);
        this.confirm$.complete();
    }
}
