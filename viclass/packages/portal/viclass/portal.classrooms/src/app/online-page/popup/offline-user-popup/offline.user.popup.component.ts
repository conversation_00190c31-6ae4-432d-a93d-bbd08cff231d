import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule, NgOptimizedImage } from '@angular/common';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: 'offline-user-popup',
    templateUrl: './offline.user.popup.component.html',
})
export class OfflineUserPopupComponent {
    protected reload() {
        window.location.reload();
    }
}
