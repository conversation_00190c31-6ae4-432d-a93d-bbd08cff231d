import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    ViewChild,
} from '@angular/core';
import { DragDropModule, Point } from '@angular/cdk/drag-drop';
import { AsyncPipe, NgClass, NgIf } from '@angular/common';

@Component({
    standalone: true,
    selector: '[quick-question-content]',
    templateUrl: 'quick-question-content.component.html',
    imports: [DragDropModule, NgIf, AsyncPipe, NgClass],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuickQuestionContentComponent implements AfterViewInit {
    protected position: Point;
    protected positionMinimize: Point;
    protected minimize: boolean;
    private width: number;

    @Input()
    boundaryClass: string;

    @Input()
    initRightPadding: HTMLElement;

    @Input('quick-question-content')
    content: string;

    @ViewChild('host')
    private readonly host: ElementRef<HTMLElement>;

    constructor(private changeDetection: ChangeDetectorRef) {}

    ngAfterViewInit(): void {
        const boundaryE = document.getElementsByClassName(this.boundaryClass)[0] as HTMLElement;
        const paddingRight = this.initRightPadding?.offsetWidth | 0;
        this.width = this.host.nativeElement.offsetWidth;
        this.position = {
            x: boundaryE.offsetWidth - this.width - paddingRight - 20,
            y: 70,
        };
        this.positionMinimize = {
            x: this.position.x + this.width,
            y: this.position.y,
        };
        this.changeDetection.detectChanges();
    }

    protected dropped(event) {
        this.positionMinimize = {
            x: event.event.clientX,
            y: event.event.clientY,
        };
    }

    protected onMinimize() {
        this.minimize = true;
        // this.positionMinimize.x += this.width
        this.changeDetection.detectChanges();
    }

    protected onShowContent() {
        this.minimize = false;
        // this.position.x -= this.width
        this.changeDetection.detectChanges();
    }
}
