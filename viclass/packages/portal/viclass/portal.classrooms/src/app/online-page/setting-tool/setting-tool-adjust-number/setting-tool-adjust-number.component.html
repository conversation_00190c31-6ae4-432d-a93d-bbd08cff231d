<div class="flex">
    <span *ngIf="label?.length" class="text-BW1">{{ label }}</span>
    <div class="flex-grow"></div>
    <div class="flex gap-3 items-center">
        <button [disabled]="disabled" (click)="changeValue(-1)">
            <span class="vcon vcon-common vcon_page-bar_zoom-out"></span>
        </button>
        <span *ngIf="!_isMixed">{{ _value }}{{ suffix ? suffix : '' }}</span>
        <span *ngIf="_isMixed">?</span>
        <button [disabled]="disabled" (click)="changeValue(1)">
            <span class="vcon vcon-common vcon_page-bar_ad"></span>
        </button>
    </div>
</div>
