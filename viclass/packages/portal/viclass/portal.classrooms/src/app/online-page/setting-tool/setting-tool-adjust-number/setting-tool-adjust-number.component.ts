import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';
import { CommonModule } from '@angular/common';

@Component({
    standalone: true,
    selector: 'lib-setting-tool-adjust-number',
    templateUrl: './setting-tool-adjust-number.component.html',
    styleUrls: ['./setting-tool-adjust-number.component.sass'],
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolAdjustNumberComponent implements OnInit {
    @Input() label?: string;
    @Input() field: string;
    @Input() value: SettingFieldValue;
    @Input() suffix?: string;
    @Input() min?: number;
    @Input() disabled?: boolean;

    @Output() onChange: EventEmitter<SettingFieldChangeEmitterData>;

    _value: number = 0;
    _isMixed: boolean = false;

    constructor() {
        this.onChange = new EventEmitter();
    }

    ngOnInit(): void {
        this._value = this.value?.value ?? this.min ?? 0;
        this._isMixed = this.value?.isMixed ?? false;
    }

    changeValue(increaseValue: number) {
        let newVal = this._value + increaseValue;

        // validate new value
        if (this.min && newVal < this.min) {
            newVal = this.min;
            return;
        }

        this._value = newVal;
        this._isMixed = false;

        this.onChange.emit({ field: this.field, value: newVal });
    }
}
