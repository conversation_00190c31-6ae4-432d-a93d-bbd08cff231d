import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';
import { CommonModule } from '@angular/common';

@Component({
    standalone: true,
    selector: 'lib-setting-tool-colors',
    templateUrl: './setting-tool-colors.component.html',
    styleUrls: ['./setting-tool-colors.component.sass'],
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolColorsComponent {
    @Input() label?: string;
    @Input() field: string;
    @Input() colorList: string[];
    @Input() value: SettingFieldValue;
    @Input() disabled?: boolean;

    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    changeValue(value: string) {
        this.onChange.emit({ field: this.field, value });
    }
}
