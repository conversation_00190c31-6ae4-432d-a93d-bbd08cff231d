import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
    standalone: true,
    selector: 'lib-setting-tool-switch',
    templateUrl: './setting-tool-switch.component.html',
    styleUrls: ['./setting-tool-switch.component.scss'],
    imports: [CommonModule, FormsModule, MatSlideToggleModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolSwitchComponent {
    @Input() label?: string;
    @Input() field: string;
    @Input() value: SettingFieldValue;
    @Input() disabled?: boolean;

    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    get checked(): boolean {
        return !!this.value?.value;
    }

    set checked(value: boolean) {
        this.value.value = value;
        this.onChange.emit({ field: this.field, value: this.value.value });
    }

    get classes() {
        let classes = 'my-slide';
        if (this.value?.isMixed) classes += ' my-slide--mixed';
        return classes;
    }
}
