import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import {
    ClassroomDocSettingState,
    ClassroomPresenterState,
    DefaultSetting,
    FitScreen,
} from '@viclass/editor.coordinator/classroom';
import { TooltipComponent } from '@viclass/portal.common';
import { BehaviorSubject, Subscription } from 'rxjs';
import { MediaDeviceSetupPopupComponent } from '../conference/media-device-setup-popup/media-device-setup-popup.component';
import { CoordStatesService } from '../coord.state.service';
import { OnlineStateService } from '../online.state.service';
import { SettingToolColorsComponent } from './setting-tool-colors/setting-tool-colors.component';
import { SettingToolSwitchComponent } from './setting-tool-switch/setting-tool-switch.component';
import { SettingFieldChangeEmitterData } from './setting-tool.models';

export type ClassroomDocSetting = DefaultSetting;

@Component({
    standalone: true,
    selector: 'setting-tool',
    templateUrl: './setting-tool.component.html',
    styleUrls: ['./setting-tool.component.sass'],
    imports: [CommonModule, FormsModule, SettingToolColorsComponent, SettingToolSwitchComponent, TooltipComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolComponent implements OnInit, OnDestroy {
    @Input() settings: ClassroomDocSettingState;
    @Input() presenter: ClassroomPresenterState;
    isEditable$ = new BehaviorSubject<boolean>(true);

    get defaultSetting(): ClassroomDocSetting {
        return this.settings?.defaultSetting;
    }

    get hasFocusedDoc() {
        return this.settings?.focusedDocSettings?.length > 0;
    }

    get setting() {
        if (this.hasFocusedDoc) {
            const settingObj = { ...this.defaultSetting };

            const allSettings = this.settings.focusedDocSettings;
            const background = new Set(allSettings.map(s => s.background));
            const backgroundColor = new Set(allSettings.map(s => s.backgroundColor));
            const border = new Set(allSettings.map(s => s.border));
            const borderType = new Set(allSettings.map(s => s.borderType));
            const borderColor = new Set(allSettings.map(s => s.borderColor));
            const shadow = new Set(allSettings.map(s => s.shadow));
            const shadowType = new Set(allSettings.map(s => s.shadowType));

            if (background.size === 1) settingObj.background = background.values().next().value;
            if (backgroundColor.size === 1) settingObj.backgroundColor = backgroundColor.values().next().value;
            if (border.size === 1) settingObj.border = border.values().next().value;
            if (borderType.size === 1) settingObj.borderType = borderType.values().next().value;
            if (borderColor.size === 1) settingObj.borderColor = borderColor.values().next().value;
            if (shadow.size === 1) settingObj.shadow = shadow.values().next().value;
            if (shadowType.size === 1) settingObj.shadowType = shadowType.values().next().value;

            return settingObj;
        } else {
            return this.defaultSetting;
        }
    }

    @Output() onClose = new EventEmitter<boolean>();
    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    selectedTab: 'settings' | 'presenter';
    colorList: string[] = ['#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    bgColorList: string[] = [
        '#FFFFFF',
        '#F6F9FE',
        '#F0F6F8',
        '#FFFAE0',
        '#F7FCFD',
        'linear-gradient(180deg, rgba(247, 252, 253, 0.80) 0%, #D8F8FF 100%)',
    ];

    shadowList: string[] = ['sd1', 'sd2', 'sd3'];

    fitScreens: any[] = [
        { name: 'Toàn bộ chiều ngang', value: 'widthFit' },
        { name: 'Toàn bộ chiều dọc', value: 'heightFit' },
        { name: 'Tự động ngang/dọc', value: 'autoFit' },
        { name: 'Giữ nguyên, theo trung tâm', value: 'noneFit' },
    ];

    borderList: string[] = ['bd1', 'bd2', 'bd3'];

    private subscription?: Subscription;

    constructor(
        private dialog: MatDialog,
        private readonly coordStateS: CoordStatesService,
        private readonly onlStateS: OnlineStateService
    ) {
        this.selectedTab = 'settings';
    }

    ngOnInit(): void {
        this.subscription = this.coordStateS.selected$.subscribe(async selected => {
            if (selected) {
                const mode = await this.onlStateS.calculateViewportMode(selected);
                this.isEditable$.next(mode === 'EditMode');
            } else {
                this.isEditable$.next(false);
            }
        });
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    onFieldChange(data: SettingFieldChangeEmitterData) {
        if (!this.isEditable$) return;
        this.onChange.emit(data);
    }

    onInputChange(e: Event, field: string) {
        const target = e.target as HTMLInputElement;
        this.onFieldChange({ field, value: target.value });
    }

    changeShadowType(type: string) {
        this.setting.shadowType = type;
        this.onShadowChange(type);
    }

    changeBorderType(type: string) {
        this.setting.borderType = type;
        this.onBorderChange(type);
    }

    onPresenterChange(data: SettingFieldChangeEmitterData) {
        this.presenter.presenterSetting.syncPresenterState = data.value;
        this.onChange.emit(data);
    }

    onFitScreenTypeChange(type: FitScreen) {
        if (type == this.presenter.presenterSetting.fitScreen) {
            this.onChange.emit({ field: 'fitScreen', value: undefined });
            this.presenter.presenterSetting.fitScreen = undefined;
        } else {
            this.onChange.emit({ field: 'fitScreen', value: type });
            this.presenter.presenterSetting.fitScreen = type;
        }
    }

    onShadowChange(type: string) {
        this.onFieldChange({ field: 'shadowType', value: type });
    }

    onBorderChange(type: string) {
        this.onFieldChange({ field: 'borderType', value: type });
    }

    openCameraSetting() {
        this.dialog.open(MediaDeviceSetupPopupComponent, {
            width: '900px',
            height: '500px',
            disableClose: true,
        });
    }
}
