import {
    AbstractCommand,
    CmdProcessor,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    ViewportManager,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.core';

import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { ShareScreenEditor } from '../sharescreen.editor';
export type ActionType = 'save' | 'create' | 'update';

export class ShareScreenCmdProcessor extends CmdProcessor {
    constructor(private editor: ShareScreenEditor) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<CmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<CmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                return this.processInsertDocCmd(cmd as FCInsertDocCmd);
            }
            case FCCmdTypeProto.PREVIEW_BOUNDARY: {
                this.processNewDocBoundaryCmd(cmd as FCPreviewBoundaryCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                await this.processRemoveDocCmd(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            case FCCmdTypeProto.UPDATE_BOUNDARY: {
                return this.processUpdateBoundaryCmd(cmd as FCUpdateDocCmd);
            }

            default:
                break;
        }

        return cmd;
    }

    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);
        return cmd;
    }

    private processNewDocBoundaryCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        return this.editor.crdFeature.processDocCreationPreviewCmd(cmd, this.editor);
    }

    private async processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const boundary = fcConvertProtoToBoundary(cmd.state.getBoundary());
        await this.editor.insertLayer(vm, docId, layerId, boundary);
    }

    private async processUpdateBoundaryCmd(cmd: FCUpdateDocCmd) {
        const isResetRatio = cmd.state.getIsresetratio();
        const l = cmd.state.getDocsList();
        const bs = cmd.state.getNewboundariesList();
        for (const idx in l) {
            const doc = l[idx];
            const b = fcConvertProtoToBoundary(bs[idx]);
            const vm: ViewportManager = cmd.meta.viewport;
            if (isResetRatio) this.editor.resetBoundary(vm, doc.getLocalId(), b);
            else {
                const selectFeature = await this.editor.updateBoundary(vm, doc.getLocalId(), b);
                selectFeature?.synchronizeBoundaryCmd(cmd);
            }
        }

        return cmd;
    }

    private async processInsertDocCmd(cmd: FCInsertDocCmd) {
        const vm: ViewportManager = cmd.meta.viewport;
        await this.editor.createDoc(vm, cmd.meta.targetId, cmd.state.getGlobalId());
        return cmd;
    }

    private async processRemoveDocCmd(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }
        return cmd;
    }
}
