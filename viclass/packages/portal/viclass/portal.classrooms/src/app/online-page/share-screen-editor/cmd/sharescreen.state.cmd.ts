import {
    Cmd,
    CmdMeta,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';

export function shareScreenCmdDeserializer(meta: CmdMeta, stateData: Uint8Array): Cmd<CmdTypeProto | FCCmdTypeProto> {
    let cmd: Cmd<CmdTypeProto | FCCmdTypeProto>;
    const cmdType = meta.cmdType as CmdTypeProto | FCCmdTypeProto;

    switch (cmdType) {
        case FCCmdTypeProto.INSERT_DOC: {
            cmd = new FCInsertDocCmd(meta);
            break;
        }
        case FCCmdTypeProto.REMOVE_DOC: {
            cmd = new FCRemoveDocCmd(meta);
            break;
        }
        case FCCmdTypeProto.INSERT_LAYER: {
            cmd = new FCInsertLayerCmd(meta);
            break;
        }
        case FCCmdTypeProto.PREVIEW_BOUNDARY: {
            cmd = new FCPreviewBoundaryCmd(meta);
            break;
        }

        case FCCmdTypeProto.RELOAD_DOC: {
            cmd = new FCReloadDocCmd(meta);
            break;
        }

        case FCCmdTypeProto.UPDATE_BOUNDARY: {
            cmd = new FCUpdateDocCmd(meta);
            break;
        }

        default:
            throw new Error(`invalid cmd type: ${cmdType}`);
    }

    cmd.state = cmd.deserialize(stateData);

    return cmd;
}
