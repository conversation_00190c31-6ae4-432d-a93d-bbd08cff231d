.fullscreen-container {
    height: 100vh;
    width: 100vw;
    backdrop-filter: blur(5px);
    background-color: rgba(0, 0, 0, 0.2);
}

.camera-area {
    position: relative;
    width: 100%;
    transition: all 0.3s ease-in-out;
    background: transparent;
    transform: translateY(0);
    opacity: 1;
}

.camera-area.hidden {
    transform: translateY(100%);
    opacity: 0;
    pointer-events: none;
}

.hide-button {
    position: absolute;
    top: 10px;
    left: -20px;
    z-index: 10;
    opacity: 0;
    transition: all 0.2s ease-in-out;
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.camera-area:hover .hide-button {
    opacity: 1;
}

.hide-button:hover {
    background: #ffffff;
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.hide-button i {
    color: #444;
    font-size: 18px;
    transition: color 0.2s ease-in-out;
}

.hide-button:hover i {
    color: #000;
}

.collapsed-toolbar {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 6px 12px;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease-in-out;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.collapsed-toolbar:hover {
    background: #ffffff;
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 -6px 20px rgba(0, 0, 0, 0.25);
}

.collapsed-toolbar i {
    font-size: 18px;
    color: #000;
    font-weight: bold;
}
