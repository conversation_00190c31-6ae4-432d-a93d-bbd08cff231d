<div
    [ngClass]="{ 'fullscreen-container': !!(isFullScreen$ | async) }"
    class="absolute bottom-0 left-1/2 transform -translate-x-1/2 z-[103] flex flex-col w-auto">
    <div *ngIf="isFullScreen$ | async" class="flex-grow overflow-auto">
        <view-full-screen class="h-full"></view-full-screen>
    </div>
    <div [ngClass]="{ hidden: !isCameraAreaVisible }" class="camera-area relative">
        <button class="hide-button" (click)="hideCameraArea()" [title]="'Ẩn bảng điều khiển camera & chia sẻ màn hình'">
            <i class="vcon-general vcon_delete"></i>
        </button>
        <div
            video-area
            class="flex-shrink-0 overflow-hidden flex items-center justify-center pointer-events-none"
            style="min-height: 70px; max-height: 170px"></div>
    </div>

    <div
        *ngIf="!isCameraAreaVisible"
        class="collapsed-toolbar"
        (click)="showCameraArea()"
        [title]="'Mở bảng điều khiển camera & chia sẻ màn hình'">
        <i
            class="vcon vcon-general vcon_arrow_next transition-all duration-300 ease-in-out"
            style="transform: rotate(-90deg)"></i>
    </div>
</div>

<div audio-area></div>

<div
    *ngIf="confirmTurnOffShareScreenPopup$ | async as d"
    class="w-screen h-screen z-[1000] bg-transparent fixed top-0 left-0">
    <div [confirm-turn-off-share-screen-popup]="d.obs"></div>
</div>
