import { CommonModule } from '@angular/common';
import { Component, ElementRef, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { BehaviorSubject, firstValueFrom, Observable, ReplaySubject, Subscription } from 'rxjs';
import { PopupConfirmType } from '../../../model';
import { AudioAreaComponent } from '../../conference/audio-area/audio-area.component';
import { VideoAreaComponent } from '../../conference/video-area/video-area.component';
import { ConfirmTurnOffShareScreenPopupComponent } from '../../popup/confirm-turn-off-share-screen-popup/confirm.turn.off.share.screen.popup.component';
import { ShareScreenService } from '../share-screen.service';
import { ShareScreenComponent } from '../share-screen/share-screen.component';
import { ViewFullScreenComponent } from '../view-full-screen/view-full-screen.component';
@Component({
    selector: 'conference',
    templateUrl: './conference.component.html',
    standalone: true,
    imports: [
        CommonModule,
        ViewFullScreenComponent,
        VideoAreaComponent,
        AudioAreaComponent,
        ConfirmTurnOffShareScreenPopupComponent,
    ],
    styleUrls: ['./conference.component.css'],
})
export class ConferenceComponent implements OnDestroy {
    private shareScreenEditorSub?: Subscription;
    isCameraAreaVisible = true;

    constructor(public shareScreenService: ShareScreenService) {
        this.shareScreenEditorSub = this.shareScreenService.shareScreenEditor$.subscribe(
            shareScreenEditor =>
                shareScreenEditor &&
                shareScreenEditor.registerTurnOffShareScreenConfirmationApprovals(this.confirmturnOffShareDocs)
        );
    }

    readonly confirmTurnOffShareScreenPopup$: BehaviorSubject<{
        obs: ReplaySubject<PopupConfirmType>;
    }> = new BehaviorSubject(undefined);
    private readonly confirmturnOffShareDocs = async () => {
        const obs = new ReplaySubject<PopupConfirmType>();
        this.confirmTurnOffShareScreenPopup$.next({ obs });
        const res = await firstValueFrom(obs);
        this.confirmTurnOffShareScreenPopup$.next(undefined);
        return res === 'yes';
    };

    @ViewChild('videoContainerElement', { static: true }) videoContainerElement!: ElementRef<HTMLElement>;

    get isFullScreen$(): Observable<ShareScreenComponent> {
        return this.shareScreenService.curFullScreen$;
    }

    hideCameraArea() {
        this.isCameraAreaVisible = false;
    }

    showCameraArea() {
        this.isCameraAreaVisible = true;
    }

    ngOnDestroy(): void {
        this.shareScreenEditorSub?.unsubscribe();
    }
}
