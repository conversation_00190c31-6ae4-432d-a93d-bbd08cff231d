import {
    BoardViewportManager,
    BoundaryRectangle,
    DefaultVDocCtrl,
    DocumentViewMode,
    DOMElementLayerCtrl,
    MouseEventData,
    mouseLocation,
    Position,
    SelectHitContext,
    VDocLayerCtrl,
    ViewportManager,
} from '@viclass/editor.core';

import { ShareScreenDoc, ShareScreenLayer } from '../model';
import { ShareScreenComponent } from '../share-screen/share-screen.component';
import { ShareScreenEditor } from '../sharescreen.editor';
import { validatePointerPos } from '../tools';

export class ShareScreenDocCtrl extends DefaultVDocCtrl {
    private docViewMode: DocumentViewMode;
    private _shareScreenComp: ShareScreenComponent;
    private _transformOrigin: number[];

    layer: DOMElementLayerCtrl;

    translation: number[];
    zoomLevel: number;

    constructor(
        override editor: ShareScreenEditor,
        public override state: ShareScreenDoc,
        viewport: ViewportManager
    ) {
        super(state, editor, viewport);
        this.docViewMode = editor.conf.docViewMode;
    }

    /**
     * Sets the reference to the ShareScreenComponent instance.
     * This allows the controller to interact with the component after it's been created dynamically.
     *
     * @param component - The ShareScreenComponent instance to associate with this controller.
     */
    setComponent(component: ShareScreenComponent) {
        this._shareScreenComp = component;
    }

    getComponent(): ShareScreenComponent {
        return this._shareScreenComp;
    }

    override addLayer(layer: VDocLayerCtrl): void {
        if (!(layer instanceof DOMElementLayerCtrl)) return;

        super.addLayer(layer);

        this.layer = layer;
        this.state.addLayer(layer.state as ShareScreenLayer);
    }

    onRemove() {
        this.state.layer = undefined;
        this.removeLayer(this.layer);
        this.viewport.removeLayer(this.layer);
    }

    checkHit(event: MouseEventData<any>, l: DOMElementLayerCtrl): SelectHitContext {
        if (this.layer === l) {
            const mousePos = mouseLocation(event);
            if (!validatePointerPos(mousePos, this)) {
                return undefined;
            }

            return {
                doc: this,
                hitDetails: undefined,
            };
        }

        return undefined;
    }
    /**
     * Zooms the video container based on the specified zoom level and optional root position.
     * Adjusts the internal translation and computes the visual offset (cssXY) for proper rendering.
     *
     * @param level - The new zoom level to apply.
     * @param zoomRoot - Optional zoom root position in SR Layer coordinates.
     */
    zoom(level: number, zoomRoot?: Position): void {
        this.zoomLevel = level;
        this.translation = zoomRoot ? [zoomRoot.x, zoomRoot.y] : [0, 0];

        const videoWrapper = this._shareScreenComp.videoWrapperRef.nativeElement;
        const videoEl = this._shareScreenComp.videoContainerElementRef.nativeElement;
        if (!videoEl || !videoWrapper) return;

        const viewportZoom = (this.viewport as BoardViewportManager).zoomLevel;
        const wrapperRect = videoWrapper.getBoundingClientRect();
        const videoRect = videoEl.getBoundingClientRect();

        const scaledWidth = wrapperRect.width * viewportZoom;
        const scaledHeight = wrapperRect.height * viewportZoom;
        const offsetX = videoRect.left - wrapperRect.left;
        const offsetY = videoRect.top - wrapperRect.top;

        const videoSpaceOrigin = {
            x: -offsetX / (this.zoomLevel * viewportZoom * 2),
            y: -offsetY / (this.zoomLevel * viewportZoom * 2),
        };

        const centerOffset = {
            x: (scaledWidth / 2 + this.translation[0]) / this.zoomLevel,
            y: (scaledHeight / 2 - this.translation[1]) / this.zoomLevel,
        };

        this._transformOrigin = [
            Math.min(Math.max(videoSpaceOrigin.x + centerOffset.x, 0), scaledWidth / 2),
            Math.min(Math.max(videoSpaceOrigin.y + centerOffset.y, 0), scaledHeight / 2),
        ];

        this.render();
    }

    /**
     * Pans the video container by updating its translation values in the SR Layer.
     * Also recalculates the visual offset (cssXY) based on viewport zoom and bounds.
     *
     * @param xInSRLayer - X coordinate to translate in SR Layer.
     * @param yInSRLayer - Y coordinate to translate in SR Layer.
     */
    pan(xInSRLayer: number, yInSRLayer: number): void {
        this.translation = [xInSRLayer, yInSRLayer];

        const videoWrapper = this._shareScreenComp.videoWrapperRef.nativeElement;
        if (!videoWrapper) return;

        // Get scaled dimensions
        const viewportZoom = (this.viewport as BoardViewportManager).zoomLevel;
        const { width, height } = videoWrapper.getBoundingClientRect();
        const scaledWidth = width * viewportZoom;
        const scaledHeight = height * viewportZoom;

        // Clamp translation within bounds
        const bounds = {
            x: scaledWidth / 2,
            y: scaledHeight / 2,
        };

        this.translation = [
            Math.max(-bounds.x, Math.min(this.translation[0], bounds.x)),
            Math.max(-bounds.y, Math.min(this.translation[1], bounds.y)),
        ];

        // Calculate transform origin
        this._transformOrigin = [
            (scaledWidth / 2 + this.translation[0]) / 2,
            (scaledHeight / 2 - this.translation[1]) / 2,
        ];

        this.render();
    }

    render() {
        const offsetX = (1 - this.zoomLevel) * this._transformOrigin[0];
        const offsetY = (1 - this.zoomLevel) * this._transformOrigin[1];

        const videoContainer = this._shareScreenComp.videoContainerElementRef.nativeElement;
        videoContainer.style.transformOrigin = `${this._transformOrigin[0]}px ${this._transformOrigin[1]}px`;
        videoContainer.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${this.zoomLevel})`;
    }

    unselect() {
        if (this.layer) this.viewport.sink(this.layer);
    }

    select() {
        if (this.layer) this.viewport.float(this.layer);
    }

    updateBoundary(boundary: BoundaryRectangle) {
        this.state.layer.boundary = { ...this.state.layer.boundary, ...boundary };
        (this.layer as DOMElementLayerCtrl).updateBoundary(boundary);
    }
}
