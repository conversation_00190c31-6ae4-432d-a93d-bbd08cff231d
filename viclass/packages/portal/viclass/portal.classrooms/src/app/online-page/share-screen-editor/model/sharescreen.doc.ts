import {
    BoundaryRectangle,
    DocLocalId,
    DocumentId,
    KeyboardEventData,
    LayerId,
    MouseEventData,
    NativeEventTarget,
    VDoc,
    VDocLayer,
} from '@viclass/editor.core';

export type ShareScreenMouseEvent = MouseEventData<NativeEventTarget<any>>;
export type ShareScreenKeyboardEvent = KeyboardEventData<NativeEventTarget<any>>;

export class ShareScreenDoc implements VDoc {
    layer: ShareScreenLayer;
    version = 0;

    constructor(
        public id: DocLocalId,
        public globalId: DocumentId,
        version?: number
    ) {
        if (version) this.version = version;
    }

    getLayers(): VDocLayer[] {
        return [this.layer as VDocLayer];
    }

    setLayers(layers: VDocLayer[]) {
        this.layer = layers[0] as ShareScreenLayer;
    }

    addLayer(layer: VDocLayer) {
        this.layer = layer as ShareScreenLayer;
    }
}

export class ShareScreenLayer implements VDocLayer {
    zindex: number;
    boundary: BoundaryRectangle;
    constructor(
        public id: LayerId,
        boundary: BoundaryRectangle
    ) {
        this.boundary = boundary;
    }
    getBoundary(): BoundaryRectangle {
        return this.boundary;
    }
    getZindex() {
        return this.zindex;
    }
}
