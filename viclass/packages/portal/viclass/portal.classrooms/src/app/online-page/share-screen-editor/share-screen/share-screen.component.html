<div class="relative inline-block w-full h-full group bg-red">
    <!-- Cover Div -->
    <div class="absolute inset-0 bg-black z-0 pointer-events-none flex flex-col items-center justify-center">
        <span class="text-white mb-2">{{ (avatarModel$ | async).username }}</span>
        <img class="w-[60px] h-[40px]" src="assets/img/screen.svg" alt="Screen Image" />
    </div>

    <!-- Video Container -->
    <div #videoWrapper class="relative w-full h-auto z-10 overflow-hidden">
        <div #videoContainerElement class="transform transition-transform origin-center scale-[var(--video-scale,1)]">
            <video #videoElement autoplay class="w-full h-auto !max-h-full"></video>
        </div>
    </div>

    <!-- Floating Controls -->

    <div
        style="box-shadow: 0px 5px 20px 0px #00424b33"
        class="hide-on-full-screen absolute top-2 left-1/2 transform -translate-x-1/2 w-[80px] h-[40px] bg-BW7 bg-opacity-60 rounded-[15px] flex items-center justify-between p-1 z-20 opacity-0 group-hover:opacity-100">
        <div class="w-[40px] h-[40px] rounded-full bg-cover bg-center p-[7px]">
            <div
                #avatar
                class="d-flex justify-content-end profile-avatar h-full w-full rounded-full"
                [ngStyle]="
                    (avatarModel$ | async).avatarUrl && {
                        'background-image': 'url(' + (avatarModel$ | async).avatarUrl + ')',
                    }
                "></div>
        </div>
        <button
            class="w-[35px] h-[35px] flex items-center justify-center bg-P3 bg-opacity-0 text-black rounded-full transition-opacity duration-300 hover:bg-opacity-100 focus:outline-none"
            (click)="viewFullScreen()">
            <i class="text-sm vcon vcon-onl vcon_view_full-screen"></i>
        </button>
    </div>

    <!-- Tooltip -->
    <lib-tooltip
        class="hide-on-full-screen z-30"
        [toolTipFor]="avatar"
        [tooltipContent]="(avatarModel$ | async).username"
        [placement]="'bottom'"></lib-tooltip>
</div>
