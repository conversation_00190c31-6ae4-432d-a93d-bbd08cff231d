/// <reference types="@viclass/ww/typings"/>
import { ClassroomCoordinator } from '@viclass/editor.coordinator/classroom';
import { BaseCoordinator } from '@viclass/editor.coordinator/common';
import {
    AwarenessFeature,
    BoundaryRectangle,
    buildLoadingAwarenessCmdOption,
    CmdChannel,
    ContentVisibilityCheckFeature,
    ContextMenuEvent,
    ContextMenuFeature,
    ContextMenuListenerFilter,
    CoordinatorEvent,
    CRDHistoryItem,
    CreateDocumentResult,
    CreatingContext,
    CRUDChangeResult,
    CRUDDelegator,
    DefaultEventEmitter,
    DefaultRegistryManager,
    DefaultVDocCtrl,
    DocBoundaryInfo,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    DOMElementLayerCtrl,
    EditorBase,
    EditorConfig,
    EditorCoordinator,
    EditorId,
    EditorType,
    fcBuildBoundaryProto,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCUpdateDocCmd,
    FEATURE_AWARENESS,
    FEATURE_CONTEXTMENU,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_PAN,
    FEATURE_ROB,
    FEATURE_SELECTION,
    FEATURE_ZOOM,
    HasSelectionFeature,
    HistoryFeature,
    HistoryTool,
    InsertDocCtrlDelegator,
    LayerOptions,
    ListMenu,
    LoadingContext,
    OperationMode,
    PanFeature,
    reliableCmdMeta,
    RemoveDocInfo,
    ROBFeature,
    SelectContext,
    SelectDelegator,
    SelectionFeature,
    SupportContentVisibilityCheckFeature,
    SupportContextMenuFeature,
    SupportSelectFeature,
    ToolBar,
    VDocCtrl,
    VEventData,
    VEventListener,
    ViewportAddedCES,
    ViewportId,
    ViewportManager,
    ZoomFeature,
} from '@viclass/editor.core';
import { DocInfo, FCCmdTypeProto } from '@viclass/proto/feature.common';
import { ShareScreenCmdProcessor } from './cmd/sharescreen.cmd.processor';
import { shareScreenCmdDeserializer } from './cmd/sharescreen.state.cmd';
import { ShareScreenDocCtrl } from './docs/sharescreen.doc.ctrl';
import { ShareScreenDoc, ShareScreenLayer } from './model';
import { shareScreenDocReg, shareScreenLayerReg } from './sharescreen.utils';
import { ShareScreenPanTool } from './tools/share-screen.pan.tool';
import { ShareScreenToolBar } from './tools/share-screen.toolbar';
import { ShareScreenZoomTool } from './tools/share-screen.zoom.tool';

/**
 * Represents a user identifier in the system.
 */
export type UserId = string;

/**
 * Event type for shared screen document creation and removal events.
 * Contains information about the event source, type, and associated document controller.
 */
export type ShareScreenCreateRemoveDocEvent = VEventData<
    'doc-created' | 'doc-removed',
    ShareScreenEditor,
    ShareScreenDocCtrl
>;

/**
 * Creates a unique local document identifier for a shared screen.
 *
 * @param vpId - The viewport identifier where the document will be displayed
 * @param channelCode - The channel code for communication
 * @param userId - The identifier of the user who is sharing their screen
 * @returns A formatted document identifier string
 */
function createDummyLocalDocId(vpId: ViewportId, channelCode: number, userId: UserId): DocumentId {
    return `LOCALDOC_${vpId}_${channelCode}_${userId}`;
}

/**
 * ShareScreenEditor - Manages screen sharing functionality in a virtual classroom environment.
 *
 * This editor is responsible for creating, managing, and removing shared screen documents
 * within viewports. It handles the lifecycle of screen sharing sessions, including:
 * - Creating new screen sharing documents
 * - Positioning and resizing shared screens
 * - Managing selection and context menu interactions
 * - Synchronizing screen sharing state across participants
 * - Handling removal of shared screens
 *
 * The editor integrates with various features like selection, history, awareness,
 * and context menus to provide a complete screen sharing experience.
 */
export class ShareScreenEditor
    extends EditorBase<ShareScreenDocCtrl>
    implements DocumentEditor, SupportContentVisibilityCheckFeature, HasSelectionFeature, SupportContextMenuFeature
{
    /**
     * Event emitter for document-related events (creation and removal).
     * Used to notify listeners about changes to shared screen documents.
     */
    protected readonly _docEventEmitter: DefaultEventEmitter<ShareScreenCreateRemoveDocEvent> =
        new DefaultEventEmitter();

    /**
     * Unique identifier for this editor instance.
     */
    readonly id: EditorId = 6;

    /**
     * Type identifier for this editor, used for type checking and registration.
     */
    readonly editorType: EditorType = 'ShareScreenEditor';

    /**
     * Command channel for sending and receiving commands related to screen sharing.
     */
    private _cmdChannel: CmdChannel;

    /**
     * Command processor that handles incoming commands for screen sharing operations.
     */
    private readonly _cmdProcessor: ShareScreenCmdProcessor;

    /**
     * Reference to the editor coordinator that manages this editor and others.
     */
    private _coordinator: EditorCoordinator;

    /**
     * Temporary storage for global document ID during document creation.
     */
    private _globalIdTemp: DocumentId;

    /**
     * Function that handles confirmation for turning off screen sharing.
     * Default implementation always returns false (no approval needed).
     */
    private _turnShareScreenConfirmationApprovals: (...args: any[]) => Promise<Boolean> = () => Promise.resolve(false);

    /**
     * Feature for handling context menu interactions with shared screen documents.
     */
    contextMenuFeature: ContextMenuFeature;

    /**
     * Feature for displaying awareness notifications during screen sharing operations.
     * Note: There's a typo in the property name (should be "awarenessFeature").
     */
    awarenessFeatrure: AwarenessFeature;

    /**
     * Feature for handling selection of shared screen documents.
     */
    selectionFeature: SelectionFeature;

    /**
     * Feature for managing operation history for undo/redo functionality.
     */
    historyFeature: HistoryFeature;

    /**
     * Feature for handling remote operation broadcasting.
     */
    robFeature: ROBFeature;

    /**
     * Feature for handling document creation, reading, and deletion operations.
     */
    crdFeature: DocCRDFeature;

    /**
     * Feature for managing visibility of shared screen content.
     */
    contentVisibilityFeature: ContentVisibilityCheckFeature;

    /**
     * Feature for handling panning operations within the viewport.
     */
    panFeature: PanFeature;

    /**
     * Feature for handling zoom operations within the viewport.
     */
    zoomFeature: ZoomFeature;

    /**
     * Delegator for Create, Read, Update, Delete operations on shared screen documents.
     */
    crudDelegator: CRUDDelegator;

    /**
     * Configuration settings for this editor instance.
     */
    public conf: EditorConfig;

    /**
     * Delegator for handling selection operations on shared screen documents.
     * Manages the selection state and triggers appropriate callbacks when documents are selected.
     */
    readonly selectDelegator = new SelectDelegator<ShareScreenDocCtrl>(this, {
        onSelect: this.onSelectDocCtrl.bind(this),
    });

    toolbars: Map<ViewportId, ShareScreenToolBar> = new Map();

    /**
     * Initializes a new instance of the ShareScreenEditor.
     *
     * Sets up the editor configuration, command channel, command processor,
     * and CRUD delegator. Also registers event listeners for coordinator events.
     *
     * @param coordinator - The editor coordinator that will manage this editor
     */
    constructor(coordinator: EditorCoordinator) {
        // Create a registry manager for document and layer registries
        const regMan = new DefaultRegistryManager();

        // Configure the editor with necessary settings
        const conf: EditorConfig = {
            id: 6,
            editorType: 'ShareScreenEditor',
            channelCode: 6,
            apiUri: null,
            registryManager: regMan,
            commandGateway: (coordinator as BaseCoordinator).cmdGateway,
            docViewMode: 'bounded',
        };
        super(conf);
        this.conf = conf;
        this._coordinator = coordinator;

        // Set up command processing
        this._cmdProcessor = new ShareScreenCmdProcessor(this);
        this._cmdChannel = this._coordinator.registerCmdChannel(this.id);
        this._cmdChannel.registerCmdListener(this._cmdProcessor);
        this._cmdChannel.registerDeserializer(shareScreenCmdDeserializer);
        this._operationMode = OperationMode.LOCAL;

        // Initialize CRUD delegator for document operations
        this.crudDelegator = new CRUDDelegator(
            this,
            this.cmdChannel,
            this.regMan,
            { doc: shareScreenDocReg, layer: shareScreenLayerReg },
            this.generateInitDocData.bind(this)
        );

        // Register event listener for coordinator events
        this._coordinator.registerCoordEventListener(new this._COORD_EVENT_HANDLER(this));

        return this;
    }

    /**
     * Handles context menu event for shared screen documents.
     *
     * When a user right-clicks (context menu) on a shared screen document, this function checks:
     * - If the selected documents match the expected editor type.
     * - If the user is either the presenter or the owner of all selected documents.
     *
     * If so, it adds a menu option to stop screen sharing for those documents.
     *
     * @param eventData - The context menu event containing information about selected documents and viewport.
     * @returns A list of context menu items based on user permissions and document ownership.
     */
    contextMenuFeatureListener = async (eventData: ContextMenuEvent): Promise<ListMenu[]> => {
        switch (eventData.eventType) {
            case 'contextmenu': {
                const coordinator = this._coordinator as ClassroomCoordinator;
                const viewportManager = coordinator.getViewportManager(eventData.viewportId);
                const result: ListMenu[] = [];

                const shareShareDocs = eventData.source.filter(item => item.doc.editor.editorType === this.editorType);

                if (shareShareDocs.length > 0) {
                    const sharedByMeDocs = shareShareDocs
                        .filter(item => item.doc.state?.globalId?.split('_')[3] === coordinator.userId)
                        .map(item => item.doc);

                    const isOnlyMySharedDocs = sharedByMeDocs.length === shareShareDocs.length;
                    const isPresenter = viewportManager?.mode === 'EditMode';

                    result.push({
                        name: 'Tắt chia sẻ màn hình',
                        data: undefined,
                        onclick: (): Promise<any> => this.turnOffMultilDocs(eventData.viewportId),
                        disabled: !(isPresenter || isOnlyMySharedDocs),
                    });
                }

                return result;
            }
            default:
                break;
        }
        return [];
    };

    createToolbar(): ToolBar<any, any> {
        const tb = new ShareScreenToolBar(this);
        const maghZoomTool = new ShareScreenZoomTool(this, this.zoomFeature);
        const shareScreenPanTool = new ShareScreenPanTool(this, this.panFeature);

        tb.addTool('ShareScreenZoomTool', maghZoomTool);
        tb.addTool('ShareScreenPanTool', shareScreenPanTool);

        return tb;
    }

    /**
     * Removes shared screen documents from the current selection in the specified viewport.
     *
     * This function checks the currently selected documents in the viewport,
     * filters those that are using the same editor type (i.e., shared screen documents),
     * and removes the screen sharing state **only** for those selected documents.
     *
     * @param vpId - The ID of the viewport where selected shared documents should be removed from screen sharing.
     */
    async turnOffMultilDocs(vpId: ViewportId) {
        const selectionCtxs: SelectContext[] = this.selectionFeature.getCurrentSelections(vpId);

        if (selectionCtxs.length < 1) return;

        const shareScreenDocCtrls = selectionCtxs
            .filter(e => e.doc.editor.editorType == this.editorType)
            .map(e => e.doc);

        if (shareScreenDocCtrls.length < 1) return;

        this.removeShareScreenDocs(vpId, shareScreenDocCtrls);
    }

    /**
     * Registers a listener for document-related events.
     *
     * @param listener - The event listener to register for document creation and removal events
     */
    registerDocEventListener(listener: VEventListener<ShareScreenCreateRemoveDocEvent>) {
        this._docEventEmitter.registerListener(listener);
    }

    /**
     * Unregisters a previously registered document event listener.
     *
     * @param listener - The event listener to unregister
     */
    unregisterDocEventListener(listener: VEventListener<ShareScreenCreateRemoveDocEvent>) {
        this._docEventEmitter.unregisterListener(listener);
    }

    /**
     * Internally removes a shared screen document from a viewport.
     * Cleans up associated registries, layers, and selection states.
     *
     * @param vpId - The viewport ID where the document is located.
     * @param docId - The local document ID to be removed.
     * @returns A promise that resolves once the document is fully removed.
     */
    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vpId);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vpId, docId);
        const docCtrl = docRegistry.getEntity(docId);
        if (docCtrl) {
            await this.selectDelegator.doBlurDocCtrl(docCtrl);
            const context: SelectContext = {
                doc: docCtrl,
                supporter: this.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION),
                selectDetails: {},
            };

            this.selectionFeature.onDeselect(context);

            docRegistry.removeEntity(docId);
            layerRegistry.removeEntity(docCtrl.layers[0].state.id);
            this.selectDelegator.blurDocCtrls([docCtrl]);
            docCtrl.onRemove();
        }

        const docRemovedEvent: ShareScreenCreateRemoveDocEvent = {
            source: this,
            eventType: 'doc-removed',
            state: docCtrl,
        };
        this._docEventEmitter.emit(docRemovedEvent);
    }

    /**
     * Creates a new shared screen document within a specified viewport.
     *
     * @param userId - The ID of the user who is sharing their screen.
     * @param vpId - The viewport ID where the shared screen should be displayed.
     */
    async createShareScreenDoc(userId: string, vpId) {
        const classCoordinator = this._coordinator as ClassroomCoordinator;
        const vm = classCoordinator.getViewportManager(vpId);
        // Get the current presenter's position to create a share screen document. If the current presenter's position cannot be obtained, get the current position of the screen sharer standing.
        const pos = classCoordinator.getPresenterState(vpId)?.vpPos || [vm.currentLookAt.x, vm.currentLookAt.y];
        const width = 400;
        const height = 400;

        const boundary: BoundaryRectangle = {
            start: { x: pos[0] - width / 2, y: pos[1] - height / 2 },
            end: { x: pos[0] + width / 2, y: pos[1] + height / 2 },
            width: width,
            height: height,
        };

        // Create a new shared screen document in the specified viewport with the given boundary
        await this.createNewDoc(vm, userId, boundary);
    }

    /**
     * Removes shared screen documents for a specific viewport.
     *
     * This function gathers the provided document controllers and organizes them for removal.
     * If confirmation is enabled, it prompts the user before proceeding.
     * Once confirmed (or if confirmation is skipped), it removes the documents and optionally
     * shows an awareness notification indicating successful screen share termination.
     *
     * @param vpId - The ID of the viewport where the documents are associated.
     * @param shareScreenDocCtrls - A list of document controllers representing shared screens to be removed.
     * @param enableTurnOffShareScreenConfirmation - Whether to prompt the user for confirmation before removal.
     */
    async removeShareScreenDocs(
        vpId: ViewportId,
        shareScreenDocCtrls: VDocCtrl[],
        enableTurnOffShareScreenConfirmation = true
    ) {
        const toBeRemoved = new Map<EditorType, RemoveDocInfo[]>();
        for (const doc of shareScreenDocCtrls) {
            let removeInfo = toBeRemoved.get(this.editorType);

            if (!removeInfo) {
                removeInfo = [];
                toBeRemoved.set(this.editorType, removeInfo);
            }

            removeInfo.push({
                vmId: vpId,
                localId: doc.state.id,
                globalId: doc.state.globalId,
                editor: this,
            });
        }

        if (!toBeRemoved.size) return;

        const classCoordinator = this._coordinator as ClassroomCoordinator;
        const vm = classCoordinator.getViewportManager(vpId);

        const processRemove = async () => {
            await this.crdFeature.removeDocument(vm!, toBeRemoved, false);
        };

        try {
            // Skip confirmation if disabled; otherwise, confirm before removing
            if (!enableTurnOffShareScreenConfirmation) await processRemove();
            else {
                if (!(await this._turnShareScreenConfirmationApprovals())) return;

                if (this.awarenessFeatrure)
                    await this.awarenessFeatrure.useAwareness(
                        vpId,
                        'Đang tắt chia sẻ màn hình',
                        buildLoadingAwarenessCmdOption('awareness-remove'),
                        processRemove
                    );
                else await processRemove();
            }
        } catch (err) {
            // Clears history for each document of the current editor type that is marked for removal.
            toBeRemoved.get(this.editorType)?.forEach(doc => {
                const docGlobalId = doc.globalId;
                this.onClearHistory(vpId, docGlobalId);
            });

            throw err;
        }
    }

    /**
     * Creates a new shared screen document within a specified viewport.
     *
     * @param vm - The viewport manager where the shared screen document should be created.
     * @param userId - The ID of the user who is sharing their screen.
     * @param boundary - The boundary dimensions where the shared screen will be positioned.
     * @returns A promise that resolves with `CreateDocumentResult`, containing the document's global and local IDs.
     */
    async createNewDoc(vm: ViewportManager, userId: string, boundary: any): Promise<CreateDocumentResult> {
        const ctx: CreatingContext = {
            vm: vm,
            boundary: boundary,
        };

        this._globalIdTemp = createDummyLocalDocId(vm.id, this.id, userId);
        const result = await this.crdFeature.createDocument(this, { ...ctx }, false, false);

        return {
            editor: this,
            globalId: result.expectedChanges[0].globalId,
            localId: result.expectedChanges[0].localId,
        };
    }

    /**
     * Saves and synchronizes the resized boundary of a shared screen document.
     * Ensures that the resized screen-sharing component updates across all participants.
     *
     * @param globalId - The global identifier of the shared screen document.
     * @param boundary - The new boundary dimensions for the shared screen.
     * @param docId - The local document ID associated with the shared screen.
     * @param vpId - The viewport ID where the screen-sharing document is located.
     * @returns A promise that resolves once the resize update is processed.
     */
    async saveAndSyncResizeScreenShare(
        globalId: string,
        boundary: BoundaryRectangle,
        docId: number,
        vpId: string
    ): Promise<void> {
        const vm = this._coordinator.getViewportManager(vpId);
        const toBeUpdated: Map<EditorType, DocBoundaryInfo> = new Map();
        toBeUpdated.set('ShareScreenEditor', {
            docs: [{ localId: docId, globalId: globalId }],
            boundaries: [boundary],
        });

        await this.crdFeature?.updateDocBoundary(toBeUpdated, vm, undefined, true);

        const meta = reliableCmdMeta(vm, 0, -1, FCCmdTypeProto.UPDATE_BOUNDARY, true);
        const cmd = new FCUpdateDocCmd(meta);

        cmd.state.setDocsList([new DocInfo().setLocalId(docId).setGlobalId(globalId!)]);
        cmd.state.setNewboundariesList([fcBuildBoundaryProto(boundary)]);
        cmd.state.setIsresetratio(true);

        return await this.cmdChannel.receive(cmd);
    }

    /**
     * Efficiently compares two arrays and determines the differences.
     * Uses Set data structure for O(1) lookups to optimize performance.
     * Identifies items that were added and removed between the old and new arrays.
     *
     * @param oldArray - The original array of strings
     * @param newArray - The updated array of strings
     * @returns An object containing arrays of added and removed items
     */
    getArrayChangesOptimized(oldArray: string[], newArray: string[]) {
        const oldSet = new Set(oldArray);
        const newSet = new Set(newArray);

        const added = Array.from(newSet).filter(item => !oldSet.has(item));
        const removed = Array.from(oldSet).filter(item => !newSet.has(item));

        return { added, removed };
    }

    /**
     * Converts a layer object into a boundary rectangle.
     * Extracts positional data and calculates width and height.
     *
     * @param layer - The input layer containing position coordinates
     * @returns A boundary rectangle object with start/end points and dimensions
     */
    private convertLayerToBoundary(layer: any) {
        // Return default boundary if layer is undefined
        if (!layer)
            return {
                start: { x: 0, y: 0 },
                end: { x: 0, y: 0 },
                width: 0,
                height: 0,
            };

        // Extract start and end points from layer position
        const startPoint = { x: layer.position[0], y: layer.position[1] };
        const endPoint = { x: layer.position[2], y: layer.position[3] };

        // Calculate width and height
        const width = endPoint.x - startPoint.x;
        const height = endPoint.y - startPoint.y;

        // Return boundary rectangle with absolute width and height
        return {
            start: { x: startPoint.x, y: startPoint.y },
            end: { x: endPoint.x, y: endPoint.y },
            width: width < 0 ? -1 * width : width,
            height: height < 0 ? -1 * height : height,
        };
    }

    /**
     * Provides the appropriate feature supporter for a given feature key.
     *
     * This method returns the appropriate delegator or handler for a specific feature,
     * allowing other components to interact with the editor's features through a common interface.
     *
     * @param featureKey - The identifier of the feature to get support for
     * @returns The appropriate feature supporter instance
     * @throws Error if the requested feature is not supported
     */
    featureSupporter<T>(featureKey: string): T {
        if (featureKey == FEATURE_CRUD) return this.crudDelegator as T;
        if (featureKey == FEATURE_SELECTION) return this.selectDelegator as T;
        if (featureKey == FEATURE_ZOOM) return this.zoomFeature as T;
        if (featureKey == FEATURE_HISTORY) return null;

        throw new Error(`ShareScreen Editor doesn't support feature ${featureKey}`);
    }

    /**
     * Generates initial document data when creating a new shared screen document.
     *
     * This method is called during document creation to set up the initial state
     * of the document, particularly setting the global ID.
     *
     * @param curChanges - The result of CRUD operations
     * @param insertDoc - The insert document command
     * @param insertLayer - The insert layer command
     */
    async generateInitDocData(curChanges: CRUDChangeResult, insertDoc: FCInsertDocCmd, insertLayer: FCInsertLayerCmd) {
        insertDoc.state.setGlobalId(this._globalIdTemp);
    }

    /**
     * Initializes a document creation tool for a specific viewport.
     *
     * This tool allows users to create new shared screen documents
     * within the specified viewport.
     *
     * @param viewport - The viewport manager where the tool will operate
     * @returns The created document tool instance
     */
    initCreateDocTool(viewport: ViewportManager) {
        const createToolType: any = 'CreateDocumentTool';
        const createTool = this.crdFeature.generateCRDToolForViewport(
            viewport.id,
            createToolType,
            this,
            this.cmdChannel,
            this.robFeature,
            this.selectionFeature
        );

        return createTool;
    }

    /**
     * Delegator for inserting document controllers.
     * Handles the creation of ShareScreenDocCtrl instances when documents are inserted.
     */
    readonly insertDocDelegator = new InsertDocCtrlDelegator<ShareScreenDocCtrl, ShareScreenDoc>(
        this,
        (vp, state) => new ShareScreenDocCtrl(this, state, vp)
    );

    /**
     * The operation mode for this editor (e.g., LOCAL, REMOTE).
     * Determines how commands are processed and synchronized.
     */
    private readonly _operationMode: OperationMode;

    /**
     * Gets the current operation mode of the editor.
     *
     * @returns The operation mode (LOCAL, REMOTE, etc.)
     */
    get operationMode(): OperationMode {
        return this._operationMode;
    }

    /**
     * Gets the local content for a document.
     *
     * This implementation returns a placeholder empty content object,
     * as shared screen documents don't have traditional content.
     *
     * @param vpId - The viewport ID where the document is located
     * @param localId - The local ID of the document
     * @returns A placeholder content object
     */
    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined {
        return { version: null, content: '' };
    }

    /**
     * Finds a key in a map by its value.
     *
     * Iterates through all entries in the map and returns the first key
     * that matches the specified value.
     *
     * @param map - The map to search in
     * @param value - The value to search for
     * @returns The key associated with the value, or undefined if not found
     */
    getKeyByValue(map: Map<string, string>, value: string): string | undefined {
        for (const [key, val] of map.entries()) {
            if (val === value) {
                return key;
            }
        }
        return undefined; // Return undefined if no key is found
    }

    /**
     * Gets the command channel used for communication.
     *
     * @returns The command channel instance
     */
    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    /**
     * Sets the command channel used for communication.
     *
     * @param cmdChannel - The command channel to use
     */
    set cmdChannel(cmdChannel: CmdChannel) {
        this._cmdChannel = cmdChannel;
    }

    /**
     * Loads a document by its global ID.
     *
     * This implementation is a placeholder that does nothing, as shared screen
     * documents are loaded through other mechanisms.
     *
     * @param globalId - The global ID of the document to load
     * @param loadingContext - Context information for loading
     * @returns A promise that resolves immediately
     */
    override loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<void> {
        return Promise.resolve();
    }

    /**
     * Loads a document by its local ID.
     *
     * This implementation is a placeholder that does nothing, as shared screen
     * documents are loaded through other mechanisms.
     *
     * @param localId - The local ID of the document to load
     * @param loadingContext - Context information for loading
     * @returns A promise that resolves immediately
     */
    override loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext): Promise<void> {
        return Promise.resolve();
    }

    /**
     * Duplicates documents by their global IDs.
     *
     * This implementation is a placeholder that returns null, as document
     * duplication is not supported for shared screen documents.
     *
     * @param docGlobalIds - Array of global document IDs to duplicate
     * @returns A promise that resolves to null
     */
    override duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return null;
    }

    /**
     * Handles the selection of a document controller.
     *
     * When a shared screen document is selected, this method:
     * 1. Calls the document's select method
     * 2. Sets the maintainRatio flag on the boundary to ensure proportional resizing
     *
     * @param docCtrl - The document controller that was selected
     */
    onSelectDocCtrl(docCtrl: ShareScreenDocCtrl): void {
        (docCtrl as ShareScreenDocCtrl).select();

        const boundary = this.selectionFeature.getBoundaryOfCtrl(docCtrl);
        if (boundary) boundary.maintainRatio = true;
    }

    /**
     * Checks if the content of a document is visible.
     *
     * Currently always returns true. May be updated in the future
     * with more sophisticated visibility settings.
     *
     * @param _docCtrl - The document controller to check visibility for
     * @returns Always true in the current implementation
     */
    isContentVisible(_docCtrl: ShareScreenDocCtrl): boolean {
        // TODO: update when there is more settings for the editor
        return true;
    }

    /**
     * Initializes the editor.
     *
     * This implementation is a placeholder that does nothing,
     * as initialization is handled in the constructor.
     *
     * @returns A promise that resolves immediately
     */
    async initialize(): Promise<void> {
        // do nothing
    }

    /**
     * Gets the editor coordinator instance.
     *
     * @returns The editor coordinator that manages this editor
     */
    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    /**
     * Loads a shared screen document using local content and places it in a viewport.
     *
     * @param vm - The viewport manager responsible for handling the document.
     * @param localId - The local document ID of the shared screen document.
     * @param localContent - The local content of the document (not currently used in this function).
     * @returns A `Promise` resolving to a `ShareScreenDocCtrl` or `undefined`.
     */
    override async loadDocumentByLocalContent(
        vm: ViewportManager,
        localId: DocLocalId,
        localContent: DocLocalContent
    ): Promise<ShareScreenDocCtrl | undefined> {
        const vpId = vm.id;
        const coordState = (this._coordinator as ClassroomCoordinator).getCoordState(vpId);
        const layer = coordState.layers.get(`${this.id}_${localId}_1`);

        const docGlobalId = coordState.docMapping.get(`${this.id}_${localId}`);
        this.createDoc(vm, localId, docGlobalId);
        const boundary = this.convertLayerToBoundary(layer);
        const layerCtrl = await this.insertLayer(vm, localId, 1, boundary);
        vm.addLayer(layerCtrl);

        return undefined;
    }

    /**
     * Reloads a document by its local ID.
     *
     * This method delegates to loadDocumentByLocalId to perform the actual loading.
     *
     * @param localId - The local ID of the document to reload
     * @param loadingContext - Context information for loading
     * @returns A promise that resolves when reloading is complete
     */
    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    /**
     * Requests a DOM element layer from the viewport manager.
     *
     * This method is used to create or retrieve a layer for rendering shared screen content
     * within a viewport.
     *
     * @param viewport - The viewport manager to request the layer from
     * @param add - Whether to add the layer to the viewport if it doesn't exist
     * @param options - Configuration options for the layer
     * @returns A DOM element layer controller for the requested layer
     */
    requestLayer(viewport: ViewportManager, add: boolean, options: LayerOptions): DOMElementLayerCtrl {
        return viewport.requestLayer(DOMElementLayerCtrl, add, options) as DOMElementLayerCtrl;
    }

    /**
     * Starts the editor by initializing the command processor and command channel.
     *
     * This method must be called before the editor can process commands and interact
     * with other components in the system.
     *
     * @returns A promise that resolves when startup is complete
     */
    async start() {
        await this._cmdProcessor.start();
        await this._cmdChannel.start();
    }

    /**
     * Checks if this editor supports a specific feature.
     *
     * @param featureKey - The identifier of the feature to check
     * @returns True if the feature is supported, false otherwise
     */
    isSupportFeature(featureKey: string): boolean {
        if (
            [
                FEATURE_SELECTION,
                FEATURE_ROB,
                FEATURE_CRD_DOC,
                FEATURE_CRUD,
                FEATURE_CONTEXTMENU,
                FEATURE_HISTORY,
                FEATURE_AWARENESS,
                FEATURE_ZOOM,
                FEATURE_PAN,
            ].includes(featureKey)
        ) {
            return true;
        }

        return false;
    }

    /**
     * Initializes a specific feature for this editor.
     *
     * This method is called when a feature is being initialized for the editor.
     * It stores the feature instance and performs any necessary setup or registration.
     *
     * @param featureKey - The identifier of the feature being initialized
     * @param feature - The feature instance to initialize
     * @returns A promise that resolves when initialization is complete
     */
    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                break;
            }
            case FEATURE_CRD_DOC:
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;

            case FEATURE_CONTEXTMENU: {
                this.contextMenuFeature = feature as ContextMenuFeature;
                break;
            }
            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }

            case FEATURE_AWARENESS: {
                this.awarenessFeatrure = feature as AwarenessFeature;
                break;
            }
            case FEATURE_ZOOM: {
                this.zoomFeature = feature as ZoomFeature;
                break;
            }
            case FEATURE_PAN: {
                this.panFeature = feature as PanFeature;
                break;
            }
            default:
                break;
        }
        return Promise.resolve();
    }

    /**
     * Notifies the content visibility feature about changes in document visibility.
     *
     * This method is called when the visibility of a shared screen document changes,
     * allowing the content visibility feature to update its state accordingly.
     *
     * @param docCtrl - The document controller whose visibility has changed
     */
    notifyContentVisibilityChange(docCtrl: ShareScreenDocCtrl) {
        this.contentVisibilityFeature?.onContentVisibilityChange(docCtrl);
    }

    /**
     * Inserts a new layer into a shared screen document within the specified viewport.
     *
     * @param vm - The viewport manager responsible for handling the document.
     * @param docId - The local document ID to which the layer belongs.
     * @param layerId - The unique identifier for the new layer.
     * @param boundary - The boundary dimensions of the new layer.
     * @returns A `Promise` resolving to a `DOMElementLayerCtrl` representing the inserted layer.
     */
    async insertLayer(
        vm: ViewportManager,
        docId: number,
        layerId: number,
        boundary: BoundaryRectangle
    ): Promise<DOMElementLayerCtrl> {
        const docRegistry = this.regMan.registry<ShareScreenDocCtrl>(shareScreenDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(docId);
        const layerState = new ShareScreenLayer(layerId, boundary);
        const docGlobalId = docCtrl.state.globalId;
        const layerCtrl: any = this.requestLayer(vm, true, {
            boundary: layerState.boundary,
            docLocalId: docId,
            docGlobalId: docGlobalId,
            viewport: vm,
            editor: this,
            state: layerState,
            domElType: 'div',
        });

        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);
        const layerRegistry = this.regMan.registry<DefaultVDocCtrl>(shareScreenLayerReg(vm.id, docId));
        layerRegistry.addEntity(layerId, layerCtrl);

        const docCreatedEvent: ShareScreenCreateRemoveDocEvent = {
            source: this,
            eventType: 'doc-created',
            state: docCtrl,
        };

        this._docEventEmitter.emit(docCreatedEvent);
        return layerCtrl;
    }

    /**
     * Resets the boundary of a shared screen document and updates the selection tool.
     *
     * @param vm - The viewport manager responsible for handling the document.
     * @param docId - The local document ID of the shared screen document.
     * @param boundary - The new boundary dimensions to be applied.
     * @returns The updated selection feature.
     */
    resetBoundary(vm: ViewportManager, docId: number, boundary: BoundaryRectangle) {
        const vpId = vm.id;
        const ctrl = this.findDocumentByLocalId(vm.id, docId);
        ctrl?.updateBoundary(boundary);

        this.selectionFeature.getSelectToolByVp(vpId).resetBoundary(docId, boundary);

        return this.selectionFeature;
    }

    /**
     * Creates a new shared screen document and inserts it into the viewport.
     *
     * @param vm - The viewport manager responsible for handling the document.
     * @param localId - The local document ID for the new shared screen document.
     * @param docGlobalId - The global document ID associated with the shared screen document.
     * @returns A promise that resolves once the document is created.
     */
    async createDoc(vm: ViewportManager, localId: DocLocalId, docGlobalId: string) {
        const shareScreenDoc = new ShareScreenDoc(localId, docGlobalId);
        this.insertDocDelegator.insertDocCtrl(vm, shareScreenDoc);
        return;
    }

    /**
     * Updates the boundary of a shared screen document within the specified viewport.
     *
     * @param vm - The viewport manager responsible for handling the document.
     * @param docId - The local document ID of the shared screen document.
     * @param boundary - The new boundary dimensions to be applied.
     * @returns A promise resolving to the updated selection feature.
     */
    async updateBoundary(vm: ViewportManager, docId: number, boundary: BoundaryRectangle): Promise<SelectionFeature> {
        const ctrl = this.findDocumentByLocalId(vm.id, docId);
        ctrl?.updateBoundary(boundary);
        return this.selectionFeature;
    }

    /**
     * Private event handler class for coordinator events.
     *
     * This class handles viewport-related events from the coordinator,
     * such as viewport addition and removal, and manages context menu
     * event listeners accordingly.
     */
    private _COORD_EVENT_HANDLER = class implements VEventListener<CoordinatorEvent> {
        constructor(private p: ShareScreenEditor) {}

        /**
         * Handles coordinator events.
         *
         * @param eventData - The event data from the coordinator
         * @returns The original event data or a promise resolving to it
         */
        onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
            switch (eventData.eventType) {
                case 'viewport-added': {
                    const viewport = eventData.state as ViewportAddedCES;

                    const contextMenuListenerKey: ContextMenuListenerFilter = {
                        anyEditorType: true,
                    };

                    // register listener for context menu on the particular viewport
                    this.p.contextMenuFeature.registerContextMenuEventListener(viewport.vmId, {
                        filter: contextMenuListenerKey,
                        handler: this.p.contextMenuFeatureListener,
                    });

                    break;
                }
                case 'viewport-removed': {
                    const viewport = eventData.state as ViewportAddedCES;

                    // register listener for context menu on the particular viewport
                    this.p.contextMenuFeature.unRegisterContextMenuEventListener(
                        viewport.vmId,
                        this.p.contextMenuFeatureListener
                    );
                    break;
                }
                default:
                    break;
            }

            return eventData;
        }
    };

    /**
     * Clears specific items from the history of a given viewport when a document is deleted.
     *
     * This function targets history items that are entirely related to a specific document
     * (identified by `globalId`). It filters and removes those history entries where **all**
     * the CRUD changes are associated with the deleted document.
     *
     * @param viewportId - The identifier for the current viewport whose history needs to be updated.
     * @param globalId - The global ID of the deleted document. Used to match and filter relevant history entries.
     */
    private onClearHistory(viewportId: ViewportId, globalId: string) {
        const historyManager = this.historyFeature?.getHistoryManager(viewportId);
        const historyTool = this.coordinator.getCommonToolbar(viewportId).getTool('history') as HistoryTool;

        if (historyManager) {
            historyManager.clearWithCondition(item => {
                const curItem = item as CRDHistoryItem;
                const { crudChanges } = curItem as any;

                // Filter changes that are related to the deleted document
                const matchedChangesForDeletedDoc = crudChanges.filter(change => {
                    const { options } = change;
                    return options.ids && options.ids.some(item => item.globalId === globalId);
                });

                // Only clear the item if all of its changes are related to the deleted document
                return matchedChangesForDeletedDoc.length === crudChanges.length;
            });

            // Update the history tool UI after clearing
            historyTool?.toolbar.update('history', historyTool.toolState);
        }
    }

    /**
     * Registers a function to handle confirmation for turning off screen sharing.
     *
     * This method allows customizing the confirmation logic when a user attempts
     * to stop screen sharing. The provided function will be called to determine
     * whether the screen sharing should be stopped.
     *
     * @param approval - A function that returns a Promise resolving to a Boolean indicating approval
     * @returns This editor instance for method chaining
     */
    registerTurnOffShareScreenConfirmationApprovals(approval: (...args: any[]) => Promise<Boolean>) {
        this._turnShareScreenConfirmationApprovals = approval;
        return this;
    }
}
