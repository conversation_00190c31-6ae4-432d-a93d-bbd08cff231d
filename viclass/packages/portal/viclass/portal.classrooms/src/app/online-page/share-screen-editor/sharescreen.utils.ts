import { DocLocalId, ViewportId } from '@viclass/editor.core';

export function shareScreenDocReg(viewportId: ViewportId) {
    return `sharescreenEditor/sharescreenDoc/${viewportId}`;
}

export function shareScreenLayerReg(viewportId: ViewportId, docId: DocLocalId) {
    return `sharescreenEditor/sharescreenLayer/${viewportId}/${docId}`;
}

export function shareScreenObjectReg(viewportId: ViewportId, docId: DocLocalId) {
    return `sharescreenEditor/sharescreenObject/${viewportId}/${docId}`;
}
