import {
    <PERSON>urs<PERSON>,
    InferredPointerEvent,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventData,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    NumDPointerChange,
    PointerEventData,
    PointerEventListener,
    PointerHandlingItem,
    Tool,
    ToolState,
    UserInputHandlerType,
    ViewportId,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';

import { ShareScreenDocCtrl } from '../docs/sharescreen.doc.ctrl';
import { ShareScreenEditor, ShareScreenKeyboardEvent, ShareScreenToolType } from '../sharescreen.api';
import { ShareScreenMouseEvent, ShareScreenToolEventData } from './models';
import { ShareScreenToolBar } from './share-screen.toolbar';

export abstract class ShareScreenTool<TState extends ToolState> implements Tool {
    readonly type: UserInputHandlerType = 'Tool';
    abstract readonly toolType: ShareScreenToolType;
    abstract readonly toolState: TState;
    mouseHandling: MouseHandlingItem[] = [];
    keyboardHandling: KeyboardHandlingItem[] = [];
    pointerHandling?: PointerHandlingItem[] = [];

    toolbar: ShareScreenToolBar;

    protected lastPointerMove: MouseEventData<NativeEventTarget<any>>;
    protected requestedFrame: boolean;
    protected started: boolean = false;

    constructor(public editor: ShareScreenEditor) {}

    readonly mouseHandler = new (class implements MouseEventListener<NativeEventTarget<any>> {
        //
        constructor(public tool: ShareScreenTool<ToolState>) {}

        onEvent(event: ShareScreenMouseEvent): ShareScreenMouseEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            return this.tool.handleMouseEvent(event);
        }
    })(this);

    readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: ShareScreenTool<ToolState>) {}

        onEvent(event: ShareScreenKeyboardEvent): ShareScreenKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    readonly pointerHandler = new (class implements PointerEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject<Cursor[] | undefined>(undefined);

        constructor(public tool: ShareScreenTool<ToolState>) {}

        onEvent(event: PointerEventData<NativeEventTarget<any>>): PointerEventData<NativeEventTarget<any>> {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            if ('nativeEvent' in event)
                // handle UI event
                return this.tool.handlePointerEvent(event);
            else return this.tool.handleNonUIPointerEvent(event);
        }
    })(this);

    registerMouseHandling(...handling: MouseHandlingItem[]) {
        if (!this.mouseHandling) this.mouseHandling = [];
        this.mouseHandling.push(...handling);
    }

    registerKeyboardHandling(...handling: KeyboardHandlingItem[]) {
        if (!this.keyboardHandling) this.keyboardHandling = [];
        this.keyboardHandling.push(...handling);
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    registerToolbar(toolbar: ShareScreenToolBar) {
        this.toolbar = toolbar;
    }

    onBlur() {}
    onFocus() {}

    onDisable() {}
    onEnable() {}

    resetState() {}

    onAttachViewport() {}

    onDetachViewport() {}

    focusAble(vpId: ViewportId): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;

        return this.editor.selectDelegator.getFocusedDocs(vpId)?.length == 1 && true;
    }

    protected getFocusedMathGraphDocCtrls(): ShareScreenDocCtrl[] {
        const vm = this.toolbar.viewport;

        if (!vm) {
            throw new Error('The toolbar of the magh editor has not been attached to any viewport.');
        }

        return this.editor.selectDelegator.getFocusedDocs(vm.id);
    }

    protected executeInFocusedDocCtrl(cb: (doc: ShareScreenDocCtrl) => void) {
        const docCtrls = this.getFocusedMathGraphDocCtrls();
        if (!docCtrls?.length) return;

        cb(docCtrls[0]);
    }

    handleKeyboardEvent(event: ShareScreenKeyboardEvent): ShareScreenKeyboardEvent {
        return event;
    }

    handlePointerEvent(event: PointerEventData<any>): PointerEventData<any> {
        return event;
    }

    handleNonUIPointerEvent(event: NumDPointerChange | InferredPointerEvent): NumDPointerChange | InferredPointerEvent {
        return event;
    }

    handleMouseEvent(event: ShareScreenMouseEvent): ShareScreenMouseEvent {
        return event;
    }

    handleToolEvent(event: ShareScreenToolEventData): ShareScreenToolEventData {
        switch (event.eventType) {
            case 'change': {
                this.processChangeToolEvent(event);
                break;
            }
            default:
                break;
        }

        return event;
    }

    protected async processChangeToolEvent(event: ShareScreenToolEventData): Promise<ShareScreenToolEventData> {
        return event;
    }
}
