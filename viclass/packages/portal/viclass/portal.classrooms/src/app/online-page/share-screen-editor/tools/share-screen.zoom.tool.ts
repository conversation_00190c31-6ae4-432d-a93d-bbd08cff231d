import { DefaultMouseEventData, mouseLocation, Position, ZoomFeature } from '@viclass/editor.core';
import { Subject } from 'rxjs';
import { ShareScreenDocCtrl } from '../docs/sharescreen.doc.ctrl';
import { ShareScreenMouseEvent } from '../model';
import { ShareScreenEditor } from '../sharescreen.editor';
import { ShareScreenToolType, ShareScreenZoomToolState } from './models';
import { ShareScreenTool } from './share-screen.tool';
import { calculatePosInLayer, validatePointerPos } from './tool.utils';

export class ShareScreenZoomTool extends ShareScreenTool<ShareScreenZoomToolState> {
    override toolState: ShareScreenZoomToolState = { zoomLevel: 1 };
    private submitServer: Subject<ShareScreenDocCtrl> = new Subject<ShareScreenDocCtrl>();

    constructor(
        editor: ShareScreenEditor,
        private zoomFeature: ZoomFeature
    ) {
        super(editor);
    }

    clear() {
        this.started = false;
        this.lastPointerMove = undefined;
    }

    override onFocus() {
        this.clear();
    }

    override resetState() {
        this.clear();
    }

    get toolType(): ShareScreenToolType {
        return 'ShareScreenZoomTool';
    }

    override onAttachViewport() {
        this.zoomFeature.registerZoomHandler(this.toolbar.viewport.id, this.editor.editorType, this);
    }

    override onEnable(): void {
        this.onDocAttached();
    }

    onDocAttached(docCtrl?: ShareScreenDocCtrl): void {
        docCtrl = docCtrl ? docCtrl : this.getFocusedMathGraphDocCtrls()?.[0];
        if (docCtrl) {
            this.toolState.zoomLevel = 1;
            this.toolbar.update('ShareScreenZoomTool', this.toolState);
        }
    }

    isZoomHandleAble(docCtrl: ShareScreenDocCtrl, event: ShareScreenMouseEvent): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;
        try {
            if (event instanceof DefaultMouseEventData) {
                const mousePos = mouseLocation(event);
                const wheelEvent = event.nativeEvent as WheelEvent;
                const isZoomIn = wheelEvent.deltaY < 0;

                const isInvalidPointer = !validatePointerPos(mousePos, docCtrl);
                const isMaxZoomIn = isZoomIn && docCtrl.zoomLevel === 5;
                const isMaxZoomOut = !isZoomIn && docCtrl.zoomLevel === 1;

                if (isInvalidPointer || isMaxZoomIn || isMaxZoomOut) {
                    return false;
                }
                return true;
            }
        } catch (error) {
            return false;
        }

        return false;
    }

    onMouseZoom(
        docCtrl: ShareScreenDocCtrl,
        event: ShareScreenMouseEvent
    ): ShareScreenMouseEvent | Promise<ShareScreenMouseEvent> {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return event;
        if (event.eventType === 'mousewheel' || event.eventType === 'wheel') {
            const zoomLevel = this.toolState.zoomLevel;
            const zoomIntensity = 0.1;
            const wheel = (event.nativeEvent as WheelEvent).deltaY < 0 ? 1 : -1;
            const maxZoom = 5;
            const minZoom = 1;

            let zoom = zoomLevel + wheel * zoomIntensity;
            if (zoom >= maxZoom) zoom = maxZoom;
            if (zoom <= minZoom) zoom = minZoom;
            const mousePos: Position = mouseLocation(event);
            const mousePosInLayer = calculatePosInLayer(mousePos, docCtrl);

            docCtrl.zoom(zoom, mousePosInLayer);

            // update toolstate
            this.toolState.zoomLevel = zoom;
            this.toolbar.update('ShareScreenZoomTool', this.toolState);

            event.continue = false;

            this.submitServer.next(docCtrl);
        }
        return event;
    }

    onTouchZoom(docCtrl: ShareScreenDocCtrl, mousePos: Position, wheel: 1 | -1) {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return;
        const zoomLevel = this.toolState.zoomLevel;
        const zoomIntensity = 0.1;

        const maxZoom = 5;
        const minZoom = 1;

        let zoom = zoomLevel + wheel * zoomIntensity;
        if (zoom >= maxZoom) zoom = maxZoom;
        if (zoom <= minZoom) zoom = minZoom;

        const mousePosInLayer = calculatePosInLayer(mousePos, docCtrl);

        docCtrl.zoom(zoom, mousePosInLayer);

        // update toolstate
        this.toolState.zoomLevel = zoom;
        this.toolbar.update('ShareScreenZoomTool', this.toolState);

        this.submitServer.next(docCtrl);
    }
}
