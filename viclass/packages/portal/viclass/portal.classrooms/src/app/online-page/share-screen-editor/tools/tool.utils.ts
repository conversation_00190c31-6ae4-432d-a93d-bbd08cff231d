import { Position, ScreenPosition } from '@viclass/editor.core';
import { ShareScreenDocCtrl } from '../docs/sharescreen.doc.ctrl';
import { ShareScreenEditor } from '../sharescreen.api';

export function calculatePosInLayer(pos: Position, docCtrl: ShareScreenDocCtrl): ScreenPosition {
    if (!docCtrl) throw 'doc is undefined';
    if ((docCtrl.editor as ShareScreenEditor).conf.docViewMode == 'bounded') {
        const bd = docCtrl.state.layer.boundary;

        const tx = Math.min(bd.start.x, bd.end.x);
        const ty = Math.max(bd.start.y, bd.end.y);
        const w = bd.width;
        const h = bd.height;

        const center: Position = { x: tx + w / 2, y: ty - h / 2 }; // center point in board coordinate
        const posInLayer: Position = {
            x: pos.x - center.x,
            y: pos.y - center.y,
        };

        return posInLayer;
    } else return pos;
}

export function validatePointerPos(pos: Position, doc: ShareScreenDocCtrl): boolean {
    if (!doc) return false;
    if ((doc.editor as ShareScreenEditor).conf.docViewMode == 'bounded') {
        const bd = doc.state.layer.boundary;

        const tx = Math.min(bd.start.x, bd.end.x);
        const ty = Math.max(bd.start.y, bd.end.y);

        const w = bd.width;
        const h = bd.height;

        const bx = tx + w;
        const by = ty - h;

        // check pos inside document boundary or not
        return pos.x > tx && pos.x < bx && pos.y > by && pos.y < ty;
    } else return true;
}
