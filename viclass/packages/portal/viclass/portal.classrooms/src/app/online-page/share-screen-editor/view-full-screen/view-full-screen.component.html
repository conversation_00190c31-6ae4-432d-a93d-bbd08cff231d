<div class="pt-[10px] inset-0 z-[100] h-full w-full flex justify-center items-center">
    <div class="flex justify-center items-center w-full h-full" (click)="close()">
        <div class="relative group h-full w-full" (click)="$event.stopPropagation()">
            <div
                #viewportContainerElement
                class="full-screen absolute inset-0 w-full h-auto !max-h-full cursor-none"></div>
            <ng-template [ngIf]="avatarModel$ | async">
                <div
                    style="box-shadow: 0px 5px 20px 0px #00424b33"
                    class="absolute top-2 left-1/2 transform -translate-x-1/2 w-[120px] h-[40px] bg-BW7 bg-opacity-60 rounded-[15px] flex items-center justify-between p-1 z-[100] opacity-0 group-hover:opacity-100">
                    <div class="w-[40px] h-[40px] rounded-full bg-cover bg-center p-[7px]">
                        <div
                            #avatar
                            class="d-flex justify-content-end profile-avatar h-full w-full rounded-full"
                            [ngStyle]="
                                (avatarModel$ | async).avatarUrl && {
                                    'background-image': 'url(' + (avatarModel$ | async).avatarUrl + ')',
                                }
                            "></div>
                    </div>
                    <button
                        #markerBtnEl
                        [class.bg-P2]="markerS.isActiveMarkerTool$ | async"
                        class="v-tool-btn w-[35px] h-[35px] flex items-center justify-center rounded-full"
                        (click)="markerS.toggleActiveMarkerTool()">
                        <span class="vcon vcon-onl vcon_marker"></span>
                    </button>
                    <button
                        class="w-[35px] h-[35px] flex items-center justify-center bg-P3 bg-opacity-0 text-black rounded-full transition-opacity duration-300 hover:bg-opacity-100 focus:outline-none"
                        (click)="close()">
                        <i class="text-sm vcon vcon-onl vcon_view_exit-full-screen"></i>
                    </button>
                </div>

                <lib-tooltip
                    class="z-20"
                    [toolTipFor]="avatar"
                    [tooltipContent]="(avatarModel$ | async).username"
                    [placement]="'bottom'"></lib-tooltip>
            </ng-template>
        </div>
    </div>
</div>
