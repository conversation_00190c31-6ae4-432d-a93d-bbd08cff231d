import { Injectable } from '@angular/core';
import { ClassroomCriticalError, SignalMessage } from '@viclass/editor.coordinator/classroom';
import { ErrorHandlerDecorator } from '@viclass/editor.core';
import { firstValueFrom, ReplaySubject } from 'rxjs';
import { DocumentInfo } from '../model';
import { CoordStatesService } from './coord.state.service';
import { DocinfoStateService } from './docinfo.state.service';
import { classroomErrorHandler } from './error-handler';
import { OnlineStateService } from './online.state.service';
import { UserStatusDetector } from './user.status.detector';

@Injectable()
export class SignalProcessor {
    // observable that inform about connection, for example when polling error, peer problem,...
    private readonly signalMessage$: ReplaySubject<SignalMessage> = new ReplaySubject(1);

    constructor(
        private readonly onlineStateS: OnlineStateService,
        private readonly coordStateS: CoordStatesService,
        private readonly userStatusDetector: UserStatusDetector,
        private readonly docinfoStateS: DocinfoStateService
    ) {
        this.signalMessage$.subscribe(msg => this.handleSignalMessage(msg));
    }

    @ErrorHandlerDecorator([classroomErrorHandler])
    onNewSignalMessage(msg: SignalMessage) {
        this.signalMessage$.next(msg);
    }

    @ErrorHandlerDecorator([classroomErrorHandler])
    private handleKickedOut(e: any) {
        throw new ClassroomCriticalError({ type: 'DUPLICATED_SESSION', data: true });
    }

    @ErrorHandlerDecorator([classroomErrorHandler])
    private async handleSignalMessage(signal: SignalMessage) {
        switch (signal.signalType) {
            case 'kickOut': {
                this.handleKickedOut(signal);
                break;
            }

            case 'RTCConnectionChange': {
                this.userStatusDetector.rtcConnectionChange(signal.data.status);
                if (signal.data.status == 'closed' || signal.data.status == 'failed') {
                    throw new ClassroomCriticalError({ type: 'CONNECTION_ERROR', data: true });
                } else if (signal.data.status == 'preparing') {
                    // Trying to reconnect
                } else if (signal.data.status == 'connected') {
                    classroomErrorHandler.dismissError('CONNECTION_ERROR');
                }
                break;
            }

            case 'AcceptPresentationRequestND':
            case 'AcceptRaiseHandND': {
                await this.processAllowedSyncingMsg(signal);
                break;
            }
            case 'StopPresentationND': {
                await this.processStoppedSyncingMsg(signal);
                break;
            }
            case 'presentCoordState': {
                await this.processPresentCoordState(signal);
                break;
            }
            case 'renamedCoordState': {
                await this.processRenamedCoordState(signal);
                break;
            }
            case 'PinnedCoordStateND': {
                await this.processPinCoordState(signal);
                break;
            }
            case 'UnpinnedCoordStateND': {
                await this.processUnpinCoordState(signal);
                break;
            }
            case 'DocInfoUpdated': {
                await this.processUpdateDocInfo(signal);
                break;
            }
            case 'DocInfoDeleted': {
                await this.processDeleteDocInfo(signal);
                break;
            }
            default:
                break;
        }
    }

    async processPinCoordState(msg: SignalMessage) {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const coordStateId = msg.data['coordStateId'];

        // update room-info
        coord.roomInfo.pinnedCoordStates.push(coordStateId);

        if (!coord.hasCoordState(coordStateId)) {
            await coord.loadCoordinatorState(coordStateId);
            this.coordStateS.add(coord.getCoordState(coordStateId));
        }

        const coordState = coord.getCoordState(coordStateId);

        coordState.pinned = true;
        this.coordStateS.update(coordState);
    }

    async processUnpinCoordState(msg: SignalMessage) {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const coordStateId = msg.data['coordStateId'];

        // update room-info
        coord.roomInfo.pinnedCoordStates = coord.roomInfo.pinnedCoordStates.filter(id => id != coordStateId);

        if (!coord.hasCoordState(coordStateId)) return;

        const coordState = coord.getCoordState(coordStateId);
        coordState.pinned = false;
        this.coordStateS.update(coordState);

        const presentingVpId = coord.roomInfo.presentingCoordState;

        // remove pinned viewport if it's satisfied condition
        if (coordStateId != presentingVpId && coordState.owner != coord.userId) {
            // if the unpinned viewport is selected -> switch to presenting vp before removing
            if (this.coordStateS.isSelected(coordStateId)) {
                const vpMode = await this.onlineStateS.calculateViewportMode(presentingVpId);
                await coord.switchViewportMode(presentingVpId, vpMode);
                await coord.switchViewport(presentingVpId);
            }

            await coord.removeViewport(coordStateId);
        }

        if (coordStateId != presentingVpId && coordState.owner == coord.userId) {
            await coord.switchViewportMode(coordStateId, 'EditMode');
        }
    }

    async processRenamedCoordState(msg: SignalMessage) {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const coordState = coord.getCoordState(msg.data['coordStateId']);
        if (coordState == null) return;

        coordState.title = msg.data['title'];
        this.coordStateS.update(coordState);
    }

    async processPresentCoordState(msg: SignalMessage) {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
        const newPresentingCoordId = msg.data['coordStateId'];

        if (!coord.hasCoordState(newPresentingCoordId)) {
            await coord.loadCoordinatorState(newPresentingCoordId);
            coord.cmdGateway.registerViewport(newPresentingCoordId);
            this.coordStateS.add(coord.getCoordState(newPresentingCoordId));
        }

        // remove current viewport if it's not default coordinator state
        if (
            !prePresentingCoord.default &&
            prePresentingCoord.owner != coord.userId &&
            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
        ) {
            // remove viewport and coordinator state
            await coord.removeViewport(prePresentingCoord.id);
        } else {
            if (newPresentingCoordId != prePresentingCoord.id) prePresentingCoord.presenting = false;
            if (prePresentingCoord.owner == coord.userId)
                await coord.switchViewportMode(prePresentingCoord.id, 'EditMode');
            else await coord.switchViewportMode(prePresentingCoord.id, 'InteractiveMode');
            await coord.switchViewport(prePresentingCoord.id);
        }

        this.coordStateS.setPresenting(newPresentingCoordId);

        coord.roomInfo.presentingCoordState = newPresentingCoordId;

        const newPresenting = coord.getCoordState(newPresentingCoordId);
        newPresenting.presenting = true;
        this.coordStateS.update(newPresenting);

        if (coord.getViewportManager(newPresentingCoordId)) {
            const vpMode = await this.onlineStateS.calculateViewportMode(newPresentingCoordId);
            await coord.switchViewportMode(newPresentingCoordId, vpMode);
            await coord.switchViewport(newPresentingCoordId);
        }
    }

    async processAllowedSyncingMsg(msg: SignalMessage) {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);

        if (msg.data['presentingCoordStateId'] != prePresentingCoord.id) prePresentingCoord.presenting = false;

        if (
            !prePresentingCoord.default &&
            prePresentingCoord.owner != coord.userId &&
            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
        ) {
            await coord.removeViewport(prePresentingCoord.id);
        }

        if (
            prePresentingCoord.owner == coord.userId &&
            prePresentingCoord.id != coord.roomInfo.defaultCoordState &&
            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
        ) {
            await coord.switchViewportMode(prePresentingCoord.id, 'EditMode');
            await coord.switchViewport(prePresentingCoord.id);
        }

        coord.roomInfo.presentingCoordState = msg.data['presentingCoordStateId'];
        coord.roomInfo.presentingPeer = msg.data['presentingPeerId'];
        coord.roomInfo.presentingUser = msg.data['presentingUserId'];

        const newPresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
        newPresentingCoord.presenting = true;

        const vpMode = await this.onlineStateS.calculateViewportMode(newPresentingCoord.id);
        await coord.switchViewportMode(newPresentingCoord.id, vpMode);
        await coord.switchViewport(newPresentingCoord.id);
        this.coordStateS.setPresenting(newPresentingCoord.id);
    }

    async processStoppedSyncingMsg(msg: SignalMessage) {
        const coord = await firstValueFrom(this.onlineStateS.coordinator$);
        const prePresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);

        if (msg.data['presentingCoordStateId'] != prePresentingCoord.id) prePresentingCoord.presenting = false;

        if (
            !prePresentingCoord.default &&
            prePresentingCoord.owner != coord.userId &&
            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
        ) {
            await coord.removeViewport(prePresentingCoord.id);
        }

        if (
            prePresentingCoord.owner == coord.userId &&
            prePresentingCoord.id != coord.roomInfo.defaultCoordState &&
            !coord.roomInfo.pinnedCoordStates.includes(prePresentingCoord.id)
        ) {
            await coord.switchViewportMode(prePresentingCoord.id, 'EditMode');
            await coord.switchViewport(prePresentingCoord.id);
        }

        coord.roomInfo.presentingCoordState = msg.data['presentingCoordStateId'];
        coord.roomInfo.presentingPeer = msg.data['presentingPeerId'];
        coord.roomInfo.presentingUser = msg.data['presentingUserId'];

        const newPresentingCoord = coord.getCoordState(coord.roomInfo.presentingCoordState);
        newPresentingCoord.presenting = true;

        const vpMode = await this.onlineStateS.calculateViewportMode(newPresentingCoord.id);
        await coord.switchViewportMode(newPresentingCoord.id, vpMode);
        await coord.switchViewport(newPresentingCoord.id);

        this.coordStateS.setPresenting(newPresentingCoord.id);
    }

    private async processUpdateDocInfo(msg: SignalMessage) {
        const coordStateId = msg.data['coordStateId'];
        const docInfo: DocumentInfo = msg.data['docInfo'];
        this.docinfoStateS.addDocInfos(coordStateId, [docInfo]);
    }

    private async processDeleteDocInfo(msg: SignalMessage) {
        const coordStateId = msg.data['coordStateId'];
        const docGlobalId = msg.data['docGlobalId'];
    }
}
