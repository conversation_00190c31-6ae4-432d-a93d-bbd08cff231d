import {
    FEATURE_SELECTION,
    SelectContext,
    SelectionEvent,
    VEventListener,
    ViewportContentEvent,
} from '@viclass/editor.core';
import { Injectable } from '@angular/core';
import { DocinfoStateService } from './docinfo.state.service';

@Injectable()
export class ViewportContentEventListener implements VEventListener<ViewportContentEvent> {
    constructor(private docinfoStateService: DocinfoStateService) {}

    onEvent(eventData: ViewportContentEvent): ViewportContentEvent | Promise<ViewportContentEvent> {
        if (eventData.state.source == FEATURE_SELECTION) {
            const origin = eventData.state.originalEvent as SelectionEvent;
            switch (origin.eventType) {
                case 'doc-selected': {
                    const selectCtx = origin.state.selectCtx as SelectContext;
                    this.docinfoStateService.markSelected(eventData.state.vm.id, [selectCtx.doc.state.globalId]);
                    break;
                }
                case 'doc-multi-selected': {
                    const selectCtxs = origin.state.selectCtxs as SelectContext[];
                    selectCtxs.forEach(selectCtx => {
                        this.docinfoStateService.markSelected(eventData.state.vm.id, [selectCtx.doc.state.globalId]);
                    });
                    break;
                }
                case 'doc-deselected': {
                    const selectCtx = origin.state.selectCtx as SelectContext;
                    this.docinfoStateService.markDeselected(eventData.state.vm.id, [selectCtx.doc.state.globalId]);
                    break;
                }
                case 'doc-multi-deselected': {
                    const selectCtxs = origin.state.selectCtxs as SelectContext[];
                    selectCtxs.forEach(selectCtx => {
                        this.docinfoStateService.markDeselected(eventData.state.vm.id, [selectCtx.doc.state.globalId]);
                    });
                    break;
                }
                default: {
                    break;
                }
            }
        }
        return eventData;
    }
    onUnregister?: () => void;
}
