import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ClassroomViewportComponent } from './classroom-viewport/classroom-viewport.component';

@Injectable({
    providedIn: 'root',
})
export class ViewportService {
    private readonly _viewports$: BehaviorSubject<ClassroomViewportComponent[]> = new BehaviorSubject([]);
    readonly viewports$: Observable<ClassroomViewportComponent[]> = this._viewports$.asObservable();
    get viewports() {
        return this._viewports$.value;
    }

    updateViewports(viewports: ClassroomViewportComponent[]) {
        this._viewports$.next(viewports);
    }

    private readonly _readyViewportCount$: BehaviorSubject<number> = new BehaviorSubject(0);
    readonly readyViewportCount$: Observable<number> = this._readyViewportCount$.pipe();
    get readyViewportCount() {
        return this._readyViewportCount$.value;
    }

    updateReadyViewportCount(count: number) {
        this._readyViewportCount$.next(count);
    }
}
