import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    AUTH_FLOW_CONFIG,
    AuthflowConfig,
    commonErrorHandler,
    LSessionDetails,
    LSessionRegisteredUserInfo,
    LSessionService,
    LSessionStatus,
    LsRegistrationService,
    LSRegStatus,
    ProcessingRequestManager,
    requireLogin,
    SpinnerLabelComponent,
    UserProfile,
    UserService,
} from '@viclass/portal.common';
import {
    BehaviorSubject,
    combineLatest,
    delay,
    finalize,
    firstValueFrom,
    from,
    map,
    mergeMap,
    Observable,
    of,
    Subscription,
    take,
} from 'rxjs';

import { FormsModule } from '@angular/forms';
import { tap } from 'rxjs';
import { AppStateService } from '../app.state.service';
import { CcsGateway } from '../gateways/ccs.gateway';
import { ErrorHandlerDecorator } from '@viclass/editor.core';

const REFRESH_INTERVAL = 7000;
const CHECK_LOGGEDIN = 10000;

@Component({
    imports: [CommonModule, SpinnerLabelComponent, FormsModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    selector: 'app-waiting-page',
    templateUrl: './waiting-page.component.html',
})
export class WaitingPageComponent implements OnInit, OnDestroy {
    goToOnlineValidation: Subscription;
    lsId: string;
    refreshDataTimeout: any;
    isStopRefreshData: boolean = false;
    title: string = '';

    editing$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    registeredUsers$ = new BehaviorSubject<LSessionRegisteredUserInfo[]>([]);
    isLoaded$ = new BehaviorSubject(false);

    _isCreator: Observable<boolean>;
    _regStatus: Observable<LSRegStatus>;
    isFull$: Observable<boolean>;
    _isMemberOrWaiting: Observable<boolean>;
    _sessionStatus: Observable<LSessionStatus>;

    constructor(
        public us: UserService,
        public ms: LsRegistrationService,
        public ls: LSessionService,
        public ccsGateway: CcsGateway,
        public as: AppStateService,
        private route: ActivatedRoute,
        private router: Router,
        @Inject(AUTH_FLOW_CONFIG) private authConf: AuthflowConfig,
        private reqMan: ProcessingRequestManager
    ) {
        this.lsId = as.lsId;
        ms.init(this.lsId);
        ls.init(this.lsId);
    }

    get isCreator(): Observable<boolean> {
        if (!this._isCreator)
            this._isCreator = combineLatest({
                user: this.us.curUser$,
                details: this.ls.details$.get(this.lsId),
            }).pipe(map(data => checkCreator(data.user, data.details)));

        return this._isCreator;
    }

    get isMemberOrWaiting(): Observable<boolean> {
        if (!this._isMemberOrWaiting)
            this._isMemberOrWaiting = this.ms.loggedInMI$.get(this.lsId).pipe(
                map(model => {
                    return model != null && ['REGISTERED', 'WAITING_CONFIRMED'].includes(model.regStatus);
                })
            );
        return this._isMemberOrWaiting;
    }

    get regStatus(): Observable<LSRegStatus> {
        if (!this._regStatus)
            this._regStatus = this.ms.loggedInMI$
                .get(this.lsId)
                .pipe(map(model => (model && model.regStatus ? model.regStatus : null)));
        return this._regStatus;
    }

    get sessionStatus(): Observable<LSessionStatus> {
        if (!this._sessionStatus)
            this._sessionStatus = this.ls.details$.get(this.lsId).pipe(
                map(details => {
                    return details.state ? details.state.status : 'NOT_STARTED';
                })
            );

        return this._sessionStatus;
    }

    get shareLink(): string {
        return window.location.href.replace(/\/(waiting|online)(\?.*)?$/, '');
    }

    get copiedShareLink$(): Observable<boolean> {
        return this.reqMan.getInprogressObs('copied');
    }

    copyToClipboard(data: string) {
        this.reqMan
            .monitor('copied', of(navigator.clipboard.writeText(data)).pipe(delay(1000), take(1)))
            .subscribe(result => result);
    }

    refreshData() {
        if (this.isStopRefreshData) return;
        this.refreshDataTimeout = setTimeout(() => {
            combineLatest([
                this.ls.loadSessionDetails(this.lsId),
                this.refreshMemberInfo(),
                this.ms.loadRegisteredUsers(this.lsId),
            ])
                .pipe(
                    take(1),
                    tap(([, , registeredUsers]) => {
                        this.isLoaded$.next(true);
                        this.registeredUsers$.next(registeredUsers);
                    }),
                    finalize(() => {
                        this.refreshData();
                    })
                )
                .subscribe();
        }, REFRESH_INTERVAL);
    }

    setupLoginCheck() {
        setTimeout(
            () =>
                requireLogin(this.us, this.authConf, 'trang chờ')
                    .pipe(finalize(() => this.setupLoginCheck()))
                    .subscribe(),
            CHECK_LOGGEDIN
        );
    }

    stopRefreshData() {
        if (this.goToOnlineValidation) this.goToOnlineValidation.unsubscribe();
        if (this.refreshDataTimeout) {
            this.isStopRefreshData = true;
            clearTimeout(this.refreshDataTimeout);
        }
    }

    refreshMemberInfo(): Observable<any> {
        return this.isCreator.pipe(
            take(1),
            mergeMap(v => {
                if (v)
                    return from([1]); // if true, this is the creator of the
                else return this.ms.loadLoggedInRegistrationInfo(this.lsId);
            })
        );
    }

    navigateToOnline() {
        this.router.navigate(['../online'], { relativeTo: this.route });
        this.stopRefreshData();
    }

    /**
     * Subscribe to observables to navigate user to online page when user is creator or the registration is completed
     */
    setUpValidationToNavigate() {
        this.goToOnlineValidation = combineLatest({
            details: this.ls.details$.get(this.lsId),
            loggedInMemberInfo: this.ms.loggedInMI$.get(this.lsId),
            profile: this.us.curUser$,
        }).subscribe(data => {
            if (checkCreator(data.profile, data.details)) {
                if (['STARTED', 'ENDED'].includes(data.details.state.status)) this.navigateToOnline();
            } else {
                // TODO remove ENDED status when whe handle it
                if (
                    data.loggedInMemberInfo.regStatus == 'REGISTERED' &&
                    ['STARTED', 'ENDED'].includes(data.details.state.status)
                ) {
                    this.navigateToOnline();
                }
            }
        });
    }

    ngOnInit(): void {
        this.setupLoginCheck();
        this.setUpValidationToNavigate();
        this.refreshData(); // setup refresh data

        this.isFull$ = combineLatest({
            registeredUsers: this.registeredUsers$,
            settings: this.ls.settings$.get(this.lsId),
        }).pipe(
            map(data => {
                if (
                    data.settings.maxRegistration &&
                    data.settings.maxRegistration > 0 &&
                    data.registeredUsers.length >= data.settings.maxRegistration
                )
                    return true;
                else return false;
            })
        );
    }

    /**
     * Toggles the editing mode on and off and update the title input to the current title value.
     * @returns {Promise<void>}
     */
    async switchEditTitle() {
        const isEditing = !this.editing$.value;
        this.editing$.next(isEditing);
        if (isEditing) {
            this.title = (await firstValueFrom(this.ls.details$.get(this.lsId)))?.title || '';
        }
    }

    /**
     * Called when the user saves the new title for the class.
     * If the new title is empty, it just switches the editing mode off. Otherwise, it updates the title.
     */
    acceptEditTitle() {
        const text = this.title.trim();
        if (!text) {
            this.switchEditTitle();
            return;
        }

        this.reqMan
            .monitor(
                'updatingTitle',
                combineLatest({
                    details: this.ls.details$.get(this.lsId),
                    settings: this.ls.settings$.get(this.lsId),
                }).pipe(
                    take(1),
                    mergeMap(data => {
                        data.details.title = text;
                        return this.ls.updateLSession(data);
                    }),
                    finalize(() => this.switchEditTitle())
                )
            )
            .subscribe();
    }

    @ErrorHandlerDecorator([commonErrorHandler])
    async startLSession() {
        const lsDetail = await firstValueFrom(this.ls.details$.get(this.lsId));

        // start class
        await firstValueFrom(
            this.ccsGateway.startClass({
                lsId: this.lsId,
                callingUserId: lsDetail.creatorId,
            })
        );

        // reload the latest session details
        await firstValueFrom(this.ls.loadSessionDetails(this.lsId));

        // go to online page
        this.navigateToOnline();
    }

    registerLSession() {
        this.reqMan.monitor('registeringLSession', this.ms.register(this.lsId)).subscribe();
    }

    unregisterLSession() {
        this.reqMan.monitor('unregisteringLSession', this.ms.unregister(this.lsId)).subscribe();
    }

    get updatingTitle$(): Observable<boolean> {
        return this.reqMan.getInprogressObs('updatingTitle');
    }

    get startingLSession$(): Observable<boolean> {
        return this.reqMan.getInprogressObs('startingLSession');
    }

    get registeringLSession$(): Observable<boolean> {
        return this.reqMan.getInprogressObs('registeringLSession');
    }

    get unregisteringLSession$(): Observable<boolean> {
        return this.reqMan.getInprogressObs('unregisteringLSession');
    }

    ngOnDestroy(): void {
        this.goToOnlineValidation.unsubscribe();
        this.reqMan.clear();
        this.stopRefreshData();
    }
}

function checkCreator(user: UserProfile, details: LSessionDetails): any {
    return user && details && user.id == details.creatorId;
}
