@font-face {
    font-family: 'viclass-onlsession';
    src:
        url('fonts/viclass-onlsession.ttf?wjp74e') format('truetype'),
        url('fonts/viclass-onlsession.woff?wjp74e') format('woff'),
        url('fonts/viclass-onlsession.svg?wjp74e#viclass-onlsession') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vcon-onl {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'viclass-onlsession' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vcon_act_pin:before {
    content: '\e600';
}
.vcon_request-list:before {
    content: '\e590';
}
.vcon_library:before {
    content: '\e588';
}
.vcon_mute-all:before {
    content: '\e589';
}
.vcon_view_exit-full-screen:before {
    content: '\e966d';
}
.vcon_view_full-screen:before {
    content: '\e966c';
}
.vcon_user:before {
    content: '\e911d';
}
.vcon_general_filter:before {
    content: '\e916';
}
.vcon_edit:before {
    content: '\e965';
}
.vcon_user-status_onl .path1:before {
    content: '\e899a';
    color: rgb(255, 255, 255);
}
.vcon_user-status_onl .path2:before {
    content: '\e899d';
    margin-left: -1em;
    color: rgb(49, 227, 124);
}
.vcon_user-status_away .path1:before {
    content: '\e899e';
    color: rgb(255, 255, 255);
}
.vcon_user-status_away .path2:before {
    content: '\e899f';
    margin-left: -1em;
    color: rgb(255, 255, 255);
}
.vcon_user-status_away .path3:before {
    content: '\e89a0';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_user-status_idle .path1:before {
    content: '\e899b';
    color: rgb(255, 184, 0);
}
.vcon_user-status_idle .path2:before {
    content: '\e899c';
    margin-left: -1em;
    color: rgb(255, 255, 255);
}
.vcon_user-status_off .path1:before {
    content: '\e89ee';
    color: rgb(255, 255, 255);
}
.vcon_user-status_off .path2:before {
    content: '\e89ef';
    margin-left: -1em;
    color: rgb(255, 255, 255);
}
.vcon_user-status_off .path3:before {
    content: '\e89f0';
    margin-left: -1em;
    color: rgb(164, 173, 180);
}
.vcon_avarta_waiting-student .path1:before {
    content: '\e900';
    color: rgb(255, 255, 255);
}
.vcon_avarta_waiting-student .path2:before {
    content: '\e901';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_avarta_waiting-student .path3:before {
    content: '\e902';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_document-camera:before {
    content: '\e929';
}
.vcon_whiteboard_default-tab:before {
    content: '\e956';
}
.vcon_logo-icon:before {
    content: '\e941';
}
.vcon_whiteboard_pinned:before {
    content: '\e935';
}
.vcon_general_action-list:before {
    content: '\e937';
}
.vcon_page-bar_zoom-in-area:before {
    content: '\e967a';
}
.vcon_page-bar_zoom-out-area:before {
    content: '\e967c';
}
.vcon_page-bar_zoom-out:before {
    content: '\e967b';
}
.vcon_page-bar_ad:before {
    content: '\e938';
}
.vcon_copy:before {
    content: '\e933';
}
.vcon_copy-and-paste:before {
    content: '\e932';
}
.vcon_live_setting:before {
    content: '\e570';
}
.vcon_doc_setting:before {
    content: '\e569';
}
.vcon_sidebar_action_accept:before {
    content: '\e934a';
}
.vcon_sidebar_action_denied:before {
    content: '\e934b';
}
.vcon_sidebar_action_play:before {
    content: '\e936a';
}
.vcon_sidebar_action_stop:before {
    content: '\e936b';
}
.vcon_page-bar_record:before {
    content: '\e939c';
}
.vcon_wb_menu:before {
    content: '\e942';
}
.vcon_wb_undo:before {
    content: '\e943a';
}
.vcon_wb_redo:before {
    content: '\e943b';
}
.vcon_wb_hand-tool:before {
    content: '\e943c';
}
.vcon_wb_move:before {
    content: '\e943d';
}
.vcon_user_student:before {
    content: '\e911b';
}
.vcon_sidebar_raise-hand:before {
    content: '\e926c';
}
.vcon_user_waiting-student:before {
    content: '\e914b';
}
.vcon_view_in-new-window:before {
    content: '\e903';
}
.vcon_move-vertical:before {
    content: '\e572';
}
.vcon_eraser:before {
    content: '\e950';
}
.vcon_eraser-all:before {
    content: '\e573';
}
.vcon_marker:before {
    content: '\e574';
}
.vcon_sidebar-setting:before {
    content: '\e926b';
}
.vcon_sidebar-document:before {
    content: '\e926a';
}
.vcon_page-bar_notification:before {
    content: '\e939d';
}
.vcon_page-bar_share:before {
    content: '\e939e';
}
.vcon_page-bar_out:before {
    content: '\e939f';
}
.vcon_document_freedrawing:before {
    content: '\e910a';
}
.vcon_document_geometry:before {
    content: '\e910d';
}
.vcon_document_word:before {
    content: '\e910b';
}
.vcon_document_mathtype:before {
    content: '\e910e';
}
.vcon_document_magh:before {
    content: '\e568';
}
.vcon_delete:before {
    content: '\e903a';
}
.vcon_mic_off:before {
    content: '\e576';
}
.vcon_mic_on:before {
    content: '\e575';
}
.vcon_camera_off:before {
    content: '\e578';
}
.vcon_camera_on:before {
    content: '\e577';
}
.vcon_share-screen:before {
    content: '\e581';
}
.vcon_share-screen_stop:before {
    content: '\e584';
}
.vcon_speaker:before {
    content: '\e579';
}
.vcon_media:before {
    content: '\e580';
}
.vcon_continue-list:before {
    content: '\e520';
}
.vcon_align_bottom:before {
    content: '\e931a';
}
.vcon_align_left:before {
    content: '\e931c';
}
.vcon_align_hor-center:before {
    content: '\e931b';
}
.vcon_align_right:before {
    content: '\e931d';
}
.vcon_align_ver-center:before {
    content: '\e931f';
}
.vcon_align_top:before {
    content: '\e931e';
}
.vcon_empty-data:before {
    content: '\e915';
}
