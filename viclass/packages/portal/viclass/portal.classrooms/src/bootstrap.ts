import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { enableProdMode, ErrorHandler, importProvidersFrom } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import {
    AUTH_FLOW_CONFIG,
    CommonErrorHandlerModule,
    ENVIRONMENT,
    FileStoreServiceModule,
    ShortURLServiceModule,
    UserServiceModule,
} from '@viclass/portal.common';
import '@viclass/ww/mfeRT';
import 'zone.js';
import { routes } from './app/app-routing';
import { AppComponent } from './app/app.component';
import { HttpErrorInterceptor } from './app/http/http-error.interceptor';
import { GlobalErrorHandler } from './app/online-page/error-handler';
import { environment } from './environments/environment';

if (environment.production) enableProdMode();

bootstrapApplication(AppComponent, {
    providers: [
        { provide: AUTH_FLOW_CONFIG, useValue: environment.authflowConfig },
        provideRouter(routes),
        importProvidersFrom(HttpClientModule),
        importProvidersFrom(NoopAnimationsModule),
        importProvidersFrom(UserServiceModule),
        importProvidersFrom(FileStoreServiceModule),
        importProvidersFrom(ShortURLServiceModule),
        importProvidersFrom(CommonErrorHandlerModule),
        { provide: ENVIRONMENT, useValue: environment },
        { provide: ErrorHandler, useClass: GlobalErrorHandler },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: HttpErrorInterceptor,
            multi: true, // multi: true is required for interceptors
        },
    ],
}).catch(err => console.error(err));
