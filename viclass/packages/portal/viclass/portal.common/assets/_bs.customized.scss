@import '~bootstrap/scss/mixins';

$grid-breakpoints: (
    xs: 0,
    sm: 576px,
    md: 768px,
    lg: 1024px,
    xl: 1280px,
    xxl: 1366px,
);

$container-max-widths: (
    xs: 0px,
    sm: 540px,
    md: 720px,
    lg: 960px,
    xl: 1200px,
    xxl: 1300px,
);

$B1: #698bff;
$B2: #bbcbff;
$B3: #d6e6ff;

$O1: #ff5c00;
$O2: #ff7600;
$O3: #ffb890;
$O4: #ffe8d8;

$D1: #212121;
$D2: #424242;
$D3: #535353;
$D4: #5f6472;
$D5: #878787;
$D6: #cdd5e3;
$D7: #f4f7ff;

$primary: $O1;
$dark: #212121;
$blue: #0066ff;
$white: #fff;
$logo: #0066ff;
$facebook: #1877f2;

$font-family-base: Montserrat;
$font-size-base: 0.875rem;

$border-width: 2px;
$border-radius: 20px;

$spacer: 1rem !default;
$spacers: (
    0: 0,
    1: $spacer / 4,
    2: $spacer / 2,
    3: $spacer,
    4: $spacer * 1.5,
    5: $spacer * 3,
);

$additional-spacers: (
    6: $spacer * 4,
    7: $spacer * 5,
    8: $spacer * 6,
);

$spacers: map-merge($spacers, $additional-spacers);

// define the maps of max width for each screen size
$max-widths: (
    xs: (
        372px: 372px,
    ),
    sm: (
        0: 0,
    ),
    md: (
        0: 0,
    ),
    lg: (
        944px: 944px,
        870px: 870px,
        470px: 470px,
    ),
    xl: (
        1280px: 1280px,
        1170px: 1170px,
        570px: 570px,
    ),
    xxl: (
        1366px: 1366px,
    ),
);

// define the maps of min width for each screen size
$min-widths: (
    xs: (
        500px: 500px,
        550px: 550px,
    ),
    sm: (
        0: 0,
    ),
    md: (
        0: 0,
    ),
    lg: (
        0: 0,
    ),
    xl: (
        1170px: 1170px,
        100per: 100%,
    ),
    xxl: (
        1170px: 1170px,
    ),
);

@mixin make-padding-x($size) {
    padding: 0 #{$size} !important;
}
@mixin make-padding-left($size) {
    padding-left: #{$size} !important;
}
@mixin make-padding-right($size) {
    padding-right: #{$size} !important;
}

@mixin make-max-width($size) {
    max-width: #{$size} !important;
}

@mixin make-min-width($size) {
    min-width: #{$size} !important;
}

// make paddings
// usage: <div class="px-xl-5px"></div>
@mixin make-paddings-x($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        $infix: breakpoint-infix($breakpoint, $breakpoints);
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            @for $i from 0 through 30 {
                $size: #{$i * 5}px;
                .px#{$infix}-#{$size} {
                    @include make-padding-x($size);
                }
                .ps#{$infix}-#{$size} {
                    @include make-padding-left($size);
                }
                .pe#{$infix}-#{$size} {
                    @include make-padding-right($size);
                }
            }
        }
    }
}

// make max widths
// usage:
// <div class="max-w-xl-5px max-w-xxl-10per"></div>
// Note: '..px' for pixel and '..per' for percentage
@mixin make-max-widths($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            $extend-breakpoint: true;
            @each $name, $sizes in $max-widths {
                $infix: breakpoint-infix($name, $max-widths);
                @if ($extend-breakpoint) {
                    @each $n, $v in $sizes {
                        .max-w#{$infix}-#{$n} {
                            @include make-max-width($v);
                        }
                    }
                    @if ($breakpoint == $name) {
                        $extend-breakpoint: false;
                    }
                }
            }
        }
    }
}

// make min widths
// usage:
// <div class="min-w-xl-5px min-w-xxl-10per"></div>
// Note: '..px' for pixel and '..per' for percentage
@mixin make-min-widths($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            $extend-breakpoint: true;
            @each $name, $sizes in $min-widths {
                $infix: breakpoint-infix($name, $min-widths);
                @if ($extend-breakpoint) {
                    @each $n, $v in $sizes {
                        .min-w#{$infix}-#{$n} {
                            @include make-min-width($v);
                        }
                    }
                    @if ($breakpoint == $name) {
                        $extend-breakpoint: false;
                    }
                }
            }
        }
    }
}

@include make-paddings-x();
@include make-max-widths();
@include make-min-widths();

$enable-shadows: true;

// ------- TEXT INPUT ---------
$input-bg: #f4f7ff;
$input-font-weight: 500;
$input-font-family: Montserrat;
$input-font-size: 14px;
$input-border-width: 0px;
$input-box-shadow: inset 1px 1px 3px 0 rgba(0, 0, 0, 0.15);
$input-padding-y: 10px;
$input-padding-x: 10px;
$input-line-height: 1.43;
$input-placeholder-color: #979797;
$input-color: inherit;

$input-padding-y-sm: 5px;
$input-padding-x-sm: 15px;
$input-font-size-sm: 14px;
$input-border-radius-sm: 15px;

$input-focus-border-color: '';
$input-focus-width: 0;
$input-focus-box-shadow: '';

$input-active-border-color: none;
$input-active-box-shadow: '';

// -------- LINKS ----------
$link-color: $blue;
$link-decoration: none;
$link-hover-decoration: underline;

// ------ BUTTONS ----------

$input-btn-padding-x: 20px;
$input-btn-padding-x-sm: 20px;
$input-btn-padding-y-sm: 2px;

$btn-font-family: Montserrat-SemiBold;
$btn-font-size: 16px;
$btn-font-size-sm: 14px;
$btn-border-radius-sm: 20px;
$btn-box-shadow: '';

$btn-focus-box-shadow: '';

// ------- FORM SWITCH -------
$form-check-margin-bottom: 0px;
$form-check-input-checked-bg-color: $blue;

// $form-switch-color:               rgba(0, 0, 0, .25) !default;
// $form-switch-width:               2em !default;
// //$form-switch-padding-start:       0;
// $form-switch-transition:          background-position .15s ease-in-out !default;

// //$form-switch-focus-color:         $input-focus-border-color !default;

// $form-switch-checked-color:       $component-active-color !default;
// $form-switch-checked-bg-position: right center !default;

.btn-sm {
    padding-bottom: 3px !important;
    height: 30px;

    i {
        padding-top: 3px;
        display: block;
    }
}

.btn-outline-primary {
    background-color: $white !important;
    color: $primary !important;
}

.btn-outline-primary:hover,
.btn-outline-primary.hover {
    background-color: $primary !important;
    color: $white !important;
}

.btn:disabled,
.btn.disabled {
    background-color: $D6 !important;
    color: $D5 !important;
    border-color: $D6 !important;
}

$btn-disabled-opacity: 1;

$min-contrast-ratio: 2; // allows white contrast on the orange background

.form-check-input {
    cursor: pointer;
}
