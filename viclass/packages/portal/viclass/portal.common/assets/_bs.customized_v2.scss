@import 'bootstrap/scss/mixins';
@import 'bootstrap/scss/functions';
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,500;0,600;0,700;1,500;1,600;1,700&display=swap');

$escaped-characters: (('<', '%3c'), ('>', '%3e'), ('#', '%23'), ('(', '%28'), (')', '%29'));

$grid-breakpoints: (
    xs: 0,
    sm: 576px,
    md: 768px,
    lg: 1024px,
    xl: 1280px,
    xxl: 1366px,
);

$container-max-widths: (
    xs: 0,
    sm: 540px,
    md: 720px,
    lg: 960px,
    xl: 1200px,
    xxl: 1300px,
);

$B1: #698bff;
$B2: #bbcbff;
$B3: #d6e6ff;

$O1: #ff5c00;
$O2: #ff7600;
$O3: #ffb890;
$O4: #ffe8d8;

$D1: #212121;
$D2: #424242;
$D3: #535353;
$D4: #5f6472;
$D5: #878787;
$D6: #cdd5e3;
$D7: #f4f7ff;

$dark: #212121;
$blue: #0066ff;
$logo: #0066ff;
$facebook: #1877f2;

// ================ begin definition color ======================
$cyan: #00aeef;
$light-cyan-1: #8de4ff;
$light-cyan-2: #d8f8ff;
$orange: #ff6b00;
$yellow: #ffb800;
$hard_dark: #121414;
$dark: #363a3e;
$medium_grey_1: #62676b;
$medium_grey_2: #a4adb4;
$light-grey-1: #f0f6f8;
$light-grey-2: #f7fcfd;
$white: #ffffff;
$primary: $cyan;

.cyan-gradient {
    background: rgb(0, 174, 239);
    background: linear-gradient(0deg, rgba(0, 174, 239, 1) 29%, rgba(187, 237, 255, 1) 100%);
}

.orange-gradient {
    background: rgb(18, 20, 20);
    background: linear-gradient(0deg, rgba(18, 20, 20, 1) 20%, rgba(255, 107, 0, 1) 100%);
}

.dark-gradient {
    background: rgb(18, 20, 20);
    background: linear-gradient(0deg, rgba(18, 20, 20, 1) 20%, rgba(54, 58, 62, 1) 100%);
}

.medium-grey-1-gradient {
    background: rgb(54, 58, 62);
    background: linear-gradient(0deg, rgba(54, 58, 62, 1) 20%, rgba(98, 103, 107, 1) 100%);
}

.text-box {
    box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.25);
}

// ================ end definition color ======================

// =============== definition font ============================

$Thin: 100;
$Extra-Light: 200;
$Light: 300;
$Regular: 400;
$Medium: 500;
$Semi-Bold: 600;
$Bold: 700;
$Extra-Bold: 800;
$Black: 900;

$font-family-sans-serif: Montserrat, sans-serif;
$font-family-base: Montserrat;
$font-size-base: 0.875rem;

// ============== end definition font =========================

$border-width: 2px;
$border-radius: 20px;

$spacer: 1rem !default;
$spacers: (
    0: 0,
    1: $spacer / 4,
    2: $spacer / 2,
    3: $spacer,
    4: $spacer * 1.5,
    5: $spacer * 3,
);

$additional-spacers: (
    6: $spacer * 4,
    7: $spacer * 5,
    8: $spacer * 6,
);

$spacers: map-merge($spacers, $additional-spacers);

// define the maps of max width for each screen size
$max-widths: (
    xs: (
        372px: 372px,
    ),
    sm: (
        0: 0,
    ),
    md: (
        0: 0,
    ),
    lg: (
        944px: 944px,
        870px: 870px,
        470px: 470px,
    ),
    xl: (
        1280px: 1280px,
        1170px: 1170px,
        570px: 570px,
    ),
    xxl: (
        1366px: 1366px,
    ),
);

// define the maps of min width for each screen size
$min-widths: (
    xs: (
        500px: 500px,
        550px: 550px,
    ),
    sm: (
        0: 0,
    ),
    md: (
        0: 0,
    ),
    lg: (
        0: 0,
    ),
    xl: (
        1170px: 1170px,
        100per: 100%,
    ),
    xxl: (
        1170px: 1170px,
    ),
);

@mixin make-padding-x($size) {
    padding: 0 #{$size} !important;
}
@mixin make-padding-left($size) {
    padding-left: #{$size} !important;
}
@mixin make-padding-right($size) {
    padding-right: #{$size} !important;
}
@mixin make-padding-y($size) {
    padding: #{$size} 0 !important;
}
@mixin make-padding-top($size) {
    padding-top: #{$size} !important;
}
@mixin make-padding-bottom($size) {
    padding-bottom: #{$size} !important;
}

@mixin make-margin-x($size) {
    margin: 0 #{$size} !important;
}
@mixin make-margin-left($size) {
    margin-left: #{$size} !important;
}
@mixin make-margin-right($size) {
    margin-right: #{$size} !important;
}
@mixin make-margin-y($size) {
    margin: #{$size} 0 !important;
}
@mixin make-margin-top($size) {
    margin-top: #{$size} !important;
}
@mixin make-margin-botton($size) {
    margin-bottom: #{$size} !important;
}

@mixin make-max-width($size) {
    max-width: #{$size} !important;
}

@mixin make-min-width($size) {
    min-width: #{$size} !important;
}

// make paddings
// usage: <div class="px-xl-5px"></div>
@mixin make-paddings($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        $infix: breakpoint-infix($breakpoint, $breakpoints);
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            @for $i from 0 through 30 {
                $size: #{$i * 5}px;
                .px#{$infix}-#{$size} {
                    @include make-padding-x($size);
                }
                .ps#{$infix}-#{$size} {
                    @include make-padding-left($size);
                }
                .pe#{$infix}-#{$size} {
                    @include make-padding-right($size);
                }
                .py#{$infix}-#{$size} {
                    @include make-padding-y($size);
                }
                .pt#{$infix}-#{$size} {
                    @include make-padding-top($size);
                }
                .pb#{$infix}-#{$size} {
                    @include make-padding-bottom($size);
                }
            }
        }
    }
}

// make margin
// usage: <div class="mx-xl-5px"></div>
@mixin make-margins($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        $infix: breakpoint-infix($breakpoint, $breakpoints);
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            @for $i from 0 through 30 {
                $size: #{$i * 5}px;
                .mx#{$infix}-#{$size} {
                    @include make-margin-x($size);
                }
                .ms#{$infix}-#{$size} {
                    @include make-margin-left($size);
                }
                .me#{$infix}-#{$size} {
                    @include make-margin-right($size);
                }
                .my#{$infix}-#{$size} {
                    @include make-margin-y($size);
                }
                .mt#{$infix}-#{$size} {
                    @include make-margin-top($size);
                }
                .mb#{$infix}-#{$size} {
                    @include make-margin-botton($size);
                }
            }
        }
    }
}

// make max widths
// usage:
// <div class="max-w-xl-5px max-w-xxl-10per"></div>
// Note: '..px' for pixel and '..per' for percentage
@mixin make-max-widths($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            $extend-breakpoint: true;
            @each $name, $sizes in $max-widths {
                $infix: breakpoint-infix($name, $max-widths);
                @if ($extend-breakpoint) {
                    @each $n, $v in $sizes {
                        .max-w#{$infix}-#{$n} {
                            @include make-max-width($v);
                        }
                    }
                    @if ($breakpoint == $name) {
                        $extend-breakpoint: false;
                    }
                }
            }
        }
    }
}

// make min widths
// usage:
// <div class="min-w-xl-5px min-w-xxl-10per"></div>
// Note: '..px' for pixel and '..per' for percentage
@mixin make-min-widths($breakpoints: $grid-breakpoints) {
    @each $breakpoint in map-keys($breakpoints) {
        @include media-breakpoint-up($breakpoint, $breakpoints) {
            $extend-breakpoint: true;
            @each $name, $sizes in $min-widths {
                $infix: breakpoint-infix($name, $min-widths);
                @if ($extend-breakpoint) {
                    @each $n, $v in $sizes {
                        .min-w#{$infix}-#{$n} {
                            @include make-min-width($v);
                        }
                    }
                    @if ($breakpoint == $name) {
                        $extend-breakpoint: false;
                    }
                }
            }
        }
    }
}

@include make-margins();
@include make-paddings();
@include make-max-widths();
@include make-min-widths();

@function escape-svg($string) {
    @if str-index($string, 'data:image/svg+xml') {
        @each $char, $encoded in $escaped-characters {
            // Do not escape the url brackets
            @if str-index($string, 'url(') == 1 {
                $string: url('#{str-replace(str-slice($string, 6, -3), $char, $encoded)}');
            } @else {
                $string: str-replace($string, $char, $encoded);
            }
        }
    }

    @return $string;
}

$enable-shadows: true;

// ------- TEXT INPUT ---------
$input-bg: $light-grey-2;
$input-font-weight: 500;
$input-font-family: Montserrat;
$input-font-size: 14px;
$input-border-width: 0px;
$input-box-shadow: inset 1px 1px 3px 0 rgba(0, 0, 0, 0.15);
$input-padding-y: 5px;
$input-padding-x: 10px;
$input-line-height: 1.43;
$input-placeholder-color: $medium_grey_2;
$input-color: $medium_grey_1;
$input-border-radius: 12px;

$input-padding-y-sm: 5px;
$input-padding-x-sm: 10px;
$input-font-size-sm: 14px;
$input-border-radius-sm: 8px;

$input-focus-border-color: '';
$input-focus-width: 0;
$input-focus-box-shadow: '';

$input-active-border-color: none;
$input-active-box-shadow: '';

input:disabled {
    color: $medium_grey_2;
    font-size: 14px;
    font-style: italic;
}

// ------- TOOL TIPS --------
.tooltip-inner {
    background-color: $dark !important;
    border: 1px solid $white;
    border-radius: 5px !important;
}

// ------- MAT MENU ---------
.mat-menu-panel {
    border: 1px solid $medium_grey_1;
    border-radius: 5px !important;
    color: $dark;
    background-color: $white;
    box-shadow: none !important;

    .mat-menu-item {
        height: 40px;
        line-height: 40px;
    }

    .mat-menu-item:hover:not([disabled]) {
        color: $hard_dark !important;
        background-color: $light-cyan-2 !important;
    }
}

// ------- FORM SELECT ------
$form-select-bg: $white !default;
$form-select-color: $dark !default;
$form-select-font-size: 14px !default;
$form-select-border-width: 1px !default;
$form-select-border-color: $medium_grey_2 !default;
$form-select-border-radius: 8px !default;
$form-select-disabled-color: $medium_grey_2 !default;

@function form-select-bg-image($color) {
    @return escape-svg(
        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 25 25'><path fill='none' stroke='#{$color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6 -6'/></svg>")
    );
}

@function form-select-listed-bg-image($color) {
    @return escape-svg(
        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{$color}' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 12l6 -6 6 6'/></svg>")
    );
}

.mat-option {
    &:hover {
        color: $hard_dark !important;
        background-color: $light-cyan-2 !important;
    }

    &:active,
    &.mat-selected {
        color: $hard_dark !important;
        background-color: $light-cyan-1 !important;
    }
}

.mat-select {
    display: block !important;
}

.mat-select-value {
    max-width: none !important;
}

.mat-select-panel {
    border: 1px solid $medium_grey_1;
    border-radius: 5px;
    color: $dark;

    &:not([class*='mat-elevation-z']) {
        box-shadow: none !important;
    }
}

.mat-select-disabled {
    font-style: italic !important;
    border-color: $medium_grey_2 !important;
    background-color: $white !important;

    &.form-select {
        &:hover,
        &:focus {
            .mat-select-arrow-wrapper {
                background-image: form-select-bg-image($medium_grey_2) !important;
            }
        }
    }
}

.form-select {
    border: none; // ignore root setting
    border-radius: 12px !important;
    padding: 5px 3px 5px 10px !important;
    height: 30px;
    background-image: none !important;

    .mat-select-arrow-wrapper {
        @extend .vcon-general, .vcon_drop-down-menu_close;
        text-align: center;
        color: $medium_grey_1;
        width: 20px;
        height: 20px;
    }

    .mat-select-arrow {
        border-top: none !important;
    }

    &:hover {
        .mat-select-arrow-wrapper {
            color: $hard_dark;
        }
    }

    &:focus {
        .mat-select-arrow-wrapper {
            @extend .vcon-general, .vcon_drop-down-menu_open;
            color: $hard_dark;
        }
    }

    &.form-select-sm {
        height: 20px;
        border-radius: 12px;
        padding: 0 2px 0 10px !important;
    }
}

// ------- FORM SWITCH -------
$form-check-input-checked-color: $white !default;
$form-switch-color: $cyan !default;
$form-switch-checked-color: $white !default;
$form-switch-width: 24px !default;
$form-switch-height: 14px !default;
$form-switch-padding-start: $form-switch-width + 5px !default;
$form-switch-border-radius: $form-switch-width !default;
$form-check-input-border: 1px solid $cyan !default;

@function form-switch-bg-image($color) {
    @return escape-svg(
        url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'><circle r='3' fill='#{$color}'/></svg>")
    );
}

.form-check-input {
    cursor: pointer;

    &:disabled {
        opacity: 1;
        background-image: form-switch-bg-image($medium_grey_2) !important;
        background-color: $white !important;
        border-color: $medium_grey_2 !important;
    }

    &:hover,
    &:focus {
        opacity: 1;
        background-image: form-switch-bg-image($light-cyan-1) !important;
        background-color: $white !important;
        border-color: $light-cyan-1 !important;
    }

    &:checked {
        &:hover,
        &:focus {
            opacity: 1;
            background-image: form-switch-bg-image($white) !important;
            background-color: $light-cyan-1 !important;
            border-color: $light-cyan-1 !important;
        }

        &:disabled {
            opacity: 1;
            background-image: form-switch-bg-image($white) !important;
            background-color: $medium_grey_2 !important;
            border-color: $medium_grey_2 !important;
        }
    }
}

// -------- LINKS ----------
$link-color: $cyan;
$link-decoration: none;
$link-hover-decoration: none;

// ------ BUTTONS ----------
$btn-border-width: 2px;
$btn-font-family: Montserrat;
$btn-font-weight: $Semi-Bold;
$btn-padding-y: 7px;
$btn-padding-x: 15px;
$btn-border-radius: 15px;
$btn-line-height: 20px;
$btn-font-size: 16px;
$btn-box-shadow: '';
$btn-focus-box-shadow: '';

$btn-padding-y-sm: 3px;
$btn-padding-x-sm: 10px;
$btn-border-radius-sm: 12px;
$btn-font-size-sm: 14px;

.btn {
    height: 40px;
    box-sizing: border-box;

    span {
        line-height: 20px !important;
    }
}

.btn-sm {
    height: 30px;
}

.btn-primary {
    background-color: $cyan !important;
    color: $white !important;
    border-color: $cyan !important;
}

.btn-outline-primary {
    background-color: $white !important;
    color: $cyan !important;
    border-color: $cyan !important;
}

.btn-primary:focus,
.btn-primary.focus,
.btn-outline-primary:focus,
.btn-outline-primary.focus {
    background-color: $cyan !important;
    color: $white !important;
    border-color: $cyan !important;
}

.btn-primary:hover,
.btn-primary.hover,
.btn-outline-primary:hover,
.btn-outline-primary.hover {
    background-color: $light-cyan-1 !important;
    color: $hard_dark !important;
    border-color: $light-cyan-1 !important;
}

.btn:disabled,
.btn.disabled {
    background-color: $light-grey-1 !important;
    color: $medium_grey_2 !important;
    border-color: $medium_grey_2 !important;
}

.mat-button-disabled {
    color: $medium_grey_1 !important;
}

$btn-disabled-opacity: 1;

$min-contrast-ratio: 2; // allows white contrast on the orange background
