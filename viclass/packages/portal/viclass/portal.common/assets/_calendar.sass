// -------- MATERIAL CALENDAR CUSTOMIZATION ---------

.mat-calendar
  font-family: Montserrat !important
  font-size: 14px


.mat-calendar-body-label
  opacity: 0

.mat-calendar-body-label[colspan="7"]
  display: none

mat-year-view
  .mat-calendar-body-label[colspan="4"]
    display: none

.mat-datepicker-content .mat-calendar
  height: auto !important

.mat-calendar-body-cell-content
  border-radius: 5px !important
  border: 1px solid $D6
  background-color: $D7

.mat-calendar-table-header-divider
  display: none

.mat-calendar-table-header

  th
    font-family: Montserrat
    font-size: 14px
    color: $dark

mat-month-view
  .mat-calendar-body > tr:first-child > td:last-child:not(.mat-calendar-body-in-preview, .mat-calendar-body-in-range) > .mat-calendar-body-cell-content,
  .mat-calendar-body > tr:first-child > td:nth-last-child(2):not(.mat-calendar-body-in-preview, .mat-calendar-body-in-range) > .mat-calendar-body-cell-content
    background-color: $O4

  .mat-calendar-body > tr > td:nth-child(6):not(.mat-calendar-body-in-preview, .mat-calendar-body-in-range) > .mat-calendar-body-cell-content,
  .mat-calendar-body > tr > td:nth-child(7):not(.mat-calendar-body-in-preview, .mat-calendar-body-in-range) > .mat-calendar-body-cell-content
    background-color: $O4

.mat-calendar-body-cell-preview
  border: none !important

td.mat-calendar-body-in-preview .mat-calendar-body-cell-content
  background-color: $B2

td.mat-calendar-body-preview-end .mat-calendar-body-cell-content
  background-color: $blue !important
  color: $white

td.mat-calendar-body-in-range

  &::before
    background: none

  .mat-calendar-body-cell-content
    background-color: $B2


.mat-calendar-body-selected
  background-color: $blue !important

.mat-calendar-header
  padding: 0px !important
  font-family: Montserrat !important
  font-size: 16px !important

.mat-calendar-body
  font-size: 14px

.mat-button
  font-family: Montserrat

.mat-calendar-body-label, .mat-calendar-period-button
  font-size: 16px
  font-weight: 600

.mat-calendar-controls
  margin: 0px !important

.mat-datepicker-actions
  padding: 0px !important
  justify-content: center !important

  .timebutton
    height: 35px
    width: 33%
    border: none
    background-color: $D5
    font-size: 14px
    font-weight: 500
    color: $white
    text-align: center
    line-height: 35px
    cursor: pointer
    display: flex
    justify-content: center
    align-items: center

    &:hover
      text-decoration: none

    &:first-child
      border-bottom-left-radius: 5px
      margin-right: 2px
      margin-left: 0px

    &:last-child
      border-bottom-right-radius: 5px
      margin-right: 0px
      margin-left: 2px

    i
      font-size: 20px
      margin-right: 5px

  .active
      background-color: $blue !important
      color: $white !important

.date-time-picker-display
    border: none
    padding-bottom: 0px
    padding-top: 0px

    &:focus
        border: none
        outline: none
        caret-color: transparent
        cursor: pointer

.datepicker-time
    color: $D5

    input[type=number].form-control
        max-width: 110px
        width: 110px
        text-align: center
        margin: 0px 5px
