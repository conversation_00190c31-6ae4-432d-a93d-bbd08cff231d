.mat-mdc-dialog-container
  padding: 0px !important
  border-radius: 10px !important
  background: none
  overflow: visible !important
  box-shadow: none

  .btn-primary
    background-color: $O2
    border-color: $O2

    &:hover
      background-color: $O1
      border-color: $O1

.dialog-auth
  width: 868px
  min-height: 558px
  position: relative
  background: white
  border-radius: 10px !important
  border: 2px solid $dark
  font-family: Montserrat
  font-size: 14px

  .btn-primary
    &:disabled
      background: #cdd5e3
      color: #878787
      border: #cdd5e3

  .btn-close
    position: absolute
    right: -50px
    width: 40px !important
    min-width: 40px !important
    height: 30px
    background-color: white
    background-image: url("./images/icon-delete.svg")
    background-position: center
    color: $primary
    border: 2px solid $primary
    background-repeat: no-repeat
    box-sizing: border-box
    opacity: 1
    border-radius: 20px !important


  .leftbanner
    width: 400px
    min-height: 554px
    background: $blue url("./images/img-auth.gif") no-repeat

  .rightbanner
    position: relative
    width: 464px
    min-height: 554px
    padding: 50px 50px 0px

    .facebook-google

      a.btn
        justify-content: center
        align-items: center
        padding: 0px
        flex-grow: 2
        line-height: 36px
        &:hover
          text-decoration: none
          background: none
        i
          font-size: 20px
          margin-right: 5px

      .btn-facebook
        color: $blue !important
        border-color: $blue !important
        i
          &:before
            color: $blue !important

      .btn-google
        color: $primary !important
        i
          &:before
            color: $primary !important

      hr
        height: 1px
        flex-grow: 2
        margin: 10px 0px 0px
        color: $dark

.color-cyan
  color: $cyan
