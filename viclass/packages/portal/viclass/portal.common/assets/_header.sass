$search-symbol-color:       $white !default
$logo-color:                $white !default
$search-symbol-focus-color: $white !default
$search-input-border-color: $white !default

// styling for header sections of all pages
.top-navigation
    padding: 0

    .logo-container
        flex-grow: 2

        .logo
            font-family: Philosopher
            font-size: 36px
            font-weight: bold
            font-stretch: normal
            font-style: normal
            line-height: 1.11
            letter-spacing: normal
            text-align: left
            color: $logo-color
            a
              text-decoration: none

        .search-box
            position: relative
            margin-left: 30px
            display: flex
            max-width: 350px
            min-width: 100px

            .search-input
                border-radius: 20px
                width: 40px
                height: 40px
                flex-grow: 0
                padding: 10px 35px 10px 10px
                border-radius: 20px
                border: solid 1px $search-input-border-color
                background: none
                z-index: 10
                color: white
                cursor: pointer
                opacity: 0
                transition: width 0.5s ease, opacity 1s ease

                &:focus
                    opacity: 1
                    width: 100%
                    transition: width 0.2s ease, opacity 0.3s ease
                    outline: none
                    background: $dark
                    clip-path: none
                    ~ .search-symbol
                        width: 100%
                        transition: width 0.2s ease

                        .symbol-lens
                            color: $search-symbol-focus-color
                            z-index: 10

            .search-symbol
                position: absolute
                width: 40px
                height: 40px
                font-size: 20px
                font-weight: bold
                display: flex
                border: 1px solid $search-symbol-color
                border-radius: 20px
                transition: width 0.5s ease

                .symbol-lens
                    position: absolute
                    color: $search-symbol-color
                    z-index: 1
                    right: 0px
                    transform: translate(-40%, 50%)

    .menu-container

        .navbar
            height: 40px

            .navbar-nav
                height: 40px

                .nav-item
                    height: 45px
                    flex-grow: 0
                    font-family: Montserrat
                    font-size: 18px
                    font-weight: 500
                    font-stretch: normal
                    font-style: normal
                    line-height: 1.56
                    letter-spacing: normal
                    text-align: left
                    color: black

                .nav-link
                    color: black
