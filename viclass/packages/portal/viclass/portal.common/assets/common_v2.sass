// $grid-breakpoints:(xs: 0,sm: 576px,md: 768px,lg: 992px,xl: 1230px)
// $container-max-widths:(sm: 540px, md: 720px, lg: 960px, xl: 1470px)
=filter($filter-type,$filter-amount)
  -webkit-filter: $filter-type+unquote('(#{$filter-amount})')
  -moz-filter: $filter-type+unquote('(#{$filter-amount})')
  -ms-filter: $filter-type+unquote('(#{$filter-amount})')
  -o-filter: $filter-type+unquote('(#{$filter-amount})')
  filter: $filter-type+unquote('(#{$filter-amount})')

=backdrop-filter($filter-type,$filter-amount)
  -webkit-backdrop-filter: $filter-type+unquote('(#{$filter-amount})')
  -moz-backdrop-filter: $filter-type+unquote('(#{$filter-amount})')
  -ms-backdrop-filter: $filter-type+unquote('(#{$filter-amount})')
  -o-backdrop-filter: $filter-type+unquote('(#{$filter-amount})')
  backdrop-filter: $filter-type+unquote('(#{$filter-amount})')

@import '@angular/material/prebuilt-themes/indigo-pink.css'
@import "bs.customized_v2"
@import "icons/style"
@import "output.css"
@import 'bootstrap/scss/bootstrap'

@import "dialog"
@import "form"
@import "header"
@import "footer"
@import "lsession-summary"
@import "calendar"

.vcon, .vcon-general
  font-size: 20px
  line-height: 20px

.vcon-link
  display: block
  height: 20px
  line-height: 20px
  color: $D5

  &:hover
    text-decoration: none
    color: $dark

  i.vcon
    line-height: inherit

a:hover
  cursor: pointer

.active
    color: $cyan
    background: none

.center-x
  margin-left: auto
  margin-right: auto
  justify-content: center

.text-right
  text-align: right

.text-o1
  color: $O1

.text-logo
    color: $logo

.text-error
  color: $O1

input
  color: inherit

.centered
  position: fixed
  top: 50%
  left: 50%
  /* bring your own prefixes */
  transform: translate(-50%, -50%)

=fill-status-tag($bg,$tx)
  >.status-tag-content
    background-color: $bg
    color: $tx
  &:after
    background: $bg

.status-tag
  font-family: Montserrat
  line-height: 20px
  font-size: 12px
  display: flex
  width: max-content
  +filter(drop-shadow, 4px 4px 0px rgba(0, 102, 255, 0.15))

  &:after
    content: ''
    margin-left: -1px
    width: 12px
    height: 20px
    clip-path: polygon(0 -1px, 100% 50%, 0% 21px)

  >.status-tag-content
    padding-left: 5px

  &.status-tag-confirm
    +fill-status-tag($B1,$white)

  &.status-tag-registered
    +fill-status-tag($logo,$white)

  &.status-tag-rejected
    +fill-status-tag($D3,$white)

  &.status-tag-full
    +fill-status-tag($O4,$O1)

.pointer:hover
  cursor: pointer

.profile-avatar
  width: 40px
  height: 40px
  border: 1px solid $medium_grey_2
  border-radius: 20px
  background-size: cover
  box-sizing: border-box
  background-image: url(images/avatar-man.png)

  &.man, .man
    background-image: url(images/avatar-man.png)

  &.woman, .woman
    background-image: url(images/avatar-woman.png)


*::-webkit-scrollbar-track
  //-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3)
  background-color: $white

*::-webkit-scrollbar
  width: 14px
  background-color: $medium_grey_2

*::-webkit-scrollbar-thumb
  background-color: $medium_grey_2
  border: 5px solid $white
  border-radius: 20px

*
  scrollbar-width: thin
  scrollbar-color: $medium_grey_2 $white
