/**
This files should only contains styles used commonly in the portal only, it is different from the
viclass themes which contains common things in all viclass
**/
@use 'icons/style';
@use 'output.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

.avatar-img {
    /* size of the avatar image is decided at application level */
    @apply rounded-[50%] bg-center bg-contain;
    background-image: url('images/avatar-man.png');
    background-repeat: no-repeat;
}

.social-zalo {
    @apply rounded-[50%] bg-center bg-contain;
    background-image: url('images/social-zalo.svg');
    background-repeat: no-repeat;
}

.social-gmail {
    @apply rounded-[50%] bg-center bg-contain;
    background-image: url('images/social-gmail.svg');
    background-repeat: no-repeat;
}

.social-facebook {
    @apply rounded-[50%] bg-center bg-contain;
    background-image: url('images/social-facebook.svg');
    background-repeat: no-repeat;
}

.social-messenger {
    @apply rounded-[50%] bg-center bg-contain;
    background-image: url('images/social-messenger.svg');
    background-repeat: no-repeat;
}

.social-twitter {
    @apply rounded-[50%] bg-center bg-contain;
    background-image: url('images/social-twitter.svg');
    background-repeat: no-repeat;
}

@mixin filter($filter-type, $filter-amount) {
    -webkit-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    -moz-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    -ms-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    -o-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    filter: $filter-type (+ unquote('(#{$filter-amount})'));
}

@mixin backdrop-filter($filter-type, $filter-amount) {
    -webkit-backdrop-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    -moz-backdrop-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    -ms-backdrop-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    -o-backdrop-filter: $filter-type (+ unquote('(#{$filter-amount})'));
    backdrop-filter: $filter-type (+ unquote('(#{$filter-amount})'));
}

@mixin fill-status-tag($bg, $tx) {
    > .status-tag-content {
        background-color: $bg;
        color: $tx;
    }

    &:after {
        background: $bg;
    }
}

.vi-status-tag {
    line-height: 20px;
    font-size: 12px;
    display: flex;
    width: max-content;

    /* @include filter(drop-shadow, 4px 4px 0px rgba(0, 102, 255, 0.15)); */

    &:after {
        content: '';
        margin-left: 0px;
        width: 12px;
        height: 100%;
        clip-path: polygon(0 0, 100% 50%, 0% 100%);
    }

    > .status-tag-content {
        padding-left: 5px;
        padding-right: 5px;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &.status-tag-waiting-confirm {
        @include fill-status-tag(rgb(var(--P3)), rgb(var(--P1)));
    }

    &.status-tag-registered {
        @include fill-status-tag(rgb(var(--P2)), rgb(var(--BW1)));
    }

    &.status-tag-not-registered {
        @include fill-status-tag(rgb(var(--P3)), rgb(var(--BW1)));
    }

    &.status-tag-rejected {
        @include fill-status-tag(rgb(var(--BW4)), rgb(var(--BW1)));
    }
}

.share-url {
    box-shadow: inset 1px 1px 3px rgba(0, 0, 0, 0.25);
    border-radius: 20px;
    height: 30px;
    background-color: rgb(var(--BW5));
    border: none;
    padding: 6px 10px 0 10px;
    color: rgb(var(--BW3));
    overflow: hidden;
    width: 300px;
    text-overflow: ellipsis;
    white-space: nowrap;

    a {
        font-size: 14px;
        pointer-events: none;
        cursor: default !important;

        &:focus-visible {
            outline: none;
        }
    }
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border: 1px solid rgb(var(--BW4));
    border-radius: 20px;
    background-size: cover;
    box-sizing: border-box;
    background-image: url(images/avatar-man.png);

    &.man,
    .man {
        background-image: url(images/avatar-man.png);
    }

    &.woman,
    .woman {
        background-image: url(images/avatar-woman.png);
    }
}

*::-webkit-scrollbar-track {
    /* -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) */
    background-color: rgb(var(--BW7));
}

*::-webkit-scrollbar {
    width: 14px;
    background-color: rgb(var(--BW4));
}

*::-webkit-scrollbar-thumb {
    background-color: rgb(var(--BW4));
    border: 5px solid rgb(var(--BW7));
    border-radius: 20px;
}

* {
    scrollbar-width: thin;
    scrollbar-color: rgb(var(--BW4)) rgb(var(--BW7));
}

.alert-error {
    background-image: url(/assets/img/alert-error.svg);
}

div[role='tooltip'] {
    font-family: Montserrat !important;
    font-size: 12px !important;
}
