import { inject } from '@angular/core';
import { TransferState } from '@angular/platform-browser';
import { ActivatedRoute, ActivatedRouteSnapshot, ResolveFn, RouterStateSnapshot } from '@angular/router';
import { firstValueFrom, Observable, of } from 'rxjs';
import { catchError, map, take, tap } from 'rxjs/operators';
import { PlatformService, UserService } from '../services';
import { PROFILE_TRANSFER_TOKEN } from '../transfer.token';
import { encodeReturnUrl } from '../utils';
import { AUTH_FLOW_CONFIG, AuthflowConfig, LOGIN_PATH, PKEY_RURL, UserProfile } from './model';

/**
 * Resolver function for fetching and caching user profile data during route navigation.
 * Used by Angular Router to resolve data before activating a route.
 * @param route - Current route snapshot containing route parameters
 * @param state - Router state snapshot with navigation info
 * @returns Observable that resolves to the user profile or null on error
 */
export const profileResolveFn: ResolveFn<UserProfile> = (
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
): Observable<UserProfile> => {
    const us = inject(UserService);
    const ts = inject(TransferState);
    const ps = inject(PlatformService);

    // Server-side rendering path
    if (ps.isServerSide) {
        return us.profile().pipe(
            take(1),
            tap(profile => {
                // Store profile in TransferState to avoid duplicate requests on a client
                ts.set(PROFILE_TRANSFER_TOKEN, profile);
            }),
            catchError(err => of(null)) // Return null if profile fetch fails
        );
    }
    // Client-side rendering path
    else {
        // Try to retrieve the profile from TransferState (if provided by server)
        const profile = ts.get(PROFILE_TRANSFER_TOKEN, null) as UserProfile;

        if (profile) {
            // If a profile exists in TransferState, use it without making an API call
            us.curUser$.next(profile);
            return of(profile);
        }

        // If the profile wasn't in TransferState, fetch it from API
        return us.profile().pipe(
            take(1),
            tap(profile => us.curUser$.next(profile)), // Update current user in UserService
            catchError(err => of(null)) // Return null if profile fetch fails
        );
    }
};

export const redirectOnLoggedInFn = async (): Promise<boolean> => {
    const authflowConf = inject(AUTH_FLOW_CONFIG);
    const userService = inject(UserService);
    const activatedRoute = inject(ActivatedRoute);
    const queryParams = await firstValueFrom(activatedRoute.queryParams);

    const user = userService.curUser$.value;
    if (user) {
        if (queryParams?.[PKEY_RURL]) window.location.href = atob(queryParams[PKEY_RURL]);
        else window.location.href = authflowConf.defaultReturnUrl;
        return false;
    }

    return true;
};

export const loginRequiredFn = (): Observable<boolean> => {
    const authflowConf = inject(AUTH_FLOW_CONFIG);
    const userService = inject(UserService);
    return requireLogin(userService, authflowConf);
};

export const requireLogin = (
    userService: UserService,
    authflowConf: AuthflowConfig,
    actionName?: string
): Observable<boolean> => {
    return userService.profile().pipe(
        map(() => true),
        catchError(err => {
            if (err?.status === 401) {
                const rInfo = {
                    actionName: actionName || '',
                    rURL: window.location.pathname,
                };
                const returnUrl = encodeReturnUrl(rInfo);
                window.location.href = `${LOGIN_PATH}?${PKEY_RURL}=${returnUrl}`;
            }

            return of(false);
        })
    );
};

export const createRequireLoginFn = (actionName?: string) => {
    return (): Observable<boolean> => {
        const authflowConf = inject(AUTH_FLOW_CONFIG);
        const userService = inject(UserService);

        return requireLogin(userService, authflowConf, actionName);
    };
};
