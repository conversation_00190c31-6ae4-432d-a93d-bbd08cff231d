import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ElementRef, Input, OnInit } from '@angular/core';
import { SocialLoginService } from '../../social-login.service';
import { take } from 'rxjs';

@Component({
    selector: 'app-google-login-button',
    standalone: true,
    imports: [CommonModule],
    template: `<span></span>`,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoogleLoginButtonComponent implements OnInit {
    @Input()
    type: 'icon' | 'standard' = 'icon';

    @Input()
    size: 'small' | 'medium' | 'large' = 'large';

    @Input()
    text: 'signin_with' | 'signup_with' | 'continue_with' = 'signin_with';

    @Input()
    shape: 'square' | 'circle' | 'pill' | 'rectangular' = 'circle';

    @Input()
    theme: 'outline' | 'filled_blue' | 'filled_black' = 'outline';

    @Input()
    logo_alignment: 'left' | 'center' = 'center';

    @Input()
    width: number = 0;

    @Input()
    locale: string = 'vi_VN';

    constructor(
        private el: ElementRef,
        private socialLoginService: SocialLoginService
    ) {}

    ngOnInit(): void {
        this.socialLoginService.initState.pipe(take(1)).subscribe(() => {
            Promise.resolve(this.width).then(value => {
                if (value > 400 || (value < 200 && value != 0)) {
                    Promise.reject(
                        'Please note .. max-width 400 , min-width 200 ' +
                            '(https://developers.google.com/identity/gsi/web/tools/configurator)'
                    );
                } else {
                    google.accounts.id.renderButton(this.el.nativeElement, {
                        type: this.type,
                        size: this.size,
                        text: this.text,
                        width: this.width,
                        shape: this.shape,
                        theme: this.theme,
                        logo_alignment: this.logo_alignment,
                        locale: this.locale,
                    });
                }
            });
        });
    }
}
