<app-google-login-button *ngIf="providerId === 'GOOGLE'; else generalProviderBtn"></app-google-login-button>

<ng-template #generalProviderBtn>
    <a class="" (click)="openLoginPopup()">
        <img class="h-[40px] shadow-[0px_1px_2px_0_rgb(var(--BW1)/0.25)]" [ngSrc]="logoSrc" width="40" height="40"
    /></a>
</ng-template>

<ng-template #providerLoadError>
    <div class="vi-popup-container">
        <div class="pt-[15px] pb-[15px] gap-[10px] flex flex-col">
            <div class="flex justify-center">
                <img class="w-[160px] h-[100px]" ngSrc="assets/img/warning.svg" height="100" width="160" />
            </div>
            <div class="vi-popup-message text-center">
                <div><PERSON>h<PERSON>ng thể đăng nhập bằng {{ providerName }}</div>
                <div>khi <PERSON>n danh trên trình duyệt này.</div>
                <div>Vui lòng đăng nhập trình duyệt và thử lại!</div>
            </div>
            <div class="flex justify-center items-center">
                <button class="vi-btn vi-btn-small vi-btn-focus" [mat-dialog-close]="true">Đã hiểu</button>
            </div>
        </div>
    </div>
</ng-template>
