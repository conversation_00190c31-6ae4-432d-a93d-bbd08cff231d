import { CommonModule, NgOptimizedImage } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input, TemplateRef, ViewChild } from '@angular/core';
import { SocialLoginService } from '../social-login.service';
import { GoogleLoginButtonComponent } from './google-login-button/google-login-button.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';

@Component({
    selector: 'app-social-login-button',
    standalone: true,
    imports: [CommonModule, GoogleLoginButtonComponent, MatDialogModule, NgOptimizedImage],
    templateUrl: './social-login-button.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialLoginButtonComponent {
    @Input()
    providerId: string = '';

    @Input()
    logoSrc: string = '';

    @ViewChild('providerLoadError', { read: TemplateRef })
    providerLoadErrorRef: TemplateRef<any>;

    constructor(
        private socialLoginService: SocialLoginService,
        private dialog: MatDialog
    ) {}

    get providerName(): string {
        return this.providerId ? this.socialLoginService.getProvider(this.providerId).providerName : '';
    }

    openLoginPopup() {
        if (!this.providerId) throw Error('please provide a valid provider id');

        const provider = this.socialLoginService.getProvider(this.providerId);

        if (provider.initSucceed) {
            provider.openLoginPopup();
        } else {
            this.dialog.open(this.providerLoadErrorRef);
        }
    }
}
