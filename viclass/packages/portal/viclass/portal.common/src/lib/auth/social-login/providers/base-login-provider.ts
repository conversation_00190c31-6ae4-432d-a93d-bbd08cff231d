import { EventEmitter } from '@angular/core';
import { Observable } from 'rxjs';
import { LoginProvider } from '../entities/login-provider';
import { SocialUser } from '../entities/social-user';

/**
 * Base for all provider. All implementation will stay hidden inside the `portal.common`.
 * Other portals will only watching the interface's asObservable for any login changes
 *
 * @export
 * @abstract
 * @class BaseLoginProvider
 * @implements {LoginProvider}
 */
export abstract class BaseLoginProvider implements LoginProvider {
    protected readonly socialUser = new EventEmitter<SocialUser>();

    protected _initSucceed = false;

    constructor() {}

    get initSucceed(): boolean {
        return this._initSucceed;
    }

    abstract get providerName(): string;

    /**
     *  Do not reject the promise here so the Promise.all waits for all providers to be ready,
     *  even if some are failed to initialize.
     */
    abstract initialize(onError: (err: any) => void): Promise<void>;

    abstract openLoginPopup(options?: object): void;

    asObservable(): Observable<SocialUser> {
        return this.socialUser.asObservable();
    }

    protected loadScript(providerId: string, src: string): Promise<void> {
        return new Promise((resolve, reject) => {
            // get document if platform is only browser
            if (typeof document !== 'undefined' && !document.getElementById('sdk-' + providerId)) {
                const signInJS = document.createElement('script');
                signInJS.id = 'sdk-' + providerId;
                signInJS.async = true;
                signInJS.src = src;
                signInJS.onload = () => resolve();
                signInJS.onerror = err => reject(err);

                document.head.appendChild(signInJS);
            }
        });
    }
}
