import { BaseLoginProvider } from './base-login-provider';
import { SocialUser } from '../entities/social-user';

declare let FB: any;

export class FacebookLoginProvider extends BaseLoginProvider {
    public static readonly PROVIDER_ID: string = 'FACEBOOK';

    private requestOptions = {
        scope: 'email,public_profile',
        locale: 'vi_VN',
        version: 'v22.0',
    };

    constructor(
        private clientId: string,
        initOptions: Object = {}
    ) {
        super();

        this.requestOptions = {
            ...this.requestOptions,
            ...initOptions,
        };
    }

    override get providerName(): string {
        return 'Facebook';
    }

    override async initialize(onError: (err: any) => void): Promise<void> {
        try {
            await this.loadScript(
                FacebookLoginProvider.PROVIDER_ID,
                `https://connect.facebook.net/${this.requestOptions.locale}/sdk.js`
            );
            FB.init({
                appId: this.clientId,
                autoLogAppEvents: true,
                version: this.requestOptions.version,
            });

            this._initSucceed = true;
        } catch (err) {
            onError(err);
        }
    }

    override openLoginPopup(signInOptions?: any): void {
        if (!this.initSucceed) return;

        const options = { ...this.requestOptions, ...signInOptions };

        FB.login((response: any) => {
            if (response.authResponse) {
                const authResponse = response.authResponse;
                const user: SocialUser = {
                    provider: FacebookLoginProvider.PROVIDER_ID,
                    authToken: authResponse.accessToken,
                };

                this.socialUser.emit(user);
            } else {
                console.log('[FACEBOOK]: User cancelled login or did not fully authorize.');
            }
        }, options);
    }
}
