import { SocialUser } from '../entities/social-user';
import { BaseLoginProvider } from './base-login-provider';

export interface GoogleInitOptions {
    /**
     * list of permission scopes to grant in case we request an access token
     */
    scopes?: string | string[];
    /**
     * This attribute sets the DOM ID of the container element. If it's not set, the One Tap prompt is displayed in the top-right corner of the window.
     */
    prompt_parent_id?: string;
}

export class GoogleLoginProvider extends BaseLoginProvider {
    public static readonly PROVIDER_ID: string = 'GOOGLE';

    constructor(
        private clientId: string,
        private readonly initOptions?: GoogleInitOptions
    ) {
        super();
    }

    override get providerName(): string {
        return 'Google';
    }

    override async initialize(onError: (err: any) => void): Promise<void> {
        try {
            await this.loadScript(GoogleLoginProvider.PROVIDER_ID, 'https://accounts.google.com/gsi/client');

            google.accounts.id.initialize({
                client_id: this.clientId,
                auto_select: false,
                callback: ({ credential }) => {
                    if (credential) {
                        const socialUser = this.createSocialUser(credential);
                        this.socialUser.emit(socialUser);
                    }
                },
                prompt_parent_id: this.initOptions?.prompt_parent_id,
                ux_mode: 'popup',
                cancel_on_tap_outside: false,
                itp_support: true,
            });

            this._initSucceed = true;
        } catch (err) {
            onError(err);
        }
    }

    override openLoginPopup(): void {
        throw Error(
            `For Google, don't call this direcly as it will be handled by the button rendered by Google library`
        );
    }

    private createSocialUser(idToken: string) {
        const user: SocialUser = {
            provider: GoogleLoginProvider.PROVIDER_ID,
            authToken: idToken,
        };
        return user;
    }
}
