import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FacebookLoginProvider } from './providers/facebook-login-provider';
import { GoogleLoginProvider } from './providers/google-login-provider';
import { SocialLoginService, SocialLoginServiceConfig } from './social-login.service';
import { environment } from '../../environment';

@NgModule({
    imports: [CommonModule],
    providers: [
        SocialLoginService,
        {
            provide: 'SocialLoginServiceConfig',
            useValue: {
                providers: [
                    {
                        id: GoogleLoginProvider.PROVIDER_ID,
                        provider: new GoogleLoginProvider(environment.socialLoginConfig.google.clientId),
                    },
                    {
                        id: FacebookLoginProvider.PROVIDER_ID,
                        provider: new FacebookLoginProvider(environment.socialLoginConfig.facebook.clientId),
                    },
                ],
            } as SocialLoginServiceConfig,
        },
    ],
})
export class SocialLoginModule {}
