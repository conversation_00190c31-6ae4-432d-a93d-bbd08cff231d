import { Inject, Injectable, Injector, Type } from '@angular/core';
import { AsyncSubject, BehaviorSubject, Observable } from 'rxjs';
import { LoginProvider } from './entities/login-provider';
import { SocialUser } from './entities/social-user';
import { FacebookLoginProvider } from './providers/facebook-login-provider';
import { GoogleLoginProvider } from './providers/google-login-provider';

export const SOCIAL_LOGIN_PROVIDERS = {
    GOOGLE: GoogleLoginProvider.PROVIDER_ID,
    FACEBOOK: FacebookLoginProvider.PROVIDER_ID,
};

/**
 * An interface to define the shape of the service configuration options.
 */
export interface SocialLoginServiceConfig {
    providers: { id: string; provider: LoginProvider | Type<LoginProvider> }[];
    onError?: (error: any) => any;
}

/**
 * Encapsulating the social login functionality.
 *
 * @dynamic
 */
@Injectable({ providedIn: 'root' })
export class SocialLoginService {
    private static readonly ERR_LOGIN_PROVIDER_NOT_FOUND = 'Login provider not found';
    private static readonly ERR_NOT_INITIALIZED = 'Login providers not ready yet. Are there errors on your console?';

    readonly socialUser$ = new BehaviorSubject<SocialUser>(null);

    private providers: Map<string, LoginProvider> = new Map();

    private initialized = false;
    private _initState: AsyncSubject<boolean> = new AsyncSubject();

    /** An `Observable` to communicate the readiness of the service and associated login providers */
    get initState(): Observable<boolean> {
        return this._initState.asObservable();
    }

    /**
     * @param config A `SocialLoginServiceConfig` object or a `Promise` that resolves to a `SocialLoginServiceConfig` object
     */
    constructor(
        @Inject('SocialLoginServiceConfig')
        config: SocialLoginServiceConfig | Promise<SocialLoginServiceConfig>,
        private readonly _injector: Injector
    ) {
        if (config instanceof Promise) {
            config.then((config: SocialLoginServiceConfig) => {
                this.initialize(config);
            });
        } else {
            this.initialize(config);
        }
    }

    private initialize(config: SocialLoginServiceConfig) {
        const { onError = console.error } = config;

        config.providers.forEach(item => {
            this.providers.set(
                item.id,
                'prototype' in item.provider ? this._injector.get(item.provider) : item.provider
            );
        });

        Promise.all(Array.from(this.providers.values()).map(provider => provider.initialize(onError)))
            .then(() => {
                this.providers.forEach((provider: LoginProvider, providerId: string) => {
                    if (!provider.initSucceed) return;

                    provider.asObservable().subscribe(user => {
                        user.provider = providerId;
                        this.socialUser$.next(user);
                    });
                });
            })
            .catch(error => {
                onError(error);
            })
            .finally(() => {
                this.initialized = true;
                this._initState.next(this.initialized);
                this._initState.complete();
            });
    }

    getProvider(providerId: string): LoginProvider {
        if (!this.initialized) {
            throw new Error(SocialLoginService.ERR_NOT_INITIALIZED);
        }

        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new Error(SocialLoginService.ERR_LOGIN_PROVIDER_NOT_FOUND);
        }

        return provider;
    }
}
