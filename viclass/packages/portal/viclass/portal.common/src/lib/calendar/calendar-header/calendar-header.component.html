<div class="flex items-center content-center py-[10px]">
    <div class="flex">
        <button class="vi-btn" (click)="previousClicked('year')" title="Năm trước">
            <span class="vcon-general vcon_double-arrow_back"></span>
        </button>
        <button class="vi-btn" (click)="previousClicked('month')" title="Tháng trước">
            <span class="vcon-general vcon_arrow_back"></span>
        </button>
    </div>
    <div class="flex-1 font-bold text-center" #dateContainer>
        <!-- Period label view when not selecting -->
        <span *ngIf="!isSelecting" (click)="toggleSelection($event)" class="cursor-pointer py-2">
            {{ periodLabel }}
        </span>
        <!-- Month and Year selectors when selecting -->
        <div *ngIf="isSelecting" class="flex justify-center items-center gap-1 calendar-header-month-year-selectors">
            <select
                [ngModel]="selectedMonth"
                (change)="onMonthChange($event)"
                class="w-auto border-BW1 border rounded-md p-1">
                <option *ngFor="let month of months" [value]="month.value">{{ month.label }}</option>
            </select>
            <span>/</span>
            <select
                [ngModel]="selectedYear"
                (change)="onYearChange($event)"
                class="w-auto border-BW1 border rounded-md p-1">
                <option *ngFor="let year of years" [value]="year">{{ year }}</option>
            </select>
        </div>
    </div>
    <div class="flex">
        <button class="vi-btn" (click)="nextClicked('month')" title="Tháng sau">
            <span class="vcon-general vcon_arrow_next"></span>
        </button>
        <button class="vi-btn" (click)="nextClicked('year')" title="Năm sau">
            <span class="vcon-general vcon_double-arrow_next"></span>
        </button>
    </div>
</div>
