import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    HostListener,
    Inject,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DateAdapter, MAT_DATE_FORMATS, MatDateFormats } from '@angular/material/core';
import { MatCalendar } from '@angular/material/datepicker';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    value: i,
    label: `${(i + 1).toString().padStart(2, '0')}`,
}));

const currentYear = new Date().getFullYear();
const yearOptions = Array.from({ length: 101 }, (_, i) => currentYear - i);

@Component({
    templateUrl: 'calendar-header.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, FormsModule],
})
export class CalendarHeaderComponent<D> implements OnInit, OnDestroy {
    private _destroyed = new Subject<void>();
    isSelecting = false;

    months: { value: number; label: string }[] = monthOptions;
    years: number[] = yearOptions;
    selectedMonth: number = 0;
    selectedYear: number = 0;

    @ViewChild('dateContainer') dateContainer!: ElementRef;

    constructor(
        private _calendar: MatCalendar<D>,
        private _dateAdapter: DateAdapter<D>,
        @Inject(MAT_DATE_FORMATS) private _dateFormats: MatDateFormats,
        private cdr: ChangeDetectorRef
    ) {
        _calendar.stateChanges.pipe(takeUntil(this._destroyed)).subscribe(() => cdr.markForCheck());
    }

    @HostListener('document:click', ['$event'])
    onClick(event: MouseEvent) {
        if (this.isSelecting && !this.dateContainer.nativeElement.contains(event.target)) {
            this.isSelecting = false;
            this.cdr.markForCheck();
        }
    }

    ngOnInit() {
        this.updateSelectedDate();
    }

    ngOnDestroy() {
        this._destroyed.next();
        this._destroyed.complete();
    }

    private updateSelectedDate() {
        this.selectedMonth = this._dateAdapter.getMonth(this._calendar.activeDate);
        this.selectedYear = this._dateAdapter.getYear(this._calendar.activeDate);
    }

    get periodLabel() {
        return this._dateAdapter.format(this._calendar.activeDate, this._dateFormats.display.monthYearLabel);
    }

    previousClicked(mode: 'month' | 'year') {
        this._calendar.activeDate =
            mode === 'month'
                ? this._dateAdapter.addCalendarMonths(this._calendar.activeDate, -1)
                : this._dateAdapter.addCalendarYears(this._calendar.activeDate, -1);
    }

    nextClicked(mode: 'month' | 'year') {
        this._calendar.activeDate =
            mode === 'month'
                ? this._dateAdapter.addCalendarMonths(this._calendar.activeDate, 1)
                : this._dateAdapter.addCalendarYears(this._calendar.activeDate, 1);
        this.updateSelectedDate();
    }

    toggleSelection(event: MouseEvent) {
        event.stopPropagation(); // Prevent click from propagating to document
        this.isSelecting = !this.isSelecting;
        if (this.isSelecting) {
            this.updateSelectedDate();
        }
    }

    onMonthChange(event: Event) {
        const selectElement = event.target as HTMLSelectElement;
        const month = parseInt(selectElement.value, 10);
        this.selectedMonth = month;
        this._calendar.activeDate = this._dateAdapter.createDate(
            this.selectedYear,
            month,
            this._dateAdapter.getDate(this._calendar.activeDate)
        );
    }

    onYearChange(event: Event) {
        const selectElement = event.target as HTMLSelectElement;
        const year = parseInt(selectElement.value, 10);
        this.selectedYear = year;
        this._calendar.activeDate = this._dateAdapter.createDate(
            year,
            this.selectedMonth,
            this._dateAdapter.getDate(this._calendar.activeDate)
        );
    }
}
