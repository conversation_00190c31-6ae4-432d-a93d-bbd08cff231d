import { CommonModule, DatePipe } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    OnInit,
    Output,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatMomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatCalendarCellClassFunction, MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import moment from 'moment';
import { CalendarHeaderComponent } from '../calendar-header/calendar-header.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput, MatInputModule } from '@angular/material/input';

@Component({
    selector: 'common-new-date-time-picker',
    templateUrl: './new-date-time-picker.component.html',
    styleUrls: ['../custom-date-picker.component.scss'],
    imports: [
        CommonModule,
        ReactiveFormsModule,
        MatMomentDateModule,
        MatDatepickerModule,
        MatFormFieldModule,
        MatInputModule,
        FormsModule,
    ],
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: MAT_DATE_LOCALE,
            useValue: 'vi',
        },
        {
            provide: DateAdapter,
            useClass: MomentDateAdapter,
            deps: [MAT_DATE_LOCALE],
        },
    ],
    encapsulation: ViewEncapsulation.None,
})
export class NewDateTimePickerComponent implements OnInit, AfterViewInit {
    calendarHeader = CalendarHeaderComponent;

    @Input() inputClasses: string = '';
    @Input() isDisabled: boolean = false;
    @Input() date: Date = null;

    @Output() dateChange = new EventEmitter<Date>();

    @ViewChild('picker', { read: MatDatepicker<any> })
    picker: MatDatepicker<any>;
    @ViewChild('input', { read: MatInput })
    input: MatInput;

    constructor() {}

    ngOnInit() {}

    ngAfterViewInit() {
        this.input.value = this.date;
    }

    dateClass: MatCalendarCellClassFunction<moment.Moment> = (cellDate, view) => {
        // Only highligh dates inside the month view.
        if (view === 'month') {
            // for locale `vi`: Monday is 0 and Sunday is 6
            const isWeekend = cellDate.weekday() >= 5;

            return isWeekend ? 'weekend' : '';
        }

        return '';
    };

    focus(e) {
        e.preventDefault();
        e.target.blur();
        if (!this.isDisabled) this.picker.open();
    }

    reset() {
        this.date = null;
        this.input.value = null;
        this.dateChange.next(null);
        this.picker?.close();
    }

    change(e) {
        this.date = e?.value?.toDate() ?? null;
        this.input.value = this.date;
        this.dateChange.next(this.date || null);
    }
}
