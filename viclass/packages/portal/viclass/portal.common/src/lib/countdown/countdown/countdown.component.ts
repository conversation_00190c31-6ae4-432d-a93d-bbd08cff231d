import { CommonModule } from '@angular/common';
import {
    Component,
    OnInit,
    Input,
    Output,
    EventEmitter,
    SimpleChanges,
    ChangeDetectionStrategy,
    OnDestroy,
} from '@angular/core';
import { BehaviorSubject, Subscription, timer } from 'rxjs';

const padTime = (timeVal: number) => {
    return String(timeVal).padStart(2, '0');
};

@Component({
    standalone: true,
    imports: [CommonModule],
    selector: 'countdown',
    templateUrl: './countdown.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CountDownComponent implements OnInit, OnDestroy {
    @Input() fromTime: number;
    @Output() action = new EventEmitter();

    constructor() {}

    readonly toNextTime$ = new BehaviorSubject<string>('00:00');

    private subscription: Subscription;

    private countdown() {
        if (Math.floor(this.fromTime) <= 0) return;

        // cancel any unfinished old timer subscription for the new one
        this.subscription?.unsubscribe();

        this.subscription = timer(0, 1000).subscribe(val => {
            const remainingTime = Math.floor(this.fromTime) - val;
            const minutes = Math.floor(remainingTime / 60);
            const seconds = Math.floor(remainingTime % 60);

            if (remainingTime > 0) {
                this.toNextTime$.next(`${padTime(minutes)}:${padTime(seconds)}`);
            } else {
                this.toNextTime$.next('00:00');
                this.action.emit();

                this.subscription.unsubscribe();
                this.subscription = null;
            }
        });
    }

    ngOnInit() {
        this.countdown();
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['fromTime']) {
            this.countdown();
        }
    }
}
