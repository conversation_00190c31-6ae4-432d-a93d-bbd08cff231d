<div class="cropper-wrapper">
    <div class="loading-block" *ngIf="isLoading$ | async">
        <div spinner></div>
    </div>

    <div class="alert alert-warning" *ngIf="(loadError$ | async) && !!loadImageErrorText">
        {{ loadImageErrorText }}
    </div>

    <div
        class="cropper"
        [ngStyle]="{
            maxHeight: cropperOptions.maxContainerHeight ? cropperOptions.maxContainerHeight + 'px' : 'auto',
        }"
        [ngClass]="{
            'cropper-rounded': cropperOptions.roundedPreview,
        }">
        <img #image alt="image" [src]="imageUrl" (load)="imageLoaded($event)" (error)="imageLoadError($event)" />
    </div>
</div>
