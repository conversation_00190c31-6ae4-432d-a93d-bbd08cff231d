import { EditorType } from '@viclass/editor.core';

export const compulsoryConfSpecs = {
    specs: [
        {
            item: 'editor-ui-base-style',
            ui: true,
        },
        {
            item: 'common-tools-editor',
            ui: true,
        },
        {
            item: 'embed-coordinator',
            impl: true,
        },
    ],
};

export const zoomEditorConfSpecs = {
    specs: [
        {
            item: 'zoom-tools-editor',
            ui: true,
        },
    ],
};

export const freeDrawingEditorConfSpecs = {
    specs: [
        {
            item: 'free-drawing-editor',
            impl: {
                useCase: 'embed',
            },
            ui: true,
        },
    ],
};

export const geometryEditorConfSpecs = {
    specs: [
        {
            item: 'geometry-editor',
            impl: {
                useCase: 'embed',
            },
            ui: true,
        },
    ],
};

export const wordEditorConfSpecs = {
    specs: [
        {
            item: 'word-editor',
            impl: {
                settings: {
                    embedded: ['free-drawing-editor', 'geometry-editor', 'math-editor', 'magh-editor'],
                },
                useCase: 'embed',
            },
            ui: {
                settings: {
                    embedded: ['free-drawing-editor', 'geometry-editor', 'math-editor', 'magh-editor'],
                },
            },
        },
    ],
};

export const mathEditorConfSpecs = {
    specs: [
        {
            item: 'math-editor',
            impl: {
                useCase: 'embed',
            },
            ui: true,
        },
    ],
};

export const mathGraphEditorConfSpecs = {
    specs: [
        {
            item: 'magh-editor',
            impl: {
                useCase: 'embed',
            },
            ui: true,
        },
    ],
};

export const getEditorConfSpecs = (editorType: EditorType) => {
    switch (editorType) {
        case 'FreeDrawingEditor':
            return freeDrawingEditorConfSpecs;
        case 'GeometryEditor':
            return geometryEditorConfSpecs;
        case 'WordEditor':
            return wordEditorConfSpecs;
        case 'MathEditor':
            return mathEditorConfSpecs;
        case 'MathGraphEditor':
            return mathGraphEditorConfSpecs;
        case 'ZoomToolsEditor':
            return zoomEditorConfSpecs;
        default:
            throw new Error('Invalid editor type');
    }
};
