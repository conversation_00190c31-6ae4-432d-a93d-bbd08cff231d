import { HttpClient } from '@angular/common/http';
import { Injectable, OnDestroy } from '@angular/core';
import { MFEConfResponse } from '@viclass/config.server';
import { EmbedCoordinator, EmbedCoordinatorConfig } from '@viclass/editor.coordinator/embed';
import { EditorLookup, EditorType, ModuleLookup, ModuleLookupWithSettings } from '@viclass/editor.core';
import { EditorUILookup } from '@viclass/editorui.loader';
import { firstValueFrom, Subscription } from 'rxjs';
import { commonErrorHandlerEmitter, CommonErrorHandlerListener } from '../error-handler';
import { UserService } from '../services';
import { compulsoryConfSpecs, getEditorConfSpecs } from './document.confspecs';

export type Confspecs = { specs: any[] };
export type Confdescs = { descs: any[] };
export const availableEditors: EditorType[] = [
    'FreeDrawingEditor',
    'GeometryEditor',
    'WordEditor',
    'MathEditor',
    'MathGraphEditor',
];

/**
 * Information to look up UI Modules
 * The viewport manager component offer a few type of UI modules to be rendered
 * - editor ui
 * - common tool ui
 * - zoom tool ui
 */
export type ViewportUIModuleConfig = {
    baseStyleModuleConf: ModuleLookup;
    editorUIConf?: EditorUILookup;
    commonToolUIConf: EditorUILookup;
    zoomToolUIConf?: EditorUILookup;
};

@Injectable()
export class DocumentService implements OnDestroy {
    private _coord: EmbedCoordinator;
    private mfeConf: MFEConfResponse;
    private coordSubscription: Subscription;
    private curConfSpecs: Confspecs;

    get baseStyleModuleConf() {
        return this.mfeConf?.descs?.find(v => v.item == 'EditorUIBaseStyle').ui as ModuleLookup;
    }

    get zoomUIConf() {
        return this.mfeConf?.descs?.find(v => v.item == 'ZoomToolsEditor').ui as EditorUILookup;
    }

    get edUIConf() {
        return this.mfeConf?.descs
            ?.filter(v => availableEditors.includes(v.item as EditorType))
            ?.map(desc => desc.ui as EditorUILookup);
    }

    get commonUIConf() {
        return this.mfeConf?.descs?.find(v => v.item == 'CommonToolsEditor').ui as EditorUILookup;
    }

    get coord() {
        return this._coord;
    }

    constructor(
        private http: HttpClient,
        private readonly commonErrorHandlerListener: CommonErrorHandlerListener,
        private readonly userService: UserService
    ) {}

    setCoord(coord: EmbedCoordinator) {
        this._coord = coord;
    }

    private newConfspecs(): Confspecs {
        return { specs: [] };
    }

    private mergeConfspecs(confspec1: Confspecs, confspec2: Confspecs) {
        const obj = {};
        confspec1.specs.push(...confspec2.specs);
        confspec1.specs.forEach(spec => (obj[spec.item] = spec));
        confspec1.specs = Object.values(obj);
    }

    private mergeConfdescs(confspec1: Confdescs, confspec2: Confdescs) {
        const obj = {};
        confspec1.descs.push(...confspec2.descs);
        confspec1.descs.forEach(desc => (obj[desc.item] = desc));
        confspec1.descs = Object.values(obj);
    }

    isEdConfLoaded(edType: EditorType) {
        return !!this.mfeConf?.descs?.find(v => v.item == edType);
    }

    canZoomConfspec(edType: EditorType) {
        return edType != 'WordEditor' && !this.isEdConfLoaded('ZoomToolsEditor');
    }

    async retrieve(e: any, addEditors: EditorType[]) {
        const confspecs = this.curConfSpecs || this.newConfspecs();
        if (!this.coord) this.mergeConfspecs(confspecs, compulsoryConfSpecs);
        for (const edType of addEditors)
            if (!this.isEdConfLoaded(edType)) this.mergeConfspecs(confspecs, getEditorConfSpecs(edType));

        if (!confspecs.specs.length) return;

        this.curConfSpecs = confspecs;

        await firstValueFrom(this.http.post<MFEConfResponse>(`/conf/editors/${e}`, confspecs)).then(newMfeConf => {
            this.coord ? this.mergeConfdescs(this.mfeConf, newMfeConf) : (this.mfeConf = newMfeConf);
        });
    }

    async createOrUpdateCoordinator() {
        if (!this.coord) await this.createCoordinator();
        else await this.updateCoordinator();
    }

    async createCoordinator(mfeConf: MFEConfResponse = null) {
        if (!mfeConf) mfeConf = this.mfeConf;

        const edLookups = mfeConf.descs
            .filter(editorConf => availableEditors.includes(editorConf.item as EditorType))
            .map(desc => desc.impl as EditorLookup);
        const lookup = (() => {
            const descs = mfeConf.descs.filter(editorConf => editorConf.item == 'EmbedCoordinator');
            if (!descs || descs.length == 0) throw new Error('Unable to find the embed coordinator module lookup');
            return descs[0].impl as ModuleLookupWithSettings;
        })();
        const partialConfig = JSON.parse(JSON.stringify(lookup.settings)) as Partial<EmbedCoordinatorConfig>;
        const embedConfig: EmbedCoordinatorConfig = {
            id: 'embed-coord',
            editorTypeMapping: partialConfig.editorTypeMapping,
            edLookups: edLookups,
            viewports: partialConfig.viewports,
            tools: ['history'],
            imageProxyPrefix: partialConfig.imageProxyPrefix,
        };

        const coord = new EmbedCoordinator(embedConfig, commonErrorHandlerEmitter);
        coord.userCtxGetter = () => {
            const currUser = this.userService.curUser$.value;
            return {
                username: currUser?.username,
                avatarUrl: currUser?.avatarUrl,
                userId: currUser?.id,
            };
        };

        await coord.initialize();
        await coord.start();

        this.setCoord(coord);
    }

    async updateCoordinator(mfeConf: MFEConfResponse = null): Promise<EditorType[]> {
        if (!mfeConf) mfeConf = this.mfeConf;

        const edLookups = mfeConf.descs
            .filter(editorConf => availableEditors.includes(editorConf.item as EditorType))
            .map(desc => desc.impl as EditorLookup);
        const coord = this.coord as EmbedCoordinator;
        const addEditorPromises: Promise<EditorType>[] = [];

        for (const edLookup of edLookups) {
            if (!coord.editorByType(edLookup.editorType)) addEditorPromises.push(coord.addEditor(edLookup));
        }

        return await Promise.all(addEditorPromises);
    }

    generateEmbedViewportUIConfig(edType: EditorType, editorUI: boolean = true): ViewportUIModuleConfig {
        const editorUIConf = this.edUIConf.filter(v => v.editorType == edType)[0];
        if (edType != 'WordEditor') {
            return {
                // this is a bit hacky at the moment since we only have word editor that are not using zoom tool.
                baseStyleModuleConf: this.baseStyleModuleConf,
                commonToolUIConf: this.commonUIConf,
                zoomToolUIConf: this.zoomUIConf,
                editorUIConf: editorUI ? editorUIConf : undefined,
            };
        } else
            return {
                baseStyleModuleConf: this.baseStyleModuleConf,
                commonToolUIConf: this.commonUIConf,
                editorUIConf: editorUIConf,
            };
    }

    isValidEditor(edType: EditorType): boolean {
        return availableEditors.includes(edType);
    }

    ngOnDestroy() {
        this.coordSubscription?.unsubscribe();
    }
}
