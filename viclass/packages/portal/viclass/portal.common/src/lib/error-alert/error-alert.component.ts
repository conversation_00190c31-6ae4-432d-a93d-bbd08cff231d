import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ErrorHandlerShowMessageData } from './error-alert.interface';

@Component({
    selector: 'error-alert',
    templateUrl: './error-alert.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule],
})
export class ErrorAlertComponent {
    constructor(
        public dialogRef: MatDialogRef<ErrorAlertComponent>,
        @Inject(MAT_DIALOG_DATA)
        public data: ErrorHandlerShowMessageData
    ) {}

    close() {
        this.dialogRef.close();
    }

    closeWithReloadAction() {
        this.dialogRef.close({ reload: true });
    }

    closeWithRedirectHome() {
        this.dialogRef.close({ redirectHome: true });
    }
}
