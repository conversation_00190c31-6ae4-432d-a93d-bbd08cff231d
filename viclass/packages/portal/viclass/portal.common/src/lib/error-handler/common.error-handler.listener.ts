import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
    CriticalDocumentAPIErr,
    CriticalErr,
    DefaultErrorHandlerListener,
    ViErr,
    ViErrEventData,
} from '@viclass/editor.core';
import { finalize, take } from 'rxjs';
import { ErrorAlertComponent, ErrorHandlerShowMessageActions, ErrorHandlerShowMessageData } from '../error-alert';

@Injectable({ providedIn: 'root' })
export class CommonErrorHandlerListener extends DefaultErrorHandlerListener {
    protected isDialogOpen = false;

    constructor(protected dialog: MatDialog) {
        super();
    }

    override onEvent(data: ViErrEventData): ViErrEventData | Promise<ViErrEventData> {
        let message = 'Có lỗi xảy ra';
        const actions: ErrorHandlerShowMessageActions = {};

        if (data.state instanceof ViErr) message = data.state.message;
        if (data.state instanceof CriticalErr || data.state instanceof CriticalDocumentAPIErr) actions.reload = true;

        this.showMessage({ message, actions });

        return data;
    }

    protected showMessage(data: ErrorHandlerShowMessageData) {
        if (this.isDialogOpen) return;
        this.isDialogOpen = true;

        const dialog = this.dialog.open(ErrorAlertComponent, {
            width: '350px',
            height: '250px',
            autoFocus: false,
            data,
        });

        try {
            dialog
                .afterClosed()
                .pipe(
                    take(1),
                    finalize(() => (this.isDialogOpen = false))
                )
                .subscribe((params: ErrorHandlerShowMessageActions) => {
                    if (params?.reload) window.location.reload();
                });
        } catch {}
    }
}
