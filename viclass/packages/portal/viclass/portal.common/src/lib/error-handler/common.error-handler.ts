import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ViErr, ViErrEventData, ViErrEventEmitter } from '@viclass/editor.core';
import { DocErr, LocalStorageErr } from './error';

export class CommonErrorHandler extends ErrorHandler {
    override handle(e: ViErrEventData): ViErrEventData | null {
        if (e.state instanceof LocalStorageErr || e.state instanceof DocErr || e.state instanceof ViErr) {
            this.emit(e);
            return null;
        }

        return e;
    }
}

export const commonErrorHandlerEmitter: ViErrEventEmitter = new ViErrEventEmitter();

export const commonErrorHandler = new CommonErrorHandler(commonErrorHandlerEmitter);
