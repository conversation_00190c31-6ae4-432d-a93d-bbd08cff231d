import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute } from '@angular/router';
import { PKEY_RURL } from '../auth';

@Component({
    selector: 'viclass-portal-internal-server-error',
    templateUrl: './internal-server-error.component.html',
    styleUrls: ['./internal-server-error.component.scss'],
    standalone: true,
    imports: [
        CommonModule,
        MatButtonModule, // Needed for mat-button
    ],
})
export class InternalServerErrorComponent implements OnInit {
    private returnUrl: string | null = null;

    constructor(private route: ActivatedRoute) {}

    ngOnInit(): void {
        // Get the _rURL query parameter if it exists
        this.route.queryParams.subscribe(params => {
            if (params && params[PKEY_RURL]) {
                this.returnUrl = params[PKEY_RURL];
            }
        });
    }

    retry(): void {
        if (this.returnUrl) {
            // If we have a return URL, decode it and navigate to it
            try {
                const decoded = atob(this.returnUrl);
                const rInfo = JSON.parse(decoded);
                window.location.href = rInfo.rURL;
                return;
            } catch (err) {
                console.warn('Invalid return URL');
            }
        }

        // If no return URL or decoding failed, just reload the current page
        window.location.reload();
    }
}
