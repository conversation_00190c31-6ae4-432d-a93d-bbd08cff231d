import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

@Component({
    selector: 'app-editor-loading',
    templateUrl: './editor-loading.component.html',
    standalone: true,
    imports: [CommonModule],
})
export class EditorLoadingComponent implements OnInit {
    @Input() loading: boolean;
    @Input() hasBackground: boolean = true;

    constructor() {}

    ngOnInit() {}
}
