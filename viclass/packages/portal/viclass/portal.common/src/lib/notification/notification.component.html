<div
    class="p-[10px] rounded-lg whitespace-nowrap border-2 border-BW7 flex items-center justify-center gap-[10px] max-w-[70vw] shadow-[0px_5px_20px_var(--TP1)]"
    [ngStyle]="{
        'background-color': colorMapping[status],
    }">
    <div class="w-[40px] h-[40px] rounded-md bg-BW7 flex items-center justify-center">
        <span [class]="'vcon-general ' + iconMapping[status]">
            <span class="path1"></span>
            <span class="path2"></span>
            <span class="path3"></span>
        </span>
    </div>
    <div class="flex flex-col items-start px-[20px]">
        <p class="font-bold">{{ titleMapping[status] }}</p>
        <p class="text-sm">{{ message }}</p>
    </div>
    <span
        class="cursor-pointer vcon-general vcon_delete hover:brightness-110 rounded-full p-1 transition-all"
        [ngStyle]="{
            'background-color': colorMapping[status],
        }"
        (click)="closeCb?.()"></span>
</div>
