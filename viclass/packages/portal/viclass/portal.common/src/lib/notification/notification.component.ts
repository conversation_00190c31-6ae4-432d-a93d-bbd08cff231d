import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { NotificationStatus } from './notification.service';

@Component({
    selector: 'notification',
    templateUrl: './notification.component.html',
})
export class NotificationComponent {
    constructor(private cdr: ChangeDetectorRef) {}

    protected readonly iconMapping = {
        success: 'vcon_sucess',
        info: 'vcon_info',
        warning: 'vcon_general_warning',
        error: 'vcon_error',
    } as const;

    protected readonly colorMapping = {
        success: 'rgb(var(--PAS4))',
        info: 'rgb(var(--P3))',
        warning: 'rgb(var(--PAS3))',
        error: 'rgb(var(--PAS7))',
    } as const;

    protected readonly titleMapping = {
        success: 'Thành công',
        info: 'Thông báo',
        warning: 'Cảnh báo',
        error: 'Lỗi',
    } as const;

    @Input() message: string;
    setMessage(message: string) {
        this.message = message;
        this.cdr.detectChanges();
    }

    @Input() status: NotificationStatus = 'info';
    setStatus(status: NotificationStatus) {
        this.status = status;
        this.cdr.detectChanges();
    }

    @Input() closeCb: () => void = () => {};
    setCloseCallback(closeCb: () => void) {
        this.closeCb = closeCb;
    }
}
