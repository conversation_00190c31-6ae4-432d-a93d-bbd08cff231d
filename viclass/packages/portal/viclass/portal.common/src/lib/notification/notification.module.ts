import { OverlayModule } from '@angular/cdk/overlay';
import { NgModule } from '@angular/core';
import { NotificationService } from './notification.service';
import { NotificationComponent } from './notification.component';
import { CommonModule } from '@angular/common';

@NgModule({
    imports: [CommonModule, OverlayModule],
    declarations: [NotificationComponent],
    providers: [NotificationService],
})
export class NotificationModule {}
