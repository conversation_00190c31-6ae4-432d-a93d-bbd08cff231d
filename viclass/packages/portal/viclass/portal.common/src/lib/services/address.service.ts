import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AddressServiceModule } from './api.service.module';

export interface AddressItem {
    wdCode: string;
    label: string;
}

@Injectable({
    providedIn: AddressServiceModule,
})
export class AddressService {
    private readonly apiEndpoint = 'https://query.wikidata.org/sparql';
    constructor(private http: HttpClient) {}

    getCountries(): Observable<AddressItem[]> {
        const sparqlQuery = `
            SELECT ?country ?countryLabel ?isoCode
            WHERE {
                ?country wdt:P31 wd:Q6256 .  # Instance of country
                ?country wdt:P297 ?isoCode . # ISO 3166-2 code
                SERVICE wikibase:label { bd:serviceParam wikibase:language "vi,en". }
            }
            ORDER BY ?countryLabel
        `;
        const params = {
            query: sparqlQuery,
            format: 'json',
        };

        return of([
            {
                wdCode: 'Q881',
                label: 'Việt Nam',
            },
        ]); // Temporary

        return this.http.get<any>(this.apiEndpoint, { params }).pipe(
            map(response => {
                const data: any = {};
                if (response && response.results && response.results.bindings) {
                    response.results.bindings.forEach(result => {
                        if (data[result.countryLabel.value] != undefined) return;
                        data[result.countryLabel.value] = <AddressItem>{
                            wdCode: result.country.value.replace('http://www.wikidata.org/entity/', ''),
                            label: result.countryLabel.value,
                        };
                    });
                }
                return Object.values(data) as AddressItem[];
            })
        );
    }

    getAddressLevelListByParentWdCode(parentWdCode: string, level: 1 | 2 | 3): Observable<AddressItem[]> {
        const levelTypeMapping = {
            1: 'Q10864048', // cities
            2: 'Q13220204', // districts
            3: 'Q13221722', // wards
        };

        const sparqlQuery = `
            SELECT DISTINCT ?division ?divisionLabel ${level === 1 ? '?divisionISOCode' : ''}
            WHERE {
                ?division wdt:P31/wdt:P279* wd:${levelTypeMapping[level]}.
                ?division wdt:P131 wd:${parentWdCode}.
                ${level === 1 ? '?division wdt:P300 ?divisionISOCode .' : ''} # ISO 3166-2 code of the division
                SERVICE wikibase:label { bd:serviceParam wikibase:language "vi,en". }
            }
            ORDER BY ?divisionLabel
        `;

        const params = {
            query: sparqlQuery,
            format: 'json',
        };

        return this.http.get<any>(this.apiEndpoint, { params }).pipe(
            map(response => {
                const data: any = {};
                if (response && response.results && response.results.bindings) {
                    response.results.bindings.forEach(result => {
                        if (
                            data[result.divisionLabel.value] != undefined ||
                            /^Q[0-9]+/.test(result.divisionLabel.value) // Remove wrong location name
                        )
                            return;
                        data[result.divisionLabel.value] = <AddressItem>{
                            wdCode: result.division.value.replace('http://www.wikidata.org/entity/', ''),
                            label: result.divisionLabel.value,
                        };
                    });
                }
                return Object.values(data) as AddressItem[];
            })
        );
    }
}
