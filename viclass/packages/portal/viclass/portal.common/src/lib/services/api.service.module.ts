import { HttpClient, HttpClientModule, HttpHeaders } from '@angular/common/http';
import { InjectionToken, NgModule } from '@angular/core';
import { UserProfile } from '../auth';

/**
 * Declare a list of server module
 */
export const USER_API: InjectionToken<string> = new InjectionToken<string>('_user_api');
export const MEMBER_API: InjectionToken<string> = new InjectionToken<string>('_member_api');
export const CLASSROOM_API: InjectionToken<string> = new InjectionToken<string>('_member_api');
export const DOCUMENT_API: InjectionToken<string> = new InjectionToken<string>('_document_api');
export const USER_PROFILE: InjectionToken<UserProfile> = new InjectionToken<UserProfile>('_user_profile');

@NgModule({
    imports: [HttpClientModule],
})
export class UserServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class SupportServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class AddressServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class LsRegistrationServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class ClassroomServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class LSessionServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class DocumentServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class ConfigServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class FileStoreServiceModule {}

@NgModule({
    imports: [HttpClientModule],
})
export class ShortURLServiceModule {}

// A function to create an RPCInvocation
export function createRPC<T>(factory: ControllerMethodFactory): RPCInvocation<T> {
    return (http: HttpClient, options?: any, ...params: any[]) => {
        const rpc = factory.apply(null, params);

        if (!options) options = {};

        if (!options.headers) options.headers = new HttpHeaders();
        options.headers = options.headers.append('Content-Type', 'application/json');

        if (!options.responseType) options.responseType = 'json';

        return http.request<T>(rpc.method, rpc.url, {
            observe: 'response',
            body: options.body,
            headers: options.headers,
            responseType: options.responseType,
            withCredentials: true,
        });
    };
}

export function createUploadRPC<T>(factory: ControllerMethodFactory): UploadRPCInvocation<T> {
    return (http: HttpClient, formData: FormData, options?: any, ...params: any[]) => {
        const rpc = factory.apply(null, params);

        if (!options) options = {};
        if (!options.responseType) options.responseType = 'json';

        return http.request<T>(rpc.method, rpc.url, {
            body: formData,
            observe: 'response',
            headers: options.headers,
            responseType: options.responseType,
            withCredentials: true,
        });
    };
}
