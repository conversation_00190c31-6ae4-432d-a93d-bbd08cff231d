import { HttpClient, HttpErrorResponse, HttpResponse, HttpStatusCode } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { DocumentId, EditorCoordinator, EditorType } from '@viclass/editor.core';
import { Observable, catchError, firstValueFrom, map, of, take } from 'rxjs';

import { PKEY_RURL, UserProfile } from '../auth';
import { TabType } from '../sharing-doc-dialog/model';
import { encodeReturnUrl } from '../utils';
import { createRPC } from './api.service.module';
import { FileStoreService } from './filestore.service';
import {
    DocumentInfoResponse,
    LoadSavedDocsOptions,
    LoadSavedDocsResponse,
    SourceType,
    UploadResponse,
    UploadTokenResponse,
} from './models';
import { DocInProfileMetadataController, DocOwnershipMetadataController } from './route/backend.route.definition';
import { UserService } from './user.service';

@Injectable({
    providedIn: 'root',
})
export class DocInProfileMetadataService {
    private readonly server = new DocInProfileMetadataController();
    private readonly metadataServer = new DocOwnershipMetadataController();

    constructor(
        private http: HttpClient,
        private userService: UserService,
        private fileStoreService: FileStoreService
    ) {}

    /**
     *  Load document metadata from server
     */
    public loadDocumentInfo(editorType: EditorType, globalId: string): Observable<DocumentInfoResponse | null> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };

        return createRPC<DocumentInfoResponse | null>(this.server.loadDocInfo)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body),
            catchError((err: HttpErrorResponse) => {
                switch (err.status) {
                    case HttpStatusCode.NotFound:
                        console.warn(`[${editorType}] Document ${globalId} not found`, err);
                        return of(null);
                    case HttpStatusCode.Forbidden:
                        console.warn(`[${editorType}] Document ${globalId} belong to another user`, err);
                        return of(null);
                    default:
                        throw err;
                }
            })
        );
    }

    /**
     *  Get latest doc created by current logged-in user with the provided editor type
     */
    public getDocsByCurrentUser(editorType: EditorType, limit: number = 1): Observable<DocumentInfoResponse[]> {
        const reqBody = {
            editorType: editorType,
            limit: limit,
        };

        return createRPC<DocumentInfoResponse[]>(this.server.docsByUser)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    /**
     *  Upload preview image and save document to profile.
     */
    async saveDocToProfile(
        docGlobalId: DocumentId,
        docName: string,
        previewBlob: Blob,
        edType: EditorType,
        source: SourceType,
        sourceDocId?: DocumentId
    ): Promise<DocumentInfoResponse> {
        try {
            const user = this.userService.curUser$.value;
            const uploadResult = await this.uploadPreviewImage(docGlobalId, previewBlob, source);
            return await this.saveToProfile(
                docGlobalId,
                docName,
                uploadResult.fileUrl,
                edType,
                source,
                user,
                0,
                sourceDocId
            );
        } catch (error) {
            throw new Error('Có lỗi xảy ra khi lưu tài liệu');
        }
    }

    /**
     *  Upload preview image to server
     */
    private async uploadPreviewImage(docGlobalId: DocumentId, blob: Blob, source: SourceType): Promise<UploadResponse> {
        const tokenResponse = await this.getPreviewImageUploadToken(docGlobalId, source);
        const formData = new FormData();

        const fileName = `preview_${docGlobalId}.png`;
        formData.set('file', blob, fileName);
        formData.set('uploadToken', tokenResponse.uploadToken);

        return firstValueFrom(this.fileStoreService.uploadFile(formData));
    }

    /**
     *  Get preview image upload token, will attempt to override the
     *  previous preview image of the same doc if possible
     */
    private getPreviewImageUploadToken(docGlobalId: DocumentId, source: SourceType): Promise<UploadTokenResponse> {
        const previewImgRequest = {
            docGlobalId,
            allowFileTypes: ['.png'],
            maxFileSize: 10 * 1024 * 1024, // 10MB
            metadataDocType: source,
        };
        return firstValueFrom(
            createRPC<UploadTokenResponse>(this.server.getDocPreviewUploadToken)(this.http, {
                body: JSON.stringify(previewImgRequest),
            }).pipe(
                take(1),
                map(res => res.body)
            )
        );
    }

    /**
     *  Save document information to the user's profile.
     */
    private saveToProfile(
        docGlobalId: DocumentId,
        docName: string,
        previewUrl: string,
        editorType: EditorType,
        source: SourceType,
        userProfile?: UserProfile,
        docLocalId: number = 0,
        sourceDocId?: DocumentId
    ): Promise<DocumentInfoResponse> {
        const reqBody = {
            docGlobalId: docGlobalId,
            editorType: editorType,
            details: {
                docGlobalId: docGlobalId,
                docLocalId: docLocalId,
                ownerUserId: userProfile.id,
                ownerUserName: userProfile.username,
                previewUrl,
                docName,
                source,
                sourceDocId,
            },
        };

        return firstValueFrom(
            createRPC<DocumentInfoResponse>(this.server.saveDocToProfile)(this.http, {
                body: JSON.stringify(reqBody),
            }).pipe(
                take(1),
                map(res => res.body)
            )
        );
    }

    /**
     * Redirects the user to the login page with the provided document ID,
     * editor type, and tab type in the return URL.
     *
     * User this when user want to continue action with popup.
     *
     *
     * @param {EditorType} editorType - The editor type of the document.
     * @param {DocumentId} docGlobalId - The global ID of the document.
     * @param {TabType} tabType - The type of the tab.
     */
    redirectToLoginToBackToPopup(editorType: EditorType, docGlobalId: DocumentId, tabType: TabType) {
        // Construct the return URL with the provided information
        const rInfo = {
            actionName: 'take-public-doc', // The action name
            rURL: `${window.location.pathname}?tabType=${tabType}&docId=${docGlobalId}&showEditor=${editorType}`, // The return URL
        };

        // Encode the return URL and construct the login URL
        const returnUrl = encodeReturnUrl(rInfo);
        const url = `/login?${PKEY_RURL}=${returnUrl}`;

        // Redirect the user to the login page
        window.location.href = url;
    }

    loadSavedDocs(options: LoadSavedDocsOptions): Observable<LoadSavedDocsResponse> {
        return createRPC<LoadSavedDocsResponse>(this.server.loadSavedDocs)(this.http, {
            body: JSON.stringify(options),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    loadShareWithMeDocs(options: LoadSavedDocsOptions): Observable<LoadSavedDocsResponse> {
        return createRPC<LoadSavedDocsResponse>(this.server.loadShareWithMeDocs)(this.http, {
            body: JSON.stringify(options),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    deleteSavedDoc(editorType: EditorType, docGlobalId: DocumentId): Observable<HttpResponse<any>> {
        return createRPC<HttpResponse<any>>(this.server.deleteSavedDoc)(
            this.http,
            {},
            `${editorType}/${docGlobalId}`
        ).pipe(take(1));
    }

    deleteShareWithMeDoc(editorType: EditorType, docGlobalId: DocumentId): Observable<HttpResponse<any>> {
        return createRPC<HttpResponse<any>>(this.server.deleteShareWithMeDoc)(
            this.http,
            {},
            `${editorType}/${docGlobalId}`
        ).pipe(take(1));
    }

    toggleSharing(editorType: EditorType, globalId: string): Promise<DocumentInfoResponse> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };

        return firstValueFrom(
            createRPC<DocumentInfoResponse>(this.server.toggleSharingDoc)(this.http, {
                body: JSON.stringify(reqBody),
            }).pipe(
                take(1),
                map(res => res.body)
            )
        );
    }

    toggleEmbedding(editorType: EditorType, globalId: string): Promise<DocumentInfoResponse> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };

        return firstValueFrom(
            createRPC<DocumentInfoResponse>(this.server.toggleEmbeddingDoc)(this.http, {
                body: JSON.stringify(reqBody),
            }).pipe(
                take(1),
                map(res => res.body)
            )
        );
    }

    public checkAllowEmbedding(globalId: DocumentId, editorType: EditorType): Promise<HttpResponse<any>> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };

        return firstValueFrom(
            createRPC<HttpResponse<any>>(this.server.isDocEmbedded)(this.http, {
                body: JSON.stringify(reqBody),
            }).pipe(take(1))
        );
    }

    async cloneDocument(
        coord: EditorCoordinator,
        editorType: EditorType,
        docGlobalId: string
    ): Promise<DocumentInfoResponse> {
        const editor = coord.editorByType(editorType);
        const docMap = await editor.duplicateDoc([docGlobalId]);
        const cloneDocId = docMap.get(docGlobalId);
        if (!cloneDocId) {
            throw new Error('Failed to clone document');
        }

        return this.saveDocumentMetadata(editorType, cloneDocId);
    }

    private async saveDocumentMetadata(editorType: EditorType, docGlobalId: string): Promise<DocumentInfoResponse> {
        if (!docGlobalId || !editorType) throw new Error('Invalid parameters');
        const userProfile = this.userService.curUser$.value;
        const reqBody = {
            docGlobalId: docGlobalId,
            editorType: editorType,
            details: {
                docGlobalId: docGlobalId,
                docLocalId: 0,
                ownerUserId: userProfile.id,
                ownerUserName: userProfile.username,
            },
        };

        return firstValueFrom(
            createRPC<DocumentInfoResponse>(this.metadataServer.createDocInfo)(this.http, {
                body: JSON.stringify(reqBody),
            }).pipe(
                take(1),
                map(res => res.body)
            )
        );
    }
}
