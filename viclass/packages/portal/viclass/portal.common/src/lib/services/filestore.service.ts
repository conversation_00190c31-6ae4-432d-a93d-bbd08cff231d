import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { FileStoreServiceModule, createRPC, createUploadRPC } from './api.service.module';
import { UploadResponse, UploadTokenRequest, UploadTokenResponse } from './models';
import { FileStoreController } from './route/backend.route.definition';

@Injectable({
    providedIn: FileStoreServiceModule,
})
export class FileStoreService {
    server = new FileStoreController();

    constructor(private http: HttpClient) {}

    getUploadToken(data: UploadTokenRequest): Observable<UploadTokenResponse> {
        return createRPC<UploadTokenResponse>(this.server.getUploadToken)(this.http, {
            body: JSON.stringify(data),
        }).pipe(map(response => response.body));
    }

    uploadFile(data: FormData): Observable<UploadResponse> {
        return createUploadRPC<UploadResponse>(this.server.uploadFile)(this.http, data).pipe(
            map(response => response.body)
        );
    }
}
