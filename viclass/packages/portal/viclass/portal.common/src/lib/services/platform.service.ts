import { Injectable, PLATFORM_ID, inject } from '@angular/core';
import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { of } from 'rxjs';

@Injectable({
    providedIn: 'root',
})
export class PlatformService {
    platformId = inject(PLATFORM_ID);
    isClientSide$ = of(this.isClientSide);
    isServerSide$ = of(this.isServerSide);

    constructor() {}

    get isClientSide() {
        return isPlatformBrowser(this.platformId);
    }

    get isServerSide() {
        return isPlatformServer(this.platformId);
    }
}
