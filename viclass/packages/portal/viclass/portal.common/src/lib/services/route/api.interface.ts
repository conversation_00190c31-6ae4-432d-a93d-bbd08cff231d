import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface IControllerMethod {
    url: string;
    method: string;
    absoluteURL: () => string;
}

export interface IControllerMethodFactory {
    apply: (...params: any[]) => ControllerMethod;
}

export interface IRPCInvocation<T> {
    (http: HttpClient, options?: any, ...params: any[]): Observable<HttpResponse<T>>;
}

export interface IUploadRPCInvocation<T> {
    (http: HttpClient, formData: FormData, options?: any, ...params: any[]): Observable<HttpResponse<T>>;
}

export class ControllerMethod implements IControllerMethod {
    url: string;
    method: string;
    absoluteURL: () => string;

    constructor({ ...options }: any) {
        this.url = options.url;
        this.method = options.method;
    }
}

export class ControllerMethodFactory implements IControllerMethodFactory {
    controllerMethod: ControllerMethod;

    constructor({ ...options }: any) {
        this.controllerMethod = new ControllerMethod(options);
    }

    public apply(...params: any[]): ControllerMethod {
        if (params[1].length != 0)
            return new ControllerMethod({
                url: this.controllerMethod.url.replace('${params}', params[1]),
                method: this.controllerMethod.method,
            });

        return this.controllerMethod;
    }
}
