import { ControllerMethodFactory } from './api.interface';

export class ClassroomController {
    loadClassroomActivities: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/classroom/activity/fetch/ls/${params}',
        method: 'GET',
    });

    getClassroomActivity: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/classroom/activity/fetch',
        method: 'POST',
    });
}

export class DocOwnershipMetadataController {
    createDocInfo: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/document/create-doc-info',
        method: 'POST',
    });

    loadDocInfo: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/document/load-doc-info',
        method: 'POST',
    });

    loadSharedDocInfo: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/document/load-shared-doc-info',
        method: 'POST',
    });

    loadSharedDocInfoGuest: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/document/load-shared-doc-info-guest',
        method: 'POST',
    });

    docsByUser: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/document/docs-by-user',
        method: 'POST',
    });

    isPublicDoc: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/document/is-public-doc',
        method: 'POST',
    });
}

export class DocInProfileMetadataController {
    loadDocInfo: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/load-doc-info',
        method: 'POST',
    });

    docsByUser: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/docs-by-user',
        method: 'POST',
    });

    getDocPreviewUploadToken: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/doc-preview-upload-token',
        method: 'POST',
    });

    saveDocToProfile: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/save-doc-to-profile',
        method: 'POST',
    });

    loadSavedDocs: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/saved-docs',
        method: 'POST',
    });

    loadShareWithMeDocs: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/share-with-me-docs',
        method: 'POST',
    });

    deleteSavedDoc: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/saved-docs/${params}',
        method: 'DELETE',
    });

    deleteShareWithMeDoc: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/share-with-me-docs/${params}',
        method: 'DELETE',
    });

    toggleSharingDoc: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/toggle-sharing',
        method: 'POST',
    });

    toggleEmbeddingDoc: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/toggle-embedding',
        method: 'POST',
    });

    isDocEmbedded: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/doc-in-profile/is-embedded',
        method: 'POST',
    });
}

export class ConfigurationsController {
    getDataConfigs: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/config/fetch',
        method: 'POST',
    });
}

export class DocumentController {
    getDocs: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/docs/fetch',
        method: 'POST',
    });
}

export class LSessionController {
    createLSession: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/create',
        method: 'POST',
    });

    uploadSessionAvatar: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/avatar/upload',
        method: 'POST',
    });

    getLSession: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/fetch',
        method: 'GET',
    });

    getClassroomSettings: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/setting/fetch',
        method: 'GET',
    });

    updateLSession: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/update',
        method: 'POST',
    });

    sessionSummaryList: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/summaries',
        method: 'POST',
    });

    approveRegistration: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/registration/${params}/approve',
        method: 'GET',
    });

    rejectRegistration: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/registration/${params}/reject',
        method: 'GET',
    });
}

export class LSessionRegistrationController {
    registerLSession: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/registration/register',
        method: 'POST',
    });

    unregisterLSession: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/registration/${params}/unregister',
        method: 'POST',
    });

    getRegistrationById: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/registration/${params}/fetch',
        method: 'GET',
    });

    getLoggedInRegistration: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/registration/current/fetch',
        method: 'POST',
    });

    getRegisteredUsers: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/registration/registered-users',
        method: 'POST',
    });

    getRegistrationsByLsId: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/lsession/${params}/registration/fetch',
        method: 'GET',
    });
}

export class NotificationController {
    loadNotificationById: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/notification/${params}/fetch',
        method: 'GET',
    });

    loadClassroomNotification: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/notification/fetch',
        method: 'POST',
    });
}

export class UserController {
    verifyResetPasswordToken: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/verify-reset-password-token/${params}',
        method: 'GET',
    });

    resetPassword: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/reset-password',
        method: 'POST',
    });

    forgotPassword: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/request-forgot-password',
        method: 'POST',
    });

    doLogin: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/login',
        method: 'POST',
    });

    changePassword: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/change-password',
        method: 'POST',
    });

    doSocialLogin: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/login/social',
        method: 'POST',
    });

    profile: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/profile/fetch',
        method: 'GET',
    });

    updateProfile: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/profile',
        method: 'PATCH',
    });

    siteKeyOfCaptcha: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/captcha/sitekey',
        method: 'GET',
    });

    briefProfiles: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/brief-profile/fetch',
        method: 'POST',
    });

    register: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/register',
        method: 'POST',
    });

    checkUserExist: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/name/${params}/exist',
        method: 'GET',
    });

    checkEmailExist: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/registrations/email-exist',
        method: 'POST',
    });

    addMissingSocialEmail: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/registrations/add-social-email',
        method: 'POST',
    });

    getRegistrationMetadata: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/registrations/${params}',
        method: 'GET',
    });

    doLogout: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/logout',
        method: 'POST',
    });

    verifyEmail: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/verify-email',
        method: 'POST',
    });

    sendVerification: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/verify-email/send-code/${params}',
        method: 'POST',
    });

    linkedRegistrations: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/linked-registrations',
        method: 'GET',
    });

    emailRegInfo: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/email-reg-info',
        method: 'GET',
    });

    unlinkSocial: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/profile/social/${params}',
        method: 'DELETE',
    });

    updateAvatar: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/profile/update-avatar',
        method: 'POST',
    });

    getUserLoginInformation: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/user/user-login-information',
        method: 'GET',
    });

    userBetaRegister: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/beta/register',
        method: 'POST',
    });
}

export class FileStoreController {
    getUploadToken: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/filestore/upload-token',
        method: 'POST',
    });

    uploadFile: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/filestore/upload',
        method: 'POST',
    });
}

export class SupportController {
    homepageFeedback: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/api/support/homepage-feedback',
        method: 'POST',
    });
}

export class ShortURLController {
    shortenURL: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/shorturl/api/shorten',
        method: 'POST',
    });

    getStats: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/shorturl/api/stats/${params}',
        method: 'GET',
    });

    getAllURLs: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/shorturl/api/urls',
        method: 'GET',
    });

    searchURLs: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/shorturl/api/urls/search',
        method: 'GET',
    });

    deleteURL: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/shorturl/api/urls/${params}',
        method: 'DELETE',
    });

    getAnalytics: ControllerMethodFactory = new ControllerMethodFactory({
        url: '/shorturl/api/analytics',
        method: 'GET',
    });
}
