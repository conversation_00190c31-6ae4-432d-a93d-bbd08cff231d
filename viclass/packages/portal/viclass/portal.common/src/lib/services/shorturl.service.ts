import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import { createRPC, ShortURLServiceModule } from './api.service.module';
import { ShortURLController } from './route/backend.route.definition';

@Injectable({
    providedIn: ShortURLServiceModule,
})
export class ShortUrlService {
    private server = new ShortURLController();

    constructor(private http: HttpClient) {}

    shortenUrl(longUrl: string): Observable<string> {
        return createRPC<string>(this.server.shortenURL)(this.http, {
            body: JSON.stringify({ longUrl }),
        }).pipe(map(response => response.body));
    }

    getUrlStats(shortUrlId: string): Observable<any> {
        return createRPC<any>(this.server.getStats)(this.http, {
            body: JSON.stringify({ shortUrlId }),
        }).pipe(map(response => response.body));
    }

    getAllShortUrls(): Observable<any[]> {
        return createRPC<any[]>(this.server.getAllURLs)(this.http).pipe(map(response => response.body));
    }

    searchShortUrls(query: string): Observable<any[]> {
        return createRPC<any[]>(this.server.searchURLs)(this.http, {
            body: JSON.stringify({ query }),
        }).pipe(map(response => response.body));
    }

    deleteShortUrl(shortUrlId: string): Observable<void> {
        return createRPC<void>(this.server.deleteURL)(this.http, {
            body: JSON.stringify({ shortUrlId }),
        }).pipe(map(response => response.body));
    }

    getAnalytics(): Observable<any> {
        return createRPC<any>(this.server.getAnalytics)(this.http).pipe(map(response => response.body));
    }
}
