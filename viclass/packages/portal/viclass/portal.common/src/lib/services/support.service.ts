import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { createRPC, SupportServiceModule } from './api.service.module';
import { SupportController } from './route/backend.route.definition';

@Injectable({
    providedIn: SupportServiceModule,
})
export class SupportService {
    server = new SupportController();

    constructor(private http: HttpClient) {}

    homepageFeedback(data: any): Observable<any> {
        return createRPC(this.server.homepageFeedback)(this.http, {
            body: JSON.stringify(data),
        }).pipe(
            take(1),
            map(response => response.body)
        );
    }
}
