<sharing-dialog (clickOutside)="onClose()">
    <sharing-header class="xs:hidden sm:flex" (closeEvent)="onClose()"></sharing-header>
    <sharing-body>
        <div class="flex flex-col gap-[20px]">
            <div class="font-[700] text-[18px] leading-[27px]">CHIA SẺ BUỔI HỌC</div>
            <div class="font-[700] text-[18px] leading-[27px]">Chia sẻ URL</div>
            <div class="flex flex-row gap-[5px] pb-[10px]">
                <span
                    class="overflow-hidden text-ellipsis whitespace-nowrap mr-auto text-[12px] leading-[30px] font-[500]"
                    >{{ data.link }}</span
                >
                <a class="vi-btn vi-btn-small vi-btn-gradient text-nowrap" (click)="onCopy()"> Sao chép </a>
            </div>
        </div>
    </sharing-body>
    <div class="border-t border-t-BW4 w-full mt-auto">
        <div class="p-[10px_20px_10px_25px]">
            <sharing-footer-social [link]="data.link"></sharing-footer-social>
        </div>
    </div>
</sharing-dialog>
