import { ClipboardModule } from '@angular/cdk/clipboard';
import { ChangeDetectionStrategy, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { NotificationService } from '../../notification';
import {
    SharingBodyComponent,
    SharingDialogComponent,
    SharingFooterSocialComponent,
    SharingHeaderComponent,
} from '../sharing-dialog.component';
import { ShortUrlService } from '../../services';
import { firstValueFrom, map } from 'rxjs';

@Component({
    selector: 'share-classroom-dialog',
    templateUrl: './share-classroom-dialog.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ClipboardModule,
        MatDialogModule,
        SharingDialogComponent,
        SharingHeaderComponent,
        SharingBodyComponent,
        SharingFooterSocialComponent,
    ],
})
export class ShareClassroomDialogComponent implements OnInit {
    constructor(
        private notificationService: NotificationService,
        private dialogRef: MatDialogRef<ShareClassroomDialogComponent>,
        private shortUrlService: ShortUrlService,
        @Inject(MAT_DIALOG_DATA) public data: { lsId: string; link: string }
    ) {
        dialogRef.disableClose = true;
        dialogRef.addPanelClass('share-dialog');
        data.link = `${location.protocol}//${location.host}/classrooms/${data.lsId}`;
    }

    async ngOnInit() {
        this.data.link = await firstValueFrom(
            this.shortUrlService.shortenUrl(this.data.link).pipe(
                map(shortUrl => {
                    return shortUrl ? shortUrl : this.data.link;
                })
            )
        );
    }

    onClose() {
        this.dialogRef.close();
    }

    async onCopy() {
        try {
            await navigator.clipboard.writeText(this.data.link);
            /* Resolved - text copied to clipboard successfully */
            this.notificationService.showNotification({
                message: 'Sao chép liên kết thành công',
                status: 'success',
            });
        } catch (err) {
            console.error('exception when copy link', err);
            /* Rejected - text failed to copy to the clipboard */
        }
    }
}
