import { NgOptimizedImage } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { ClickOutsideDirective } from '../click-outside-detector';
import { environment } from '../environment';

const env = environment;
@Component({
    selector: 'sharing-dialog',
    template: ` <div
        class="xl:w-[500px] sm:w-[400px] xs:w-full sm:rounded-[30px] xs:rounded-[0] bg-BW7 text-BW1 relative items-center overflow-hidden"
        click-outside
        (clickOutside)="onClickOutSide($event)">
        <ng-content></ng-content>
    </div>`,
    styles: [
        `
            @media (min-width: 320px) {
                .share-dialog.cdk-overlay-pane {
                    max-width: 100% !important;
                    margin-top: auto !important;
                }
            }
            @media (min-width: 640px) {
                .share-dialog.cdk-overlay-pane {
                    max-width: 100% !important;
                    margin-top: auto !important;
                    margin-bottom: auto !important;
                }
            }
            .share-dialog .mat-mdc-dialog-container {
                background: none;
                box-shadow: none;
            }

            .share-dialog .mat-mdc-dialog-container .mat-mdc-dialog-surface {
                background: transparent !important;
                box-shadow: none !important;
                border-radius: 0 !important;
            }
        `,
    ],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [ClickOutsideDirective],
})
export class SharingDialogComponent {
    @Output() clickOutside: EventEmitter<any> = new EventEmitter<any>();

    onClickOutSide($event: any) {
        this.clickOutside.emit($event);
    }
}

@Component({
    selector: 'sharing-header',
    template: `<div
        class="flex flex-row p-[10px_20px_10px_25px] w-full mb-auto text-[20px] leading-[20px] bg-BW1 text-BW7">
        <div class="mr-auto flex flex-row"><ng-content></ng-content></div>
        <a class="vcon vcon-general vcon_delete ml-auto" (click)="onClose()"></a>
    </div> `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class SharingHeaderComponent {
    @Output()
    closeEvent: EventEmitter<any> = new EventEmitter<any>();

    onClose() {
        this.closeEvent.next('');
    }
}

@Component({
    selector: 'sharing-body',
    template: `<div class="flex flex-col p-[10px_20px_5px_25px] w-full m-auto">
        <ng-content></ng-content>
    </div> `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class SharingBodyComponent {}

@Component({
    selector: 'sharing-footer',
    template: `<div class="border-t border-t-BW4 w-full mt-auto">
        <div class="flex flex-row p-[10px_20px_5px_25px] mr-auto">
            <ng-content></ng-content>
        </div>
    </div> `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class SharingFooterComponent {}

@Component({
    selector: 'sharing-footer-social',
    template: `
        <div class="flex items-center justify-start gap-[15px]">
            <!-- <a class="social-zalo size-[40px]" (click)="onShareSocial('zalo')"></a> -->
            <a class="social-gmail size-[40px]" (click)="onShareSocial('gmail')"></a>
            <a class="social-facebook size-[40px]" (click)="onShareSocial('facebook')"></a>
            <a class="social-messenger size-[40px]" (click)="onShareSocial('messenger')"></a>
            <a class="social-twitter size-[40px]" (click)="onShareSocial('twitter')"></a>
        </div>
    `,
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgOptimizedImage],
})
export class SharingFooterSocialComponent {
    @Input() link: string;

    onShareSocial(social: 'gmail' | 'facebook' | 'messenger' | 'twitter' | 'zalo') {
        const encodedLink = encodeURIComponent(this.link);
        if (!this.link || !encodedLink) return;

        switch (social) {
            case 'gmail':
                this.openSharePopup(`https://mail.google.com/mail/u/0/?view=cm&fs=1&to=&su=&body=${encodedLink}`);
                break;
            case 'facebook':
                this.openSharePopup(
                    `https://www.facebook.com/dialog/share?app_id=${env.socialLoginConfig.facebook.clientId}&display=popup&href=${encodedLink}`
                );
                break;
            case 'messenger':
                this.openSharePopup(
                    `https://www.facebook.com/dialog/send?app_id=${env.socialLoginConfig.facebook.clientId}&link=${encodedLink}&redirect_uri=${encodedLink}`
                );
                break;
            case 'twitter':
                this.openSharePopup(`https://x.com/intent/tweet?url=${encodedLink}`);
                break;
            case 'zalo':
                // TODO: implement later when Zalo OA is ready
                // https://www.panpic.vn/tao-nut-zalo-chia-se-tren-website-nhu-the-nao
                break;
        }
    }

    private openSharePopup(url: string) {
        window.open(url, 'popup', 'width=600,height=500');
    }
}
