<app-embed-preview
    *ngIf="showEmbedPreview$ | async as embedType; else embedToggle"
    [edType]="inputData.edType"
    [docGlobalId]="inputData.docGlobalId"
    [previewType]="embedType"
    (closePreview)="showEmbedPreview$.next(null)"></app-embed-preview>

<ng-template #embedToggle>
    <div class="mb-[25px]">
        <strong class="text-[24px]">NHÚNG TÀI LIỆU</strong>
    </div>
    <div class="font-bold">Hướng dẫn cho nhà phát triển</div>
    <div class="mb-[20px] overflow-hidden text-ellipsis whitespace-nowrap">
        <a class="text-sky-500 visited:text-purple-500" [href]="embedGuideLink" target="_blank">{{ embedGuideLink }}</a>
    </div>

    <hr class="border-BW4" />

    <div class="py-[20px] flex flex-col gap-[10px]">
        <div class="font-bold">
            Nhúng iframe/JavaScript
            <mat-slide-toggle
                class="ml-[10px]"
                [disabled]="!(isSaved$ | async)"
                [checked]="isEmbeded$ | async"
                (change)="onToggleChange($event)"></mat-slide-toggle>
        </div>
        <div class="flex gap-[10px]">
            <button
                [disabled]="!(isEmbeded$ | async)"
                class="vi-btn vi-btn-small vi-btn-outline italic"
                (click)="showEmbedPreview$.next('iframe')">
                Iframe <span class="vcon-general vcon_arrow_next"></span>
            </button>
            <button
                [disabled]="!(isEmbeded$ | async)"
                class="vi-btn vi-btn-small vi-btn-outline italic"
                (click)="showEmbedPreview$.next('javascript')">
                JavaScript <span class="vcon-general vcon_arrow_next"></span>
            </button>
        </div>
        <app-require-save-alert
            *ngIf="!(isSaved$ | async)"
            (toSaveDoc)="redirectSaveDocument.emit()"></app-require-save-alert>
    </div>
</ng-template>
