import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { BehaviorSubject, Observable, map } from 'rxjs';
import { ButtonClickWaitDirective } from '../../button-click-wait-directive/click-wait.directive';
import { DocInProfileMetadataService } from '../../services';
import { SharingDialogConfig } from '../model';
import { RequireSaveAlertComponent } from '../require-save-alert/require-save-alert.component';
import { EmbedPreviewComponent, EmbedPreviewType } from './embed-preview/embed-preview.component';

@Component({
    selector: 'app-doc-embedding',
    standalone: true,
    imports: [
        CommonModule,
        RequireSaveAlertComponent,
        ButtonClickWaitDirective,
        MatSlideToggleModule,
        EmbedPreviewComponent,
    ],
    templateUrl: './doc-embedding.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocEmbeddingComponent implements OnInit {
    @Input() inputData: SharingDialogConfig;

    @Output()
    redirectSaveDocument = new EventEmitter<void>();

    readonly embedGuideLink = `${location.protocol}//${location.host}/guides/embedding`;
    showEmbedPreview$ = new BehaviorSubject<EmbedPreviewType>(null);
    isSaved$: Observable<boolean>;
    isEmbeded$: Observable<boolean>;

    constructor(private metadataService: DocInProfileMetadataService) {}

    ngOnInit(): void {
        this.isSaved$ = this.inputData.docInfo?.pipe(map(info => !!info?.details?.savedToProfile));
        this.isEmbeded$ = this.inputData.docInfo?.pipe(map(info => !!info?.details?.embedded));
    }

    async onToggleChange(_event: MatSlideToggleChange) {
        try {
            const result = await this.metadataService.toggleEmbedding(
                this.inputData.edType,
                this.inputData.docGlobalId
            );
            this.inputData.docInfo.next(result);
        } catch (error) {
            console.error(error);
        }
    }
}
