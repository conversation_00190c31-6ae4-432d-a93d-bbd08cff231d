<div class="flex items-center">
    <button class="vi-btn vi-btn-normal" (click)="closePreview.emit()">
        <span class="vcon-general vcon_arrow_back"></span>
    </button>
    <strong class="text-[24px]">NHÚNG {{ previewType | uppercase }}</strong>
</div>
<form class="vi-form overflow-x-hidden overflow-y-auto max-h-[calc(100vh-300px)]">
    <div class="w-full h-[300px] relative flex justify-center items-center p-[1px] mb-[10px]" #wrapper>
        <iframe
            #previewIframe
            [width]="width"
            [height]="height"
            [src]="safePreviewSrc$ | async"
            class="absolute border rounded-[20px] shadow-SH1"
            [ngStyle]="{ transform: 'scale(' + (previewScale | async) + ')' }"></iframe>
    </div>
    <div class="flex gap-[10px] items-center mb-[10px]">
        <button
            class="vi-btn vi-btn-normal !w-[30px] !h-[30px] !rounded-[50%]"
            [ngClass]="{ 'bg-P2': fixedDimention$ | async }"
            [disabled]="width === 0 || height === 0"
            (click)="toggleFixedDimention()">
            <span class="vcon-common vcon_fixed-dimention"></span>
        </button>

        <label class="text-BW4" [htmlFor]="widthInput">Rộng</label>
        <input
            class="vi-input !max-w-[85px]"
            type="number"
            #widthInput
            [ngModel]="width"
            (ngModelChange)="onWidthChange($event)"
            name="embedWidth"
            min="0"
            max="10000"
            step="1" />
        <label class="text-BW4" [htmlFor]="heightInput">Cao</label>
        <input
            class="vi-input !max-w-[85px]"
            type="number"
            #heightInput
            [ngModel]="height"
            (ngModelChange)="onHeightChange($event)"
            name="embedHeight"
            min="0"
            max="10000"
            step="1" />
    </div>

    <textarea
        class="vi-input break-all max-h-[500px] w-full h-auto mb-[10px]"
        name="embedCode"
        [rows]="previewType === 'iframe' ? 3 : 10"
        [value]="embedCode$ | async">
    </textarea>

    <div>
        <button
            (click)="copyEmbedCodeToClipboard()"
            disabled
            [clickWait]="1000"
            class="vi-btn vi-btn-small vi-btn-focus">
            Sao chép
        </button>
    </div>
</form>

<ng-template #previewLoading>
    <div class="h-[200px] rounded-[12px] border-BW4 border-[1px] overflow-hidden animate-pulse bg-BW6"></div>
</ng-template>
