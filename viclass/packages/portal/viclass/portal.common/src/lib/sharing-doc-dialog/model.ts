import { Injector, ViewContainerRef } from '@angular/core';
import { BaseCoordinator } from '@viclass/editor.coordinator/common';
import { DocumentId, EditorType, ViewportManager } from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { DocumentInfoResponse, SourceType } from '../services';

export type TabType = 'share' | 'save' | 'embed';

export type CaptureType = 'document' | 'viewport';

// The sharing dialog might be call from a context where no coordinator is available yet
// So the sharing dialog doesn't create the doc preview by itself, but instead, it provide
// a callback so whoever open share dialog will create the preview inside the view container
// provided by the sharing dialog
export type SharingPreviewFunc = (
    inputData: SharingDialogConfig,
    viewRef: ViewContainerRef,
    injector: Injector
) => Promise<{ viewport: ViewportManager; coord: BaseCoordinator }>;

export type SharingDialogConfig = {
    allowSave?: boolean;
    selectedTab?: TabType;
    showTabs: TabType[];
    edType: EditorType;
    docGlobalId: DocumentId;
    source: SourceType;
    captureType: CaptureType;
    docInfo?: BehaviorSubject<DocumentInfoResponse>;
    docDisplay?: {
        coord: BaseCoordinator;
        viewport: ViewportManager;
    };
    previewCb?: SharingPreviewFunc;
    useCloneDoc?: boolean;
};
