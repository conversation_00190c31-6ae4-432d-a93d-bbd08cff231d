import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Output } from '@angular/core';

@Component({
    selector: 'app-require-save-alert',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './require-save-alert.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequireSaveAlertComponent {
    @Output()
    toSaveDoc = new EventEmitter<void>();
}
