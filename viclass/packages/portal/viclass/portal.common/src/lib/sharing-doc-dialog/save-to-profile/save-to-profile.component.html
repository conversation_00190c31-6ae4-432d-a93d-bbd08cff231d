<div *ngIf="previewUrl; else previewLoading">
    <div
        *ngIf="isDocSaved$ | async; else previewCropper"
        class="max-h-[500px] min-h-[150px] rounded-[12px] border-[1px] border-BW4 overflow-hidden flex justify-center items-center">
        <img [src]="previewUrl" alt="document preview" />
    </div>

    <ng-template #previewCropper>
        <lib-cropper
            #cropper
            [imageUrl]="previewUrl"
            [cropperOptions]="cropConfig"
            (ready)="cropperReady$.next(true)"
            (loadError)="onLoadCropperError($event)"></lib-cropper>
    </ng-template>
</div>

<div *ngIf="!(isDocSaved$ | async); else linkToSavedDoc" class="pt-[10px]">
    <strong class="text-sm">Tên tài liệu</strong>
    <div>
        <form
            class="vi-form"
            *fflow="let fum; by: buildSaveDocForm; fflow as f; submit: submitSaveDoc; noNav: true"
            [formGroup]="fum"
            [ferrcoord]="f">
            <span class="vi-text-error block" *ngIf="docNameError">! {{ docNameError.msg }}</span>
            <div class="flex gap-[5px] items-center">
                <div class="grow">
                    <input
                        class="vi-input input-sm"
                        formControlName="docName"
                        placeholder="Tài liệu 1"
                        [(ferror)]="docNameError" />
                </div>
                <button
                    class="vi-btn vi-btn-small vi-btn-focus"
                    [disabled]="!f.canSubmit() || !previewUrl || !(cropperReady$ | async) || (isSaving$ | async)"
                    [form-flow-submit]="f"
                    [spinner]="isSaving$">
                    Lưu
                </button>
                <button class="vi-btn vi-btn-small vi-btn-outline" (click)="closeDialog.emit()">Hủy</button>
            </div>
            <span class="vi-text-error block mt-3" *ngIf="formError$ | async">! {{ (formError$ | async).msg }}</span>
        </form>
    </div>
</div>

<ng-template #previewLoading>
    <div class="h-[150px] rounded-[12px] border-BW4 border-[1px] overflow-hidden animate-pulse bg-BW6"></div>
</ng-template>

<ng-template #linkToSavedDoc>
    <div class="p-[20px] text-center" *ngIf="this.inputData.docInfo | async as docInfo">
        <a
            href="/user/doc/{{ docInfo.editorType }}/{{ docInfo.docGlobalId }}"
            target="_blank"
            class="text-sky-500 visited:text-purple-500 underline">
            {{ docInfo.details?.docName || 'Tài liệu đã lưu' }}
        </a>
    </div>
</ng-template>
