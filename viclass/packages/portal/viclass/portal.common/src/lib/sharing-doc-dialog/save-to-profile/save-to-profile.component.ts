import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { DomSanitizer } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { DocumentId, EditorType, ViewportId } from '@viclass/editor.core';
import { BehaviorSubject, Observable, firstValueFrom, map } from 'rxjs';
import { CropperComponent, ImageCropperOptions } from '../../cropper';
import { ErrorModel, FormBuildingResult, FormCreator, FormFlowSubmitEvent, FormUtilModule } from '../../formutils';
import { NotificationModule, NotificationService } from '../../notification';
import { UserService } from '../../services';
import { DocInProfileMetadataService } from '../../services/doc-in-profile-metadata.service';
import { DocumentInfoResponse, SourceType } from '../../services/models';
import { SpinnerLabelComponent } from '../../spin.label/spinner.label.component';
import { CaptureType, SharingDialogConfig } from '../model';

type DocumentNameData = {
    docName: string;
};

@Component({
    selector: 'app-save-to-profile',
    standalone: true,
    imports: [
        CommonModule,
        FormUtilModule,
        ReactiveFormsModule,
        SpinnerLabelComponent,
        CropperComponent,
        NotificationModule,
        RouterModule,
    ],
    templateUrl: './save-to-profile.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SaveToProfileComponent implements OnInit {
    @ViewChild('cropper', { read: CropperComponent })
    public cropperElement: CropperComponent;

    @Input() inputData: SharingDialogConfig;

    @Input() previewUrl: string;

    @Output() closeDialog = new EventEmitter();

    get viewportId(): ViewportId {
        return this.inputData.docDisplay.viewport.id;
    }
    get edType(): EditorType {
        return this.inputData.edType;
    }
    get docGlobalId(): DocumentId {
        return this.inputData.docGlobalId;
    }
    get source(): SourceType {
        return this.inputData.source;
    }
    get captureType(): CaptureType {
        return this.inputData.captureType || 'document';
    }

    readonly cropperReady$ = new BehaviorSubject<boolean>(false);

    isDocSaved$: Observable<boolean>;

    saveDocToProfileForm: UntypedFormGroup;
    docNameError: ErrorModel;
    readonly formError$ = new BehaviorSubject<ErrorModel>(null);
    readonly isSaving$ = new BehaviorSubject<boolean>(false);

    readonly cropConfig: ImageCropperOptions = {
        viewMode: 1, // contains mode
        dragMode: 'move', // allow to move the image
        autoCropArea: 1, // always auto crop the whole image
        resultType: 'image/jpeg', // save as jpeg to reduce size
        maxContainerHeight: 450,
    };

    constructor(
        private metadataService: DocInProfileMetadataService,
        public userService: UserService,
        public sanitizer: DomSanitizer,
        public fb: UntypedFormBuilder,
        private notificationService: NotificationService
    ) {}

    ngOnInit(): void {
        this.isDocSaved$ = this.inputData.docInfo.pipe(map(info => !!info?.details?.savedToProfile));
    }

    /**
     *  Build the form for saving document to profile.
     */
    buildSaveDocForm = (data?: DocumentNameData): FormBuildingResult => {
        let newDocName = this.inputData.docInfo?.value?.details?.docName || '';
        if (this.inputData.useCloneDoc && newDocName) {
            newDocName += '_Copy';
        }

        data = data || {
            docName: newDocName,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                __fields__: {
                    docName: [Validators.required],
                },
            })
            .validatorMessages({
                __fields__: {
                    docName: {
                        required: 'Tên tài liệu là bắt buộc',
                    },
                },
            })
            .build();

        this.saveDocToProfileForm = result.control as UntypedFormGroup;

        if (data.docName?.length) (result.control as FormGroup).markAsDirty();

        return result;
    };

    /**
     *  Submits the save document request.
     */
    submitSaveDoc = async (data: FormFlowSubmitEvent): Promise<void> => {
        this.isSaving$.next(true);

        try {
            const croppedPreview = await this.cropperElement.exportCanvas();

            let targetDocId = this.docGlobalId;
            let sourceDocId = undefined;

            if (this.inputData.useCloneDoc) {
                const coord = this.inputData.docDisplay.coord;
                const cloneDocRes = await this.metadataService.cloneDocument(coord, this.edType, this.docGlobalId);

                targetDocId = cloneDocRes.docGlobalId;
                sourceDocId = this.docGlobalId;
            }

            const res: DocumentInfoResponse = await this.metadataService.saveDocToProfile(
                targetDocId,
                data.data.docName,
                croppedPreview.blob,
                this.edType,
                // if save as clone doc, always use `doc-ownership` as source
                this.inputData.useCloneDoc ? 'doc-ownership' : this.source,
                sourceDocId
            );

            this.inputData.docInfo.next(res);
            this.showSaveSuccessNotification();
        } catch (e) {
            console.error(e);
            this.showFormError({
                key: 'addEmailFailed',
                msg: 'Lưu tài liệu thất bại, xin vui lòng thử lại',
            });
        } finally {
            this.isSaving$.next(false);
        }
    };

    /**
     * Handle the onLoadCropperError event.
     */
    onLoadCropperError($event: ErrorEvent) {
        console.error('Failed to load cropper', $event);
        this.showFormError({
            key: 'load-cropper-error',
            msg: 'Định dạng ảnh không hợp lệ',
        });
    }

    /**
     *  Show the save success notification and auto close after sometime
     */
    showSaveSuccessNotification() {
        this.notificationService.showNotification({
            message: 'Lưu tài liệu thành công',
            status: 'success',
            duration: 3000,
        });
    }

    /**
     * Shows the form error by emitting the error model through the formError$ subject.
     * It also clears the error when the form value changes.
     */
    showFormError(errorModel: ErrorModel) {
        this.formError$.next(errorModel);

        firstValueFrom(this.saveDocToProfileForm.valueChanges).then(() => {
            this.formError$.next(null);
        });
    }
}
