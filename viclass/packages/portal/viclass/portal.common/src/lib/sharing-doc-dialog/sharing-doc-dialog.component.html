<div class="bg-white flex flex-col max-h-[inherit]">
    <div class="flex w-full justify-between bg-BW1 px-[10px] py-[5px]">
        <div class="flex-grow" *ngIf="loadingDocInfo"></div>
        <div class="flex" *ngIf="!loadingDocInfo">
            <ng-container
                *ngTemplateOutlet="
                    tabButton;
                    context: {
                        tab: 'share',
                        iconClass: inputData.useCloneDoc
                            ? 'vcon-general vcon_general_upload'
                            : 'vcon-general vcon_page-bar_share',
                    }
                "></ng-container>
            <ng-container
                *ngTemplateOutlet="
                    tabButton;
                    context: {
                        tab: 'embed',
                        iconClass: 'vcon-common vcon_embed',
                    }
                "></ng-container>
            <ng-container
                *ngTemplateOutlet="
                    tabButton;
                    context: {
                        tab: 'save',
                        iconClass: 'vcon-general vcon_download',
                    }
                "></ng-container>
        </div>
        <button (click)="closePopup()" class="p-[5px] w-[50px] text-center">
            <span class="vcon-general vcon_delete align-middle text-BW7"></span>
        </button>
    </div>
    <div *ngIf="loadingDocInfo" class="px-[15px] pt-[20px] pb-[15px] max popup-container">
        <div class="relative flex flex-col gap-y-[15px] min-h-[150px] h-full">
            <app-editor-loading [loading]="loadingDocInfo" [hasBackground]="false"></app-editor-loading>
        </div>
    </div>
    <div
        *ngIf="inputData.showTabs.includes('share') && visitedTabs.has('share') && !loadingDocInfo"
        [ngClass]="{ hidden: inputData.selectedTab !== 'share' }"
        class="px-[15px] pt-[20px] pb-[15px] max popup-container w-[min(80vw,800px)]">
        <div
            *ngIf="userService.curUser$ | async; else loginRequired"
            class="relative flex flex-col gap-y-[15px] min-h-[150px] h-full">
            <app-sharing
                *ngIf="!loadingDocInfo"
                [inputData]="inputData"
                [previewBlob]="previewBlob$ | async"
                (redirectSaveDocument)="selectTab('save')"></app-sharing>
        </div>
    </div>

    <div
        *ngIf="inputData.showTabs.includes('embed') && visitedTabs.has('embed')"
        [ngClass]="{ hidden: inputData.selectedTab !== 'embed' }"
        class="px-[15px] pt-[20px] pb-[15px] max popup-container w-[min(80vw,800px)]">
        <div
            *ngIf="userService.curUser$ | async; else embedLoginRequire"
            class="relative flex flex-col gap-y-[15px] min-h-[150px] h-full">
            <app-doc-embedding [inputData]="inputData" (redirectSaveDocument)="selectTab('save')"></app-doc-embedding>
        </div>

        <ng-template #embedLoginRequire>
            <div class="mb-[25px]">
                <strong class="text-[24px]">NHÚNG TÀI LIỆU</strong>
            </div>
            <ng-container *ngTemplateOutlet="loginRequired"></ng-container>
        </ng-template>
    </div>

    <div
        *ngIf="inputData.showTabs.includes('save') && visitedTabs.has('save') && inputData.allowSave && !loadingDocInfo"
        [ngClass]="{ hidden: inputData.selectedTab !== 'save' }"
        class="flex flex-col gap-[10px] p-[20px] popup-container w-[min(80vw,800px)] overflow-y-auto">
        <strong class="text-[24px]">{{
            inputData.useCloneDoc ? 'LƯU BẢN SAO VÀO THƯ VIỆN' : 'THÊM VÀO THƯ VIỆN'
        }}</strong>
        <div *ngIf="inputData.docDisplay && userService.curUser$ | async; else loginRequired">
            <app-save-to-profile
                [inputData]="inputData"
                [previewUrl]="safePreviewUrl$ | async"
                (closeDialog)="closePopup()"></app-save-to-profile>
        </div>
    </div>
</div>

<ng-template #tabButton let-tab="tab" let-iconClass="iconClass">
    <button
        *ngIf="inputData.showTabs.includes(tab)"
        [ngClass]="inputData.selectedTab === tab ? 'bg-P2 rounded-[20px]' : 'bg-BW1 text-BW7'"
        class="px-[20px] text-center focus:outline-none focus:ring-0"
        (click)="selectTab(tab)">
        <span class="{{ iconClass }} align-middle"></span>
    </button>
</ng-template>

<ng-template #loginRequired>
    <div class="flex gap-[10px] items-center align-middle bg-PAS1 rounded-[15px] px-[10px] py-[5px]">
        <span class="vcon-general vcon_general_warning"
            ><span class="path1"></span><span class="path2"></span><span class="path3"></span
        ></span>
        <div class="vi-popup-message text-center">
            <div>Bạn cần đăng nhập để thực hiện chức năng này</div>
        </div>
    </div>

    <div class="py-[15px] flex flex-row gap-[10px] text-center justify-center">
        <button class="vi-btn vi-btn-small vi-btn-focus" (click)="redirectToLogin()">Đăng nhập</button>
        <button class="vi-btn vi-btn-small vi-btn-outline" (click)="closePopup()">Bỏ qua</button>
    </div>
</ng-template>
