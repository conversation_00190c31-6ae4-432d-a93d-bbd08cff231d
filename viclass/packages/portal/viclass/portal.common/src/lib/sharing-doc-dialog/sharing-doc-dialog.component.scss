::ng-deep .sharing-doc-popup {
    max-width: min(80vw, 800px) !important;
}

::ng-deep .sharing-doc-popup .mat-mdc-dialog-container {
    border-radius: 35px !important;
    min-width: 500px;
    max-height: calc(100vh - 100px) !important;
    overflow: hidden !important;
}

::ng-deep .sharing-doc-popup .mat-mdc-dialog-container .mdc-dialog__surface {
    overflow: hidden !important;
    background: transparent !important;
}

::ng-deep .sharing-doc-popup app-sharing-doc-dialog {
    max-height: inherit;
    display: block;
}

::ng-deep .sharing-doc-popup .mat-mdc-dialog-container .popup-container {
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

@media (min-width: 1200px) {
    ::ng-deep .sharing-doc-popup .mat-mdc-dialog-container {
        min-width: 500px;
    }
}

/* xl */
@media (min-width: 992px) and (max-width: 1199px) {
    ::ng-deep .sharing-doc-popup .mat-mdc-dialog-container {
        min-width: 400px;
    }
}

/* lg */
@media (min-width: 768px) and (max-width: 991px) {
    ::ng-deep .sharing-doc-popup .mat-mdc-dialog-container {
        min-width: 400px;
    }
}

/* md */
@media (min-width: 576px) and (max-width: 767px) {
    ::ng-deep .sharing-doc-popup .mat-mdc-dialog-container {
        min-width: 400px;
    }
}

/* sm */
@media (max-width: 575px) {
    ::ng-deep .sharing-doc-popup {
        max-width: 100vw !important;
    }

    ::ng-deep .cdk-global-overlay-wrapper {
        align-items: end !important;
    }

    ::ng-deep .cdk-global-overlay-wrapper {
        padding: 0px !important;
    }

    ::ng-deep .sharing-doc-popup .mat-mdc-dialog-container {
        width: 100vw !important;
        border-radius: 0px !important;
    }

    ::ng-deep .sharing-doc-popup .mat-mdc-dialog-container .popup-container {
        overflow-y: scroll;
        max-height: 100vw;
    }
}

/* xs */

::ng-deep .mat-slide-toggle-bar {
    background-color: #363a3e !important;
}

::ng-deep .mat-slide-toggle-thumb {
    background-color: white !important;
    border: 2px #363a3e solid !important;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    background-image: url('/assets/img/minus.svg') !important;
    background-size: 65% auto !important;
}

::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
    background-color: #00aeef !important;
}

::ng-deep .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
    background-color: white !important;
    border: 2px #00aeef solid !important;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    background-image: url('/assets/img/tick-resgistration-completion.svg') !important;
    background-size: 100% auto !important;
}
