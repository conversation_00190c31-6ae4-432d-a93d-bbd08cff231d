import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Inject,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DomSanitizer } from '@angular/platform-browser';
import { BehaviorSubject, firstValueFrom, map } from 'rxjs';
import { EditorLoadingComponent } from '../loading/editor-loading/editor-loading.component';
import { DocInProfileMetadataService, UserService } from '../services';
import { DocEmbeddingComponent } from './doc-embedding/doc-embedding.component';
import { SharingDialogConfig, TabType } from './model';
import { SaveToProfileComponent } from './save-to-profile';
import { SharingComponent } from './sharing';

@Component({
    standalone: true,
    selector: 'app-sharing-doc-dialog',
    templateUrl: './sharing-doc-dialog.component.html',
    imports: [CommonModule, SaveToProfileComponent, SharingComponent, EditorLoadingComponent, DocEmbeddingComponent],
    styleUrls: ['./sharing-doc-dialog.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharingDocDialogComponent implements OnInit, OnDestroy {
    @Output() openDialog = new EventEmitter<void>();

    loadingDocInfo: boolean = true;
    isDocSavedInProfile: boolean = false;

    protected readonly visitedTabs = new Set<TabType>();

    get docGlobalId(): string | null {
        return this.inputData.docGlobalId;
    }

    get needPreviewImage(): boolean {
        return this.inputData.showTabs.includes('share') || this.inputData.showTabs.includes('save');
    }

    protected readonly previewBlob$ = new BehaviorSubject<Blob>(null);
    protected readonly previewBlobUrl$ = new BehaviorSubject<string>(null);
    protected readonly safePreviewUrl$ = this.previewBlobUrl$.pipe(
        map(url => (url ? (this.sanitizer.bypassSecurityTrustUrl(url) as string) : null))
    );

    constructor(
        public userService: UserService,
        private metadataService: DocInProfileMetadataService,
        private dialogRef: MatDialogRef<SharingDocDialogComponent>,
        private cdr: ChangeDetectorRef,
        private sanitizer: DomSanitizer,
        @Inject(MAT_DIALOG_DATA) protected inputData: SharingDialogConfig
    ) {
        if (!this.inputData.selectedTab) this.inputData.selectedTab = 'share';
        if (this.inputData.allowSave === undefined) this.inputData.allowSave = true;

        this.visitedTabs.add(this.inputData.selectedTab);
        this.dialogRef.addPanelClass('sharing-doc-popup');
    }

    async ngOnInit() {
        if (!this.inputData) throw new Error('There is no input data available to display the sharing dialog.');

        if (!this.inputData.docInfo) this.inputData.docInfo = new BehaviorSubject(null);

        if (this.needPreviewImage && this.inputData.docDisplay?.coord && this.inputData.docDisplay?.viewport) {
            // If the preview is needed and coord + viewport available, we need to capture it
            this.capturePreview();
        }

        if (!this.inputData.docInfo.value) {
            try {
                const docInfo = await firstValueFrom(
                    this.metadataService.loadDocumentInfo(this.inputData.edType, this.docGlobalId)
                );
                if (docInfo) {
                    this.inputData.docInfo.next(docInfo);
                    this.isDocSavedInProfile = docInfo.details?.savedToProfile;
                }
            } catch (err) {}
        } else this.isDocSavedInProfile = !!this.inputData.docInfo.value?.details?.savedToProfile;

        this.loadingDocInfo = false;
        this.cdr.detectChanges();
    }

    ngOnDestroy(): void {
        if (this.previewBlobUrl$.value) {
            URL.revokeObjectURL(this.previewBlobUrl$.value);
        }
    }

    /**
     * Captures a preview of the viewport and updates the preview blob and URL.
     */
    private async capturePreview() {
        let blob = null;
        const coord = this.inputData.docDisplay.coord;
        const viewportId = this.inputData.docDisplay.viewport.id;
        const captureType = this.inputData.captureType || 'document';

        switch (captureType) {
            case 'document':
                blob = await coord.captureDocumentPreview(this.inputData.edType, viewportId, this.docGlobalId);
                break;
            case 'viewport':
                blob = await coord.captureViewportPreview(viewportId);
                break;
        }

        if (this.previewBlobUrl$.value) {
            URL.revokeObjectURL(this.previewBlobUrl$.value);
        }

        this.previewBlob$.next(blob);
        this.previewBlobUrl$.next(URL.createObjectURL(blob));
    }

    selectTab(tabName: TabType) {
        this.inputData.selectedTab = tabName;
        this.visitedTabs.add(tabName);
        this.cdr.markForCheck();
    }

    closePopup() {
        this.selectTab(undefined);
        this.dialogRef.close();
    }

    redirectToLogin(tabType: TabType = 'share') {
        const userId = this.userService.curUser$.value?.id;
        if (!userId) {
            this.metadataService.redirectToLoginToBackToPopup(this.inputData.edType, this.docGlobalId, tabType);
        }
    }
}
