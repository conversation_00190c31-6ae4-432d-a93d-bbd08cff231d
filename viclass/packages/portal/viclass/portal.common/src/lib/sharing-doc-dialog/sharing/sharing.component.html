<ng-template [ngIf]="!(isPreviewing$ | async)">
    <div class="flex flex-col gap-y-[15px]">
        <div class="flex flex-col gap-y-[15px]">
            <div class="mb-[25px]">
                <strong class="text-[24px]">{{
                    inputData.useCloneDoc ? 'XUẤT BẢN TÀI LIỆU' : 'CHIA SẺ TÀI LIỆU'
                }}</strong>
            </div>
            <strong class="text-[14px]">Xuất bản bằng</strong>
        </div>

        <div class="flex flex-row gap-[10px] text-center justify-start">
            <button class="vi-btn vi-btn-small vi-btn-outline" [clickWait]="1000" (click)="printPDF()">Tải PDF</button>
            <button
                class="vi-btn vi-btn-small vi-btn-outline"
                (click)="shareImage()"
                [clickWait]="(!needPreviewViewport && !previewBlob) || (processingCopy$ | async)">
                <img
                    *ngIf="!needPreviewViewport && !previewBlob"
                    class="object-cover h-[1.15rem] w-[1.15rem] mr-1"
                    src="assets/img/mini-spinner.svg" />
                Sao chép PNG
            </button>
        </div>

        <hr class="border-BW4" *ngIf="!inputData.useCloneDoc" />

        <div class="flex flex-col gap-y-[15px]" *ngIf="!inputData.useCloneDoc">
            <div>
                <strong class="pr-[10px] text-[14px]">Chia sẻ URL</strong>
                <mat-slide-toggle
                    [disabled]="!(isSaved$ | async)"
                    [checked]="isShared$ | async"
                    (change)="onToggleChange($event)"></mat-slide-toggle>
            </div>

            <div
                *ngIf="!(isSaved$ | async)"
                class="flex gap-[10px] items-center align-middle bg-PAS1 rounded-[15px] px-[10px] py-[5px]">
                <span class="vcon-general vcon_info"
                    ><span class="path1"></span><span class="path2"></span><span class="path3"></span
                ></span>
                <div class="vi-popup-message text-center">
                    <div class="text-[14px] text-justify">
                        <a class="text-P1 text-[14px]" (click)="redirectSaveDocument.emit()"> Thêm tài liệu </a>
                        vào trang cá nhân để mở chức năng này!
                    </div>
                </div>
            </div>

            <div *ngIf="isShared$ | async">
                <div
                    class="flex items-center flex-row gap-[10px] text-center justify-between overflow-hidden pb-[20px]">
                    <span
                        class="text-[16px] overflow-hidden text-ellipsis whitespace-nowrap align-middle text-center inline-block">
                        {{ shareLink }}
                    </span>
                    <button
                        (click)="copyToClipboard(shareLink)"
                        [clickWait]="1000"
                        class="vi-btn vi-btn-small vi-btn-focus flex-grow-1 d-flex justify-center min-w-[100px] items-center"
                        type="button">
                        Sao chép
                    </button>
                </div>

                <hr class="border-t-[2px] my-[10px] bg-BW4" />

                <div class="pt-[10px]">
                    <sharing-footer-social [link]="shareLink"></sharing-footer-social>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template [ngIf]="isPreviewing$ | async">
    <div class="flex flex-col gap-y-[15px]">
        <div class="flex relative flex-col gap-y-[15px]">
            <div class="flex gap-3">
                <strong class="text-[24px] cursor-pointer" (click)="isPreviewing$.next(false)">
                    <span class="vcon vcon-general vcon_arrow_back"></span>
                </strong>
                <strong class="text-[24px]">KHUNG XUẤT BẢN</strong>
            </div>

            <div>
                <div #previewArea></div>
            </div>
            <div class="flex flex-row gap-[10px] text-center justify-start mt-3">
                <button
                    class="vi-btn vi-btn-small vi-btn-outline"
                    [disabled]="!(previewDocLoaded$ | async)"
                    (click)="printPDF()"
                    [clickWait]="1000">
                    Tải PDF
                </button>
                <button
                    class="vi-btn vi-btn-small vi-btn-outline"
                    [clickWait]="!(previewDocLoaded$ | async) || (processingCopy$ | async)"
                    (click)="shareImage()">
                    Sao chép PNG
                </button>
            </div>
        </div>
    </div>
</ng-template>
