export const fromUnixToDate = (unixTimestamp: number): Date => new Date(unixTimestamp);

export const formatUnixToDDMMYYYY = (unixTimestamp: number): string => {
    if (unixTimestamp == null) return '';

    const date = fromUnixToDate(unixTimestamp);
    return formatDateToDDMMYYYY(date);
};

export const formatDateToDDMMYYYY = (date: Date): string => {
    return (
        date.getDate().toString().padStart(2, '0') +
        '/' +
        (date.getMonth() + 1).toString().padStart(2, '0') +
        '/' +
        date.getFullYear().toString()
    );
};

export const fromDDMMYYYYToDate = (date: string): Date => new Date(date);
