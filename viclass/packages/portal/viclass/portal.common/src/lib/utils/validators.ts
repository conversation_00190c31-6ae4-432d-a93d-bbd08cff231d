import { AbstractControl, ValidationErrors } from '@angular/forms';

/**
 * Form validator to checks if a given full name is valid.
 *
 * A valid full name is one that matches the following rules:
 * 1. Contains only letters, spaces, and the characters .',-
 * 2. The length is between 2 and 100 characters
 * 3. Does not contain consecutive spaces
 * 4. Does not contain any digits
 */
export function fullNameValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value?.trim();

    if (!value) return null; // Ignore empty values (handled by `Validators.required`)

    if (value.length < 2) {
        return { tooShort: true };
    }

    if (value.length > 100) {
        return { tooLong: true };
    }

    if (/\d/.test(value)) {
        return { containsNumbers: true };
    }

    if (/\s{2,}/.test(value)) {
        return { consecutiveSpaces: true };
    }

    if (!/^[\p{L} .'-]+$/u.test(value)) {
        return { invalidCharacters: true };
    }

    return null;
}

/**
 * Form validator to checks if a given phone number is a valid Vietnam phone number.
 *
 * A valid Vietnam phone number is one that matches the following rules:
 * 1. Starts with either "0" (for national numbers) or "+84" (for international numbers)
 * 2. Followed by exactly 9 digits
 */

export function phoneValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;

    if (!value) {
        return null; // Let 'required' validator handle empty values
    }

    if (!/^\+?\d+$/.test(value)) {
        return { invalidCharacters: true };
    }

    if (value.startsWith('+84')) {
        if (value.length !== 12) {
            return { invalidLength: true };
        }
    } else if (value.startsWith('0')) {
        if (value.length !== 10) {
            return { invalidLength: true };
        }
    } else {
        return { invalidFormat: true };
    }

    return null;
}

/**
 * Form validator to checks if a given password is valid.
 *
 * A valid password is one that matches the following rules:
 * 1. At least one uppercase letter
 * 2. At least one lowercase letter
 * 3. At least one digit
 * 4. At least one special character
 * 5. Length of at least 6 characters
 * 6. No spaces
 */

export function passwordValidator(control: AbstractControl): ValidationErrors | null {
    const value: string = control.value;

    if (!value) {
        return null; // Let 'required' validator handle empty values
    }

    if (value.length < 6) {
        return { minlength: true };
    }
    if (value.length > 20) {
        return { maxlength: true };
    }
    if (!/[A-Z]/.test(value)) {
        return { uppercaseRequired: true };
    }
    if (!/[a-z]/.test(value)) {
        return { lowercaseRequired: true };
    }
    if (!/[0-9]/.test(value)) {
        return { numberRequired: true };
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(value)) {
        return { specialCharacterRequired: true };
    }
    if (/\s/.test(value)) {
        return { noSpaces: true };
    }
    if (
        !/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?\":{}|<>])[A-Za-z\d!@#$%^&*(),.?\":{}|<>]{6,20}$/.test(
            value
        )
    ) {
        // for other cases that we don't want to check too details
        return { invalidPassword: true };
    }

    return null;
}

/**
 * Form validator to checks if a given username is valid.
 *
 * A valid username is one that matches the following rules:
 * 1. Starts with a letter
 * 2. Followed by 3-29 alphanumeric characters or underscores
 */

export function usernameValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;

    if (!value) return null; // Ignore empty values (handled by `required` validator)

    if (!/^[A-Za-z]/.test(value)) {
        return { mustStartWithLetter: true };
    }

    if (!/^[A-Za-z0-9_.]{4,30}$/.test(value)) {
        return { invalidCharacters: true };
    }

    return null;
}

/**
 * Form validator to checks if a given email address is valid.
 *
 * A valid email address is one that matches the following rules:
 * 1. Matches the basic format of an email address (letters, numbers, underscores, periods, and hyphens)
 * 2. Does not contain consecutive dots
 * 3. Has a TLD (top-level domain) of at least 2 characters
 */

export function emailValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null; // Allow empty values, handled separately

    const domainPart = control.value.split('@')?.[1];
    if (domainPart) {
        const tld = domainPart.substring(domainPart.lastIndexOf('.') + 1);
        if (tld.length < 2) {
            return { invalidTld: true }; // Ensure TLD is at least 2 characters
        }
    }

    const allowCharsRegex = /^[\w-.@]+$/;

    if (!allowCharsRegex.test(control.value)) {
        return { invalidCharacters: true }; // Invalid characters
    }

    if (control.value.includes('..')) {
        return { consecutiveDots: true }; // Prevent consecutive dots
    }

    const emailRegex = /^[\w-.]+@([\w-]+\.)+[\w-]{2,}$/;

    if (!emailRegex.test(control.value)) {
        return { invalidEmail: true }; // Invalid email format
    }

    return null; // Valid email
}
