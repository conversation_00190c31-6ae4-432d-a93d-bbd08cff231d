import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule, Routes } from '@angular/router';
import {
    loginRequiredFn,
    onlyClientSideFn,
    profileResolveFn,
    redirectOnLoggedInFn,
    NotFoundPageComponent,
    InternalServerErrorComponent,
} from '@viclass/portal.common';
import { environment } from '../environments/environment';
import { HomePageComponent } from './home-page/home-page.component';
import { HomeLayoutComponent } from './home-layout/home-layout.component';
import { HiringPageComponent } from './hiring-page/hiring-page.component';
import { RegistrationPageComponent } from './registration-page/registration-page.component';
import { LoginPageComponent } from './login-page/login-page.component';
import { FeaturePageComponent } from './feature-page/feature-page.component';
import { ComingSoonComponent } from './coming-soon/coming-soon.component';
import { TncPageComponent } from './tnc-page/tnc-page.component';

const routes: Routes = [
    {
        path: '',
        component: HomeLayoutComponent,
        children: [
            {
                path: '',
                component: HomePageComponent,
                data: {
                    name: 'home',
                },
            },
            {
                path: 'hiring',
                component: HiringPageComponent,
                data: {
                    name: 'hiring',
                },
            },
            {
                path: 'terms-and-conditions',
                component: TncPageComponent,
                data: {
                    name: 'tnc',
                },
            },
            {
                path: 'registration',
                component: RegistrationPageComponent,
                canActivate: [redirectOnLoggedInFn],
            },
            {
                path: 'login',
                component: LoginPageComponent,
                canActivate: [redirectOnLoggedInFn],
            },
            {
                path: 'registration/verify/:registrationId',
                loadComponent: () =>
                    import('./verification-page/verification-page.component').then(m => m.VerificationPageComponent),
                canActivate: [onlyClientSideFn],
            },
            {
                path: 'registration/missing-email/:registrationId',
                loadComponent: () =>
                    import('./enter-missing-email/enter-missing-email.component').then(
                        m => m.EnterMissingEmailComponent
                    ),
                canActivate: [onlyClientSideFn],
            },
            {
                path: 'registration-completion',
                loadComponent: () =>
                    import('./registration-completion-page/registration-completion-page').then(
                        m => m.RegistrationCompletionPageComponent
                    ),
                canActivate: [onlyClientSideFn],
            },
            {
                path: 'email-change-notification/:oldEmail',
                loadComponent: () =>
                    import('./email-change-notification/email-change-notification').then(
                        m => m.EmailChangeNotificationPageComponent
                    ),
                canActivate: [onlyClientSideFn],
            },
            {
                path: 'forgot-password',
                loadComponent: () =>
                    import('./forgot-password/forgot-password').then(m => m.ForgotPasswordPageComponent),
                canActivate: [onlyClientSideFn],
            },
            {
                path: 'reset-password/:token',
                loadComponent: () => import('./reset-password/reset-password').then(m => m.ResetPasswordPageComponent),
                canActivate: [onlyClientSideFn],
            },
        ],
        resolve: {
            profile: profileResolveFn,
        },
    },
    {
        path: 'internal-server-error',
        component: InternalServerErrorComponent,
    },
    {
        path: '',
        component: HomeLayoutComponent,
        children: [
            {
                path: 'features',
                component: FeaturePageComponent,
                data: {
                    name: 'features',
                },
            },
        ],
        resolve: {
            profile: profileResolveFn,
        },
    },
    {
        path: 'user/doc',
        resolve: {
            profile: profileResolveFn,
        },
        canActivate: [onlyClientSideFn],
        children: [
            {
                path: ':edType/:id',
                loadComponent: () =>
                    import('./document-page/document-page.component').then(m => m.DocumentPageComponent),
            },
            {
                path: 'embed/:edType/:id',
                providers: [],
                loadComponent: () =>
                    import('./embedded-page/embedded-page.component').then(m => m.EmbeddedPageComponent),
            },
        ],
    },
    {
        path: 'profile',
        loadChildren: () => import('./profile-page/profile-page.module').then(m => m.ProfilePageModule),
        resolve: {
            profile: profileResolveFn,
        },
        canActivate: [onlyClientSideFn, loginRequiredFn],
    },
    environment.enableBeta
        ? {
              path: 'beta/coming-soon',
              component: ComingSoonComponent,
          }
        : {},
    { path: '**', component: NotFoundPageComponent },
];

@NgModule({
    imports: [
        CommonModule,
        BrowserModule,
        RouterModule.forRoot(routes, {
            useHash: false,
            scrollPositionRestoration: 'enabled',
            anchorScrolling: 'enabled',
            initialNavigation: 'enabledBlocking',
        }),
    ],
    exports: [RouterModule],
})
export class AppRoutingModule {}
