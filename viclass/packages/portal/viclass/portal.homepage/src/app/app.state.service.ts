import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { HomePageComponent } from './home-page/home-page.component';
import { FeaturePageComponent } from './feature-page/feature-page.component';
import { HiringPageComponent } from './hiring-page/hiring-page.component';
import { LoginPageComponent } from './login-page/login-page.component';
import { RegistrationPageComponent } from './registration-page/registration-page.component';
import { ProfilePageComponent } from './profile-page/profile-page.component';

/**
 * Containing states of the application
 */
@Injectable({
    providedIn: 'root',
})
export class AppStateService {
    curPage$: BehaviorSubject<
        | typeof HomePageComponent
        | typeof FeaturePageComponent
        | typeof HiringPageComponent
        | typeof LoginPageComponent
        | typeof RegistrationPageComponent
        | typeof ProfilePageComponent
    > = new BehaviorSubject(HomePageComponent);

    showLogin$: BehaviorSubject<boolean> = new BehaviorSubject(true);
}
