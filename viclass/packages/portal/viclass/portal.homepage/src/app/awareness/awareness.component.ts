import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    SimpleChanges,
} from '@angular/core';
import { EmbedCoordinator } from '@viclass/editor.coordinator/embed';
import { Awareness, ViewportId } from '@viclass/editor.core';
import { NotificationService } from '@viclass/portal.common';
import { map, Observable, of, tap } from 'rxjs';

const AW_TYPES_TO_SHOW = ['aw-document', 'aw-loading'];

@Component({
    standalone: true,
    imports: [CommonModule],
    selector: 'awareness',
    templateUrl: './awareness.component.html',
    styles: [],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AwarenessComponent implements AfterViewInit, OnInit, OnDestroy, OnChanges {
    @Input() coord: EmbedCoordinator;
    @Input() vpId: ViewportId;

    awareness$: Observable<Awareness[]>;

    constructor(private notificationService: NotificationService) {
        this.awareness$ = of([]);
    }

    ngOnInit(): void {
        this.updateAwareness();
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['coord'] || changes['vpId']) this.updateAwareness();
    }

    ngAfterViewInit(): void {}

    ngOnDestroy(): void {}

    private updateAwareness(): void {
        this.awareness$ =
            this.coord.awarenessFeature.getReceivedAwareness(this.vpId)?.pipe(
                tap(this.showNotifications),
                map(awareness => awareness.filter(a => AW_TYPES_TO_SHOW.includes(a.options.type)))
            ) ?? of([]);
    }

    private showNotifications = (awareness: Awareness[]) => {
        awareness
            .filter(a => a.options.type === 'aw-notification')
            .forEach(a =>
                this.notificationService.showNotification({
                    message: a.message,
                    status: a.options.payload?.msgType ?? 'info',
                    duration: 2000,
                })
            );
    };
}
