<div class="bg">
    <div class="banner"></div>
    <ng-container *ngIf="!isSuccess">
        <p class="text-center text-[14px] font-medium leading-[21px] text-BW3">
            Xem <a href="/features" class="text-P1 no-underline">tính năng nổi bật</a> và dùng thử
        </p>
        <form
            class="email-form flex flex-row items-center justify-center w-[170px] mx-auto gap-[7px]"
            *fflow="let fum; by: buildForm; fflow as f; submit: submitForm; noNav: true"
            [formGroup]="fum"
            [ferrcoord]="f"
            [(ferror)]="formError">
            <div class="w-0 h-[21px] z-10">
                <i class="mail ml-[15px] text-gray-400 text-[12px] vcon-general vcon_footer_email prepend-icon"></i>
            </div>
            <input
                name="email"
                class="font-['Montserrat'] p-[10px] pl-[30px] rounded-[12px] shadow-none border border-solid border-BW4 italic text-black outline-none placeholder:text-BW4 text-[14px]"
                placeholder="Email đăng ký"
                formControlName="email"
                [(ferror)]="emailError" />
            <div
                [ngClass]="{
                    'px-[2px]': f.canSubmit(),
                }">
                <button
                    type="submit"
                    [ngClass]="{
                        'vi-btn vi-btn-normal vi-btn-focus cursor-pointer font-[\'Montserrat\'] text-[14px]': true,
                        'w-[44px]': loading,
                    }"
                    [disabled]="loading || !f.canSubmit()"
                    [form-flow-submit]="f">
                    <span [ngClass]="{ '!hidden': loading }">Gửi</span>
                    <img
                        [ngClass]="{ hidden: !loading }"
                        class="object-cover h-[16px] w-[16px]"
                        src="assets/img/mini-spinner.svg" />
                </button>
            </div>
        </form>
    </ng-container>
    <div class="max-w-[450px] mx-auto text-center" *ngIf="isSuccess || isError">
        <p class="font-[700] text-[18px] leading-[27px] text-P1" *ngIf="isSuccess">
            {{ isAlreadyRegistered ? 'Cám ơn bạn đã nhập email' : 'Cảm ơn bạn đã đăng ký' }}
            <span class="vcon-general vcon_icon_heart text-SC5 text-md inline-block -mb-3"></span>
        </p>
        <span *ngIf="isSuccess" class="text-[14px] text-center block mt-[10px] px-[6px] text-BW3">
            {{
                isAlreadyRegistered
                    ? 'Vui lòng kiểm tra email! Chúng tôi đã gửi cho bạn liên kết để trải nghiệm hệ thống.'
                    : 'Chúng tôi sẽ gửi cho bạn liên kết để trải nghiệm hệ thống qua email trong thời gian sớm nhất có thể!'
            }}
        </span>
        <span *ngIf="isError" class="text-[14px] text-center block mt-[10px] px-[6px] text-SC5">
            Có lỗi xảy ra! Vui lòng thử lại!
        </span>
    </div>
    <div class="text-center text-[14px] text-BW3 font-medium leading-[21px]">
        Liên hệ với chúng tôi qua email:
        <a class="text-P1 underline" href="mailto:<EMAIL>">support&#64;viclass.vn</a>
    </div>
    <div style="height: 3rem"></div>
</div>
