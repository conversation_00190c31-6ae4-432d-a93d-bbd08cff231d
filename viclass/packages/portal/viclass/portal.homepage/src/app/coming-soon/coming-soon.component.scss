.bg {
    width: 100vw;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
    background-image: url('../../assets/img/coming-soon/bg-coming-soon.webp');
    background-position: center top;
    background-size: max(max(100vw, 1400px), 100vh) auto;
    background-repeat: no-repeat;
    @apply flex flex-col gap-[12px];
}

.banner {
    margin: 10vw auto 0;
    width: 18vw;
    height: 0;
    padding-top: 15vw;
    background-image: url('../../assets/img/coming-soon/banner-coming-soon.svg');
    background-position: center top;
    background-size: cover;
    background-repeat: no-repeat;
}

@media screen and (max-width: 1275px) {
    .banner {
        margin: 127.5px auto 0;
        width: 229.5px;
        height: 0;
        padding-top: 191.25px;
        background-image: 'banner-coming-soon.svg';
        background-position: center top;
        background-size: cover;
        background-repeat: no-repeat;
    }
}

@media screen and (max-width: 250px) {
    .banner {
        width: 100vw;
        padding-top: 80vw;
        background-size: contain;
    }
    .email-form {
        @apply w-[90vw];
    }
}
