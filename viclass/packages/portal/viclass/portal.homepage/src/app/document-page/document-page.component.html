<app-editor-loading [loading]="loading$ | async"></app-editor-loading>

<div class="v-toolbar absolute top-4 left-4 z-20">
    <div class="v-tool-group">
        <a href="/" class="v-tool-btn">
            <img src="assets/img/vi-logo.svg" class="h-6" />
        </a>
    </div>
</div>

<div *ngIf="docInfo$ | async as docInfo" class="absolute top-2 right-5 z-20">
    <div class="flex flex-col justify-start items-end bg-BW7 rounded-[15px] py-1 px-2">
        <div class="flex flex-row gap-2 items-center">
            <span class="text-SC1 vcon-general {{ editorIcon }}"></span>
            <p class="font-bold">{{ docInfo.details.docName }}</p>
        </div>
        <div *ngIf="!(loading$ | async) && !isOwn" class="flex items-center">
            <p class="italic text-xs">
                <PERSON><PERSON><PERSON><PERSON> tạ<PERSON> bởi <span class="font-bold">{{ docInfo.details.ownerUserName }}</span>
            </p>
        </div>
    </div>
</div>

<div *ngIf="!(loading$ | async)" class="documentpage-viewport w-full h-full" [ngClass]="{ 'is-own': isOwn }">
    <div
        *ngIf="(docInfo$ | async)?.details as docDetails; else errorContainer"
        app-viewport-manager
        [vpToolBar]="isOwn"
        [allowReset]="false"
        [allowInfoToggle]="false"
        (onCmd)="onViewportCmd($event)"
        [uiModules]="uiModules()"
        [edType]="edType"
        [coord]="this.documentService.coord"
        [docId]="id"
        [docName]="docDetails.docName"
        class="viewport-container absolute w-full h-full"
        [viewportClasses]="edType === 'WordEditor' ? 'pt-12 pl-16 pb-12 pr-2' : ''"></div>

    <ng-template #errorContainer>
        <div class="w-full h-full flex items-center justify-center">
            <div
                class="min-w-[300px] min-h-[300px] bg-BW7 rounded-[15px] overflow-hidden flex flex-col items-center justify-center">
                <div class="p-10 pb-5 flex flex-col gap-6 items-center">
                    <img src="/assets/img/alert-error.svg" width="184" height="100" />
                    <p class="text-BW1">{{ errorOnLoadData || 'Lỗi không xác định' }}</p>
                    <div class="flex gap-2 items-center justify-center">
                        <a href="/profile/documents" class="vi-btn vi-btn-normal vi-btn-outline ml-auto mr-auto">
                            Trang tài liệu
                        </a>
                        <button
                            class="vi-btn vi-btn-normal vi-btn-gradient ml-auto mr-auto"
                            (click)="handleLoadDocRetry()">
                            Thử lại
                        </button>
                    </div>
                </div>
                <div class="bg-BW5 w-full h-[50px] p-5 flex items-center justify-center">
                    <p class="text-sm">
                        <a class="text-P1 underline" href="/#contact-form" target="_blank">Liên hệ</a> với chúng tôi nếu
                        bạn cần sự trợ giúp.
                    </p>
                </div>
            </div>
        </div>
    </ng-template>
</div>
