import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { AfterViewInit, ChangeDetectionStrategy, Component, Injector } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ActivatedRoute } from '@angular/router';
import { EditorType, InitializationErr, ViErr } from '@viclass/editor.core';
import { EditorUILoaderModule } from '@viclass/editorui.loader';
import {
    DocumentDocMetadataService,
    DocumentInfoResponse,
    DocumentService,
    EditorLoadingComponent,
    FormUtilModule,
    NotificationModule,
    SharingDialogConfig,
    SharingDocDialogComponent,
    TabType,
    UserService,
    UserServiceModule,
} from '@viclass/portal.common';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { environment } from '../../environments/environment';
import { ViewportManagerComponent, VpCmd } from '../viewport-manager/viewport-manager.component';
import { EDITOR_ICONS } from '../model';

const e = environment.confEnv;

/**
 * Component for document page for the doc user has saved into profile or shared by other users
 */
@Component({
    selector: 'app-document-page',
    templateUrl: './document-page.component.html',
    standalone: true,
    imports: [
        CommonModule,
        FormUtilModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatSlideToggleModule,
        NotificationModule,
        FormsModule,
        EditorUILoaderModule,
        OverlayModule,
        UserServiceModule,
        EditorLoadingComponent,
        ViewportManagerComponent,
    ],
    providers: [DocumentService, DocumentDocMetadataService],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentPageComponent implements AfterViewInit {
    // path params
    id: string;
    edType: EditorType;
    isOwn: boolean = false;
    enableShareToolbar: boolean = false;

    errorOnLoadData?: string = undefined;
    stoppedSharing?: boolean = undefined;

    protected readonly loading$ = new BehaviorSubject<boolean>(true);
    protected readonly docInfo$ = new BehaviorSubject<DocumentInfoResponse | null>(null);

    get editorIcon(): string {
        return EDITOR_ICONS[this.edType] || '';
    }

    constructor(
        private route: ActivatedRoute,
        private userService: UserService,
        public documentService: DocumentService,
        private documentDocMetadataService: DocumentDocMetadataService,
        private dialog: MatDialog,
        private injector: Injector
    ) {}

    async ngAfterViewInit() {
        this.id = this.route.snapshot.paramMap.get('id');
        this.edType = <EditorType>this.route.snapshot.paramMap.get('edType');

        await this.loadDocumentInfo();
    }

    /**
     * To be called from the UI to generate UI config for the viewport component
     */
    uiModules() {
        if (this.isOwn) return this.documentService.generateEmbedViewportUIConfig(this.edType);
        else return this.documentService.generateEmbedViewportUIConfig(this.edType, false);
    }

    /**
     * Load editor config and initialize the coordinator
     */
    private async initCoord() {
        try {
            const addEditors: EditorType[] = [this.edType];
            if (this.documentService.canZoomConfspec(this.edType)) addEditors.push('ZoomToolsEditor');
            await this.documentService.retrieve(e, addEditors);
            await this.documentService.createOrUpdateCoordinator();
        } catch (err) {
            throw new InitializationErr('Khởi tạo tài liệu thất bại', err);
        }
    }

    /**
     * Handle initialization, load shared document info and update relevant variables.
     *
     * If the user is not logged in, load document info as guest.
     * Only allow showing the viewport if the document is shared or user is the doc owner.
     */
    private async loadDocumentInfo(isRetry?: boolean): Promise<boolean> {
        this.errorOnLoadData = undefined;
        this.stoppedSharing = undefined;
        this.loading$.next(true);

        try {
            await this.initCoord();

            const user = this.userService.curUser$.value;

            // process doc info
            const docInfo = await firstValueFrom(
                user?.id
                    ? this.documentDocMetadataService.loadSharedDocumentInfo(this.edType, this.id)
                    : this.documentDocMetadataService.loadSharedDocumentInfoGuest(this.edType, this.id)
            );
            this.docInfo$.next(docInfo);
            if (!docInfo) throw new InitializationErr('Không thể tải thông tin tài liệu');

            // process user
            this.isOwn = user?.id ? docInfo?.details?.ownerUserId === user.id : false;

            this.loading$.next(false);
            return true;
        } catch (err) {
            if (err instanceof HttpErrorResponse) {
                switch (err.status) {
                    case HttpStatusCode.NotFound:
                        this.errorOnLoadData = 'Không tìm thấy tài liệu';
                        break;
                    case HttpStatusCode.Forbidden:
                        this.errorOnLoadData = 'Tài liệu đã ngừng chia sẻ';
                        this.stoppedSharing = true;
                        break;
                    default:
                        this.errorOnLoadData = `Lỗi không xác định (${err.status})`;
                        break;
                }
            } else if (err instanceof ViErr) {
                this.errorOnLoadData = err.message || 'Lỗi không xác định';
            } else {
                this.errorOnLoadData = 'Lỗi không xác định';
            }

            // we don't want to stop the loading spinner on retry as we will fully reload the page after this
            if (!isRetry) this.loading$.next(false);

            return false;
        }
    }

    /**
     * To be called from the UI to retry loading the document.
     * If the document is no longer shared, we will attempt to reload in-place first before reload the full page.
     */
    async handleLoadDocRetry() {
        if (!this.stoppedSharing) {
            window.location.reload();
            return;
        }

        const success = await this.loadDocumentInfo(true);
        if (!success) {
            window.location.reload();
        }
    }

    /**
     * Handle cmd from the viewport component.
     * E.g. to know when the viewport is ready so we can start load the doc on viewport
     */
    onViewportCmd(cmd: VpCmd) {
        switch (cmd.type) {
            case 'share': // handle sharing
                this.onShare(cmd.cmp);
                break;
            case 'viewport-ready':
                this.onViewportReady(cmd.cmp);
                break;
        }
    }

    /**
     * Handle `viewport-ready` cmd from the viewport component.
     * Start loading the doc on viewport and decide the viewport mode
     * based on the current user is owner or not
     */
    private async onViewportReady(cmp: ViewportManagerComponent) {
        await this.documentService.coord.loadDocOnViewport(cmp.viewport, {
            gId: this.id,
            edType: this.edType,
        });

        if (this.isOwn) cmp.use('EditMode');
        else cmp.use('InteractiveMode');
    }

    /**
     * Handle `share` cmd from the viewport component to show the sharing dialog
     */
    private async onShare(cmp: ViewportManagerComponent, defaultTab: TabType = 'share') {
        const showTabs: TabType[] = ['share', 'embed', 'save'];
        if (!showTabs.find(t => t === defaultTab)) {
            defaultTab = showTabs[0];
        }

        this.dialog.open<SharingDocDialogComponent, SharingDialogConfig>(SharingDocDialogComponent, {
            injector: this.injector,
            data: {
                showTabs: showTabs,
                selectedTab: defaultTab,
                captureType: 'viewport',
                edType: cmp.edType,
                source: 'doc-ownership',
                docGlobalId: this.docInfo$.value.docGlobalId,
                docInfo: this.docInfo$,
                docDisplay: {
                    coord: this.documentService.coord,
                    viewport: cmp.viewport,
                },
            },
        });
    }
}
