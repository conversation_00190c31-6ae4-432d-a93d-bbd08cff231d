<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/login-animation.svg">
            <img src="assets/img/login-animation.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[370px] w-[290px] mx-auto">
            <div class="text-[36px]"><strong>Đ<PERSON>ng nhập thành công</strong></div>
            <div>
                <div class="text-[14px] mt-[15px] text-red-500">! Email đăng ký Facebook của bạn đã thay đổi.</div>

                <div class="text-[14px] mt-[15px] text-red-500">
                    Li<PERSON>n kết Facebook này sẽ liên kết như một tài khoản mới trên viclass.
                </div>
                <div class="text-[14px] mt-[15px]">
                    Bạn có thể đăng nhập bằng email
                    <span class="text-P1" *ngIf="oldEmail$ | async as oldEmail">{{ oldEmail }}</span>
                    để không làm gián đoạn quá trình học
                </div>

                <div class="flex items-start mt-[15px]">
                    <button class="vi-btn vi-btn-normal vi-btn-focus" (click)="returnUrl()">Tiếp tục</button>
                    <button class="vi-btn vi-btn-normal vi-btn-outline ml-[15px]" (click)="returnLogin()">
                        Đăng nhập lại
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>
