import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { FormUtilModule, PKEY_RURL, SpinnerLabelComponent, UserService } from '@viclass/portal.common';
import { BehaviorSubject, firstValueFrom } from 'rxjs';
import { InputUppercaseDirective } from '../input-uppercase.directive';
import { CountDownComponent } from 'packages/portal/viclass/portal.common/src/lib/countdown';
import { environment } from '../../environments/environment';

@Component({
    selector: 'app-email-change-notification-page',
    standalone: true,
    imports: [
        RouterModule,
        FormUtilModule,
        ReactiveFormsModule,
        CommonModule,
        SpinnerLabelComponent,
        InputUppercaseDirective,
        CountDownComponent,
    ],
    templateUrl: './email-change-notification.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmailChangeNotificationPageComponent implements OnInit {
    readonly oldEmail$ = new BehaviorSubject<string>('');
    constructor(
        private userService: UserService,
        private route: ActivatedRoute,
        private router: Router
    ) {}

    async ngOnInit() {
        const oldEmailEncoded = this.route.snapshot.paramMap.get('oldEmail');

        const oldEmail = window.atob(decodeURIComponent(oldEmailEncoded));

        this.oldEmail$.next(oldEmail);
    }

    async returnUrl() {
        var params = await firstValueFrom(this.route.queryParams);
        if (params && params[PKEY_RURL])
            window.location.href = JSON.parse(window.atob(decodeURIComponent(params[PKEY_RURL]))).rURL;
        else this.router.navigate([environment.authflowConfig.defaultReturnUrl]);
    }

    async returnLogin() {
        await firstValueFrom(this.userService.doLogout());

        var params = await firstValueFrom(this.route.queryParams);
        if (params && params[PKEY_RURL]) window.location.href = `/login?${PKEY_RURL}=${params[PKEY_RURL]}`;
        else window.location.href = '/login';
    }
}
