/// <reference types="@viclass/ww/typings" />

import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BoardType } from '@viclass/editor.coordinator/docloader';
import { DocumentId, EditorType } from '@viclass/editor.core';
import { DocInProfileMetadataService } from '@viclass/portal.common';
import '@viclass/ww/mfe.vi.docloader';

@Component({
    selector: 'app-embedded-page',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './embedded-page.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmbeddedPageComponent implements OnInit, AfterViewInit, OnDestroy {
    public id: DocumentId;
    public edType: EditorType;

    public lookAtX: number = 0;
    public lookAtY: number = 0;
    public zoomLevel: number = 1;

    @ViewChild('vpContainer') vpContainer: ElementRef<HTMLDivElement>;

    get viewportType(): BoardType {
        return this.edType === 'WordEditor' ? 'inline' : 'board';
    }

    private removeVpEventFuncs: viclass.ViDocLoader.RemoveEventListenerFunc[] = [];

    constructor(
        private route: ActivatedRoute,
        private metadataService: DocInProfileMetadataService
    ) {}

    async ngOnInit(): Promise<void> {
        this.id = this.route.snapshot.paramMap.get('id');
        this.edType = <EditorType>this.route.snapshot.paramMap.get('edType');

        const lookAtX = this.route.snapshot.queryParamMap.get('lookAtX');
        if (lookAtX) {
            this.lookAtX = Number(lookAtX) || 0;
        }
        const lookAtY = this.route.snapshot.queryParamMap.get('lookAtY');
        if (lookAtY) {
            this.lookAtY = Number(lookAtY) || 0;
        }
        const zoomLevel = this.route.snapshot.queryParamMap.get('zoom');
        if (zoomLevel) {
            this.zoomLevel = Number(zoomLevel) || 1;
        }
    }

    async ngAfterViewInit(): Promise<void> {
        try {
            await this.metadataService.checkAllowEmbedding(this.id, this.edType);
            await viclass.ViDocLoader.initialize();
            const vpId = await viclass.ViDocLoader.renderEl(this.vpContainer.nativeElement);

            this.addViewportChangeListener(vpId);
        } catch (err) {
            // 403 if doc is not embeddable or 404 if doc not found
            console.error(err);
        }
    }

    ngOnDestroy(): void {
        this.removeVpEventFuncs.forEach(func => func?.());
    }

    private async addViewportChangeListener(vpId: string) {
        const vpType = this.viewportType;
        if (vpType === 'inline') return;

        const removePanEv = await viclass.ViDocLoader.addViewportEventListener(
            vpId,
            vpType,
            'viewport-pan',
            this.handleViewportEvent
        );
        const removeZoomEv = await viclass.ViDocLoader.addViewportEventListener(
            vpId,
            vpType,
            'viewport-zoom',
            this.handleViewportEvent
        );
        this.removeVpEventFuncs.push(removePanEv, removeZoomEv);
    }

    private handleViewportEvent = (event: viclass.ViDocLoader.ViewportEvent) => {
        window.parent?.dispatchEvent(
            new CustomEvent('viewport-change', {
                detail: event,
            })
        );
    };
}
