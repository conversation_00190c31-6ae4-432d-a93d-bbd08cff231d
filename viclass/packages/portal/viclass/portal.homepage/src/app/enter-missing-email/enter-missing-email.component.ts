import { CommonModule } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
    AbstractControl,
    AsyncValidatorFn,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    ValidationErrors,
    ValidatorFn,
    Validators,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
    EnterMissingEmailData,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    PKEY_RURL,
    ProcessingRequestManager,
    RegistrationMetadata,
    SpinnerLabelComponent,
    UserService,
} from '@viclass/portal.common';
import { BehaviorSubject, catchError, debounceTime, firstValueFrom, map, Observable, of, switchMap, take } from 'rxjs';
import { AppStateService } from '../app.state.service';

const ADD_MISING_EMAIL = 'addMissingEmail';

@Component({
    selector: 'app-enter-missing-email',
    standalone: true,
    imports: [RouterModule, FormUtilModule, ReactiveFormsModule, CommonModule, SpinnerLabelComponent],
    templateUrl: './enter-missing-email.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EnterMissingEmailComponent {
    registrationId: string;
    form: UntypedFormGroup;

    emailError: ErrorModel;
    emailRetypeError: ErrorModel;
    emailRetypeNotMatchError: ErrorModel;

    formError$ = new BehaviorSubject<ErrorModel>(null);
    getRegistrationError$ = new BehaviorSubject<ErrorModel>(null);

    registration$ = new BehaviorSubject<RegistrationMetadata>(null);

    constructor(
        public appState: AppStateService,
        private fb: UntypedFormBuilder,
        private userService: UserService,
        private prm: ProcessingRequestManager,
        private router: Router,
        private route: ActivatedRoute
    ) {}

    get addMissingEmail$(): Observable<boolean> {
        return this.prm.getInprogressObs(ADD_MISING_EMAIL);
    }

    // async ngOnInit() {
    //     this.registrationId = this.route.snapshot.paramMap.get('registrationId');

    //     this.getRegistrationMetadata();
    // }

    // ngOnDestroy(): void {
    //     this.prm.clearIndividual(ADD_MISING_EMAIL);
    // }

    getRegistrationMetadata() {
        this.userService
            .getRegistrationMetadata(this.registrationId)
            .pipe(
                take(1),
                catchError(error => {
                    console.error('Error occurred:', error);
                    return of(null);
                })
            )
            .subscribe(registration => {
                if (!registration) {
                    this.getRegistrationError$.next({
                        key: 'getRegistrationMetadataFailed',
                        msg: 'Tìm thông tin tài khoản thất bại, xin vui lòng thử lại',
                    });
                    return;
                }

                // navigate to the correct page for the state of registration
                const { registrationType, email, isVerified } = registration;
                if (isVerified || !registrationType) {
                    this.redirectTo(['/login']);
                    return;
                }

                if (registrationType.toUpperCase() == 'EMAIL' || email?.length > 0) {
                    this.redirectTo(['/registration', 'verify', this.registrationId]);
                    return;
                }

                this.registration$.next(registration);
            });
    }

    submitMissingEmail = async (data: FormFlowSubmitEvent) => {
        const regId = this.registration$.value?.registrationId;
        if (!regId) {
            console.error('Registration not initialized', this.registration$.value);
            return;
        }

        try {
            const response = await firstValueFrom(
                this.prm.monitor(ADD_MISING_EMAIL, this.userService.addMissingSocialEmail(regId, data.data.email))
            );

            if (response.status == HttpStatusCode.Ok) {
                this.redirectTo(['/registration', 'verify', regId]);
            }
        } catch (e) {
            console.error(e);
            this.formError$.next({
                key: 'addEmailFailed',
                msg: 'Bổ sung email thất bại, xin vui lòng thử lại',
            });
        }
    };

    buildForm = (data?: EnterMissingEmailData): FormBuildingResult => {
        data = data || {
            email: null,
            emailRetype: null,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                __self__: [this.retypeEmailValidation()],
                __fields__: {
                    email: [Validators.pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/), Validators.required],
                    emailRetype: [Validators.required],
                },
            })
            .validatorMessages({
                __self__: {
                    emailNotMatch: 'Xác thực email không khớp',
                },
                __fields__: {
                    email: {
                        required: 'Email là bắt buộc',
                        pattern: 'Email không đúng định dạng',
                        emailExist: 'Email đã được đăng ký',
                        emailExistCheckFailed:
                            'Không kiểm tra được email của bạn. Bạn hãy thử lại hoặc liên hệ vỡi hỗ trợ',
                    },
                    emailRetype: { required: 'Bạn cần nhập xác thực email' },
                },
            })
            .asyncValidators({
                email: [this.conflictEmail()],
            })
            .build();

        this.form = result.control as UntypedFormGroup;

        return result;
    };

    validateExistingEmail(s: string): Observable<ValidationErrors> {
        const regType = this.registration$.value?.registrationType;
        return this.userService.checkEmailExist(regType, s).pipe(
            map(exist => {
                if (!exist) return null;
                else return { emailExist: true };
            }),
            catchError(err => {
                return of({ emailExistCheckFailed: true });
            })
        );
    }

    conflictEmail(): AsyncValidatorFn {
        return (c: AbstractControl): Observable<ValidationErrors> =>
            of(c.value).pipe(
                debounceTime(1000),
                switchMap(s => this.validateExistingEmail(s))
            );
    }

    retypeEmailValidation(): ValidatorFn {
        return (_: AbstractControl) => {
            if (this.form) {
                const email = this.form.get('email');
                const retypeEmail = this.form.get('emailRetype');
                if (email?.value?.length > 0 && retypeEmail?.value?.length > 0) {
                    if (email.value != retypeEmail.value) return { emailNotMatch: true };
                }
            }

            return null;
        };
    }

    async redirectTo(commands: any[]) {
        var params = await firstValueFrom(this.route.queryParams);
        if (params && params[PKEY_RURL])
            this.router.navigate(commands, {
                queryParams: {
                    [PKEY_RURL]: params[PKEY_RURL],
                },
            });
        else this.router.navigate(commands);
    }
}
