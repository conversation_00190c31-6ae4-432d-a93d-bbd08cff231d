<div class="grow lg:mt-[80px] md:mt-[70px] mt-[50px] text-[14px] leading-[21px]">
    <div class="bg-white pb-[90px]" style="clip-path: polygon(0 0, 100% 0, 100% calc(100% - 90px), 0 100%)">
        <section class="text-center py-10 mt-12">
            <h1 class="font-[700] text-[36px] leading-[54px] sm:text-[45px] sm:leading-[67.5px] px-3 xl1:italic">
                Tính năng <span class="text-SC1">nổi bật</span>
            </h1>
            <p class="text-gray-600 mt-2 max-w-[500px] mx-auto px-3">
                viclass cung cấp những tính năng thiết yếu và vượt trội cần có cho một buổ<PERSON> học tr<PERSON><PERSON> tuyến diễn ra hi<PERSON>u
                qu<PERSON> nhất.
            </p>
            <div class="mt-10 grid grid-cols-2 md:grid-cols-4 gap-10 px-10 lg:w-[1170px] mx-auto">
                <!-- First Feature -->
                <div class="flex flex-col items-center">
                    <img src="assets/img/feature/feature-01.svg" alt="Feature 1" class="w-16 h-auto" />
                    <h3 class="font-bold text-[14px] leading-[21px] sm:text-xl mt-4">Tạo đa dạng</h3>
                    <p class="text-[14px] leading-[21px] text-BW3">tài liệu học tập</p>
                </div>

                <!-- Second Feature -->
                <div class="flex flex-col items-center">
                    <img src="assets/img/feature/feature-02.svg" alt="Feature 2" class="w-16 h-auto" />
                    <h3 class="font-bold text-[14px] leading-[21px] sm:text-xl mt-4">Dạy và học</h3>
                    <p class="text-[14px] leading-[21px] text-BW3">2 chiều</p>
                </div>

                <!-- Third Feature -->
                <div class="flex flex-col items-center">
                    <img src="assets/img/feature/feature-03.svg" alt="Feature 3" class="w-16 h-auto" />
                    <h3 class="font-bold text-[14px] leading-[21px] sm:text-xl mt-4">Tương tác</h3>
                    <p class="text-[14px] leading-[21px] text-BW3">nội dung trực quan</p>
                </div>

                <!-- Fourth Feature -->
                <div class="flex flex-col items-center">
                    <img src="assets/img/feature/feature-04.svg" alt="Feature 4" class="w-16 h-auto" />
                    <h3 class="font-bold text-[14px] leading-[21px] sm:text-xl mt-4">Xuất bản, chia sẻ</h3>
                    <p class="text-[14px] leading-[21px] text-BW3">và hơn thế nữa</p>
                </div>
            </div>
        </section>
    </div>
    <div
        class="bg-BW5 py-[150px] -mt-[91px]"
        style="clip-path: polygon(0 90px, 100% 0, 100% 100%, 0 calc(100% - 90px))">
        <section class="text-center py-10">
            <h1 class="font-[700] text-[36px] leading-[54px] sm:text-[45px] sm:leading-[67.5px] px-3">
                Tạo <span class="text-P1">tài liệu</span>
            </h1>
            <p class="text-BW3 mt-2 max-w-[800px] mx-auto px-6">
                Kiến thức bạn tạo ra không chỉ giới hạn trên viclass mà dễ dàng được chia sẻ bằng việc hiển thị những
                tài liệu, hình ảnh, buổi học của bạn trên bất kì nền tảng cá nhân nào mà bạn mong muốn.
            </p>
            <div class="sm:grid grid-cols-4 mt-16 gap-10 max-w-[1280px] mx-auto px-5">
                <img
                    src="assets/img/feature/feature-5.webp"
                    alt="Robotic hand holding a logo"
                    class="mx-auto mb-12 max-w-[calc(min(100%,570px))] col-span-4 xl1:col-span-2 xl1:order-2" />
                <div class="text-left sm:col-span-2 xl1:col-span-1 xl1:order-1">
                    <div>
                        <h3 class="text-[24px] leading-[36px] font-bold text-P1">Đa dạng</h3>
                        <p class="text-BW3 text-[16px] leading-[24px]">
                            Tạo nhiều loại tài liệu cơ bản hay phức tạp bao gồm hình học, đồ thị hàm số, các công thức
                            toán, văn bản, hình ảnh, v.v.
                        </p>
                    </div>
                    <div class="mt-[30px]">
                        <h3 class="text-[24px] leading-[36px] font-bold text-P1">Không cài đặt</h3>
                        <p class="text-BW3 text-[16px] leading-[24px]">
                            viclass hoạt động trên nền tảng web nên bạn có thể dễ dàng truy cập và hoạt động ở bất cứ
                            đâu có kết nối internet.
                        </p>
                    </div>
                </div>
                <div class="text-left sm:col-span-2 xl1:col-span-1 xl1:order-3">
                    <div>
                        <h3 class="text-[24px] leading-[36px] font-bold text-P1">Dễ sử dụng</h3>
                        <p class="text-BW3 text-[16px] leading-[24px]">
                            Mọi công cụ được thiết kế để bạn có thể dễ dàng sử dụng mà không cần kỹ năng hay kinh nghiệm
                            đặc biệt nào.
                        </p>
                    </div>
                    <div class="mt-[30px]">
                        <h3 class="text-[24px] leading-[36px] font-bold text-P1">Phát triển vô hạn</h3>
                        <p class="text-BW3 text-[16px] leading-[24px]">
                            Không chỉ giới hạn trong các môn học phổ thông chính khóa, viclass còn đang tích cực phát
                            triển thêm các công cụ cho những môn học ngoại khóa khác như cờ vua, thống kê, v.v.
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div
        class="bg-white py-[150px] -mt-[91px]"
        [ngStyle]="{
            'clip-path': 'polygon(0 0, 100% 90px, 100% calc(100% - 90px),0 100%)',
            'background-image': 'url(assets/img/feature/bg-pattern.png)',
        }">
        <div class="flex flex-col items-center pt-10">
            <h1
                class="font-[700] text-[36px] leading-[54px] sm:text-[45px] sm:leading-[67.5px] text-gray-800 mb-6 px-3 text-center">
                Dạy và học <span class="text-P1">hai chiều</span>
            </h1>
            <div #scrollContainer class="w-full overflow-x-auto xl1:mt-[70px]">
                <div
                    class="justify-between items-start w-full min-w-[936px] space-x-8 grid grid-cols-3 gap-4 mb-12 px-10 md:max-w-6xl md:gap-8 mx-auto">
                    <div class="scroll-item text-center">
                        <img
                            src="assets/img/feature/feature 8.svg"
                            alt="Cùng tương tác"
                            class="w-[300px] h-[174px] mx-auto mb-[20px]" />
                        <h2 class="font-[700] text-[24px] leading-[36px] text-BW1">Cùng tương tác</h2>
                        <p class="text-BW3 text-[14px]">
                            Tất cả thành viên lớp học đều có thể tương tác trực tiếp với nội dung buổi học. Giáo viên và
                            học sinh dùng chung một bộ công cụ.
                        </p>
                    </div>
                    <div class="scroll-item text-center">
                        <img
                            src="assets/img/feature/feature 6.svg"
                            alt="Dễ dàng quản lý"
                            class="w-[300px] h-[174px] mx-auto mb-[20px]" />
                        <h2 class="font-[700] text-[24px] leading-[36px] text-BW1">Dễ dàng quản lý</h2>
                        <p class="text-BW3 text-[14px]">
                            Dễ dàng quản lý hoặc thao tác trên danh sách, trạng thái học viên, những người đang xung
                            phong hoặc đang xin vào lớp.
                        </p>
                    </div>
                    <div class="scroll-item text-center">
                        <img
                            src="assets/img/feature/feature 7.svg"
                            alt="Đa phương tiện"
                            class="w-[300px] h-[174px] mx-auto mb-[20px]" />
                        <h2 class="font-[700] text-[24px] leading-[36px] text-BW1">Đa phương tiện</h2>
                        <p class="text-BW3 text-[14px]">Âm thanh, hình ảnh sống động hoặc chia sẻ màn hình,...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div
        class="interact-with-content bg-P1 py-[140px] -mt-[91px]"
        style="clip-path: polygon(0 90px, 100% 0, 100% 100%, 0 calc(100% - 90px))">
        <!-- Section 1: Tương tác với nội dung -->
        <div class="py-12 px-[27.62px] xl1:px-[55px]">
            <h1 class="font-[700] text-[36px] leading-[54px] sm:text-[45px] sm:leading-[67.5px] mb-4 text-center px-3">
                <span class="text-BW1">Tương tác</span><span class="text-BW7"> với nội dung</span>
            </h1>
            <div class="mt-12 relative max-w-[1169px] mx-auto">
                <img
                    src="assets/img/feature/feature-9.png"
                    alt="Feature 9"
                    class="mx-auto sm:absolute sm:top-0 sm:left-0 z-10 mt-[90.09px] w-[270px] h-[199px] sm:w-[285px] sm:h-[209px] sm:mt-[207px] md:w-[485px] md:h-[355.91px] md:mt-[39.5px] xl1:h-[397px] xl1:w-[541px] xl1:mt-[74px]" />
                <div
                    class="triangle rounded-[30px] sm:rounded-r-[60px] sm:rounded-l-none bg-white px-6 pt-[24px] pb-6 sm:pl-[52.5%] xl1:pl-[541px]">
                    <img
                        src="assets/img/feature/feature-10.svg"
                        alt="Tương tác với nội dung"
                        class="mx-auto w-[315px] h-[239px] sm:w-[260px] sm:h-[239px] xl1:w-[290px] xl1:h-[315px]" />
                    <p
                        class="max-w-[360px] md:max-w-[558px] text-BW1 text-center text-[14px] mb-[10px] sm:mb-[51.09px] mx-auto mt-[15px]">
                        Dễ dàng <b>chỉnh sửa</b> để làm nổi bật thông điệp muốn truyền đạt, hoặc để làm rõ nội dung đang
                        trình bày.
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!-- Section 2: Xuất bản và chia sẻ -->
    <div
        class="bg-BW1 py-[120px] -mt-[90px]"
        style="clip-path: polygon(0 0, 100% 90px, 100% calc(100% - 90px), 0 100%)">
        <div class="text-center text-white py-12 px-[27px]">
            <div class="max-w-[792px] mx-auto">
                <h2 class="font-[700] text-[36px] leading-[54px] sm:text-[45px] sm:leading-[67.5px]">
                    Xuất bản <span class="text-P1">và chia sẻ</span>
                </h2>
                <p class="text-BW4 text-[16px] leading-[24px] max-w-[792px] mx-auto mb-12 font-[500]">
                    Tài liệu bạn tạo ra không chỉ giới hạn trên viclass mà dễ dàng được chia sẻ bằng nhiều cách khác
                    nhau
                </p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-12 max-w-[1170px] mx-auto">
                <div>
                    <img
                        src="assets/img/feature/feature-14.svg"
                        alt="Sharing Interface"
                        class="w-[500px] h-[213px] sm:h-[396px] rounded-lg shadow-lg -mt-[1rem] mx-auto" />
                </div>

                <div class="text-left">
                    <ul class="space-y-4 flex flex-col gap-4">
                        <li class="text-sm">
                            <span class="font-[700] text-[16px] leading-[24px] text-[BW7]">Chia sẻ </span>
                            <span class="font-500 text-[14px] leading-[21px] text-BW4">liên kết</span>
                        </li>
                        <li class="text-sm">
                            <span class="font-[700] text-[16px] leading-[24px] text-[BW7]">Xuất bản </span>
                            <span class="font-500 text-[14px] leading-[21px] text-BW4">tệp tin hình ảnh hoặc PDF</span>
                        </li>
                        <li class="text-sm">
                            <span class="font-[700] text-[16px] leading-[24px] text-[BW7]">In </span>
                            <span class="font-500 text-[14px] leading-[21px] text-BW4">ấn thông qua trình duyệt</span>
                        </li>
                        <li class="text-sm">
                            <span class="font-[700] text-[16px] leading-[24px] text-[BW7]">Nhúng </span>
                            <span class="font-500 text-[14px] leading-[21px] text-BW4">trên web hoặc blog cá nhân</span>
                        </li>
                    </ul>
                    <div class="mt-10">
                        <a
                            href="#"
                            class="vi-btn vi-btn-normal vi-btn-focus !inline-flex !text-[16px] !leading-[24px] !text-BW1 !px-[15px] !py-[8px]">
                            Tìm hiểu thêm
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bg-BW6 -mt-[90px] pt-[120px] pb-[100px]" style="clip-path: polygon(0 90px, 100% 0, 100% 100%, 0 100%)">
        <app-footer></app-footer>
    </div>
</div>
