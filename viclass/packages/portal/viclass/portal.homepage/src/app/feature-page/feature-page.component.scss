::ng-deep .body-for-feature-page .home-layout {
    @apply bg-white #{!important};
}

::ng-deep .body-for-feature-page .home-layout > div {
    @apply text-BW1 lg:text-inherit #{!important};
}

::ng-deep .body-for-feature-page .topbar-menu-item {
    @apply hover:text-BW1 #{!important};
}

::ng-deep .body-for-feature-page .active-page {
    @apply text-BW1 #{!important};
}

::ng-deep .body-for-feature-page .registration-btn-hover:hover {
    @apply text-BW1;
}

.topbar-menu-item {
    @apply text-BW3 hover:text-BW1 font-semibold uppercase leading-[25.6px] text-base;
}

@media (min-width: 640px) {
    .interact-with-content .triangle {
        clip-path: polygon(42.5% 0, 100% 0, 100% 100%, 0 100%);
    }
}
