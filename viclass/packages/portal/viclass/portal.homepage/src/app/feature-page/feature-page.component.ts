import { CommonModule, DOCUMENT } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    inject,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FooterComponent, PlatformService } from '@viclass/portal.common';
import { MetaService } from '../meta.service';

@Component({
    standalone: true,
    selector: 'app-feature-page',
    templateUrl: './feature-page.component.html',
    styleUrls: ['./feature-page.component.scss'],
    imports: [CommonModule, FooterComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FeaturePageComponent implements OnInit, OnDestroy, AfterViewInit {
    document = inject(DOCUMENT);
    @ViewChild('scrollContainer') scrollContainer: ElementRef<HTMLElement>;

    constructor(
        public ps: PlatformService,
        private ms: MetaService
    ) {}

    ngOnInit(): void {
        this.ms.initMetaPage();
        this.ms.setTitle('Viclass - Tính năng');

        this.document.body.classList.add('body-for-feature-page');
    }

    ngOnDestroy(): void {
        this.document.body.classList.remove('body-for-feature-page');
    }

    ngAfterViewInit(): void {
        if (this.ps.isClientSide) {
            // Scroll to center
            const parent = this.scrollContainer.nativeElement;
            const child = this.scrollContainer.nativeElement.firstChild as HTMLElement;
            this.scrollContainer.nativeElement.scrollTo({
                left: child.offsetWidth / 2 - parent.offsetWidth / 2,
            });
        }
    }
}
