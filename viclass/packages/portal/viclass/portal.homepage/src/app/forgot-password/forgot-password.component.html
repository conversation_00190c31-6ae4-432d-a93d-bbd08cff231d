<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/forgot-password.svg">
            <img src="assets/img/forgot-password.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[400px] w-[290px] mx-auto">
            <div class="text-[36px] mb-[25px]">
                <strong>Khôi phục mật khẩu</strong>
            </div>
            <ng-template [ngIf]="isForgotPasswordSuccess$ | async" [ngIfElse]="formSubmit">
                <div class="flex flex-col gap-1 items-center">
                    <div class="flex-[1_0_80px]">
                        <img src="assets/img/tick-resgistration-completion.svg" alt="" />
                    </div>
                    <div>Vui lòng kiểm tra email để tiến hành khôi phục mật khẩu</div>
                </div>
            </ng-template>
            <ng-template #formSubmit>
                <form
                    class="vi-form"
                    *fflow="let fum; by: buildForm; fflow as f; submit: submitForgotPassword; noNav: true"
                    [formGroup]="fum"
                    [ferrcoord]="f">
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_user prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon"
                                formControlName="email"
                                placeholder="Nhập số điện thoại/ email"
                                [(ferror)]="emailError" />
                        </div>
                        <span class="vi-text-error block" *ngIf="emailError">! {{ emailError.msg }}</span>
                    </div>
                    <div *ngIf="sitekey | async as sitekeyRes">
                        <ng-hcaptcha #hcaptchaElement formControlName="captcha" [siteKey]="sitekeyRes"> </ng-hcaptcha>
                    </div>

                    <span class="vi-text-error block mt-3" *ngIf="forgotPasswordError$ | async"
                        >! {{ (forgotPasswordError$ | async).msg }}</span
                    >

                    <button
                        class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
                        [disabled]="!f.canSubmit() || (forgotPasswordIn$ | async)"
                        [form-flow-submit]="f"
                        [spinner]="forgotPasswordIn$">
                        Gửi yêu cầu
                    </button>
                </form>
            </ng-template>
        </div>
    </div>
</section>
