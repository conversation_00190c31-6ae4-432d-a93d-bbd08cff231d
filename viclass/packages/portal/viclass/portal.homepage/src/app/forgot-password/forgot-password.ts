import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, ViewChild, NO_ERRORS_SCHEMA } from '@angular/core';

import { FormGroup, ReactiveFormsModule, UntypedFormBuilder, Validators } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
    CommonModule as viCommon,
    CaptchaSiteKey,
    ErrorModel,
    ForgotPasswordData,
    FormBuildingResult,
    FormCreator,
    FormUtilModule,
    ProcessingRequestManager,
    SpinnerLabelComponent,
    UserService,
    FormFlowSubmitEvent,
} from '@viclass/portal.common';
import { NgHcaptchaComponent, NgHcaptchaModule } from 'ng-hcaptcha';
import { BehaviorSubject, Observable, firstValueFrom, map } from 'rxjs';

@Component({
    standalone: true,
    imports: [
        CommonModule,
        RouterModule,
        ReactiveFormsModule,
        FormUtilModule,
        Spinner<PERSON>abelComponent,
        NgHcaptchaModule,
        viCommon,
    ],
    selector: 'app-forgot-password-page',
    templateUrl: './forgot-password.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    schemas: [NO_ERRORS_SCHEMA],
})
export class ForgotPasswordPageComponent implements OnInit {
    form: FormGroup;
    usernameError: ErrorModel;
    passwordError: ErrorModel;
    emailError: ErrorModel;

    isForgotPasswordSuccess$ = new BehaviorSubject<boolean>(false);
    forgotPasswordError$: BehaviorSubject<ErrorModel> = new BehaviorSubject(null);

    sitekey: Observable<string | undefined> = this.userService
        .siteKeyOfCaptcha()
        .pipe(map((result: CaptchaSiteKey) => result?.sitekey));

    @ViewChild('hcaptchaElement') hcaptchaElement: NgHcaptchaComponent;

    constructor(
        private fb: UntypedFormBuilder,
        private userService: UserService,
        private prm: ProcessingRequestManager
    ) {}
    async ngOnInit() {}

    buildForm = (data?: ForgotPasswordData): FormBuildingResult => {
        console.log('Building form!!');
        data = data || {
            email: null,
            captcha: null,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                email: [Validators.pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/), Validators.required],
            })
            .validatorMessages({
                email: {
                    required: 'Email là bắt buộc',
                    pattern: 'Email không đúng định dạng',
                    emailExist: 'Email đã được đăng ký',
                    emailExistCheckFailed: 'Không kiểm tra được email của bạn. Bạn hãy thử lại hoặc liên hệ vỡi hỗ trợ',
                },
            })
            .build();

        this.form = result.control as FormGroup;
        return result;
    };

    submitForgotPassword = async (data: FormFlowSubmitEvent) => {
        this.hcaptchaElement.reset();
        try {
            await firstValueFrom(this.prm.monitor('forgotPasswordIn', this.userService.forgotPassword(data.data)));
            this.isForgotPasswordSuccess$.next(true);
        } catch (err) {
            if (err.status == 429) {
                this.forgotPasswordError$.next({
                    key: 'forgotPwFailed',
                    msg: 'Yêu cầu thay đổi mật khẩu đã được gửi, tạo yêu cầu mới sau một tiếng nữa.',
                });
            }

            if (err.status == 500) {
                this.forgotPasswordError$.next({
                    key: 'forgotPwFailed',
                    msg: 'Yêu cầu thay đổi mật khẩu bị lỗi, vui lòng kiểm tra lại.',
                });
            }

            if (err.status == 400) {
                this.forgotPasswordError$.next({
                    key: 'forgotPwFailed',
                    msg: 'captcha không đúng',
                });
            }

            if (err.status == 404) {
                this.forgotPasswordError$.next({
                    key: 'forgotPwFailed',
                    msg: 'Email này chưa được đăng ký.',
                });
            }

            firstValueFrom(this.form.valueChanges).then(() => {
                this.forgotPasswordError$.next(null);
            });

            return;
        }
    };

    get forgotPasswordIn$(): Observable<boolean> {
        return this.prm.getInprogressObs('forgotPasswordIn');
    }
}
