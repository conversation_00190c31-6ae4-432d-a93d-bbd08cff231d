import { AfterViewInit, Component, OnInit } from '@angular/core';
import { AppStateService } from '../app.state.service';
import { ChangeDetectionStrategy } from '@angular/core';

@Component({
    selector: 'app-hiring-page',
    templateUrl: './hiring-page.component.html',
    styleUrls: ['./hiring-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HiringPageComponent {
    constructor() {}
}
