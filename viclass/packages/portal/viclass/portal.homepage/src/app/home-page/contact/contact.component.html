<form
    class="vi-form"
    *fflow="let fum; by: buildForm; fflow as f; submit: submitContact; noNav: true"
    [formGroup]="fum"
    [ferrcoord]="f">
    <div class="mb-[10px] mt-[16px] flex gap-[20px] justify-center">
        <label class="vi-inline-label">
            <input
                class="vi-input"
                type="radio"
                formControlName="userType"
                name="userType"
                value="Teacher"
                checked /><span>Giáo viên</span>
        </label>
        <label class="vi-inline-label">
            <input class="vi-input" type="radio" formControlName="userType" name="userType" value="Student" /><span>
                Học viên
            </span>
        </label>
        <label class="vi-inline-label">
            <input class="vi-input" type="radio" formControlName="userType" name="userType" value="Other" /><span>
                Khác
            </span>
        </label>
    </div>
    <div class="mb-[10px] flex gap-[20px]">
        <div class="grow">
            <input class="vi-input w-full" formControlName="name" placeholder="Tên (không bắt buộc)" />
        </div>
        <div class="grow">
            <input
                class="vi-input w-full"
                formControlName="phone"
                placeholder="Di động (không bắt buộc)"
                [(ferror)]="phoneError" />
            <span class="vi-text-error block" *ngIf="phoneError">! {{ phoneError.msg }}</span>
        </div>
    </div>
    <div class="mb-[10px]">
        <div>
            <input class="vi-input" formControlName="email" placeholder="Email" [(ferror)]="emailError" />
        </div>
        <span class="vi-text-error block" *ngIf="emailError">! {{ emailError.msg }}</span>
    </div>
    <div class="mb-[10px]">
        <div>
            <textarea
                #message
                class="vi-input min-h-[200px] py-[10px]"
                formControlName="message"
                placeholder="Góp ý, liên hệ, hoặc câu hỏi của bạn"
                [(ferror)]="messageError"></textarea>
        </div>
        <div class="flex flex-wrap w-full">
            <span class="vi-text-error justify-self-start block grow" *ngIf="messageError">
                ! {{ messageError.msg }}
            </span>
            <span class="text-BW4 justify-self-end block" *ngIf="message$ && (message$ | async)">
                {{ (message$ | async)?.length }} kí tự
            </span>
        </div>
    </div>
    <div>
        <div class="mt-[20px] mb-[20px]">
            <div *ngIf="sitekey | async as sitekeyRes">
                <ng-hcaptcha #hcaptchaElement (verify)="onVerifyCaptcha($event)" [siteKey]="sitekeyRes"> </ng-hcaptcha>
            </div>
        </div>
    </div>
    <button
        class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
        [disabled]="!f.canSubmit()"
        [form-flow-submit]="f"
        [spinner]="processSubmitContactForm$">
        Gửi lời nhắn
    </button>

    <div *ngIf="(alert | async)?.type === 'success'" class="vi-text-success block mt-3">
        ! {{ (alert | async).content }}
    </div>
    <div *ngIf="(alert | async)?.type === 'error'" class="vi-text-error block mt-3">
        ! {{ (alert | async).content }}
    </div>
</form>
