<div
    class="absolute bottom-[4rem] right-[1.25rem] bg-white rounded-[20px] min-h-[248px] w-[80vw] max-w-[429px] z-10 overflow-hidden border border-BW4 p-[20px] shadow-SH1">
    <button class="absolute top-4 right-4 m-0" (click)="close()">
        <span class="vcon-general vcon_delete"></span>
    </button>
    <div class="mt-2 text-[1.5rem] italic font-[700] text-center leading-[2.25rem] mb-4">
        Đa thao tác -<br />
        đơn giản - chính xác
    </div>
    <div *ngIf="edType === 'GeometryEditor'" class="font-[400] leading-[25.6px] text-justified text-BW1 mb-4">
        V<PERSON><PERSON> công cụ hình học, bạn có thể dựng hình theo quy tắc một cách nhanh nhất.
    </div>
    <div *ngIf="edType === 'FreeDrawingEditor'" class="font-[400] leading-[25.6px] text-justified text-BW1 mb-4">
        <PERSON><PERSON>ng cụ vẽ tự do giúp người dùng ghi chú, trang trí trên bảng trắng, góp phần thu hút cho phần trình bày.
    </div>
    <div *ngIf="edType === 'WordEditor'" class="font-[400] leading-[25.6px] text-justified text-BW1 mb-4">
        Công cụ văn bản vượt trội, tích hợp nhiều tài liệu khác nhau trên cùng một văn bản.
    </div>
    <div class="flex justify-center gap-4">
        <a class="vi-btn vi-btn-normal vi-btn-outline" [href]="supportLink" target="_blank"> Hướng dẫn </a>
    </div>
</div>
