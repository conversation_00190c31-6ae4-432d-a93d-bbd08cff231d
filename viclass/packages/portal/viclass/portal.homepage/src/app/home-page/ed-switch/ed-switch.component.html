<ng-template [ngIf]="showEditor | async" #switchbar let-curEd="ngIf">
    <a
        class="flex items-center hover:cursor-pointer"
        (click)="showEditor.next('GeometryEditor')"
        [ngClass]="{ 'active-switch': curEd === 'GeometryEditor' }">
        <span
            class="switch-icon rounded-[var(--BR2)] w-[50px] h-[50px] flex items-center justify-center px-SP2 vcon-general vcon_document_geometry"
            [ngClass]="{
                '!bg-P2': curEd === 'GeometryEditor',
            }"></span>
        <span *ngIf="curEd === 'GeometryEditor'" class="max-sm:hidden text-[18px] px-SP2 text-BW5"> Hình học </span>
    </a>
    <span class="border-[1px] mx-SP2 border-BW3"></span>
    <a
        class="flex items-center hover:cursor-pointer"
        (click)="showEditor.next('WordEditor')"
        [ngClass]="{ 'active-switch': curEd === 'WordEditor' }">
        <span
            class="switch-icon rounded-[var(--BR2)] w-[50px] h-[50px] flex items-center justify-center px-SP2 vcon-general vcon_document_word"
            [ngClass]="{
                '!bg-P2': curEd === 'WordEditor',
            }"></span>
        <span *ngIf="curEd === 'WordEditor'" class="max-sm:hidden text-[18px] px-SP2 text-BW5"> Văn bản </span>
    </a>
    <span class="border-[1px] mx-SP2 border-BW3"></span>
    <a
        class="flex items-center hover:cursor-pointer"
        (click)="showEditor.next('FreeDrawingEditor')"
        [ngClass]="{ 'active-switch': curEd === 'FreeDrawingEditor' }">
        <span
            class="switch-icon rounded-[var(--BR2)] w-[50px] h-[50px] flex items-center justify-center px-SP2 vcon-general vcon_document_freedrawing"
            [ngClass]="{
                '!bg-P2': curEd === 'FreeDrawingEditor',
            }"></span>
        <span *ngIf="curEd === 'FreeDrawingEditor'" class="max-sm:hidden text-[18px] px-SP2 text-BW5"> Vẽ tự do </span>
    </a>
    <span class="border-[1px] mx-SP2 border-BW3"></span>
    <a
        class="flex items-center hover:cursor-pointer"
        (click)="showEditor.next('MathEditor')"
        [ngClass]="{ 'active-switch': curEd === 'MathEditor' }">
        <span
            class="switch-icon rounded-[var(--BR2)] w-[50px] h-[50px] flex items-center justify-center px-SP2 vcon-general vcon_document_mathtype"
            [ngClass]="{
                '!bg-P2': curEd === 'MathEditor',
            }"></span>
        <span *ngIf="curEd === 'MathEditor'" class="max-sm:hidden text-[18px] px-SP2 text-BW5"> Công thức toán </span>
    </a>
    <span class="border-[1px] mx-SP2 border-BW3"></span>
    <a
        class="flex items-center hover:cursor-pointer"
        (click)="showEditor.next('MathGraphEditor')"
        [ngClass]="{ 'active-switch': curEd === 'MathGraphEditor' }">
        <span
            class="switch-icon rounded-[var(--BR2)] w-[50px] h-[50px] flex items-center justify-center px-SP2 vcon-general vcon_document_magh"
            [ngClass]="{
                '!bg-P2': curEd === 'MathGraphEditor',
            }"></span>
        <span *ngIf="curEd === 'MathGraphEditor'" class="max-sm:hidden text-[18px] px-SP2 text-BW5">
            Đồ thị hàm số
        </span>
    </a>
</ng-template>
