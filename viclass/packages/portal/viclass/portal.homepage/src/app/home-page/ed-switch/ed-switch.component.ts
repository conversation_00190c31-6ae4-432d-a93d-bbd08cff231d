import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BehaviorSubject } from 'rxjs';
import { EditorType } from '@viclass/editor.core';

@Component({
    standalone: true,
    selector: '[app-ed-switch]',
    templateUrl: './ed-switch.component.html',
    imports: [CommonModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdSwitchComponent {
    @Input() showEditor: BehaviorSubject<EditorType>;

    constructor() {}
}
