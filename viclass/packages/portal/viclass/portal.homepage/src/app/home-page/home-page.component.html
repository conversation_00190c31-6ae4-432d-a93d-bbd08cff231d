<!-- banner image -->
<section class="bg-BW1">
    <!-- Banner -->
    <div
        class="lg:w-[1170px] lg:mt-[120px] lg:h-[484px] md:w-[970px] md:mx-auto mt-[50px] md:mt-[80px] md:h-[401px] md:bg-[url('assets/img/home-banner.png')] md:bg-center md:mb-0 xs:mx-smallPageMargin xs:mb-[110px] bg-no-repeat bg-contain">
        <div class="md:hidden">
            <img src="assets/img/top-banner-mobile.svg" class="mx-auto" />
        </div>
        <div
            class="lg:top-[240px] lg:w-[40%] md:top-[185px] text-center md:w-[50%] md:relative xs:max-w-[640px] text-BW3 mx-auto leading-[1.5]">
            <p class="text-sm font-medium leading-[25.6px] text-center text-BW5">
                V<PERSON><PERSON> những tính năng v<PERSON><PERSON><PERSON> tr<PERSON>, viclass là nền tảng cung cấp những công cụ tối ưu hoá quá trình dạy và
                học trực tuyến giúp cho việc tương tác, chia sẻ, kết nối giữa học sinh và giáo viên trong quá trình dạy
                và học được diễn ra theo cách tự nhiên nhất.
            </p>
            <a
                routerLink="/features"
                class="vi-btn vi-btn-normal vi-btn-focus !inline-flex mx-auto mt-[.125rem] !text-sm !h-auto !py-[.5rem]">
                Tính năng
            </a>
        </div>
    </div>
    <!-- End Banner -->
</section>

<div class="mt-[-60px] h-[50px] flex justify-center pb-4" app-ed-switch [showEditor]="showEd"></div>
<!-- switcher-->

<section class="bg-BW1 section-margin" *ngIf="ps.isClientSide">
    <!-- Embedded frame -->
    <div class="mt-[10px] mb-[50px]">
        <div
            class="xl:h-[900px] lg:max-w-[1400px] lg:h-[600px] md:h-[450px] sm:h-[400px] h-[400px] relative overflow-hidden mx-auto rounded-[20px]"
            *ngIf="loadingViewport">
            <app-editor-loading [loading]="loadingViewport"></app-editor-loading>
        </div>
        <div
            [ngClass]="{
                '!hidden': loadingViewport,
            }"
            class="lg:max-w-[1400px] xl:h-[700px] lg:h-[600px] md:h-[450px] sm:h-[400px] h-[400px] relative overflow-hidden mx-auto">
            <ng-template [ngIf]="showEd | async" let-curEd="ngIf">
                <div *ngFor="let ed of seenEditors">
                    <div
                        *ngFor="let vmInfo of (vpCmps.get(ed) | async) ?? []"
                        [homepageViewportId]="vmInfo.homepageViewportId"
                        [ngClass]="[curEd === ed ? 'show' : 'hide']"
                        class="homepage-viewport viewport-container absolute w-full h-full"
                        app-viewport-manager
                        [attr.edType]="ed"
                        [edType]="ed"
                        [coord]="this.documentService.coord"
                        (onCmd)="onViewportCmd($event, ed)"
                        [uiModules]="uiModules(ed)"
                        [showInfo]="showEdIntro"></div>
                </div>
                <ed-intro-popup
                    *ngIf="showEdIntro | async"
                    (whenClose)="showEdIntro.next(false)"
                    [edType]="showEd | async"></ed-intro-popup>
            </ng-template>
        </div>
    </div>
</section>
<!-- Section separator with previous section -->
<div
    class="h-[90px] bg-BW7 bg-no-repeat bg-[url('assets/img/home-bg-01.svg')] bg-[top_center] bg-[length:100%_90px]"></div>

<!-- section target user -->
<section class="py-[75px] md:py-[150px] bg-BW7 section-margin">
    <!-- section content -->
    <div
        class="section-content flex flex-wrap md:justify-items-center md:gap-[30px] mb-[50px] md:flex-row flex-col gap-[10px]">
        <div class="xs:w-full xs:max-w-[570px] max-md:mx-auto md:w-[470px] lg:w-[570px]">
            <!-- CONTENT -->
            <div class="text-center text-[26px] md:text-[36px] mb-[20px]">
                <strong
                    ><i><span>Bạn là&nbsp;</span><span class="text-P1">giáo viên</span></i></strong
                >
            </div>
            <div class="text-[14px] md:text-[16px] mb-[20px]">
                <ul class="pl-[27px] list-disc">
                    <li>Nhanh chóng tạo tài liệu phục vụ công việc giảng dạy</li>
                    <li>Dễ dàng chia sẻ và tương tác với học viên trên những nội dung mà bạn đã tạo ra</li>
                    <li>Xây dựng buổi học trực tuyến một cách tự nhiên và chân thật, từ nội dung của bạn</li>
                </ul>
            </div>
            <div class="flex justify-center items-center gap-[20px]">
                <a class="vi-btn vi-btn-normal vi-btn-focus">Đăng ký làm giảng viên</a>
                <a class="vi-btn vi-btn-normal vi-btn-outline" href="/classrooms/create">Tạo buổi học</a>
            </div>
        </div>

        <div class="xs:w-full md:w-[470px] lg:w-[570px]">
            <!-- ILLUSTRATION -->
            <object
                class="max-w-[570px] mx-auto w-full"
                type="image/svg+xml"
                data="assets/img/illustration-teacher.svg">
                <img src="assets/img/illustration-teacher.svg" />
            </object>
        </div>
    </div>

    <div
        class="section-content flex flex-wrap justify-items-center md:gap-[30px] md:flex-row-reverse flex-col gap-[10px]">
        <div class="xs:w-full xs:max-w-[570px] max-md:mx-auto md:w-[470px] lg:w-[570px]">
            <!-- CONTENT -->
            <div class="text-center text-[26px] md:text-[36px] mb-[20px]">
                <strong
                    ><i><span class="text-P1">Học tập </span><br /><span>và thảo luận nhóm</span></i></strong
                >
            </div>
            <div class="text-[14px] md:text-[16px] mb-[20px]">
                <ul class="pl-[27px] list-disc">
                    <li>Nhanh chóng tạo tài liệu phục vụ thảo luận và học tập</li>
                    <li>Dễ dàng chia sẻ và tương tác với giảng viên trên những nội dung mà bạn đã tạo ra</li>
                    <li>
                        Tham gia buổi học trực tuyến với khả năng trình bày nội dung tương tác, giơ tay phát biểu như
                        trong một lớp học thực tế
                    </li>
                </ul>
            </div>
            <div class="flex justify-center items-center gap-[20px]">
                <a
                    *ngIf="!(userService.curUser$ | async)"
                    class="vi-btn vi-btn-normal vi-btn-focus"
                    href="/registration">
                    Đăng ký miễn phí
                </a>
                <a class="vi-btn vi-btn-normal vi-btn-outline" href="/classrooms/create">Thảo luận nhóm</a>
            </div>
        </div>

        <div class="xs:w-full md:w-[470px] lg:w-[570px]">
            <!-- ILLUSTRATION -->
            <object
                class="max-w-[570px] mx-auto w-full"
                type="image/svg+xml"
                data="assets/img/illustration-student.svg">
                <img src="assets/img/illustration-student.svg" />
            </object>
        </div>
    </div>
</section>

<!-- Section separator with previous section -->
<div
    class="h-[90px] bg-BW7 bg-no-repeat bg-[url('assets/img/home-bg-02.svg')] bg-[top_center] bg-[length:100%_90px]"></div>

<section class="py-[75px] md:py-[150px] bg-BW1 section-margin text-BW7">
    <div class="section-content relative">
        <img
            class="absolute z-0 top-[-150px] md:top-[-275px] md:w-[453px] w-[250px] pointer-events-none"
            src="assets/img/home-rocket.png" />
        <div class="relative z-1">
            <div class="text-center text-[26px] md:text-[36px] mb-[20px]">
                <strong>
                    <i> <span class="text-P1">Chọn viclass</span><br /><span>chọn trải nghiệm của tương lai</span> </i>
                </strong>
            </div>
            <div class="text-center mx-auto w-[80%] sm:w-[60%] md:w-[50%] text-[14px] md:text-[16px]">
                <p>
                    Chúng tôi tự hào đã và đang nghiên cứu, phát triển viclass theo định hướng sử dụng hiệu quả, mang
                    đến sự khác biệt, thuận tiện và giá trị cộng đồng, hòa mình vào dòng chảy số hóa mạnh mẽ trong lĩnh
                    vực giáo dục tại Việt Nam.
                </p>
            </div>

            <div id="feature-table" class="z-1 mt-[20px] md:mt-[50px]">
                <div
                    class="overflow-x-auto mx-auto grid grid-cols-[116px_repeat(4,_minmax(95px,_1fr))_85px] sm:grid-cols-[159px_repeat(5,_minmax(90px,_1fr))] md:grid-cols-[240px_repeat(5,_minmax(146px,_1fr))] lg:grid-cols-[370px_repeat(5,_minmax(160px,_1fr))] grid-rows-[auto_repeat(6,_minmax(80px,_auto))]">
                    <div><!-- empty --></div>
                    <div class="p-[0px_5px_40px] text-BW1">
                        <div class="header-shape bg-PAS3">Hệ thống tạo cuộc gọi trực tuyến</div>
                    </div>
                    <div class="p-[40px_5px_0px] text-BW1">
                        <div class="header-shape bg-PAS2">Hệ thống bán khóa học qua video</div>
                    </div>
                    <div class="p-[0px_5px_40px] text-BW1">
                        <div class="header-shape bg-PAS4">LMS</div>
                    </div>
                    <div class="p-[40px_5px_0px] text-BW1">
                        <div class="header-shape bg-PAS5">Hệ thống kết nối học viên với giáo viên và khóa học</div>
                    </div>
                    <div class="p-[0px_5px_40px] text-BW7">
                        <div class="header-shape bg-P1 flex-col">
                            <i class="vcon-general vcon_logo-icon text-[80px]"></i>
                            <strong><span class="text-[20px]">viclass</span></strong>
                        </div>
                    </div>

                    <div>Bộ công cụ tạo, chỉnh sửa, chia sẻ tài liệu thiết kế đặc biệt theo từng môn</div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div class="feature-check"></div>

                    <div>Giáo viên và học viên có thể tương tác trên cùng một tài liệu theo thời gian thực</div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div class="feature-check"></div>

                    <div>Tương tác với nội dung buổi học được ghi lại, sau khi buổi học đã kết thúc</div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div class="feature-check"></div>

                    <div>Quản lý quy trình dạy và học, trong và ngoài lớp học trực tuyến</div>
                    <div></div>
                    <div></div>
                    <div class="feature-check"></div>
                    <div></div>
                    <div class="feature-check"></div>

                    <div>Cộng đồng kết nối rộng rãi</div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div class="feature-check"></div>
                    <div class="feature-check"></div>

                    <div>Tích hợp trên nền tảng thứ 3</div>
                    <div class="feature-check"></div>
                    <div></div>
                    <div class="feature-check"></div>
                    <div></div>
                    <div class="feature-check"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Section separator with previous section -->
<div
    class="h-[90px] bg-BW7 bg-no-repeat bg-[url('assets/img/home-bg-01.svg')] bg-[top_center] bg-[length:100%_90px]"></div>

<!-- Góp ý / liên hệ -->
<section class="py-[75px] md:py-[150px] bg-BW7 section-margin">
    <!-- section content -->
    <div
        class="section-content relative flex flex-wrap md:justify-items-center md:gap-[30px] mb-[50px] md:flex-row flex-col gap-[10px]">
        <div
            class="absolute h-full min-h-[700px] w-full pointer-events-none bg-[url(assets/img/homepage-idea.png)] bg-[center_top] top-[-100px] md:top-[-200px] bg-[length:300px] md:bg-[length:570px] bg-no-repeat"></div>

        <div id="contact-form" class="relative xs:w-full z-1 xs:max-w-[570px] max-md:mx-auto md:w-[470px] lg:w-[570px]">
            <!-- CONTENT -->
            <div class="text-center text-[31px] md:text-[36px] mb-[20px]">
                <strong>
                    <i>
                        <span>viclass trân trọng </span><br /><span>những&nbsp;</span>
                        <span class="text-P1">góp ý từ bạn</span>
                    </i>
                </strong>
            </div>
            <div class="text-[14px] text-justify md:text-[16px] mb-[20px]">
                <span>
                    Với tinh thần cầu thị và không ngừng hướng tới sự phát triển mọi góp ý từ bạn là động lực to lớn
                    trong quá trình đưa viclass đến với nhiều người dùng hơn nữa
                </span>
                <br />
                <br />
                <span>
                    Liên hệ với chúng tôi tại email:
                    <b><a class="text-P1 underline" href="mailto:<EMAIL>">admin&#64;viclass.vn</a></b>
                </span>
            </div>
        </div>

        <div class="relative xs:w-full md:w-[470px] lg:w-[570px]" *ngIf="ps.isClientSide" app-contact>
            <!-- contact / suggestion form -->
        </div>
    </div>
</section>
<div class="bg-BW6 -mt-[90px] pt-[120px] pb-[100px]" style="clip-path: polygon(0 90px, 100% 0, 100% 100%, 0 100%)">
    <app-footer></app-footer>
</div>
