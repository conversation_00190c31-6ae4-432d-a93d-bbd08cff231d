import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EmbedCoordinator } from '@viclass/editor.coordinator/embed';
import {
    CreateDocumentResult,
    DocumentId,
    EditorType,
    FEATURE_HISTORY,
    HistoryFeature,
    SelectTool,
    SupportFeatureHistory,
    ViewportManager,
} from '@viclass/editor.core';
import {
    CheckPublicDocResponse,
    createRPC,
    DocErr,
    DocOwnershipMetadataController,
    DocumentDocMetadataService,
    DocumentInfoResponse,
    LocalStorageDocIds,
    LocalStorageErr,
    UserProfile,
} from '@viclass/portal.common';
import { HttpStatusCode } from 'axios';
import { firstValueFrom, map, Observable, take } from 'rxjs';

export type HomepageCreateDocConfig = {
    coord: EmbedCoordinator;
    viewport: ViewportManager;
    edType: EditorType;
    userProfile: UserProfile | null;
    defaultDocId?: DocumentId;
};

@Injectable()
export class HomePageService {
    private readonly KEY_PREFIX = 'HOME_DOCMETA';
    private readonly server = new DocOwnershipMetadataController();

    constructor(
        private http: HttpClient,
        private documentDocMetadataService: DocumentDocMetadataService
    ) {}

    /**
     *  Load existing document for the current user with the specified editor type,
     *  and create a new document if no document exists
     *  @param config
     *  @returns the document global ID
     */
    public async createOrLoadDoc(config: HomepageCreateDocConfig): Promise<string> {
        const userId = config.userProfile?.id;
        const localDocId = this.getLocalStorageDocId(config.edType, userId);
        let gid: string;

        try {
            if (config.defaultDocId) {
                const isPublic = await this.isValidPublicDoc(config.edType, config.defaultDocId);

                if (!userId) {
                    if (isPublic) {
                        gid = await this.loadDoc(config, config.defaultDocId);
                    }
                } else {
                    if (isPublic) {
                        // take ownership of the public doc
                        this.saveDocMetadata(config, config.defaultDocId);
                        gid = await this.loadDoc(config, config.defaultDocId);
                    } else {
                        const isValidForUser = await this.isDocBelongToUser(config.edType, config.defaultDocId, userId);
                        if (isValidForUser) {
                            gid = await this.loadDoc(config, config.defaultDocId);
                        }
                    }
                }
            }

            if (!gid) {
                if (!userId) {
                    // NO LOGIN
                    if (localDocId) {
                        const isPublic = await this.isValidPublicDoc(config.edType, localDocId);
                        if (isPublic) {
                            gid = await this.loadDoc(config, localDocId);
                        }
                    }
                } else {
                    // LOGGED IN USERS
                    // Local doc created by this user
                    if (localDocId) {
                        const isValidForUser = await this.isDocBelongToUser(config.edType, localDocId, userId);
                        if (isValidForUser) {
                            gid = await this.loadDoc(config, localDocId);
                        }
                    }

                    if (!gid) {
                        // Last doc in the user profile
                        const lastDocId = await this.lastDocIdFromCurrentUser(config.edType);
                        if (lastDocId) {
                            gid = await this.loadDoc(config, lastDocId);
                        }
                    }
                }
            }

            if (!gid) throw new DocErr('Error while loading document');
        } catch (err) {
            if (err instanceof DocErr) gid = await this.createNewDoc(config);
        }

        return gid;
    }

    public async loadDoc(config: HomepageCreateDocConfig, globalId: string): Promise<string> {
        try {
            await config.coord.loadDocOnViewport(config.viewport, {
                gId: globalId,
                edType: config.edType,
            });

            // metadata already in server, only need to put the doc ID and edType to localStorage
            this.mergeLocalStorageData(config.edType, globalId, config.userProfile?.id);

            return globalId;
        } catch (err) {
            throw new DocErr('Error while loading document', err);
        }
    }

    public async createNewDoc(config: HomepageCreateDocConfig): Promise<string> {
        const docCreated: CreateDocumentResult = await config.coord.createDoc(config.viewport);

        this.saveDocMetadata(config, docCreated.globalId);
        this.clearHistory(config);

        return docCreated.globalId;
    }

    private saveDocMetadata(config: HomepageCreateDocConfig, globalId: string) {
        if (!globalId) {
            throw new Error(`[ViewportManager][${config.edType}] Invalid document global ID ${globalId}`);
        }

        if (!config.userProfile) {
            this.mergeLocalStorageData(config.edType, globalId);
        } else {
            this.saveDocumentMetadata(config.edType, globalId, config.userProfile).pipe(take(1)).subscribe();
        }
    }

    /**
     *  Save document ID to localStorage and create metadata on server
     */
    public saveDocumentMetadata(
        editorType: EditorType,
        docGlobalId: string,
        userProfile?: UserProfile,
        docLocalId: number = 0
    ): Observable<DocumentInfoResponse> {
        if (!docGlobalId || !editorType) {
            throw new Error('Invalid parameters');
        }

        this.mergeLocalStorageData(editorType, docGlobalId, userProfile?.id);

        const reqBody = {
            docGlobalId: docGlobalId,
            editorType: editorType,
            details: {
                docGlobalId: docGlobalId,
                docLocalId: docLocalId,
                ownerUserId: userProfile.id,
                ownerUserName: userProfile.username,
            },
        };

        return createRPC<DocumentInfoResponse>(this.server.createDocInfo)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    /**
     *  Check if this document is public (not owned by any user)
     */
    public isPublicDocument(editorType: EditorType, globalId: string): Observable<CheckPublicDocResponse> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };

        return createRPC<CheckPublicDocResponse | null>(this.server.isPublicDoc)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    /**
     *  Get doc globalID created by the current user saved in the localStorage
     */
    public getLocalStorageDocId(editorType: EditorType, userId?: string): string | null {
        if (!this.isLocalStorageSupported()) throw new LocalStorageErr();

        const key = this.getLocalStorageKey(userId);
        const item = localStorage.getItem(key);
        if (!item) return null;

        const docIdByEditor = JSON.parse(item) as LocalStorageDocIds;
        return docIdByEditor[editorType] ?? null;
    }

    private isLocalStorageSupported() {
        return (
            !!window.localStorage &&
            typeof localStorage.getItem === 'function' &&
            typeof localStorage.setItem === 'function' &&
            typeof localStorage.removeItem === 'function'
        );
    }

    /**
     *  Merge new doc ID with the other IDs of other EditorType in localStorage
     */
    public mergeLocalStorageData(editorType: EditorType, docGlobalId: string, userId?: string) {
        const key = this.getLocalStorageKey(userId);
        const item = localStorage.getItem(key) ?? '{}';
        const docIdByEditor = JSON.parse(item) as LocalStorageDocIds;

        docIdByEditor[editorType] = docGlobalId;
        localStorage.setItem(key, JSON.stringify(docIdByEditor));
    }

    private getLocalStorageKey(userId?: string) {
        return `${this.KEY_PREFIX}_${userId ? userId : 'NOLOGIN'}`;
    }

    private async isValidPublicDoc(edType: EditorType, docId: string): Promise<boolean> {
        try {
            const result = await firstValueFrom(this.isPublicDocument(edType, docId));
            return result.isPublic && result.validEditorType;
        } catch (err) {
            throw new DocErr('Error while loading document', err);
        }
    }

    public async isDocBelongToUser(edType: EditorType, docId: string, userId: string): Promise<boolean> {
        try {
            const docInfo = await firstValueFrom(this.documentDocMetadataService.loadDocumentInfo(edType, docId));
            return docInfo?.details?.ownerUserId == userId;
        } catch (err) {
            throw new DocErr('Error while loading document metadata', err);
        }
    }

    private async lastDocIdFromCurrentUser(edType: EditorType): Promise<string | null> {
        try {
            const docsInfo = await firstValueFrom(this.documentDocMetadataService.getDocsByCurrentUser(edType, 1));
            if (docsInfo?.length > 0) return docsInfo[0].docGlobalId;
            return null;
        } catch (err) {
            if (err?.status === HttpStatusCode.NotFound) throw new DocErr('Error while loading document metadata', err);
            throw err;
        }
    }

    private clearHistory(config: HomepageCreateDocConfig) {
        if (!config.coord.editorByType(config.edType)?.isSupportFeature(FEATURE_HISTORY)) {
            return;
        }

        const editor = config.coord.editorByType(config.edType) as unknown as SupportFeatureHistory;

        // clear history
        const historyFeature = config.coord.getFeature('history') as HistoryFeature;

        historyFeature.clear(editor, config.viewport.id);
    }

    private clearCurSelection(config: HomepageCreateDocConfig) {
        const selectTool = config.coord.getCommonToolbar(config.viewport.id).getTool('select') as SelectTool;
        selectTool.deselectAllDocCtx();
    }
}
