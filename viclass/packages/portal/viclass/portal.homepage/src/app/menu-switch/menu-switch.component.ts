import {
    Component,
    OnInit,
    Input,
    HostListener,
    ViewContainerRef,
    ElementRef,
    On<PERSON><PERSON>roy,
    ChangeDetectionStrategy,
} from '@angular/core';
import { MenuComponent } from '../menu/menu.component';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { Observable, Subscription, merge } from 'rxjs';
import { Router } from '@angular/router';

@Component({
    standalone: true,
    selector: '[app-menu-switch]',
    templateUrl: './menu-switch.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuSwitchComponent implements OnInit, OnDestroy {
    @Input('show-menu') menu: MenuComponent;

    overlayRef: OverlayRef;
    closingActionSub: Subscription;
    routerSub: Subscription;

    constructor(
        private overlay: Overlay,
        private viewContainerRef: ViewContainerRef,
        private elRef: ElementRef,
        router: Router
    ) {
        this.routerSub = router.events.subscribe(e => {
            this.destroyDropdown(); // when navigate away, also destroy the drop down
        });
    }

    ngOnInit(): void {}

    @HostListener('change', ['$event'])
    switch(event?: Event) {
        let el: HTMLInputElement = event && (event.target as HTMLInputElement);

        if (!el) {
            // if not from an event, we switch programmatically
            el = this.elRef.nativeElement as HTMLInputElement;
            el.checked = !el.checked;
        }

        if (el.checked) {
            this.overlayRef = this.overlay.create({
                panelClass: 'menu-overlay-pane',
                hasBackdrop: true,
                backdropClass: 'cdk-overlay-transparent-backdrop',
                // scrollStrategy: this.overlay.scrollStrategies.close(),
                positionStrategy: this.overlay
                    .position()
                    .flexibleConnectedTo(this.menu.elRef)
                    .withPositions([
                        {
                            originX: 'end',
                            originY: 'bottom',
                            overlayX: 'end',
                            overlayY: 'top',
                        },
                    ]),
            });

            const templatePortal = new TemplatePortal(this.menu.menuTemplateRef, this.viewContainerRef);
            this.overlayRef.attach(templatePortal);

            this.closingActionSub = this.dropdownClosingActions().subscribe(() => this.destroyDropdown());
        } else {
            this.destroyDropdown();
        }
    }

    private dropdownClosingActions(): Observable<MouseEvent | void> {
        const backdropClick$ = this.overlayRef.backdropClick();
        const detachment$ = this.overlayRef.detachments();

        return merge(backdropClick$, detachment$);
    }

    private destroyDropdown(): void {
        if (!this.overlayRef) {
            return;
        }

        this.closingActionSub.unsubscribe();

        this.overlayRef.detach();
        this.overlayRef.dispose();

        const el = this.elRef.nativeElement as HTMLInputElement;

        if (el.checked) el.checked = false;

        delete this.overlayRef;
    }

    ngOnDestroy() {
        this.destroyDropdown();
        this.routerSub.unsubscribe();
    }
}
