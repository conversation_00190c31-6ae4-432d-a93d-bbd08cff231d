import { ChangeDetectionStrategy, Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';

@Component({
    standalone: true,
    selector: 'app-menu',
    templateUrl: './menu.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuComponent implements OnInit {
    @ViewChild(TemplateRef)
    public menuTemplateRef: TemplateRef<any>;

    constructor(public elRef: ElementRef) {}

    ngOnInit(): void {}
}
