import { Injectable } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';

type MetaPage = {
    title: string;
    description: string;
    keywords: string;
    logo: string;
    websiteUrl: string;
};

/**
 * Containing states of the application
 */
@Injectable({
    providedIn: 'root',
})
export class MetaService {
    constructor(
        private titleService: Title,
        private metaService: Meta
    ) {}

    getInitialMetaPage(): MetaPage {
        return {
            title: 'Viclass - Dạy và học online, không thể dễ hơn!',
            description:
                'Viclass là một hệ sinh thái giáo dục toàn diện, nơi cung cấp tài li<PERSON>, c<PERSON><PERSON> c<PERSON>, s<PERSON> kết nối, và quy trình giúp giáo viên và học sinh dạy và học từ bất kỳ đâu, bất kỳ lúc nào với hiệu quả cao nhất trên môi trường trực tuyến.',
            keywords:
                'công cụ dạy học tr<PERSON> t<PERSON>, n<PERSON><PERSON> tảng dạy học tr<PERSON> t<PERSON>, d<PERSON><PERSON> v<PERSON> h<PERSON>, <PERSON><PERSON> sinh thái dạy và học, kết nối học sinh giáo viên, hệ thống quản lý học tập, tài liệu học tập, sách giáo khoa số, số hóa giáo dục, số hóa tài liệu học tập, dạy học tương tác, gia sư trực tuyến, lớp học trực tuyến, buổi học online, tài liệu tương tác, hướng dẫn trả lời câu hỏi, tìm kiếm khóa học, khóa học theo yêu cầu, hỏi đáp giáo dục, hỏi đáp phổ thông, hỏi đáp trung học',
            logo: '/assets/img/vi-logo.svg',
            websiteUrl: 'https://viclass.vn',
        };
    }

    initMetaPage() {
        this.setMetaPage(this.getInitialMetaPage());
    }

    setTitle(title: string) {
        this.titleService.setTitle(title);

        /** OpenGraph meta tags **/
        this.metaService.updateTag({
            property: 'og:title',
            content: title,
        });

        this.metaService.updateTag({
            name: 'twitter:title',
            content: title,
        });
        this.metaService.updateTag({
            name: 'twitter:image:alt',
            content: title,
        });
    }

    setDescription(description: string) {
        this.metaService.updateTag({
            name: 'description',
            content: description,
        });

        /** OpenGraph meta tags **/
        this.metaService.updateTag({
            property: 'og:description',
            content: description,
        });

        this.metaService.updateTag({
            name: 'twitter:description',
            content: description,
        });
    }

    setKeywords(keywords: string) {
        this.metaService.updateTag({
            name: 'keywords',
            content: keywords,
        });
    }

    setMetaPage(metaPage: MetaPage) {
        this.setTitle(metaPage.title);
        this.setDescription(metaPage.description);
        this.setKeywords(metaPage.keywords);

        /** OpenGraph meta tags **/
        this.metaService.updateTag({
            property: 'og:type',
            content: 'website',
        });
        this.metaService.updateTag({
            property: 'og:image',
            content: metaPage.logo,
        });
        this.metaService.updateTag({
            property: 'og:url',
            content: metaPage.websiteUrl,
        });

        /** Twitter Card meta tags **/
        this.metaService.updateTag({
            name: 'twitter:card',
            content: 'summary_large_image',
        });
        this.metaService.updateTag({
            name: 'twitter:image',
            content: metaPage.logo,
        });
        this.metaService.updateTag({
            name: 'twitter:image:alt',
            content: metaPage.title,
        });
    }
}
