import { DocumentId, EditorType, ViewportManager } from '@viclass/editor.core';

export type LoadHomepageDocEvent = {
    edType: EditorType;
    docGlobalId: DocumentId;
    viewport: ViewportManager;
};

export const EDITOR_ICONS: Partial<Record<EditorType, string>> = {
    FreeDrawingEditor: 'vcon_document_freedrawing',
    GeometryEditor: 'vcon_document_geometry',
    MathEditor: 'vcon_document_mathtype',
    MathGraphEditor: 'vcon_document_magh',
    PdfEditor: 'vcon_document_pdf',
    WordEditor: 'vcon_document_word',
};

export const EDITOR_NAMES: Partial<Record<EditorType, string>> = {
    FreeDrawingEditor: 'Vẽ tự do',
    GeometryEditor: 'Hình học',
    MathEditor: 'Công thức toán',
    MathGraphEditor: '<PERSON><PERSON> thị hàm số',
    PdfEditor: 'PDF',
    WordEditor: 'Văn bản',
};
