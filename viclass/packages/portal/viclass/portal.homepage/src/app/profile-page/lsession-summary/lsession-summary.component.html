<vi-card>
    <vi-card-header>
        <div class="flex sm:flex-row flex-col-reverse">
            <vi-sticker
                class="mr-auto"
                *ngIf="!isOwner && !['ENDED', 'CLOSED'].includes(model.lsStatus)"
                [color]="stickerColor"
                [style.color]="registrationTextColor"
                >{{ registrationText }}</vi-sticker
            >
            <div class="ml-auto flex flex-row gap-[10px]">
                <span class="vcon vcon-general vcon_page-bar_share p-[5px] cursor-pointer" (click)="onShare()"></span>
                <span
                    class="vcon vcon-general vcon_open-in-new-tab p-[5px] cursor-pointer"
                    (click)="openInNewTab()"></span>
            </div>
        </div>
    </vi-card-header>
    <vi-card-body>
        <div class="flex flex-col gap-[10px]">
            <div class="text-[18px] leading-[27px] font-[600]">
                <span>{{ model.title }}</span>
            </div>
            <div class="flex flex-row gap-[5px]">
                <div *ngIf="!model.creatorAvatarUrl" class="w-[20px] h-[20px] border-[1px] border-BW4 avatar-img"></div>
                <div
                    *ngIf="model.creatorAvatarUrl"
                    [style.background-image]="'url(' + model.creatorAvatarUrl + ')'"
                    class="w-[20px] h-[20px] border-[1px] border-BW4 avatar-img"></div>
                <div>{{ model.creatorName }}</div>
            </div>
        </div>
    </vi-card-body>
    <vi-card-footer>
        <div class="flex sm:flex-row flex-col-reverse text-[14px] leading-[21px]">
            <div class="mr-auto">
                <div *ngIf="model.lsStatus === 'NOT_STARTED'" class="text-SC1">Chờ bắt đầu</div>
                <div *ngIf="model.lsStatus === 'STARTED'" class="text-SC1">
                    {{ learningTime }}
                </div>
                <div *ngIf="model.lsStatus === 'ENDED'">{{ learningTime }}</div>
                <div *ngIf="model.lsStatus === 'CLOSED'">Đã đóng</div>
            </div>
            <div class="ml-auto flex flex-row gap-[5px]">
                <span *ngIf="!['ENDED', 'CLOSED'].includes(model.lsStatus)" class="text-P1">{{
                    model.registered
                }}</span>
                <span *ngIf="['ENDED', 'CLOSED'].includes(model.lsStatus)">{{ model.registered }}</span>
                <span class="vcon vcon-general vcon_user_students"></span>
            </div>
        </div>
    </vi-card-footer>
</vi-card>
