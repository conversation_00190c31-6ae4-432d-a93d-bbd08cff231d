import { LSessionStatus, LSRegStatus } from '@viclass/portal.common';

export interface LsessionSummaryModel {
    id: string;
    userId: string;
    creatorId: string;
    creatorName: string;
    creatorAvatarUrl: string;
    title: string;
    grade: string;
    subject: string;
    regId?: string; // reg id of currently logged in user for this summary
    regStatus?: LSRegStatus; // reg status of currently logged in user for this summary
    lsStatus: LSessionStatus;
    registered?: number;
    startedAt?: number;
    endedAt?: number;
}

export function defaultLsessionSummary(): LsessionSummaryModel {
    return {
        id: 'string',
        userId: 'string',
        creatorId: 'string1',
        creatorName: 'string',
        creatorAvatarUrl: undefined,
        title: '<PERSON>ình học',
        grade: '9',
        subject: 'Toán',
        regId: 'string', // reg id of currently logged in user for this summary
        regStatus: 'WAITING_CONFIRMED', // reg status of currently logged in user for this summary
        lsStatus: 'NOT_STARTED',
        registered: 34,
        startedAt: new Date().getTime() - 60000,
        endedAt: new Date().getTime() + 60000,
    };
}
