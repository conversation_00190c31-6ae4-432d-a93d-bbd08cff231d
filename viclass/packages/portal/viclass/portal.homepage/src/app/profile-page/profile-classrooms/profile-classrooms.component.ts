import { Component, OnInit } from '@angular/core';
import { LsessionSummaryComponent } from '../lsession-summary/lsession-summary.component';
import { LSessionService, SearchSessionFormData, UserProfile, UserService } from '@viclass/portal.common';
import { BehaviorSubject } from 'rxjs';
import { LsessionSummaryModel } from '../lsession-summary/model';
import { AsyncPipe, NgForOf, NgIf } from '@angular/common';

@Component({
    selector: 'profile-classrooms',
    templateUrl: './profile-classrooms.component.html',
    styles: [],
    standalone: true,
    imports: [LsessionSummaryComponent, NgForOf, AsyncPipe, NgIf],
})
export class ProfileClassroomsComponent implements OnInit {
    createdSummaries$: BehaviorSubject<LsessionSummaryModel[]> = new BehaviorSubject([]);
    registeredSummaries$: BehaviorSubject<LsessionSummaryModel[]> = new BehaviorSubject([]);

    private user: UserProfile;

    tab: 'creator' | 'registered' = 'creator';

    constructor(
        private userService: UserService,
        private lSessionService: LSessionService
    ) {
        this.user = this.userService.curUser$.getValue();
        const request = <SearchSessionFormData>{
            userId: this.user.id,
            lsStatus: ['NOT_STARTED', 'STARTED'],
        };
        this.lSessionService.sessionSummaryList(request).subscribe(lss => {
            const models = lss.map(
                ls =>
                    <LsessionSummaryModel>{
                        id: ls.summary.id,
                        userId: this.user.id,
                        title: ls.summary.title,
                        subject: ls.summary.subject,
                        grade: ls.summary.grade,
                        lsStatus: ls.summary.state.status,
                        creatorId: ls.creator.id,
                        creatorAvatarUrl: ls.creator.avatarUrl,
                        creatorName: ls.creator.username,
                        regStatus: ls.summary.regStatus,
                        regId: ls.summary.regId,
                        registered: ls.summary.state.registered,
                        startedAt: ls.summary.state.startedAt,
                        endedAt: ls.summary.state.endedAt,
                    }
            );
            this.createdSummaries$.next(models.filter(s => s.creatorId === this.user.id));
            this.registeredSummaries$.next(models.filter(s => s.creatorId !== this.user.id));
        });
    }

    ngOnInit(): void {}

    switchTab(tab: 'creator' | 'registered') {
        this.tab = tab;
    }

    createNewLsession() {
        window.open(`/classrooms/create`, '_blank');
    }
}
