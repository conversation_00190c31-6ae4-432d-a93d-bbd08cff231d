import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    OnDestroy,
    OnInit,
    Output,
} from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { EditorType } from '@viclass/editor.core';
import {
    DocInProfileMetadataService,
    DocumentDocMetadataService,
    DocumentService,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    NotificationModule,
    NotificationService,
    SpinnerLabelComponent,
    UserService,
    ViewportUIModuleConfig,
} from '@viclass/portal.common';
import { BehaviorSubject, Subscription } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { HomepageCreateDocConfig, HomePageService } from '../../../home-page/home-page.service';
import { ViewportManagerComponent, VpCmd } from '../../../viewport-manager/viewport-manager.component';
import { EDITOR_ICONS } from '../../../model';

const e = environment.confEnv;

type CreateDocumentData = {
    docName: string;
};

type CreateDocOptions = {
    editor: EditorType;
    label: string;
    icon: string;
};

const CREATE_DOC_OPTION_DEFS: CreateDocOptions[] = [
    {
        editor: 'GeometryEditor',
        label: 'Hình học',
        icon: EDITOR_ICONS['GeometryEditor'],
    },
    {
        editor: 'FreeDrawingEditor',
        label: 'Vẽ tự do',
        icon: EDITOR_ICONS['FreeDrawingEditor'],
    },
    {
        editor: 'WordEditor',
        label: 'Văn bản',
        icon: EDITOR_ICONS['WordEditor'],
    },
    {
        editor: 'MathEditor',
        label: 'Công thức toán',
        icon: EDITOR_ICONS['MathEditor'],
    },
    {
        editor: 'MathGraphEditor',
        label: 'Đồ thị hàm số',
        icon: EDITOR_ICONS['MathGraphEditor'],
    },
];

@Component({
    selector: 'profile-create-document',
    standalone: true,
    imports: [
        CommonModule,
        FormUtilModule,
        ReactiveFormsModule,
        NotificationModule,
        ViewportManagerComponent,
        SpinnerLabelComponent,
    ],
    providers: [HomePageService, DocumentDocMetadataService],
    templateUrl: './profile-create-document-dialog.component.html',
    styleUrls: ['./profile-create-document-dialog.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileCreateDocumentComponent implements OnInit, OnDestroy {
    /**
     * Emitted when the user closes the create document with the boolean value
     * that indicates if the document was created or not
     */
    @Output() readonly closeCreateDoc = new EventEmitter<boolean>();

    readonly isSaving$ = new BehaviorSubject<boolean>(false);
    readonly formError$ = new BehaviorSubject<ErrorModel>(null);
    form: UntypedFormGroup;

    docNameError: ErrorModel = null;

    createDocOptions: CreateDocOptions[] = CREATE_DOC_OPTION_DEFS;
    createdViewports: EditorType[] = [];

    vpCmps: Map<EditorType, ViewportManagerComponent> = new Map();
    showEditor: BehaviorSubject<EditorType> = new BehaviorSubject(null);
    showEditorSub: Subscription;

    /**
     * Set of viewports that are currently loading.
     * We won't render the viewport component until the coord is ready for it.
     */
    loadingViewport = new Set<EditorType>();

    constructor(
        private fb: UntypedFormBuilder,
        private metadataService: DocInProfileMetadataService,
        private notificationService: NotificationService,
        public userService: UserService,
        public documentService: DocumentService,
        private homepageService: HomePageService,
        private cdr: ChangeDetectorRef
    ) {}

    async ngOnInit() {
        // load editor configs
        await this.documentService.retrieve(e, ['ZoomToolsEditor']);
        await this.documentService.createOrUpdateCoordinator();

        this.showEditorSub = this.showEditor.subscribe(edType => {
            if (!edType) return;
            const cmp = this.vpCmps.get(edType);
            cmp?.use('EditMode');
        });
    }

    /**
     * To be called from UI to switch to another editor tab.
     * Perform loading editor config and update coord if the editor is not loaded
     *
     * @param editor the editor tab to be selected
     */
    async onSelectEditor(editor: EditorType) {
        this.showEditor.next(editor);

        if (!this.createdViewports.includes(editor)) {
            this.createdViewports.push(editor);
            this.loadingViewport.add(editor);

            await this.documentService.retrieve(e, [editor]);
            await this.documentService.createOrUpdateCoordinator();

            this.loadingViewport.delete(editor);
        }

        this.cdr.markForCheck();
    }

    ngOnDestroy(): void {
        if (this.showEditorSub) this.showEditorSub.unsubscribe();
    }

    /**
     * Build form to save the new document to profile
     */
    buildForm = (data?: CreateDocumentData): FormBuildingResult => {
        data = data || { docName: '' };

        const result = new FormCreator(this.fb, data)
            .validators({
                docName: [Validators.required],
            })
            .validatorMessages({
                docName: { required: 'Tên tài liệu là bắt buộc' },
            })
            .build();

        this.form = result.control as UntypedFormGroup;
        return result;
    };

    /**
     * Handle saving the new document to profile on submit.
     */
    submitCreateDoc = async (data: FormFlowSubmitEvent): Promise<void> => {
        this.isSaving$.next(true);

        const currEdType = this.showEditor.value;
        if (!this.form.valid || !currEdType) return;

        const docName: string = data.data.docName;
        const cmp = this.vpCmps.get(currEdType);

        if (!cmp || !cmp.docId)
            throw new Error(
                'Not supposed to happen. Submit create doc must be called when viewport already set and document already created.'
            );

        const viewport = cmp.viewport;
        const gid = cmp.docId;

        const blob = await this.capturePreview(viewport.id);
        if (!blob) return;

        try {
            await this.metadataService.saveDocToProfile(gid, docName, blob, currEdType, 'doc-ownership');

            this.notificationService.showNotification({
                message: 'Lưu tài liệu thành công',
                status: 'success',
            });
            this.closeCreateDoc.emit(true);
        } catch (e) {
            console.error(e);

            this.notificationService.showNotification({
                message: 'Lưu tài liệu thất bại, xin vui lòng thử lại',
                status: 'error',
            });
        } finally {
            this.isSaving$.next(false);
        }
    };

    /**
     * Handle viewport commands from the child viewport-manager component.
     * So we can know when the viewport is ready and start loading the doc on it
     */
    onViewportCmd(cmd: VpCmd, edType: EditorType) {
        switch (cmd.type) {
            case 'viewport-ready':
                this.onViewportReady(cmd.cmp);
                break;
        }
    }

    /**
     *  Capture the viewport preview. Will return null if failed.
     */
    async capturePreview(viewportId: string): Promise<Blob> {
        try {
            const blob = await this.documentService.coord.captureViewportPreview(viewportId);

            if (!blob) throw Error('Please make sure the viewport is visible');
            return blob;
        } catch (e) {
            console.error(e);

            this.notificationService.showNotification({
                message: 'Tạo bản xem trước thất bại',
                status: 'error',
            });
            return null;
        }
    }

    /**
     * Handle the callback from the child viewport component
     * when the viewport is ready to create a new document on it
     */
    private async onViewportReady(cmp: ViewportManagerComponent) {
        this.vpCmps.set(cmp.edType, cmp);

        const createDocConfig: HomepageCreateDocConfig = {
            coord: this.documentService.coord,
            viewport: cmp.viewport,
            edType: cmp.edType,
            userProfile: this.userService.curUser$.value,
        };

        const gid = await this.homepageService.createNewDoc(createDocConfig);

        if (!gid) throw new Error('Unable to create new document');

        cmp.docId = gid;

        if (this.showEditor.value == cmp.edType) cmp.use('EditMode');
    }

    /**
     * Get editor UI config for a specific editor type.
     * For passing the config to the child viewport component
     */
    uiModules(edType: EditorType): ViewportUIModuleConfig {
        return this.documentService.generateEmbedViewportUIConfig(edType);
    }
}
