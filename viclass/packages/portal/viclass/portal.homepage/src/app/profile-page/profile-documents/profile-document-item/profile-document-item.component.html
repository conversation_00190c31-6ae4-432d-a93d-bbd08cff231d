<vi-card>
    <vi-card-header>
        <div class="flex sm:flex-row flex-col-reverse items-center">
            <span class="text-SC1 vcon-general" #editorIconEl [ngClass]="editorIcon"
                ><lib-tooltip [toolTipFor]="editorIconEl" [tooltipContent]="editorName"></lib-tooltip
            ></span>
            <div class="ml-auto flex flex-row gap-[10px]">
                <span
                    class="vcon vcon-general vcon_page-bar_share p-[5px] cursor-pointer"
                    *ngIf="doc.docType === 'doc-in-profile'"
                    #shareEl
                    (click)="onShare()"
                    ><lib-tooltip [toolTipFor]="shareEl" [tooltipContent]="'Chia sẻ tài liệu'"></lib-tooltip
                ></span>
                <span
                    class="vcon vcon-general vcon_open-in-new-tab p-[5px] cursor-pointer"
                    (click)="openInNewTab()"
                    #openInNewTabEl
                    ><lib-tooltip [toolTipFor]="openInNewTabEl" [tooltipContent]="'Mở trong tab mới'"></lib-tooltip
                ></span>
                <span class="vcon vcon-general vcon_delete p-[5px] cursor-pointer" (click)="onDelete()" #deleteEl
                    ><lib-tooltip [toolTipFor]="deleteEl" [tooltipContent]="'Xóa tài liệu'"></lib-tooltip
                ></span>
            </div>
        </div>
    </vi-card-header>
    <vi-card-body>
        <div class="flex flex-col gap-[10px]">
            <div
                class="w-full h-[150px] bg-contain bg-no-repeat bg-center"
                [style.background-image]="'url(' + doc.details.previewUrl + ')'"></div>
            <div
                class="text-[18px] leading-[27px] font-[600] overflow-hidden text-ellipsis whitespace-nowrap"
                #docNameEl>
                <span>{{ doc.details.docName }}</span>
            </div>
        </div>
        <lib-tooltip [toolTipFor]="docNameEl" [tooltipContent]="doc.details.docName"></lib-tooltip>
    </vi-card-body>
    <vi-card-footer>
        <div class="flex flex-row gap-[5px]">
            <span class="vcon-general vcon_session_time"></span>
            <span>{{ this.doc.details.savedDate | date: 'dd/MM/yyyy HH:mm' }}</span>
        </div>
    </vi-card-footer>
</vi-card>

<ng-template #previewTemplate>
    <div
        class="profilepage-viewport viewport-container w-full h-[60vh]"
        app-viewport-manager
        [attr.edType]="doc.editorType"
        [edType]="doc.editorType"
        [coord]="this.documentService.coord"
        (onCmd)="onViewportCmd($event)"
        [uiModules]="uiModules()"
        [vpToolBar]="false"
        [showEditorUi]="false"></div>
</ng-template>
