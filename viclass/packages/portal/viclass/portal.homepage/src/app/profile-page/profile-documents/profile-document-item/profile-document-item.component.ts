import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Injector,
    Input,
    Output,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { EmbedCoordinator } from '@viclass/editor.coordinator/embed';
import { ViewportManager } from '@viclass/editor.core';
import {
    CaptureType,
    DocumentInfoResponse,
    DocumentService,
    SharingDialogConfig,
    SharingDocDialogComponent,
    SourceType,
    TabType,
    TooltipComponent,
    ViewportUIModuleConfig,
} from '@viclass/portal.common';
import { BehaviorSubject, take, tap } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { EDITOR_ICONS, EDITOR_NAMES } from '../../../model';
import { ViCardBodyComponent, ViCardComponent, ViCardFooterComponent, ViCardHeaderComponent } from '../../../vi-card';
import { ViewportManagerComponent, VpCmd } from '../../../viewport-manager/viewport-manager.component';

const e = environment.confEnv;

@Component({
    selector: 'profile-document-item',
    standalone: true,
    imports: [
        CommonModule,
        ViCardComponent,
        ViCardHeaderComponent,
        ViCardBodyComponent,
        ViCardFooterComponent,
        ViewportManagerComponent,
        TooltipComponent,
    ],
    providers: [DocumentService],
    templateUrl: './profile-document-item.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileDocumentItemComponent {
    @Input() doc: DocumentInfoResponse;

    @Output() delete = new EventEmitter<DocumentInfoResponse>();
    @Output() share = new EventEmitter<DocumentInfoResponse>();

    @ViewChild('previewTemplate')
    previewTpl: TemplateRef<any>;

    vpPromise?: Promise<{ viewport: ViewportManager; coord: EmbedCoordinator }>;
    private vpPromiseRs: any;
    private vpPromiseRj: any;

    private dialogRef: MatDialogRef<any>;

    constructor(
        private dialog: MatDialog,
        private injector: Injector,
        protected documentService: DocumentService,
        private cdr: ChangeDetectorRef
    ) {}

    get editorIcon(): string {
        return EDITOR_ICONS[this.doc.editorType];
    }

    get editorName(): string {
        return EDITOR_NAMES[this.doc.editorType] || this.doc.editorType;
    }

    openInNewTab() {
        window.open(`/user/doc/${this.doc.editorType}/${this.doc.docGlobalId}`, '_blank');
    }

    onShare() {
        this.dialogRef = this.dialog.open<SharingDocDialogComponent, SharingDialogConfig>(SharingDocDialogComponent, {
            injector: this.injector,
            data: {
                showTabs: ['share', 'embed'] as TabType[],
                selectedTab: 'share' as TabType,
                captureType: 'viewport' as CaptureType,
                docGlobalId: this.doc.docGlobalId,
                docInfo: new BehaviorSubject(this.doc),
                edType: this.doc.editorType,
                source: 'doc-ownership' as SourceType,
                previewCb: this.createPreviewInContainer.bind(this),
            },
        });

        this.dialogRef
            .afterClosed()
            .pipe(
                take(1),
                tap(() => this.cleanUpPreview())
            )
            .subscribe();
    }
    cleanUpPreview(): void {
        if (this.vpPromise) {
            delete this.vpPromise;
        }
    }

    async createPreviewInContainer(
        inputData: SharingDialogConfig,
        viewRef: ViewContainerRef,
        injector: Injector
    ): Promise<{ viewport: ViewportManager; coord: EmbedCoordinator }> {
        if (!this.documentService.coord || !this.documentService.isEdConfLoaded(this.doc.editorType))
            await this.initializeCoordinator();

        viewRef.createEmbeddedView(this.previewTpl); // create the view with the viewport manager component inside the preview area

        this.dialogRef.updatePosition();
        setTimeout(() => this.cdr.markForCheck());

        this.vpPromise = new Promise((rs, rj) => {
            this.vpPromiseRs = rs;
            this.vpPromiseRj = rj;
        });

        return this.vpPromise;
    }

    private async initializeCoordinator() {
        // load editor configs
        await this.documentService.retrieve(e, ['ZoomToolsEditor', this.doc.editorType]);
        await this.documentService.createOrUpdateCoordinator();
    }

    // this will be called when the viewport manager component finish created viewport
    async onViewportCmd(cmd: VpCmd) {
        switch (cmd.type) {
            case 'viewport-ready':
                await this.documentService.coord.loadDocOnViewport(cmd.cmp.viewport, {
                    gId: this.doc.docGlobalId,
                    edType: this.doc.editorType,
                });
                cmd.cmp.use('InteractiveMode');
                // complete the promise
                this.vpPromiseRs({
                    viewport: cmd.cmp.viewport,
                    coord: cmd.cmp.coord,
                });
                break;
        }
    }

    uiModules(): ViewportUIModuleConfig {
        return this.documentService.generateEmbedViewportUIConfig(this.doc.editorType, false);
    }

    onDelete() {
        this.delete.next(this.doc);
    }
}
