import { CommonModule, NgOptimizedImage } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    HostListener,
    OnDestroy,
    OnInit,
    TemplateRef,
    ViewChild,
} from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatMenuModule } from '@angular/material/menu';
import { MatTabsModule } from '@angular/material/tabs';
import {
    DateRange,
    DateRangePickerComponent,
    DocInProfileMetadataService,
    DocType,
    DocumentInfoResponse,
    DocumentService,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormUtilModule,
    LoadSavedDocsResponse,
    NotificationModule,
    NotificationService,
    ScrollNearEndDirective,
} from '@viclass/portal.common';
import {
    BehaviorSubject,
    debounceTime,
    firstValueFrom,
    map,
    merge,
    scan,
    Subject,
    Subscription,
    switchMap,
    tap,
} from 'rxjs';
import { EDITOR_ICONS } from '../../model';
import { ProfileCreateDocumentComponent } from './profile-create-document-dialog/profile-create-document-dialog.component';
import { ProfileDocumentItemComponent } from './profile-document-item/profile-document-item.component';

type SupportedEditor =
    | 'GeometryEditor'
    | 'FreeDrawingEditor'
    | 'WordEditor'
    | 'PdfEditor'
    | 'MathEditor'
    | 'MathGraphEditor';

type EditorOptions = SupportedEditor | '';

export type DocumentFilterData = {
    textSearch: string;
    dateRange: DateRange;
    editorType: EditorOptions;
};

type EditorOptionDefinition = {
    key: EditorOptions;
    label: string;
    icon?: string;
};

type DocOptions = {
    docType: DocType;
    label: string;
};

const EDITOR_OPTION_DEFS: EditorOptionDefinition[] = [
    {
        key: '',
        label: 'Tất cả',
    },
    {
        key: 'GeometryEditor',
        label: 'Hình học',
        icon: EDITOR_ICONS['GeometryEditor'],
    },
    {
        key: 'MathEditor',
        label: 'Công thức toán',
        icon: EDITOR_ICONS['MathEditor'],
    },
    {
        key: 'MathGraphEditor',
        label: 'Đồ thị hàm số',
        icon: EDITOR_ICONS['MathGraphEditor'],
    },
    {
        key: 'FreeDrawingEditor',
        label: 'Vẽ tự do',
        icon: EDITOR_ICONS['FreeDrawingEditor'],
    },
    {
        key: 'WordEditor',
        label: 'Văn bản',
        icon: EDITOR_ICONS['WordEditor'],
    },
];

const DOC_OPTION_DEFS: DocOptions[] = [
    {
        docType: 'doc-in-profile',
        label: 'Đã tạo',
    },
    {
        docType: 'doc-share-with-me',
        label: 'Được chia sẻ',
    },
];

@Component({
    selector: 'profile-documents',
    templateUrl: './profile-documents.component.html',
    standalone: true,
    imports: [
        CommonModule,
        ProfileDocumentItemComponent,
        FormUtilModule,
        ReactiveFormsModule,
        ScrollNearEndDirective,
        MatDialogModule,
        NgOptimizedImage,
        NotificationModule,
        DateRangePickerComponent,
        MatMenuModule,
        ProfileCreateDocumentComponent,
        MatTabsModule,
    ],
    providers: [DocumentService],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileDocumentsComponent implements OnInit, OnDestroy {
    @ViewChild('confirmDeletePopup', { read: TemplateRef })
    confirmDeletePopupRef: TemplateRef<any>;

    @ViewChild('createDocPopup', { read: TemplateRef })
    createDocPopupRef: TemplateRef<any>;

    createDocPopupInstance: MatDialogRef<any, any>;

    readonly formError$ = new BehaviorSubject<ErrorModel | null>(null);
    form: UntypedFormGroup;

    savedDocs$ = new BehaviorSubject<LoadSavedDocsResponse | null>(null); // Current load saved docs result
    shareWithMeDocs$ = new BehaviorSubject<LoadSavedDocsResponse | null>(null);

    clearAccumulatedDocs$ = new Subject<void>(); // Subject to trigger clear accumulated docs
    clearAccumulatedShareDocs$ = new Subject<void>(); // Subject to trigger clear accumulated docs

    loadingMore$ = new BehaviorSubject<boolean>(false);
    smallScreen$ = new BehaviorSubject<boolean>(false);
    creatingNewDoc$ = new BehaviorSubject<boolean>(false);

    curDocType$ = new BehaviorSubject<DocType>('doc-in-profile');
    docOptions: DocOptions[] = DOC_OPTION_DEFS;

    /**
     *  Accumulated docs for the infinite scroll
     */
    accumulatedDocs$ = merge(
        this.savedDocs$.pipe(
            map(loadDocsRes => (state: DocumentInfoResponse[]) => [...state, ...(loadDocsRes?.documents || [])])
        ),
        this.clearAccumulatedDocs$.pipe(map(() => (_state: DocumentInfoResponse[]) => []))
    ).pipe(scan((acc: DocumentInfoResponse[], fn) => fn(acc), []));

    /**
     *  Accumulated docs for the infinite scroll
     */
    accumulatedDocsForShare$ = merge(
        this.shareWithMeDocs$.pipe(
            map(loadDocsRes => (state: DocumentInfoResponse[]) => [...state, ...(loadDocsRes?.documents || [])])
        ),
        this.clearAccumulatedShareDocs$.pipe(map(() => (_state: DocumentInfoResponse[]) => []))
    ).pipe(scan((acc: DocumentInfoResponse[], fn) => fn(acc), []));

    formChangesSubscriptions: Subscription[] = [];
    editorTypeOptions: EditorOptionDefinition[] = EDITOR_OPTION_DEFS; // Definition for the editor type selection filter

    get fromEditorType(): SupportedEditor | '' {
        return this.form?.get('editorType')?.value || '';
    }

    get fromDateRange(): DateRange {
        return (
            this.form?.get('dateRange')?.value || {
                startDate: null,
                endDate: null,
            }
        );
    }

    get fromTextSearch(): string {
        return this.form?.get('textSearch')?.value || '';
    }

    constructor(
        private dialog: MatDialog,
        public fb: UntypedFormBuilder,
        private cdRef: ChangeDetectorRef,
        private metadataService: DocInProfileMetadataService,
        private notificationService: NotificationService
    ) {}

    ngOnInit(): void {
        firstValueFrom(this.metadataService.loadSavedDocs({}))
            .then(res => {
                this.savedDocs$.next(res);
            })
            .catch(e => {
                console.error(e);
                this.notificationService.showNotification({
                    message: 'Tải tài liệu đã tạo thất bại',
                    status: 'error',
                });
            });

        firstValueFrom(this.metadataService.loadShareWithMeDocs({}))
            .then(res => {
                this.shareWithMeDocs$.next(res);
            })
            .catch(e => {
                console.error(e);
                this.notificationService.showNotification({
                    message: 'Tải tài liệu được chia sẻ thất bại',
                    status: 'error',
                });
            });

        this.detectSmallScreen();
    }

    ngOnDestroy(): void {
        this.formChangesSubscriptions.forEach(sub => sub.unsubscribe());
    }

    /**
     *  Builds the form for filtering documents
     */
    buildForm = (data?: DocumentFilterData): FormBuildingResult => {
        data =
            data ||
            ({
                textSearch: '',
                dateRange: { startDate: undefined, endDate: undefined } as DateRange,
                editorType: '',
            } as DocumentFilterData);

        const result = new FormCreator(this.fb, data).build();

        this.form = result.control as UntypedFormGroup;

        // clear and reload first page of both docs to get the total number of docs
        const getSearchOption = (formData: any) => ({
            textSearch: formData.textSearch,
            page: 1,
            startDate: formData.dateRange.startDate,
            endDate: formData.dateRange.endDate,
            editorType: formData.editorType,
        });
        const docInProfileSub = this.form.valueChanges
            .pipe(
                debounceTime(500),
                switchMap(data =>
                    this.metadataService
                        .loadSavedDocs(getSearchOption(data))
                        // clear before add new docs result
                        .pipe(tap(() => this.clearAccumulatedDocs$.next()))
                )
            )
            .subscribe(res => this.savedDocs$.next(res));

        const docSharedWithMeSub = this.form.valueChanges
            .pipe(
                debounceTime(500),
                switchMap(data =>
                    this.metadataService
                        .loadShareWithMeDocs(getSearchOption(data))
                        // clear before add new docs result
                        .pipe(tap(() => this.clearAccumulatedShareDocs$.next()))
                )
            )
            .subscribe(res => this.shareWithMeDocs$.next(res));

        this.formChangesSubscriptions.push(docInProfileSub, docSharedWithMeSub);
        return result;
    };

    onDateRangeChange(dateRange: DateRange) {
        this.form.get('dateRange')?.setValue(dateRange);
    }

    onEditorTypeChanges(editorType: EditorOptions) {
        this.form.get('editorType')?.setValue(editorType);
    }

    /**
     *  Handles load more documents when the user scrolls near the end of the page.
     */
    async onScrollNearEnd() {
        const currLoadResult = this.savedDocs$.value;
        if (!currLoadResult?.hasNext || this.loadingMore$.value) return;

        this.loadingMore$.next(true);

        try {
            const res = await firstValueFrom(
                this.metadataService.loadSavedDocs({
                    textSearch: this.fromTextSearch,
                    page: currLoadResult.page + 1,
                    startDate: this.fromDateRange.startDate,
                    endDate: this.fromDateRange.endDate,
                    editorType: this.fromEditorType,
                })
            );
            this.savedDocs$.next(res);
        } catch (e) {
            console.error(e);
            this.notificationService.showNotification({
                message: 'Tải thêm tài liệu thất bại',
                status: 'error',
            });
        } finally {
            this.loadingMore$.next(false);
        }
    }

    /**
     *  Handles load more documents when the user scrolls near the end of the page.
     */
    async onScrollNearEndForShareWithMe() {
        const currLoadResult = this.shareWithMeDocs$.value;
        if (!currLoadResult?.hasNext || this.loadingMore$.value) return;

        this.loadingMore$.next(true);

        try {
            const res = await firstValueFrom(
                this.metadataService.loadShareWithMeDocs({
                    textSearch: this.fromTextSearch,
                    page: currLoadResult.page + 1,
                    startDate: this.fromDateRange.startDate,
                    endDate: this.fromDateRange.endDate,
                    editorType: this.fromEditorType,
                })
            );
            this.shareWithMeDocs$.next(res);
        } catch (e) {
            console.error(e);
            this.notificationService.showNotification({
                message: 'Tải thêm tài liệu thất bại',
                status: 'error',
            });
        } finally {
            this.loadingMore$.next(false);
        }
    }

    onCreateDoc() {
        this.creatingNewDoc$.next(true);

        this.createDocPopupInstance = this.dialog.open(this.createDocPopupRef, {
            disableClose: true,
        });
        this.createDocPopupInstance.afterClosed().subscribe(() => {
            this.creatingNewDoc$.next(false);
        });
    }

    onCreateDocPopupClose(isCreated) {
        if (isCreated) this.reloadSavedDocs();
        this.createDocPopupInstance?.close();
    }

    /**
     *  Handle delete document confirmation
     */
    onDeleteDoc(doc: DocumentInfoResponse) {
        const dialogRef = this.dialog.open(this.confirmDeletePopupRef);

        dialogRef.afterClosed().subscribe(async (result?: boolean) => {
            if (!result) return;
            const isSavedDoc = this.curDocType$.value == 'doc-in-profile';

            try {
                if (isSavedDoc) {
                    await this.handleDeleteSavedDoc(doc);
                } else {
                    await this.handleDeleteShareWithMeDoc(doc);
                }

                this.notificationService.showNotification({
                    message: 'Xóa tài liệu thành công',
                    status: 'success',
                });
            } catch (e) {
                console.error(e);
                this.notificationService.showNotification({
                    message: 'Xóa tài liệu thất bại',
                    status: 'error',
                });
                return;
            }

            if (isSavedDoc) {
                await this.reloadSavedDocs();
            } else {
                await this.reloadShareWithMeDocs();
            }
        });
    }

    /**
     *  Actually delete the document from profile
     */
    private async handleDeleteSavedDoc(doc: DocumentInfoResponse) {
        const res = await firstValueFrom(this.metadataService.deleteSavedDoc(doc.editorType, doc.docGlobalId));
        if (res.status !== HttpStatusCode.Ok) {
            throw Error(`Delete failed with status ${res.status}: ${JSON.stringify(res)}`);
        }
    }

    /**
     *  Removed doc from the list of share with me (not affect the real doc of the owner)
     */
    private async handleDeleteShareWithMeDoc(doc: DocumentInfoResponse) {
        const res = await firstValueFrom(this.metadataService.deleteShareWithMeDoc(doc.editorType, doc.docGlobalId));
        if (res.status !== HttpStatusCode.Ok) {
            throw Error(`Delete share with me failed with status ${res.status}: ${JSON.stringify(res)}`);
        }
    }

    /**
     *  Reload all saved docs to the current page
     *  so that the user can stand in the same scroll position
     */
    private async reloadSavedDocs() {
        const loadedState = this.savedDocs$.value;
        if (!loadedState) return;

        try {
            // reload all pages until the current page
            const currentPage = loadedState.page;
            const loadPagePromises: Promise<LoadSavedDocsResponse>[] = Array.from(
                { length: currentPage },
                (_, i) => i + 1
            ).map(pageNum =>
                firstValueFrom(
                    this.metadataService.loadSavedDocs({
                        page: pageNum,
                        textSearch: this.fromTextSearch,
                        startDate: this.fromDateRange.startDate,
                        endDate: this.fromDateRange.endDate,
                        editorType: this.fromEditorType,
                    })
                )
            );

            const pageResults = await Promise.all(loadPagePromises);

            await this.detachedUpdate(() => {
                this.clearAccumulatedDocs$.next();
                pageResults
                    .filter((res, idx) => idx === 0 || res?.documents?.length > 0)
                    .forEach(res => this.savedDocs$.next(res));
            });
        } catch (e) {
            console.error(e);
            this.notificationService.showNotification({
                message: 'Tải lại tài liệu thất bại, vui lòng tải lại trang',
                status: 'error',
            });
        }
    }

    /**
     *  Reload all shared with me docs to the current page
     *  so that the user can stand in the same scroll position
     */
    private async reloadShareWithMeDocs() {
        const loadedState = this.shareWithMeDocs$.value;
        if (!loadedState) return;

        try {
            // reload all pages until the current page
            const currentPage = loadedState.page;
            const loadPagePromises: Promise<LoadSavedDocsResponse>[] = Array.from(
                { length: currentPage },
                (_, i) => i + 1
            ).map(pageNum =>
                firstValueFrom(
                    this.metadataService.loadShareWithMeDocs({
                        page: pageNum,
                        textSearch: this.fromTextSearch,
                        startDate: this.fromDateRange.startDate,
                        endDate: this.fromDateRange.endDate,
                        editorType: this.fromEditorType,
                    })
                )
            );

            const pageResults = await Promise.all(loadPagePromises);

            await this.detachedUpdate(() => {
                this.clearAccumulatedShareDocs$.next();
                pageResults
                    .filter((res, idx) => idx === 0 || res?.documents?.length > 0)
                    .forEach(res => this.shareWithMeDocs$.next(res));
            });
        } catch (e) {
            console.error(e);
            this.notificationService.showNotification({
                message: 'Tải lại tài liệu thất bại, vui lòng tải lại trang',
                status: 'error',
            });
        }
    }

    /**
     *  Perform update in a detached state and reattach after that.
     *  To prevent the screen flickering from many updates happening at the same time.
     */
    private async detachedUpdate(action: () => Promise<void> | void) {
        try {
            this.cdRef.detach();
            await action();
        } finally {
            this.cdRef.reattach();
            this.cdRef.markForCheck();
        }
    }

    @HostListener('window:resize', ['$event'])
    onWindowResize() {
        this.detectSmallScreen();
    }

    /**
     *  Small screen detection.
     *  We use a different layout for the filters in small screen
     */
    private detectSmallScreen() {
        this.smallScreen$.next(window.innerWidth < 568);
    }

    onSelectDocOption(docType: DocType) {
        this.curDocType$.next(docType);
    }

    countByDocType(docType: DocType): number | undefined {
        switch (docType) {
            case 'doc-in-profile':
                return this.savedDocs$.value?.documents?.length;
            case 'doc-share-with-me':
                return this.shareWithMeDocs$.value?.documents?.length;
            default:
                return undefined;
        }
    }
}
