<div *ngIf="userService.curUser$ | async as userProfile; else profileLoading">
    <div class="avatar pb-4">
        <div class="w-28 h-28 relative">
            <img class="avatar-img rounded-full w-28 h-28" [src]="userProfile.avatarUrl" />
            <div
                class="absolute right-0 bottom-0 py-[5px] px-[10px] rounded-full border-2 border-sky-500 bg-white flex items-center justify-center cursor-pointer hover:bg-sky-100 transition-opacity duration-300 ease-in-out"
                title="Tải lên avatar"
                (click)="uploadAvatar()">
                <span class="vcon-general vcon_document-camera"></span>
                <input
                    #uploadAvatarInput
                    type="file"
                    accept=".png, .jpg, .jpeg, .webp, .svg, .gif"
                    class="hidden"
                    (change)="onFileInput($event)" />
            </div>
        </div>
    </div>
    <p class="text-3xl">Xin chào</p>
    <div class="flex items-start gap-1">
        <h2 class="text-3xl font-bold">
            {{ userProfile.name.trim().length ? userProfile.name : userProfile.username }}
            <span class="vcon-general vcon_verified-sticker text-[15px] align-top">
                <span class="path1"></span>
                <span class="path2"></span>
            </span>
        </h2>
    </div>
</div>

<ng-template #profileLoading>
    <div class="avatar pb-4">
        <div class="w-28 h-28 relative">
            <div class="animate-pulse w-28 h-28 avatar-img rounded-full"></div>
        </div>
    </div>
</ng-template>

<ng-template #editAvatarDialog>
    <div class="vi-popup-container" style="height: auto">
        <div class="pt-[15px] pb-[15px] gap-[10px] flex flex-col">
            <div class="vi-popup-message text-center font-semibold">
                <div>CANH CHỈNH HÌNH ĐẠI DIỆN</div>
            </div>
            <div class="flex justify-center items-center">
                <lib-cropper
                    #cropper
                    class="w-[310px] h-[310px]"
                    [imageUrl]="imgUrl$ | async"
                    [cropperOptions]="cropConfig"
                    (ready)="cropperReady$.next(true)"
                    (loadError)="onLoadCropperError($event)"></lib-cropper>
            </div>
            <div class="flex justify-center items-center" *ngIf="errorMessage$ | async as errorMessage">
                <div
                    class="border-[1px] border-SC5 bg-PAS1 rounded-[5px] py-[5px] px-[10px] text-SC5 flex items-center">
                    <span class="vcon-general vcon_error mr-[10px]"></span>
                    {{ errorMessage }}
                </div>
            </div>
            <div class="flex flex-row gap-[10px] text-center justify-center">
                <button
                    class="vi-btn vi-btn-small vi-btn-focus"
                    (click)="confirmCrop()"
                    [disabled]="shouldDisableOkButton$ | async"
                    [spinner]="loading$">
                    Xác nhận
                </button>
                <button class="vi-btn vi-btn-small vi-btn-outline" [mat-dialog-close]="false">Hủy</button>
            </div>
        </div>
    </div>
</ng-template>
