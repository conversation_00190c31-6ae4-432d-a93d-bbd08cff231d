import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UserProfile, UserService } from '@viclass/portal.common';
import { Subscription, firstValueFrom } from 'rxjs';
import { ProfileTabKey } from '../profile-page.component';

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'profile-head-menu',
    templateUrl: './profile-head-menu.component.html',
    styleUrls: ['./profile-head-menu.component.scss'],
})
export class ProfileHeadMenuComponent implements OnInit, OnDestroy {
    @Input()
    currentTabKey: ProfileTabKey;

    profile: UserProfile;
    profileSub: Subscription;

    constructor(
        public userService: UserService,
        cdr: ChangeDetectorRef
    ) {
        const profileSub = userService.curUser$.subscribe(profile => {
            this.profile = profile;
            cdr.markForCheck();
        });
    }

    ngOnInit(): void {}

    ngOnDestroy(): void {
        this.profileSub?.unsubscribe();
    }

    async logout() {
        await firstValueFrom(this.userService.doLogout());
        window.location.href = '/'; // go back to home page
    }

    protected readonly ProfilePageRouteKey = ProfileTabKey;
}
