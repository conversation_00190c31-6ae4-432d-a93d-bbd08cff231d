<form id="profile-info" (ngSubmit)="onSubmit()">
    <div class="bg-white p-8 rounded-lg dark:border-gray-700 shadow-SH1">
        <div class="w-full max-w-3xl grid md:grid-cols-[275px_1fr]" [formGroup]="form">
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_footer_email !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Email đăng ký</span>
                </div>
            </div>
            <div class="pl-7 flex items-center">
                <b>
                    <span>
                        {{ (userService.curUser$ | async).email }}
                    </span>
                </b>
            </div>
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_mobile !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Số điện thoại</span>
                </div>
            </div>
            <div class="flex items-center">
                <b *ngIf="!isEdit" class="pl-7">
                    <span>
                        {{ (userService.curUser$ | async).phone }}
                    </span>
                </b>
                <div class="w-full md:pl-7" *ngIf="isEdit">
                    <input
                        [attr.disabled]="isSubmitting.getValue() ? 'disabled' : null"
                        type="text"
                        formControlName="phone"
                        class="w-full rounded-xl border border-gray-400 py-1 px-3 mt-2"
                        placeholder="Ví dụ: 0123xxxxxxx" />
                    <div class="alert alert-danger"></div>

                    <div *ngIf="phoneField?.invalid" class="text-red-500">
                        <small *ngIf="phoneField?.errors?.['invalidCharacters']"
                            >! Số điện thoại chỉ được chứa số.</small
                        >
                        <small *ngIf="phoneField?.errors?.['invalidLength']"
                            >! Số điện thoại phải có đúng 10 số (hoặc 11 số nếu bắt đầu bằng +84).</small
                        >
                        <small *ngIf="phoneField?.errors?.['invalidFormat']"
                            >! Định dạng số điện thoại không hợp lệ.</small
                        >
                    </div>
                </div>
            </div>
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_name !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Họ và tên</span>
                </div>
            </div>
            <div class="flex items-center">
                <b *ngIf="!isEdit" class="pl-7">
                    <span> {{ name }} </span>
                </b>
                <div class="w-full md:pl-7" *ngIf="isEdit">
                    <input
                        [attr.disabled]="isSubmitting.getValue() ? 'disabled' : null"
                        type="text"
                        formControlName="name"
                        class="w-full rounded-xl border border-gray-400 py-1 px-3 mt-2"
                        placeholder="Nguyễn Văn A" />
                    <div class="alert alert-danger"></div>

                    <div *ngIf="nameField?.invalid" class="text-red-500">
                        <small *ngIf="nameField?.errors?.['tooShort']">! Họ và tên phải có ít nhất 2 ký tự.</small>
                        <small *ngIf="nameField?.errors?.['maxLength']"
                            >! Họ và tên không được vượt quá 100 ký tự.</small
                        >
                        <small *ngIf="nameField?.errors?.['containsNumbers']">! Họ và tên không được chứa số.</small>
                        <small *ngIf="nameField?.errors?.['consecutiveSpaces']"
                            >! Họ và tên không được chứa nhiều khoảng trắng liên tiếp.</small
                        >
                        <small *ngIf="nameField?.errors?.['invalidCharacters']"
                            >! Họ và tên chỉ được chứa chữ cái, khoảng trắng, hoặc dấu (<b>.</b>, <b>'</b>,
                            <b>-</b>).</small
                        >
                    </div>
                </div>
            </div>
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_connection !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Liên kết mạng xã hội</span>
                </div>
            </div>
            <div class="flex items-center">
                <div class="flex items-center gap-3" *ngIf="!(isLoadingSocialRegistration | async)">
                    <span *ngIf="!isSocialExists" class="pl-7"> _ </span>
                    <div class="flex items-center gap-3 md:pl-7">
                        <div class="relative w-full" *ngIf="socialRegistration[Social.FACEBOOK]">
                            <img src="assets/img/facebook.svg" class="w-8 h-8 rounded-circle" />
                            <span
                                *ngIf="isEdit && canUnlinkSocial"
                                (click)="!isLoadingSocialRegistration.getValue() && unlinkSocial(Social.FACEBOOK)"
                                class="cursor-pointer absolute -top-1 -right-1 inline-flex items-center justify-center bg-white border rounded-full vcon-general vcon_delete !text-xs h-4 w-4 border-[rgb(var(--P1))]"></span>
                        </div>
                        <div class="relative w-full" *ngIf="socialRegistration[Social.GOOGLE]">
                            <img src="assets/img/google.svg" class="w-8 h-8 rounded-circle" />
                            <span
                                *ngIf="isEdit && canUnlinkSocial"
                                (click)="!isLoadingSocialRegistration.getValue() && unlinkSocial(Social.GOOGLE)"
                                class="cursor-pointer absolute -top-1 -right-1 inline-flex items-center justify-center bg-white border rounded-full vcon-general vcon_delete !text-xs h-4 w-4 border-[rgb(var(--P1))]"></span>
                        </div>
                    </div>
                </div>
                <img
                    *ngIf="isLoadingSocialRegistration | async"
                    src="assets/img/mini-spinner.svg"
                    class="w-[1rem] invert md:ml-7"
                    alt="loading" />
            </div>
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_calendar !text-2xl text-gray-600"></span>
                    <span class="text-gray-700"> Ngày / tháng / năm sinh </span>
                </div>
            </div>
            <div class="flex items-center">
                <b *ngIf="!isEdit" class="pl-7">
                    <span>
                        {{ formatUnixToDDMMYYYY((userService.curUser$ | async).dateOfBirth) }}
                    </span>
                </b>
                <div *ngIf="isEdit" class="w-full md:pl-7">
                    <div class="grid lg:grid-cols-2 gap-6 w-full">
                        <common-new-date-time-picker
                            class="mt-2 w-full"
                            [isDisabled]="isSubmitting | async"
                            [date]="fromUnixToDate((userService.curUser$ | async).dateOfBirth)"
                            (dateChange)="form.get('dob').setValue($event)"
                            inputClasses="w-full rounded-xl border border-gray-400 py-[0.15rem] py-1 px-3">
                            <i class="vcon-general vcon_calendar absolute right-1 top-1"></i>
                        </common-new-date-time-picker>
                    </div>
                </div>
            </div>
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_gender !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Giới tính</span>
                </div>
            </div>
            <div class="flex items-center">
                <b *ngIf="!isEdit" class="pl-7">
                    <span>
                        {{ GenderMapping[Gender[(userService.curUser$ | async).gender]] }}
                    </span>
                </b>
                <div *ngIf="isEdit" class="w-full md:pl-7">
                    <div class="grid lg:grid-cols-2 gap-6 w-full">
                        <select
                            [attr.disabled]="isSubmitting.getValue() ? 'disabled' : null"
                            formControlName="gender"
                            class="w-full rounded-xl border border-gray-400 py-1 px-3 mt-2">
                            <option
                                *ngFor="let gender of GenderMapping | keyvalue"
                                [value]="gender.key"
                                [selected]="(userService.curUser$ | async).gender === gender.key">
                                {{ GenderMapping[gender.key] }}
                            </option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_footer_location !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Địa chỉ</span>
                </div>
            </div>
            <div class="flex items-center">
                <b *ngIf="!isEdit" class="pl-7">
                    <span>{{ address }}</span>
                </b>
                <div *ngIf="isEdit" class="w-full md:pl-7">
                    <div class="grid lg:grid-cols-2 gap-6 mt-2 w-full" formGroupName="address">
                        <select
                            *ngFor="let ind of [0, 1, 2, 3]"
                            (focus)="fetchAddressLevel(ind)"
                            (change)="onAddressLevelChange(addressLevelInput, ind)"
                            [attr.disabled]="isAddressLoading || isSubmitting.getValue() ? 'disabled' : null"
                            class="w-full rounded-xl border border-gray-400 py-1 px-3"
                            #addressLevelInput>
                            <option
                                *ngIf="addressLoadingState[ind]"
                                [value]="form.get('address.' + addressLevelNameMapping[ind]).value"
                                selected
                                hidden
                                disabled>
                                Đang tải...
                            </option>
                            <option *ngIf="!addressLoadingState[ind]" value="">
                                {{ addressPlaceholderMapping[ind] }}
                            </option>
                            <option
                                *ngFor="let item of addressLevelListMapping[ind]"
                                [value]="item.label"
                                [selected]="form.get('address.' + addressLevelNameMapping[ind]).value === item.label">
                                {{ item.label }}
                            </option>
                        </select>
                        <input
                            [attr.disabled]="isSubmitting.getValue() ? 'disabled' : null"
                            formControlName="street"
                            type="text"
                            class="lg:col-span-2 w-full rounded-xl border border-gray-400 py-1 px-3"
                            placeholder="Số nhà" />
                    </div>
                </div>
            </div>

            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_calendar !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Ngày tạo tài khoản</span>
                </div>
            </div>
            <div class="pl-7 flex items-center">
                <b>
                    <span>
                        {{ (loginInfo$ | async)?.dateCreated || '-' }}
                    </span>
                </b>
            </div>

            <div class="md:border-r border-gray-400 align-top">
                <div class="flex items-center gap-1 py-3">
                    <span class="vcon-general vcon_user_students !text-2xl text-gray-600"></span>
                    <span class="text-gray-700">Lần đăng nhập cuối</span>
                </div>
            </div>
            <div class="pl-7 flex items-center">
                <b>
                    <span>
                        {{ (loginInfo$ | async)?.lastLogin || '-' }}
                    </span>
                </b>
            </div>
        </div>
    </div>
    <button
        *ngIf="!isEdit"
        type="button"
        (click)="enableEdit()"
        class="vi-btn vi-btn-normal vi-btn-outline mx-auto mt-4">
        Chỉnh sửa
    </button>
    <div *ngIf="isEdit" class="w-full flex items-center justify-center mt-4 gap-6">
        <button
            [disabled]="!isFormChanged || form.invalid || isSubmitting.getValue()"
            type="submit"
            class="vi-btn vi-btn-normal vi-btn-focus"
            [ngClass]="{
                '!border-[0.2rem] !px-[0.4rem]': !isFormChanged || form.invalid || isSubmitting.getValue(),
            }"
            [spinner]="isSubmitting">
            Lưu
        </button>
        <button
            [disabled]="isSubmitting | async"
            type="button"
            (click)="disableEdit()"
            class="vi-btn vi-btn-normal vi-btn-outline">
            Hủy
        </button>
    </div>
</form>
