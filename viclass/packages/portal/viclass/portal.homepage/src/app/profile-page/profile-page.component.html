<div class="bg-white md:min-h-screen">
    <div
        class="sticky top-0 left-0 inline-block md:hidden bg-BW7/95 text-BW1 w-full shadow-[0px_1px_30px_0_rgb(var(--BW1)/0.25)] z-[100]">
        <profile-head-menu [currentTabKey]="currentTabKey"></profile-head-menu>
    </div>
    <div class="w-full md:w-80 md:fixed top-0 left-0 p-8 bg-white md:h-screen">
        <div class="sticky top-0 pb-8 bg-white hidden md:block z-[100]">
            <a routerLink="/">
                <img src="assets/img/vi-logo.svg" class="lg:h-[35px] md:h-[30px] xs:h-[20px]" />
            </a>
        </div>
        <div class="text-center mb-8 md:hidden">
            <h2 class="uppercase text-pink-500 text-xl font-bold">
                {{ title() }}
            </h2>
        </div>
        <head-info></head-info>
        <div class="h-full py-4 overflow-y-auto hidden md:block">
            <ul class="space-y-2 font-medium">
                <li>
                    <a
                        [routerLink]="'../' + ProfileTabKey.INFO"
                        [ngClass]="{
                            'text-pink-500': currentTabKey === ProfileTabKey.INFO,
                        }"
                        class="flex items-center py-2 text-gray-900 rounded-lg group">
                        <span>{{ title(ProfileTabKey.INFO) }}</span>
                    </a>
                </li>
                <li>
                    <a
                        [routerLink]="'../' + ProfileTabKey.SECURITY"
                        class="flex items-center py-2 text-gray-900 rounded-lg group"
                        [ngClass]="{
                            'text-pink-500': currentTabKey === ProfileTabKey.SECURITY,
                        }">
                        <span>{{ title(ProfileTabKey.SECURITY) }}</span>
                    </a>
                </li>
                <!-- <li>
                    <a
                        [routerLink]="'../' + ProfileTabKey.SETTINGS"
                        class="flex items-center py-2 text-gray-900 rounded-lg group"
                        [ngClass]="{
                            'text-pink-500': currentTabKey === ProfileTabKey.SETTINGS,
                        }">
                        <span>{{ title(ProfileTabKey.SETTINGS) }}</span>
                    </a>
                </li> -->
                <hr class="w-40" />
                <li>
                    <a
                        [routerLink]="'../' + ProfileTabKey.CLASSROOMS"
                        class="flex items-center py-2 text-gray-900 rounded-lg group"
                        [ngClass]="{
                            'text-pink-500': currentTabKey === ProfileTabKey.CLASSROOMS,
                        }">
                        <span>{{ title(ProfileTabKey.CLASSROOMS) }}</span>
                    </a>
                </li>
                <li>
                    <a
                        [routerLink]="'../' + ProfileTabKey.DOCUMENTS"
                        class="flex items-center py-2 text-gray-900 rounded-lg group"
                        [ngClass]="{
                            'text-pink-500': currentTabKey === ProfileTabKey.DOCUMENTS,
                        }">
                        <span>{{ title(ProfileTabKey.DOCUMENTS) }}</span>
                    </a>
                </li>
                <li>
                    <button type="button" (click)="logout()" class="vi-btn vi-btn-normal vi-btn-outline">
                        Đăng xuất
                    </button>
                </li>
            </ul>
        </div>
    </div>
    <div class="flex flex-col md:pt-24 min-h-screen gap-8">
        <div class="flex px-8 flex-grow">
            <div class="w-0 md:w-[500px]"></div>
            <div class="w-full" [ngSwitch]="tab">
                <div class="md:min-h-[550px]">
                    <profile-info *ngSwitchCase="ProfileTabKey.INFO"></profile-info>
                    <profile-security *ngSwitchCase="ProfileTabKey.SECURITY"></profile-security>
                    <!-- <profile-settings *ngSwitchCase="ProfileTabKey.SETTINGS"></profile-settings> -->
                    <profile-classrooms *ngSwitchCase="ProfileTabKey.CLASSROOMS"></profile-classrooms>
                    <profile-documents *ngSwitchCase="ProfileTabKey.DOCUMENTS"></profile-documents>
                </div>
            </div>
        </div>
        <div class="bg-BW6 pt-[60px] pb-[60px]" style="clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%)">
            <app-footer></app-footer>
        </div>
    </div>
</div>
