import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';

import { UserService } from '@viclass/portal.common';
import { firstValueFrom } from 'rxjs';
import { ActivatedRoute, ParamMap } from '@angular/router';

export enum ProfileTabKey {
    INFO = 'info',
    SECURITY = 'security',
    SETTINGS = 'settings',
    CLASSROOMS = 'classrooms',
    DOCUMENTS = 'documents',
}

const profilePageRouteTitle = {
    [ProfileTabKey.INFO]: 'Thông tin cá nhân',
    [ProfileTabKey.SECURITY]: 'Bảo mật',
    // [ProfileTabKey.SETTINGS]: 'Cài đặt',
    [ProfileTabKey.CLASSROOMS]: 'Buổi học',
    [ProfileTabKey.DOCUMENTS]: 'Thư viện',
};

@Component({
    selector: 'profile-page',
    templateUrl: './profile-page.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfilePageComponent implements OnInit {
    public readonly ProfileTabKey = ProfileTabKey;
    public currentTabKey: ProfileTabKey;

    public tab: string;

    constructor(
        public userService: UserService,
        private route: ActivatedRoute
    ) {}

    ngOnInit(): void {
        this.route.paramMap.subscribe((params: ParamMap) => {
            this.tab = params.get('tab');
            this.currentTabKey = this.tab as ProfileTabKey;
        });
    }

    title(key?: string): string {
        if (!key || key.length == 0) return profilePageRouteTitle[this.currentTabKey];

        return profilePageRouteTitle[key];
    }

    async logout() {
        const logoutResult = await firstValueFrom(this.userService.doLogout());
        window.location.href = '/'; // go back to home page
    }
}
