import { NgModule } from '@angular/core';
import { Async<PERSON>ip<PERSON>, CommonModule, NgOptimizedImage } from '@angular/common';
import { RouterLinkWithHref, RouterModule, RouterOutlet, Routes } from '@angular/router';
import { ProfileHeadInfoComponent } from './profile-head-info/profile-head-info.component';
import { ProfilePageComponent } from './profile-page.component';
import { ProfileInfoComponent } from './profile-info/profile-info.component';
import { ProfileSecurityComponent } from './profile-security/profile-security.component';
import { ProfileSettingsComponent } from './profile-settings/profile-settings.component';
import { ProfileDocumentsComponent } from './profile-documents/profile-documents.component';
import { StillEditWarningComponent } from './still-edit-warning/still-edit-warning.component';
import { MenuComponent } from '../menu/menu.component';
import { MenuSwitchComponent } from '../menu-switch/menu-switch.component';
import { OverlayModule } from '@angular/cdk/overlay';
import { ProfileHeadMenuComponent } from './profile-head-menu/profile-head-menu.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { ProfileUnlinkSocialComponent } from './profile-unlink-social/profile-unlink-social.component';
import {
    AddressServiceModule,
    CropperComponent,
    DateRangePickerComponent,
    FileStoreServiceModule,
    FormUtilModule,
    NotificationModule,
    SpinnerLabelComponent,
    NewDateTimePickerComponent,
    DocumentService,
    FooterComponent,
} from '@viclass/portal.common';
import { MatCardModule } from '@angular/material/card';
import { ProfileClassroomsComponent } from './profile-classrooms/profile-classrooms.component';
import { ProfileDocumentItemComponent } from './profile-documents/profile-document-item/profile-document-item.component';

export const routes: Routes = [
    {
        path: '',
        redirectTo: 'info',
        pathMatch: 'full',
    },
    {
        path: ':tab',
        component: ProfilePageComponent,
    },
];

@NgModule({
    declarations: [
        ProfileHeadInfoComponent,
        ProfilePageComponent,
        ProfileInfoComponent,
        ProfileSecurityComponent,
        ProfileSettingsComponent,
        ProfileHeadMenuComponent,
    ],
    imports: [
        AsyncPipe,
        RouterLinkWithHref,
        RouterOutlet,
        CommonModule,
        RouterModule.forChild(routes),
        MenuComponent,
        MenuSwitchComponent,
        OverlayModule,
        FormsModule,
        ReactiveFormsModule,
        StillEditWarningComponent,
        ProfileUnlinkSocialComponent,
        MatDialogModule,
        CropperComponent,
        NgOptimizedImage,
        FileStoreServiceModule,
        SpinnerLabelComponent,
        ReactiveFormsModule,
        FormUtilModule,
        MatCardModule,
        NotificationModule,
        ProfileClassroomsComponent,
        ProfileDocumentsComponent,
        ProfileDocumentItemComponent,
        AddressServiceModule,
        DateRangePickerComponent,
        NewDateTimePickerComponent,
        FooterComponent,
    ],
    providers: [
        MenuComponent,
        StillEditWarningComponent,
        ProfileUnlinkSocialComponent,
        MatDialog,
        DateRangePickerComponent,
        DocumentService,
    ],
})
export class ProfilePageModule {}
