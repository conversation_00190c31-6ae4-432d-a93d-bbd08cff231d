<div>
    <div
        *ngIf="successMessage$ | async as successMessage"
        id="notification"
        class="fixed left-0 right-0 top-[100px] flex items-center justify-center">
        <div
            class="bg-blue-200 py-[5px] px-[10px] rounded shadow-SH1 whitespace-nowrap border-2 border-sky-500 flex items-center justify-center gap-[10px] max-w-[500px]">
            <span class="vcon-general vcon_verified-sticker"
                ><span class="path1"> </span> <span class="path2"> </span>
            </span>
            {{ successMessage }}
        </div>
    </div>
    <div class="flex justify-center items-center" *>
        <div class="border-[1px] border-SC5 bg-PAS1 rounded-[5px] py-[5px] px-[10px] text-SC5 flex items-center">
            <span class="vcon-general vcon_error mr-[10px]"></span>
            loginError
        </div>
    </div>
    <div *ngIf="hasLoadingError$ | async">
        <div class="text-red-500 text-center">! Không thể tải thông tin, vui lòng thử lại</div>
    </div>
    <div *ngIf="loading$ | async">
        <div class="flex justify-center items-center" [spinner]="loading$"></div>
    </div>
    <div *ngIf="!(loading$ | async) && !(hasLoadingError$ | async)">
        <div *ngIf="hasEmailPswdReg$ | async; else noEmailReg">
            <form
                class="vi-form"
                *fflow="let fum; by: buildForm; fflow as f; submit: submitChangePassword; noNav: true"
                [formGroup]="fum"
                [ferrcoord]="f">
                <div class="bg-white p-8 rounded-lg dark:border-gray-700 shadow-SH1">
                    <table class="w-full max-w-3xl [&>tr>td:first-child]:w-[275px]">
                        <tr>
                            <td class="border-r border-gray-400">
                                <div class="flex items-center gap-1 py-3">
                                    <span class="vcon-general vcon_user"></span>
                                    <span class="text-gray-700">Tên đăng nhập</span>
                                </div>
                            </td>
                            <td class="pl-4">
                                <b
                                    ><span>{{ (emailRegInfo$ | async)?.username || 'Đang tải' }}</span></b
                                >
                            </td>
                        </tr>
                        <tr *ngIf="!(hasEmailPswdReg$ | async); else changePswdForm">
                            <td class="border-r border-gray-400">
                                <div class="flex items-center gap-1 py-3">
                                    <span class="vcon-general vcon_general_password"></span>
                                    <span class="text-gray-700">Mật khẩu</span>
                                </div>
                            </td>
                            <td class="pl-4">
                                <span class="font-thin italic text-gray-400">-</span>
                            </td>
                        </tr>
                        <ng-template #changePswdForm>
                            <tr>
                                <td class="border-r border-gray-400 align-top">
                                    <div class="flex items-center gap-1 py-3">
                                        <span class="vcon-general vcon_general_password"></span>
                                        <span class="text-gray-700">Mật khẩu</span>
                                    </div>
                                </td>
                                <td class="pl-4 py-1">
                                    <div *ngIf="isEdit$ | async; else dummyPassword" class="flex flex-col gap-2">
                                        <div class="relative">
                                            <i
                                                class="absolute right-2 top-2 vcon-general vcon_general_preview_view append pointer"
                                                *ngIf="oldPwInput.type === 'password'"
                                                (click)="oldPwInput.type = 'text'"></i>
                                            <i
                                                class="absolute right-2 top-2 vcon-general vcon_general_preview_hide append pointer"
                                                *ngIf="oldPwInput.type === 'text'"
                                                (click)="oldPwInput.type = 'password'"></i>
                                            <input
                                                formControlName="password"
                                                placeholder="Mật khẩu hiện tại"
                                                type="password"
                                                #oldPwInput
                                                [(ferror)]="passwordError"
                                                class="w-full rounded-xl border border-gray-400 py-1 px-3" />
                                        </div>
                                        <span class="vi-text-error block" *ngIf="passwordError"
                                            >! {{ passwordError.msg }}</span
                                        >
                                        <div class="relative">
                                            <i
                                                class="absolute right-2 top-2 vcon-general vcon_general_preview_view append pointer"
                                                *ngIf="newPwInput.type === 'password'"
                                                (click)="newPwInput.type = 'text'"></i>
                                            <i
                                                class="absolute right-2 top-2 vcon-general vcon_general_preview_hide append pointer"
                                                *ngIf="newPwInput.type === 'text'"
                                                (click)="newPwInput.type = 'password'"></i>
                                            <input
                                                formControlName="newPassword"
                                                placeholder="Mật khẩu mới"
                                                type="password"
                                                #newPwInput
                                                [(ferror)]="newPasswordError"
                                                class="w-full rounded-xl border border-gray-400 py-1 px-3" />
                                        </div>
                                        <span class="vi-text-error block" *ngIf="newPasswordError"
                                            >! {{ newPasswordError.msg }}</span
                                        >
                                        <div class="relative">
                                            <i
                                                class="absolute right-2 top-2 vcon-general vcon_general_preview_view append pointer"
                                                *ngIf="retypePwInput.type === 'password'"
                                                (click)="retypePwInput.type = 'text'"></i>
                                            <i
                                                class="absolute right-2 top-2 vcon-general vcon_general_preview_hide append pointer"
                                                *ngIf="retypePwInput.type === 'text'"
                                                (click)="retypePwInput.type = 'password'"></i>
                                            <input
                                                formControlName="retypePassword"
                                                placeholder="Nhập lại mật khẩu"
                                                type="password"
                                                #retypePwInput
                                                [(ferror)]="retypePasswordError"
                                                class="w-full rounded-xl border border-gray-400 py-1 px-3" />
                                        </div>
                                        <span class="vi-text-error block" *ngIf="retypePasswordError"
                                            >! {{ retypePasswordError.msg }}</span
                                        >
                                    </div>

                                    <ng-template #dummyPassword>
                                        <input input type="password" value="********" disabled class="bg-transparent" />
                                    </ng-template>
                                </td>
                            </tr>
                        </ng-template>
                    </table>
                    <span class="vi-text-error block mt-3" *ngIf="formError$ | async"
                        >! {{ (formError$ | async).msg }}</span
                    >
                </div>

                <div class="w-full flex items-center justify-center mt-4 gap-6">
                    <button
                        *ngIf="!(isEdit$ | async); else formBtns"
                        type="button"
                        (click)="toggleFormEdit()"
                        [disabled]="!(hasEmailPswdReg$ | async)"
                        class="vi-btn vi-btn-normal vi-btn-outline">
                        Cài lại mật khẩu
                    </button>

                    <ng-template #formBtns>
                        <button
                            type="button"
                            class="vi-btn vi-btn-normal vi-btn-focus"
                            [disabled]="!f.canSubmit()"
                            [form-flow-submit]="f">
                            Lưu
                        </button>
                        <button type="button" (click)="toggleFormEdit()" class="vi-btn vi-btn-normal vi-btn-outline">
                            Hủy
                        </button>
                    </ng-template>
                </div>
            </form>
        </div>

        <ng-template #noEmailReg>
            <div
                class="text-center flex justify-center items-center gap-2 bg-white p-8 rounded-lg dark:border-gray-700 shadow-SH1"
                *ngIf="linkedRegistrations$ | async as linkedRegistrations">
                Tài khoản được đăng ký bằng mạng xã hội
                <img
                    *ngIf="linkedRegistrations.includes('FACEBOOK')"
                    src="assets/img/facebook.svg"
                    class="w-8 h-8 rounded-circle" />
                <img
                    *ngIf="linkedRegistrations.includes('GOOGLE')"
                    src="assets/img/google.svg"
                    class="w-8 h-8 rounded-circle" />
            </div>
        </ng-template>
    </div>
</div>
