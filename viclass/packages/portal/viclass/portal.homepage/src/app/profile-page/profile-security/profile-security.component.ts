import { HttpErrorResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';

import { AbstractControl, FormGroup, UntypedFormBuilder, ValidationErrors, Validators } from '@angular/forms';
import {
    ChangePasswordData,
    EmailRegInfoResponse,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    NotificationService,
    passwordValidator,
    UserService,
} from '@viclass/portal.common';
import { HttpStatusCode } from 'axios';
import { initFlowbite } from 'flowbite';
import { BehaviorSubject, firstValueFrom, map } from 'rxjs';

/**
 * Validator fow new password and retype password must match
 */
const matchPasswords = (control: AbstractControl): ValidationErrors | null => {
    const passwordField = control.parent?.get('newPassword')?.value;
    const otherPasswordField = control.value;

    if (!passwordField || !otherPasswordField) {
        return null; // No error if fields are empty
    }

    return passwordField === otherPasswordField ? null : { passwordMismatch: true };
};

/**
 * <PERSON>ida<PERSON> fow new password should not match the old password
 */
const useDifferentPassword = (control: AbstractControl): ValidationErrors | null => {
    const oldPasswordField = control.parent?.get('password')?.value;
    const newPasswordField = control.value;

    if (!oldPasswordField || !newPasswordField) {
        return null; // No error if fields are empty
    }

    return oldPasswordField === newPasswordField ? { useDifferentPassword: true } : null;
};

type ChangePasswordFormData = ChangePasswordData & { retypePassword: string };

@Component({
    selector: 'profile-security',
    templateUrl: './profile-security.component.html',
    styleUrls: ['./profile-security.component.scss'],
})
export class ProfileSecurityComponent implements OnInit {
    showNotification = false;
    passwordError: ErrorModel = null;
    newPasswordError: ErrorModel = null;
    retypePasswordError: ErrorModel = null;

    form: FormGroup;

    formError$: BehaviorSubject<ErrorModel> = new BehaviorSubject(null);

    readonly isEdit$ = new BehaviorSubject<boolean>(false);
    readonly hasLoadingError$ = new BehaviorSubject<boolean>(false);

    readonly linkedRegistrations$ = new BehaviorSubject<string[]>([]);
    readonly loading$ = this.linkedRegistrations$.pipe(map(regs => regs.length === 0));
    readonly hasEmailPswdReg$ = this.linkedRegistrations$.pipe(map(regs => regs.includes('EMAIL')));
    readonly emailRegInfo$ = new BehaviorSubject<EmailRegInfoResponse | null>(null);

    successMessage$ = new BehaviorSubject<String>(null);

    constructor(
        private fb: UntypedFormBuilder,
        protected userService: UserService,
        private notificationService: NotificationService
    ) {}

    async ngOnInit(): Promise<void> {
        initFlowbite();

        this.loadLinkedRegistrations();
        this.loadEmailRegInfo();
    }

    /**
     * Build the change password form
     */
    buildForm = (data?: ChangePasswordFormData): FormBuildingResult => {
        data = data || {
            password: null,
            newPassword: '',
            retypePassword: '',
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                password: [Validators.required],
                newPassword: [Validators.required, passwordValidator, useDifferentPassword],
                retypePassword: [Validators.required, matchPasswords],
            })
            .validatorMessages({
                password: {
                    required: 'Mật khẩu là bắt buộc',
                },
                newPassword: {
                    required: 'Mật khẩu là bắt buộc',
                    minlength: 'Độ dài tối thiểu 6 ký tự',
                    maxlength: 'Độ dài tối đa 20 ký tự',
                    uppercaseRequired: 'Phải có ít nhất một chữ hoa',
                    lowercaseRequired: 'Phải có ít nhất một chữ thường',
                    numberRequired: 'Phải có ít nhất một số',
                    specialCharacterRequired: 'Phải có ít nhất một ký tự đặc biệt (@, #, $, ...)',
                    noSpaces: 'Mật khẩu không được chứa khoảng trắng',
                    invalidPassword: 'Mật khẩu không đúng định dạng',
                    useDifferentPassword: 'Mật khẩu mới trùng với mật khẩu hiện tại',
                },
                retypePassword: {
                    required: 'Xác thực mật khẩu là bắt buộc',
                    passwordMismatch: 'Xác thực mật khẩu không khớp',
                },
            })
            .build();

        this.form = result.control as FormGroup;

        this.form.get('newPassword')?.valueChanges.subscribe(() => {
            this.form.get('retypePassword')?.updateValueAndValidity();
        });

        this.isEdit$.subscribe(value => {
            if (value) {
                this.form.setErrors(null);
            }
        });

        return result;
    };

    /**
     * Submit change password form
     */
    submitChangePassword = async (data: FormFlowSubmitEvent) => {
        try {
            const { retypePassword, ...formData } = data.data as ChangePasswordFormData;
            await firstValueFrom(this.userService.changePassword(formData));

            this.toggleFormEdit();

            this.notificationService.showNotification({
                message: 'Thay đổi mật khẩu thành công.',
                status: 'success',
            });
            this.formError$.next(null);
        } catch (err) {
            let msg = 'Lỗi không xác định';
            if (err.status == 401) {
                msg = 'Mật khẩu hiện tại không đúng';
            } else if (err.status == 500) {
                msg = 'Đổi mật khẩu thất bại';
            }

            this.formError$.next({
                key: 'changePasswordFail',
                msg,
            });

            firstValueFrom(this.form.valueChanges).then(() => {
                this.formError$.next(null);
            });
            return;
        }
    };

    /**
     * Toggle form edit mode
     */
    toggleFormEdit = () => {
        const nextEditValue = !this.isEdit$.value;
        this.isEdit$.next(nextEditValue);

        if (nextEditValue) {
            this.form?.reset({
                password: null,
                newPassword: '',
                retypePassword: '',
            });
        }
    };

    /**
     * Only the user with email-password registration can change password
     */
    private async loadLinkedRegistrations() {
        try {
            const regTypes = await firstValueFrom(this.userService.linkedRegistrations());
            this.linkedRegistrations$.next(regTypes);
        } catch (err) {
            console.error(err);
            this.hasLoadingError$.next(true);
        }
    }

    /**
     * Load email-password registration info, include the username for login
     */
    private async loadEmailRegInfo() {
        try {
            const user = await firstValueFrom(this.userService.getEmailRegInfo());
            this.emailRegInfo$.next(user);
        } catch (err) {
            if (err instanceof HttpErrorResponse && err.status === HttpStatusCode.NotFound) {
                // ignore, expected
            } else {
                console.error(err);
                this.hasLoadingError$.next(true);
            }
        }
    }
}
