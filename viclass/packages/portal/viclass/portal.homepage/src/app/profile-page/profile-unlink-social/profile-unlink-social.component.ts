import { ChangeDetectionStrategy, Component, Inject, Input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Social } from '@viclass/portal.common';

@Component({
    selector: 'profile-unlink-social',
    templateUrl: './profile-unlink-social.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [MatButtonModule],
})
export class ProfileUnlinkSocialComponent {
    socialNameMapping: { [key in Social]: string };
    socialImageNameMapping: { [key in Social]: string };
    social: Social;

    constructor(
        public dialogRef: MatDialogRef<ProfileUnlinkSocialComponent>,
        @Inject(MAT_DIALOG_DATA)
        public data: {
            social: Social;
        }
    ) {
        this.social = data.social;
        this.socialNameMapping = {
            [Social.FACEBOOK]: 'Facebook',
            [Social.GOOGLE]: 'Google',
        };
        this.socialImageNameMapping = {
            [Social.FACEBOOK]: 'facebook-full.png',
            [Social.GOOGLE]: 'google-full.png',
        };
    }

    close(confirmed: boolean) {
        this.dialogRef.close({ confirmed });
    }
}
