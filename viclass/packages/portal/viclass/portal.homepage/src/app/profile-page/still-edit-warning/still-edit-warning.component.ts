import { ChangeDetectionStrategy, Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
    selector: 'still-edit-warning',
    templateUrl: './still-edit-warning.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [MatButtonModule],
})
export class StillEditWarningComponent {
    constructor(public dialogRef: MatDialogRef<StillEditWarningComponent>) {}

    close() {
        this.dialogRef.close({ stillEdit: true });
    }
    closeWithDisableEdit() {
        this.dialogRef.close({ stillEdit: false });
    }
}
