<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/register-animation.svg">
            <img src="assets/img/register-animation.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[400px] w-[290px] mx-auto">
            <div class="text-[36px]"><strong>Gửi thành công!</strong></div>
            <div class="flex flex-col gap-1 items-center">
                <div class="flex-[1_0_80px]">
                    <img src="assets/img/tick-resgistration-completion.svg" alt="" />
                </div>
                <div>
                    <div *ngIf="registrations$ | async as regTypes">
                        <div *ngIf="regTypes.length === 1">
                            <div class="text-[14px] mt-[15px]">Bạn đã đăng ký tài khoản thành công</div>
                        </div>

                        <div *ngIf="regTypes.length > 1">
                            <div *ngIf="isGoogleRegistered && !isFacebookRegistered" class="text-[14px] mt-[15px]">
                                Email này đã được đăng ký đồng thời thông qua Google mail.
                            </div>

                            <div *ngIf="!isGoogleRegistered && isFacebookRegistered" class="text-[14px] mt-[15px]">
                                Email này đã được đăng ký đồng thời thông qua Facebook
                            </div>

                            <div *ngIf="isGoogleRegistered && isFacebookRegistered" class="text-[14px] mt-[15px]">
                                Email này đã được đăng ký đồng thời thông qua Google mail and Facebook.
                            </div>

                            <div class="text-[14px] mt-[15px]">Chúng tôi sẽ ghi nhận như một tài khoản duy nhất.</div>
                            <div class="flex mt-[10px]">
                                <div class="flex justify-center" *ngFor="let regType of regTypes; let i = index">
                                    <div class="w-[40px] h-[40px]" *ngIf="regType === 'FACEBOOK'">
                                        <img src="assets/img/social-facebook.svg" />
                                    </div>

                                    <div class="w-[40px] h-[40px]" *ngIf="regType === 'GOOGLE'">
                                        <img src="assets/img/social-gg.svg" />
                                    </div>

                                    <div class="w-[40px] h-[40px]" *ngIf="regType === 'EMAIL'">
                                        <img src="assets/img/email.svg" />
                                    </div>

                                    <div
                                        class="w-[40px] h-[40px] flex justify-center items-center"
                                        *ngIf="i < regTypes.length - 1">
                                        <img src="assets/img/plus.svg" />
                                    </div>
                                </div>
                                <div class="w-[40px] h-[40px] flex justify-center items-center ml-[10px]">
                                    <img src="assets/img/social-link.svg" />
                                </div>
                            </div>

                            <div
                                *ngIf="isGoogleRegistered && !isFacebookRegistered"
                                class="text-[14px] mt-[15px] mb-[15px]">
                                Bạn có thể đăng nhập bằng cách truyền thống, thông qua Google Mail.
                            </div>

                            <div
                                *ngIf="!isGoogleRegistered && isFacebookRegistered"
                                class="text-[14px] mt-[15px] mb-[15px]">
                                Bạn có thể đăng nhập bằng cách truyền thống, thông qua Facebook.
                            </div>

                            <div
                                *ngIf="isGoogleRegistered && isFacebookRegistered"
                                class="text-[14px] mt-[15px] mb-[15px]">
                                Bạn có thể đăng nhập bằng cách truyền thống, thông qua Google mail and Facebook.
                            </div>
                        </div>
                    </div>
                    <div class="text-[14px]">
                        Tiếp tục
                        <a href="{{ rURL }}" class="text-P1">{{ actionName }}</a>
                        sau
                        <countdown [fromTime]="15" (action)="countDownAction()"></countdown>
                        giây.
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
