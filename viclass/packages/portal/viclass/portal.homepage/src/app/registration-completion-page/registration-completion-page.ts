import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { decodeReturnUrlFromParams, FormUtilModule, UserService } from '@viclass/portal.common';
import { CountDownComponent } from 'packages/portal/viclass/portal.common/src/lib/countdown';
import { BehaviorSubject, firstValueFrom } from 'rxjs';

@Component({
    selector: 'app-registration-completion-page',
    standalone: true,
    imports: [RouterModule, FormUtilModule, ReactiveFormsModule, CommonModule, CountDownComponent],
    templateUrl: './registration-completion-page.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RegistrationCompletionPageComponent implements OnInit {
    rURL: string;
    actionName: string;

    readonly registrations$ = new BehaviorSubject<Array<any>>([]);

    get isGoogleRegistered(): boolean {
        return this.registrations$.value.indexOf('GOOGLE') != -1;
    }

    get isFacebookRegistered(): boolean {
        return this.registrations$.value.indexOf('FACEBOOK') != -1;
    }

    constructor(
        private route: ActivatedRoute,
        private userService: UserService
    ) {}

    /**
     * Redirect to the return URL when the count down reach 0.
     * To be triggered from the UI
     */
    countDownAction() {
        window.location.href = this.rURL || '/';
    }

    /**
     * @inheritdoc
     */
    ngOnInit() {
        this.getLinkedRegistrations();
        this.prepareReturnUrl();
    }

    /**
     * Fetch the linked registrations to show the social accounts
     * that linked with this email-password registration
     */
    private async getLinkedRegistrations() {
        const linkedRegs = await firstValueFrom(this.userService.linkedRegistrations());
        this.registrations$.next(linkedRegs);
    }

    /**
     * Prepare the return URL and the associated action name from the query parameters.
     * Example: return to `/classroom/create` with the action "tạo lớp học"
     */
    private async prepareReturnUrl() {
        const params = await firstValueFrom(this.route.queryParams);
        const data = decodeReturnUrlFromParams(params);
        if (data) {
            this.rURL = data?.rURL;
            this.actionName = data?.actionName;
        }

        if (!this.rURL) {
            this.rURL = '/';
            this.actionName = 'quay lại trang chủ';
        }
    }
}
