<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/register-animation.svg">
            <img src="assets/img/register-animation.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[370px] w-[290px] mx-auto">
            <div class="text-[36px]"><strong>Đ<PERSON>ng ký</strong></div>
            <ng-template [ngIf]="(userService.curUser$ | async) !== null" [ngIfElse]="hasNoUser">
                <div class="mt-15">
                    <span class="vi-text-error block"
                        >! Bạn đã đăng nhập! <PERSON><PERSON>y đăng xuất trước khi đăng ký một tài khoản mới!</span
                    >
                </div>
            </ng-template>
            <ng-template #hasNoUser>
                <div class="text-[14px] mt-[15px] mb-[30px]">
                    Bạn đã có tài khoản?
                    <a class="text-P1" routerLink="/login" [queryParams]="{ _rURL: _rURL || undefined }">Đăng nhập</a>
                </div>
                <form
                    class="vi-form"
                    *fflow="let fum; by: buildForm; fflow as f; submit: submitRegistration; noNav: true"
                    [formGroup]="fum"
                    [ferrcoord]="f"
                    [(ferror)]="passwordRetypeError">
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_footer_email prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon"
                                formControlName="email"
                                placeholder="Email của bạn"
                                [(ferror)]="emailError" />
                        </div>
                        <span class="vi-text-error block" *ngIf="emailError">! {{ emailError.msg }}</span>
                    </div>
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_user prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon"
                                formControlName="username"
                                placeholder="Tên đăng nhập"
                                [(ferror)]="usernameError" />
                        </div>
                        <span class="vi-text-error block" *ngIf="usernameError">! {{ usernameError.msg }}</span>
                    </div>
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_general_password prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon append-icon"
                                type="password"
                                #pwinput
                                formControlName="password"
                                placeholder="Mật khẩu"
                                [(ferror)]="passwordError" />
                            <i
                                class="vcon-general vcon_general_preview_view append-icon cursor-pointer"
                                *ngIf="pwinput.type === 'password'"
                                (click)="pwinput.type = 'text'"></i>
                            <i
                                class="vcon-general vcon_general_preview_hide append-icon cursor-pointer"
                                *ngIf="pwinput.type === 'text'"
                                (click)="pwinput.type = 'password'"></i>
                        </div>
                        <span class="vi-text-error block" *ngIf="passwordError">! {{ passwordError.msg }}</span>
                    </div>
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_general_password prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon append-icon"
                                type="password"
                                #retypePw
                                formControlName="passwordRetype"
                                placeholder="Nhập lại mật khẩu"
                                [(ferror)]="passwordRetypeError1" />
                            <i
                                class="vcon-general vcon_general_preview_view append-icon cursor-pointer"
                                *ngIf="retypePw.type === 'password'"
                                (click)="retypePw.type = 'text'"></i>
                            <i
                                class="vcon-general vcon_general_preview_hide append-icon cursor-pointer"
                                *ngIf="retypePw.type === 'text'"
                                (click)="retypePw.type = 'password'"></i>
                        </div>
                        <span class="vi-text-error block" *ngIf="passwordRetypeError"
                            >! {{ passwordRetypeError.msg }}</span
                        >
                        <span class="vi-text-error block" *ngIf="passwordRetypeError1"
                            >! {{ passwordRetypeError1.msg }}</span
                        >
                    </div>
                    <div class="text-[14px] text-justify">
                        Tôi đã đọc và đồng ý với các
                        <a class="text-P1 text-[14px]" href="/terms-and-conditions" target="_blank">
                            Điều khoản dịch vụ
                        </a>
                        và
                        <a class="text-P1 text-[14px]" href="/terms-and-conditions#privacy-policy" target="_blank">
                            Chính sách bảo mật
                        </a>
                    </div>
                    <div *ngIf="sitekey | async as sitekeyRes">
                        <ng-hcaptcha #hcaptchaElement formControlName="captcha" [siteKey]="sitekeyRes"> </ng-hcaptcha>
                    </div>
                    <span class="vi-text-error block mt-3" *ngIf="registrationError$ | async as registrationError"
                        >! {{ registrationError.msg }}</span
                    >
                    <button
                        class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
                        [disabled]="!f.canSubmit() || (registering$ | async)"
                        [form-flow-submit]="f"
                        [spinner]="registering$">
                        Đăng ký
                    </button>
                </form>
                <div class="flex mt-[50px] items-center">
                    <hr class="grow opacity-100 border-BW1" />
                    <div class="text-[14px] grow-0 mx-3">hoặc đăng nhập với</div>
                    <hr class="grow opacity-100 border-BW1" />
                </div>
                <app-social-login-buttons (loginError)="onSocialRegistrationError($event)"></app-social-login-buttons>
            </ng-template>
        </div>
    </div>
</section>
