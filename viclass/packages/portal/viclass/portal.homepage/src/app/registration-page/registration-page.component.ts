import { CommonModule } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, NO_ERRORS_SCHEMA, OnDestroy, OnInit, ViewChild } from '@angular/core';
import {
    AbstractControl,
    AsyncValidatorFn,
    ReactiveFormsModule,
    UntypedFormBuilder,
    UntypedFormGroup,
    ValidationErrors,
    ValidatorFn,
    Validators,
} from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import {
    CaptchaSiteKey,
    decodeReturnUrlFromParams,
    emailValidator,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    passwordValidator,
    PKEY_RURL,
    ProcessingRequestManager,
    RegistrationData,
    SpinnerLabelComponent,
    usernameValidator,
    UserService,
    CommonModule as viCommon,
} from '@viclass/portal.common';
import { NgHcaptchaComponent, NgHcaptchaModule } from 'ng-hcaptcha';
import {
    BehaviorSubject,
    catchError,
    debounceTime,
    firstValueFrom,
    map,
    Observable,
    of,
    Subject,
    switchMap,
    take,
    tap,
} from 'rxjs';
import { AppStateService } from '../app.state.service';
import { MetaService } from '../meta.service';
import { SocialLoginButtonsComponent } from '../social-login-buttons/social-login-buttons.component';

@Component({
    standalone: true,
    imports: [
        RouterModule,
        FormUtilModule,
        ReactiveFormsModule,
        CommonModule,
        viCommon,
        SpinnerLabelComponent,
        NgHcaptchaModule,
        SocialLoginButtonsComponent,
    ],
    selector: 'app-registration-page',
    templateUrl: './registration-page.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    schemas: [NO_ERRORS_SCHEMA],
})
export class RegistrationPageComponent implements OnInit, OnDestroy {
    _rURL: string;
    form: UntypedFormGroup;
    // FormErrors
    formError: ErrorModel;
    usernameError: ErrorModel;
    passwordError: ErrorModel;
    passwordRetypeError: ErrorModel;
    passwordRetypeError1: ErrorModel;
    emailError: ErrorModel;

    registrationError$ = new BehaviorSubject<ErrorModel>(null);

    sitekey: Observable<string | undefined> = this.userService
        .siteKeyOfCaptcha()
        .pipe(map((result: CaptchaSiteKey) => result?.sitekey));

    @ViewChild('hcaptchaElement') hcaptchaElement: NgHcaptchaComponent;

    usernameValidationSubject = new Subject<string>();
    usernameDebounce = this.usernameValidationSubject.pipe(
        map(s => {
            return s;
        }),
        debounceTime(500),
        switchMap(s => {
            return this.validateExistingUsername(s);
        })
    );

    constructor(
        public appState: AppStateService,
        private fb: UntypedFormBuilder,
        public userService: UserService,
        private prm: ProcessingRequestManager,
        private router: Router,
        private route: ActivatedRoute,
        private ms: MetaService
    ) {}

    async ngOnInit() {
        this.ms.initMetaPage();
        this.ms.setTitle('Viclass - Đăng ký');
        this.ms.setDescription('Đăng ký tài khoản Viclass để học và chia sẻ kiến thức với cộng đồng.');

        const params = await firstValueFrom(this.route.queryParams);

        this._rURL = params?.[PKEY_RURL] ? params[PKEY_RURL] : '';
        this.userService.curUser$
            .pipe(
                take(1),
                tap(user => {
                    if (user) window.location.href = decodeReturnUrlFromParams(params)?.rURL || '/';
                })
            )
            .subscribe();
    }

    submitRegistration = async (data: FormFlowSubmitEvent) => {
        this.hcaptchaElement.reset();
        try {
            const response = await firstValueFrom(
                this.prm.monitor('registering', this.userService.register(data.data))
            );

            if (response.status == HttpStatusCode.Ok) {
                const userId = response.body.userId;

                var params = await firstValueFrom(this.route.queryParams);
                if (params && params[PKEY_RURL])
                    this.router.navigate(['/registration', 'verify', userId], {
                        queryParams: {
                            _rURL: params[PKEY_RURL],
                        },
                    });
                else this.router.navigate(['/registration', 'verify', userId]);
            }
        } catch (e) {
            console.error(e);
            this.setRegistrationError({
                key: 'registrationFailed',
                msg: 'Đăng ký thất bại, xin vui lòng thử lại',
            });
        }
    };

    buildForm = (data?: RegistrationData): FormBuildingResult => {
        data = data || {
            username: null,
            password: null,
            passwordRetype: null,
            email: null,
            captcha: null,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                __self__: [this.retypePasswordValidation()],
                __fields__: {
                    username: [
                        Validators.required,
                        Validators.minLength(4),
                        Validators.maxLength(30),
                        usernameValidator,
                    ],
                    password: [Validators.required, passwordValidator],
                    passwordRetype: [Validators.required],
                    email: [emailValidator, Validators.required],
                },
            })
            .validatorMessages({
                __self__: {
                    pwdNotMatch: 'Xác thực mật khẩu không khớp',
                },
                __fields__: {
                    username: {
                        required: 'Trường này bắt buộc',
                        minlength: 'Tên đăng nhập phải có ít nhất 4 ký tự.',
                        maxlength: 'Tên đăng nhập không được vượt quá 30 ký tự.',
                        mustStartWithLetter: 'Tên đăng nhập phải bắt đầu bằng một chữ cái.',
                        invalidCharacters:
                            'Tên đăng nhập chỉ có thể chứa chữ cái, số, dấu chấm (.) và dấu gạch dưới (_).',
                        userExist: 'Tên đăng nhập đã tồn tại',
                    },
                    password: {
                        required: 'Mật khẩu là bắt buộc',
                        minlength: 'Độ dài tối thiểu 6 ký tự',
                        maxlength: 'Độ dài tối đa 20 ký tự',
                        uppercaseRequired: 'Phải có ít nhất một chữ hoa',
                        lowercaseRequired: 'Phải có ít nhất một chữ thường',
                        numberRequired: 'Phải có ít nhất một số',
                        specialCharacterRequired: 'Phải có ít nhất một ký tự đặc biệt (@, #, $, ...)',
                        noSpaces: 'Mật khẩu không được chứa khoảng trắng',
                        invalidPassword: 'Mật khẩu không đúng định dạng',
                    },
                    passwordRetype: {
                        required: 'Trường này bắt buộc',
                        passwordMismatch: 'Xác thực mật khẩu không khớp',
                    },
                    email: {
                        required: 'Trường này bắt buộc',
                        invalidEmail: 'Email không đúng định dạng',
                        consecutiveDots: 'Email không được chứa dấu chấm liên tiếp.',
                        invalidTld: 'Tên miền email không hợp lệ.',
                        invalidCharacters:
                            'Email chỉ được chứa chữ cái, số, dấu chấm (.), gạch ngang (-) và gạch dưới (_).',
                        emailExist: 'Email đã được đăng ký',
                        emailExistCheckFailed:
                            'Không kiểm tra được email của bạn. Bạn hãy thử lại hoặc liên hệ vỡi hỗ trợ',
                    },
                },
            })
            .asyncValidators({
                username: [this.conflictUsername()],
                email: [this.conflictEmail()],
            })
            .build();

        this.form = result.control as UntypedFormGroup;

        return result;
    };

    validateExistingUsername(s: string): Observable<ValidationErrors> {
        return this.userService.checkUserExist(s).pipe(
            map(exist => {
                if (!exist) return null;
                else return { userExist: true };
            }),
            catchError(err => {
                return of({ userExistCheckFailed: true });
            })
        );
    }

    conflictUsername(): AsyncValidatorFn {
        return (c: AbstractControl): Observable<ValidationErrors> =>
            of(c.value).pipe(
                debounceTime(1000),
                switchMap(s => this.validateExistingUsername(s))
            );
    }

    validateExistingEmail(s: string): Observable<ValidationErrors> {
        return this.userService.checkEmailExist('EMAIL', s).pipe(
            map(exist => {
                if (!exist) return null;
                else return { emailExist: true };
            }),
            catchError(err => {
                return of({ emailExistCheckFailed: true });
            })
        );
    }

    conflictEmail(): AsyncValidatorFn {
        return (c: AbstractControl): Observable<ValidationErrors> =>
            of(c.value).pipe(
                debounceTime(1000),
                switchMap(s => this.validateExistingEmail(s))
            );
    }

    retypePasswordValidation(): ValidatorFn {
        return (c: AbstractControl) => {
            if (this.form) {
                const pwc = this.form.get('password');
                const rpwc = this.form.get('passwordRetype');
                if (pwc?.value?.length > 0 && rpwc?.value?.length > 0 && pwc.value != rpwc.value) {
                    return { pwdNotMatch: true };
                }
            }

            return null;
        };
    }

    get registering$(): Observable<boolean> {
        return this.prm.getInprogressObs('registering');
    }

    ngOnDestroy(): void {
        this.prm.clearIndividual('registering');
    }

    onSocialRegistrationError(error: ErrorModel) {
        this.setRegistrationError(error);
    }

    setRegistrationError(error: ErrorModel) {
        if (!error) return;
        this.registrationError$.next(error);

        firstValueFrom(this.form.valueChanges).then(() => {
            this.registrationError$.next(null);
        });
    }
}
