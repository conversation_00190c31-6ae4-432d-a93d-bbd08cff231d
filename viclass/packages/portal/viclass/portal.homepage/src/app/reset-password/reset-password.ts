import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';

import {
    AbstractControl,
    FormGroup,
    ReactiveFormsModule,
    UntypedFormBuilder,
    ValidatorFn,
    Validators,
} from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import {
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    ProcessingRequestManager,
    ResetPasswordData,
    SpinnerLabelComponent,
    UserService,
    passwordValidator,
} from '@viclass/portal.common';
import { BehaviorSubject, Observable, firstValueFrom } from 'rxjs';

@Component({
    selector: 'app-reset-password-page',
    standalone: true,
    imports: [CommonModule, RouterModule, ReactiveFormsModule, FormUtilModule, SpinnerLabelComponent],
    templateUrl: './reset-password.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResetPasswordPageComponent implements OnInit {
    usernameError: ErrorModel;
    passwordError: ErrorModel;
    passwordRetypeError: ErrorModel;
    passwordRetypeError1: ErrorModel;
    formError: ErrorModel;

    isResetPasswordSuccess$ = new BehaviorSubject<boolean>(false);

    isjwtTokenInvalid$ = new BehaviorSubject<boolean>(false);

    form: FormGroup;
    resetPasswordError$: BehaviorSubject<ErrorModel> = new BehaviorSubject(null);
    constructor(
        private prm: ProcessingRequestManager,
        private fb: UntypedFormBuilder,
        private userService: UserService,
        private route: ActivatedRoute
    ) {}
    async ngOnInit() {
        try {
            const params = await firstValueFrom(this.route.params);
            const jwtToken = params['token'];

            await firstValueFrom(
                this.prm.monitor('restPasswordIn', this.userService.verifyResetPasswordToken(jwtToken))
            );
        } catch (err) {
            this.isjwtTokenInvalid$.next(true);
            return;
        }
    }

    buildForm = (data?: ResetPasswordData): FormBuildingResult => {
        data = data || {
            password: null,
            passwordRetype: null,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                __self__: [this.retypePasswordValidation()],
                __fields__: {
                    password: [Validators.required, passwordValidator],
                    passwordRetype: [Validators.required],
                },
            })
            .validatorMessages({
                __self__: {
                    pwdNotMatch: 'Xác thực mật khẩu không khớp',
                },
                __fields__: {
                    password: {
                        required: 'Mật khẩu là bắt buộc',
                        minlength: 'Độ dài tối thiểu 6 ký tự',
                        maxlength: 'Độ dài tối đa 20 ký tự',
                        uppercaseRequired: 'Phải có ít nhất một chữ hoa',
                        lowercaseRequired: 'Phải có ít nhất một chữ thường',
                        numberRequired: 'Phải có ít nhất một số',
                        specialCharacterRequired: 'Phải có ít nhất một ký tự đặc biệt (@, #, $, ...)',
                        noSpaces: 'Mật khẩu không được chứa khoảng trắng',
                        invalidPassword: 'Mật khẩu không đúng định dạng',
                    },
                    passwordRetype: {
                        required: 'Bạn cần nhập xác thực mật khẩu',
                    },
                },
            })
            .build();

        this.form = result.control as FormGroup;

        return result;
    };

    retypePasswordValidation(): ValidatorFn {
        return (c: AbstractControl) => {
            if (this.form) {
                const pwc = this.form.get('password');
                const rpwc = this.form.get('passwordRetype');

                if (pwc?.value?.length > 0 && rpwc?.value?.length > 0 && pwc.value != rpwc.value) {
                    return { pwdNotMatch: true };
                }
            }

            return null;
        };
    }

    submitResetPassword = async (data: FormFlowSubmitEvent) => {
        try {
            const params = await firstValueFrom(this.route.params);
            const jwtToken = params['token'];
            const dataRes = {
                jwtToken: jwtToken,
                newPassword: data.data.password,
            };

            await firstValueFrom(this.prm.monitor('restPasswordIn', this.userService.resetPassword(dataRes)));

            this.isResetPasswordSuccess$.next(true);
        } catch (err) {
            this.resetPasswordError$.next({
                key: 'forgotPwFailed',
                msg: 'Có lỗi khi đổi mật khẩu, vui lòng thử lại',
            });
            firstValueFrom(this.form.valueChanges).then(() => {
                this.resetPasswordError$.next(null);
            });

            return;
        }
    };

    get loggingIn$(): Observable<boolean> {
        return this.prm.getInprogressObs('loggingIn');
    }
}
