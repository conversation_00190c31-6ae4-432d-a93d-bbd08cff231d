import { CommonModule } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
    ErrorModel,
    PKEY_RURL,
    SocialLoginButtonComponent,
    SocialLoginModule,
    SocialLoginService,
    SocialUser,
    UserService,
} from '@viclass/portal.common';
import { firstValueFrom } from 'rxjs';
import { environment } from '../../environments/environment';

@Component({
    selector: 'app-social-login-buttons',
    standalone: true,
    imports: [CommonModule, SocialLoginModule, SocialLoginButtonComponent],
    templateUrl: './social-login-buttons.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialLoginButtonsComponent implements OnInit {
    @Output() loginError = new EventEmitter<ErrorModel>();

    constructor(
        private socialLoginService: SocialLoginService,
        private userService: UserService,
        private route: ActivatedRoute
    ) {}

    ngOnInit(): void {
        this.socialLoginService.socialUser$.subscribe(user => {
            if (user) {
                this.loginWithSocialAccount(user);
            }
        });
    }

    async loginWithSocialAccount(user: SocialUser) {
        var params = await firstValueFrom(this.route.queryParams);
        const rUrlValue = params?.[PKEY_RURL];

        try {
            const result = await firstValueFrom(this.userService.doSocialLogin(user));
            this.redirectToOriginalAction(rUrlValue);
        } catch (err) {
            console.error(`[${user.provider}] Social login API error: `, err);

            if (err.status == HttpStatusCode.Found) {
                // need redirect to do something (ex: verification, add email, notify account changes...)
                window.location.href = err.error.rURL + (rUrlValue ? `?${PKEY_RURL}=${rUrlValue}` : '');
            } else {
                this.loginError.emit({
                    key: 'loginFailed',
                    msg: 'Lỗi hệ thống khi đăng nhập. Vui lòng thử lại! ',
                });
            }
        }
    }

    private redirectToOriginalAction(rUrlValue: string) {
        if (rUrlValue) {
            window.location.href = JSON.parse(window.atob(decodeURIComponent(rUrlValue))).rURL;
        } else {
            window.location.href = environment.authflowConfig.defaultReturnUrl;
        }
    }
}
