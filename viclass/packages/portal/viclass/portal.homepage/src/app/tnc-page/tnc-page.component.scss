::ng-deep .body-for-tnc-page .home-layout {
    @apply bg-white #{!important};
}

::ng-deep .body-for-tnc-page .home-layout > div {
    @apply text-BW1 lg:text-inherit #{!important};
}

::ng-deep .body-for-tnc-page .topbar-menu-item {
    @apply hover:text-BW1 #{!important};
}

::ng-deep .body-for-tnc-page .active-page {
    @apply text-BW1 #{!important};
}

::ng-deep .body-for-tnc-page .registration-btn-hover:hover {
    @apply text-BW1;
}

.mat-accordion > .mat-expansion-panel-spacing {
    // @apply pt-[50px];
}

.topbar-menu-item {
    @apply text-BW3 hover:text-BW1 font-semibold uppercase leading-[25.6px] text-base;
}

.container {
    max-width: 900px;
    margin: auto;
    padding: 16px;

    a {
        color: #007bff;
        text-decoration: none;
        &:hover {
            text-decoration: underline;
        }
    }
}
.content {
    padding: 10px 20px;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}
.text-center {
    text-align: center;
}
.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}
