import { DOCUMENT, ViewportScroller } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MatExpansionPanel, MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { FooterComponent } from '@viclass/portal.common';

@Component({
    standalone: true,
    selector: 'app-tnc-page',
    templateUrl: './tnc-page.component.html',
    styleUrls: ['./tnc-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MatExpansionModule, MatIconModule, FooterComponent],
})
export class TncPageComponent implements AfterViewInit {
    document = inject(DOCUMENT);
    @ViewChild('privacyPolicy') privacyPolicyPanel!: MatExpansionPanel;
    @ViewChild('dataDeletion') dataDeletionPanel!: MatExpansionPanel;

    constructor(private route: ActivatedRoute) {}

    ngAfterViewInit(): void {
        this.route.fragment.subscribe(fragment => {
            setTimeout(() => {
                if (fragment === 'privacy-policy') {
                    this.privacyPolicyPanel.open();
                } else if (fragment === 'deletion-instructions') {
                    this.dataDeletionPanel.open();
                }
            }, 300);
        });
        this.document.body.classList.add('body-for-tnc-page');
    }
}
