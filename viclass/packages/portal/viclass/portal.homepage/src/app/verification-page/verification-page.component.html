<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/register-animation.svg">
            <img src="assets/img/register-animation.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[370px] w-[290px] mx-auto">
            <div class="text-[36px]"><strong>Đ<PERSON>ng ký</strong></div>
            <div class="text-[14px] mt-[15px] mb-[30px]">Mã xác nhận đã đượ<PERSON> gửi qua email đăng ký.</div>
            <div class="text-[14px] mt-[15px] mb-[30px]">
                Vui lòng kiểm tra email và điền mã xác nhận để hoàn tất đăng ký!
            </div>
            <form
                class="vi-form"
                *fflow="let fum; by: buildForm; fflow as f; submit: submitVerification; noNav: true"
                [formGroup]="fum"
                [ferrcoord]="f">
                <div class="mb-[30px]">
                    <div class="vi-icon-input">
                        <input
                            class="vi-input input-lg p-[10px] text-[24px] font-bold"
                            formControlName="verificationCode"
                            placeholder="Mã xác nhận"
                            [(ferror)]="verificationCodeError"
                            toUppercase />
                    </div>
                    <span class="vi-text-error block" *ngIf="verificationCodeError"
                        >! {{ verificationCodeError.msg }}</span
                    >
                </div>
                <div class="text-[14px] mt-[15px] mb-[30px]">
                    Bạn chưa nhận được email?
                    <button
                        *ngIf="isAllowSend$ | async; else disableSend"
                        type="button"
                        class="text-P1"
                        (click)="sendVerificationEmail()">
                        Gửi lại
                    </button>
                    <ng-template #disableSend>
                        <button class="text-SC1" [disabled]="true">
                            Gửi lại sau
                            <countdown [fromTime]="remainTime$ | async" (action)="countDownAction()"></countdown>
                        </button>
                    </ng-template>
                </div>
                <span class="vi-text-error block mt-3" *ngIf="verificationError$ | async as verificationError"
                    >! {{ verificationError.msg }}</span
                >
                <button
                    class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
                    [disabled]="!f.canSubmit() || (verifying$ | async)"
                    [form-flow-submit]="f"
                    [spinner]="verifying$">
                    Hoàn tất
                </button>
            </form>
        </div>
    </div>
</section>
