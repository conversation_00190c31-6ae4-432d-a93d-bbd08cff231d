import { CommonModule } from '@angular/common';
import { HttpStatusCode } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import {
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    PKEY_RURL,
    ProcessingRequestManager,
    SendVerificationResult,
    SpinnerLabelComponent,
    UserService,
    VerificationData,
} from '@viclass/portal.common';
import { CountDownComponent } from 'packages/portal/viclass/portal.common/src/lib/countdown';
import { BehaviorSubject, Observable, firstValueFrom, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { AppStateService } from '../app.state.service';
import { InputUppercaseDirective } from '../input-uppercase.directive';

@Component({
    selector: 'app-verification-page',
    standalone: true,
    imports: [
        RouterModule,
        FormUtilModule,
        ReactiveFormsModule,
        CommonModule,
        SpinnerLabelComponent,
        InputUppercaseDirective,
        CountDownComponent,
    ],
    templateUrl: './verification-page.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VerificationPageComponent implements OnInit, OnDestroy {
    registrationId: string;
    form: UntypedFormGroup;
    verificationCodeError: ErrorModel;

    verificationError$ = new BehaviorSubject<ErrorModel>(null);

    readonly sendResult$ = new BehaviorSubject<SendVerificationResult>(null);

    readonly isAllowSend$ = new BehaviorSubject<boolean>(false);

    readonly remainTime$ = new BehaviorSubject<number>(null);

    get verifying$(): Observable<boolean> {
        return this.prm.getInprogressObs('verifying');
    }

    constructor(
        public appState: AppStateService,
        private fb: UntypedFormBuilder,
        private userService: UserService,
        private prm: ProcessingRequestManager,
        private route: ActivatedRoute
    ) {}

    ngOnInit(): void {
        this.registrationId = this.route.snapshot.paramMap.get('registrationId');

        this.sendVerificationEmail();
    }

    ngOnDestroy(): void {
        this.prm.clearIndividual('verifying');
    }

    buildForm = (data?: VerificationData): FormBuildingResult => {
        data = data || {
            verificationCode: '',
            registrationId: this.registrationId,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                __fields__: {
                    verificationCode: [Validators.required],
                },
            })
            .validatorMessages({
                __fields__: {
                    verificationCode: {
                        required: 'Mã xác nhận là bắt buộc',
                    },
                },
            })
            .build();

        this.form = result.control as UntypedFormGroup;
        return result;
    };

    submitVerification = async (data: FormFlowSubmitEvent) => {
        this.resetVerificationError();
        try {
            const response = await firstValueFrom(
                this.prm.monitor(
                    'verifying',
                    this.userService.verify({
                        verificationCode: data.data.verificationCode.trim().toUpperCase(),
                        registrationId: this.registrationId,
                    })
                )
            );
            if (response.status == HttpStatusCode.Ok) {
                var params = await firstValueFrom(this.route.queryParams);

                const rUrl = params?.[PKEY_RURL];
                window.location.href = '/registration-completion' + (rUrl ? `?${PKEY_RURL}=${rUrl}` : '');
            }
        } catch (e) {
            console.error(e);
            this.verificationError$.next({
                key: 'verificationFailed',
                msg: 'Mã xác nhận không đúng hoặc đã hết hạn, xin vui lòng thử lại hoặc gửi mã mới.',
            });
        }
    };

    /**
     *  Try send a verification email. If the time from last sent is less than 3 minutes,
     *  the server will not actually send the email and only return the lastSent epoch time
     */
    sendVerificationEmail = async () => {
        this.resetVerificationError();

        try {
            await firstValueFrom(
                this.userService.sendVerification(this.registrationId).pipe(
                    tap(result => {
                        const sendResult = result;
                        if (!sendResult) return this.remainTime$.next(0);

                        const threeMinutesFromLastSent = new Date(
                            (sendResult.lastSent + environment.verificationEmailResendCooldownSecs) * 1000
                        );

                        const newCountDown = Math.floor((threeMinutesFromLastSent.getTime() - Date.now()) / 1000);
                        if (newCountDown > 0) {
                            this.isAllowSend$.next(false);
                            this.remainTime$.next(newCountDown);
                        } else {
                            this.isAllowSend$.next(true);
                            this.remainTime$.next(0);
                        }
                    })
                )
            );
        } catch (e) {
            console.warn(e);
            if (
                e.status == HttpStatusCode.NotFound &&
                e.error?.backendErrorCode == 5 // NOT_FOUND code of grpc
            ) {
                // registration ID not found or already verified
                this.verificationError$.next({
                    key: 'registrationNotFound',
                    msg: 'Không tìm thấy tài khoản hoặc tài khoản đã được xác nhận',
                });
            } else {
                this.verificationError$.next({
                    key: 'sendEmailFailed',
                    msg: 'Không thể gửi email, xin vui lòng thử lại.',
                });
                this.isAllowSend$.next(true);
            }
        }
    };

    countDownAction = () => {
        this.isAllowSend$.next(true);
    };

    private resetVerificationError = () => {
        this.verificationError$.next(null);
    };
}
