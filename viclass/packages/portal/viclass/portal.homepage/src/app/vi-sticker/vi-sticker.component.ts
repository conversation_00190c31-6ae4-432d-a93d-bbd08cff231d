import { Component, Input } from '@angular/core';

@Component({
    selector: 'vi-sticker',
    template: `<div class="h-[24px] flex flex-row rounded-[5px_0_0_5px] overflow-hidden">
        <div class="w-max h-full text-[14px] leading-[21px] p-[2px_0_2px_5px]" [style.background-color]="color">
            <ng-content></ng-content>
        </div>
        <div class="h-full w-[11px] [clip-path:polygon(0_0,0_100%,100%_50%)]" [style.background-color]="color"></div>
    </div> `,
    standalone: true,
    imports: [],
})
export class ViStickerComponent {
    @Input() color: string;
}
