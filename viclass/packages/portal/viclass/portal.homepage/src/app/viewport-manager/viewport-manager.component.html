<div class="relative w-full h-full">
    <div
        class="viewport-root"
        [ngClass]="{
            'no-ed-toolbar': !showEditorUi,
            'no-zoom-toolbar': !uiModules.zoomToolUIConf,
        }">
        <div class="board-viewport w-full h-full" [ngClass]="{ viewportClasses: true }" #viewportEl></div>
    </div>
    <div *ngIf="viewport" #viewportToolbarEl>
        <editor-ui-group
            class="editor-ui-group editor-ui-group-vertical absolute top-left-side-tools"
            [ngClass]="{ '!hidden': !showEditorUi }"
            vAlign="top"
            hAlign="left"
            direction="ttb"
            (loaderEvent)="onEditorUILoaderEvent($event, 'tlt-toolbar')">
        </editor-ui-group>
    </div>
    <editor-ui-group
        *ngIf="viewport && uiModules.zoomToolUIConf"
        class="editor-ui-group absolute zoom-bar"
        vAlign="bottom"
        hAlign="right"
        direction="ltr"
        (loaderEvent)="onEditorUILoaderEvent($event, 'brl-toolbar')"
        [ngStyle]="{
            'max-width': 'calc(100% - 131px)',
        }">
    </editor-ui-group>

    <!-- The viewport tool will be inserted here if needed -->
    <ng-template [ngIf]="vpToolBar">
        <div class="vptools-bar v-toolbar-group absolute bottom right ltr rounded-[20px] overflow-hidden">
            <div class="v-toolbar">
                <div class="v-tool-group ltr gap-[4px]" [ngClass]="{ '!bg-transparent !shadow-none': !vpToolBg }">
                    <ng-template [ngIf]="allowReset">
                        <button #resetBtn [ngClass]="{ '!m-0': !vpToolBg }" class="v-tool-btn" (click)="onReset()">
                            <span class="vcon vcon-common vcon_refresh"></span>
                        </button>
                        <lib-tooltip [toolTipFor]="resetBtn" tooltipContent="Đặt lại tài liệu"></lib-tooltip>
                    </ng-template>
                    <button #copyBtn class="v-tool-btn" [ngClass]="{ '!m-0': !vpToolBg }" (click)="onCopy()">
                        <img src="assets/img/copy.svg" class="w-[20px] h-[20px] rounded-circle" />
                    </button>
                    <ng-template [ngIf]="allowShare">
                        <button
                            #shareBtn
                            class="v-tool-btn vi-btn-gradient"
                            [ngClass]="{ '!m-0': !vpToolBg }"
                            (click)="onCmd.emit({ type: 'share', cmp: self })">
                            <span class="vcon vcon-common vcon_page-bar_share"></span>
                        </button>
                        <lib-tooltip [toolTipFor]="shareBtn" tooltipContent="Chia sẻ"></lib-tooltip>
                    </ng-template>
                    <ng-template [ngIf]="allowInfoToggle">
                        <button
                            #infoBtn
                            class="v-tool-btn"
                            [ngClass]="{
                                '!m-0': !vpToolBg,
                                '!bg-P2': showInfo | async,
                            }"
                            (click)="onCmd.emit({ type: 'info-toggle', cmp: self })">
                            <div class="rounded-full w-5 h-5 bg-black">
                                <span class="text-white">i</span>
                            </div>
                        </button>
                        <lib-tooltip [toolTipFor]="infoBtn" tooltipContent="Thông tin"></lib-tooltip>
                    </ng-template>
                </div>
            </div>
        </div>
    </ng-template>

    <ng-template #confirmDeletePopup>
        <div class="vi-popup-container">
            <div class="pt-[15px] pb-[15px] gap-[10px] d-flex flex-col">
                <div class="flex justify-center">
                    <img class="w-[160px] h-[100px]" ngSrc="assets/img/recycle-bin.svg" height="100" width="160" />
                </div>
                <div class="vi-popup-message text-center">
                    <div>Tài liệu hiện tại sẽ bị xóa</div>
                    <div>và không thể khôi phục</div>
                </div>
                <div class="d-flex flex-row gap-[10px] text-center justify-center">
                    <button class="vi-btn vi-btn-small vi-btn-focus" [mat-dialog-close]="true">Xác nhận</button>
                    <button class="vi-btn vi-btn-small vi-btn-outline" [mat-dialog-close]="false">Hủy</button>
                </div>
            </div>
        </div>
    </ng-template>

    <div class="absolute top-[30px] left-[50%] z-[101]">
        <awareness
            *ngIf="viewport && coord"
            [vpId]="viewport.id"
            [coord]="coord"
            class="relative top-0 left-[calc(-50%+20px)]"></awareness>
    </div>
</div>
