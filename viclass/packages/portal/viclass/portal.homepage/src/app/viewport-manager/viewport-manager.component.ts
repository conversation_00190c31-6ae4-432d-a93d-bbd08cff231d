import { CommonModule, NgOptimizedImage } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
    TemplateRef,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { EmbedCoordinator } from '@viclass/editor.coordinator/embed';
import {
    CopyPasteTool,
    EditorType,
    FEATURE_ZOOM,
    OperationMode,
    ViewportManager,
    ViewportMode,
} from '@viclass/editor.core';
import { EditorUILoaderEvent, EditorUILoaderModule } from '@viclass/editorui.loader';
import { NotificationService, TooltipComponent, ViewportUIModuleConfig } from '@viclass/portal.common';
import { Observable } from 'rxjs';
import { AwarenessComponent } from '../awareness/awareness.component';

// command that the user can perform on the viewport toolbar on homepage
export type VpCmdType = 'share' | 'reset' | 'info-toggle' | 'viewport-ready';
export type VpCmd = {
    type: VpCmdType;
    cmp: ViewportManagerComponent;
};

/**
 * A component that when created will create a viewport and load the necessary UI as well as editors
 * to be used on that viewport.
 * using the coordinator available in the document service.
 */
@Component({
    standalone: true,
    selector: '[app-viewport-manager]',
    templateUrl: './viewport-manager.component.html',
    imports: [
        EditorUILoaderModule,
        CommonModule,
        MatDialogModule,
        NgOptimizedImage,
        AwarenessComponent,
        TooltipComponent,
    ],
    providers: [],
    encapsulation: ViewEncapsulation.None,
})
export class ViewportManagerComponent implements OnDestroy, AfterViewInit {
    @Input() homepageViewportId: number;
    @Input() edType: EditorType;
    @Input() vpToolBar = true;
    @Input() showEditorUi = true; // whether to show the editor UI or not, default is true
    @Input() allowReset = true;
    @Input() allowShare = true;
    @Input() allowInfoToggle = true;
    @Input() coord: EmbedCoordinator;
    @Input() uiModules: ViewportUIModuleConfig;
    @Input() vpToolBg = true;
    @Input() viewportClasses: string = '';
    @Input() showInfo: Observable<boolean>;
    @Input() docName?: string = undefined;

    @Output() onCmd = new EventEmitter<VpCmd>();
    @Output() onLoaded = new EventEmitter<void>();

    @ViewChild('viewportEl') vpEl: ElementRef<HTMLDivElement>;
    @ViewChild('viewportToolbarEl') vpToolbarEl: ElementRef<HTMLElement>;
    @ViewChild('confirmDeletePopup', { read: TemplateRef })
    confirmDeletePopupRef: TemplateRef<any>;

    protected self = this;
    public viewport: ViewportManager = null;
    loadedFinished = 0;

    @Input() docId?: string;

    constructor(
        private dialog: MatDialog,
        private cdr: ChangeDetectorRef,
        private notification: NotificationService
    ) {}

    ngAfterViewInit() {
        this.viewport = this.coord.createViewport(this.vpEl.nativeElement, this.edType);
        setTimeout(() => this.cdr.markForCheck());
    }

    async use(vpMode: ViewportMode = 'InteractiveMode') {
        await this.coord.focusViewport(this.viewport);
        this.coord.focusEditor(this.edType, this.viewport);
        await this.coord.switchViewportMode(this.viewport.id, vpMode);
        this.coord.selectDoc(this.viewport, this.edType, this.docId);
        this.coord.ham.em.captureEventsFor(this.vpToolbarEl.nativeElement);
    }

    stopUsing() {
        if (this.viewport.mode !== 'Disabled') this.coord.switchViewportMode(this.viewport.id, 'Disabled');
        this.coord.ham.em.uncaptureEventsFor(this.vpToolbarEl.nativeElement);
    }

    async ngOnDestroy() {
        const coord = this.coord;

        if (coord?.curFocusVm && this.viewport === coord.curFocusVm) coord.removeViewport(this.viewport.id);
    }

    async onEditorUILoaderEvent(event: EditorUILoaderEvent, toolbarId: string) {
        const coord = this.coord;

        switch (event.eventType) {
            case 'loader-initialized': {
                switch (toolbarId) {
                    case 'tlt-toolbar': {
                        // loaded once for both tlt and brl
                        await event.source.loadBaseTheme(this.uiModules.baseStyleModuleConf);

                        if (this.uiModules.editorUIConf)
                            await event.source.createUI([this.uiModules.commonToolUIConf, this.uiModules.editorUIConf]);
                        else await event.source.createUI([this.uiModules.commonToolUIConf]);

                        break;
                    }
                    case 'brl-toolbar':
                        if (this.uiModules.zoomToolUIConf) await event.source.createUI([this.uiModules.zoomToolUIConf]);
                        break;
                }

                break;
            }
            case 'ui-loaded': {
                const edType = event.state as EditorType;
                const loadedUI = event.source.getUI(edType);
                const tb = ['CommonToolsEditor', 'ZoomToolsEditor'].includes(edType)
                    ? coord.getCommonToolbar(this.viewport.id)
                    : coord.getEditorToolbar(this.viewport.id, edType);
                loadedUI.connectToolbar(tb);
                event.source.showUI(edType);
                break;
            }
            case 'all-ui-loaded': {
                this.loadedFinished++;

                const noZoomUi = !this.uiModules.zoomToolUIConf;
                // when all editor ui group is loaded then this viewport manager component is ready to perform task!
                if ((noZoomUi && this.loadedFinished === 1) || (!noZoomUi && this.loadedFinished === 2))
                    this.onCmd.emit({ type: 'viewport-ready', cmp: this });

                this.onLoaded.emit();
                break;
            }
        }
    }

    async onReset() {
        const dialogRef = this.dialog.open(this.confirmDeletePopupRef);

        dialogRef.afterClosed().subscribe((result?: boolean) => {
            if (result) {
                this.onCmd.emit({ type: 'reset', cmp: this });
            }
        });
    }

    async onCopy() {
        try {
            const copyTool = this.coord.getCommonToolbar(this.viewport.id).getTool('copypaste') as CopyPasteTool;
            const editor = this.coord.editorByType(this.edType);
            const doc = editor.findDocumentByGlobalId(this.viewport.id, this.docId);
            const res = await copyTool.copy(this.viewport.id, {
                type: 'documents',
                vpId: this.viewport.id,
                documents: [
                    {
                        editorType: this.edType as EditorType,
                        docGlobalId: this.docId,
                        docLocalId: 1,
                        layers: copyTool.extractDocLayersInfo(doc),
                        localContent:
                            editor.operationMode === OperationMode.LOCAL
                                ? editor.getLocalContent(this.viewport.id, doc.state.id)
                                : undefined,
                        docName: this.docName,
                    },
                ],
            });

            if (res) {
                this.notification.showNotification({
                    status: 'success',
                    message: 'Sao chép tài liệu thành công',
                });
            } else {
                this.notification.showNotification({
                    status: 'error',
                    message: 'Không thể sao chép tài liệu này',
                });
            }
        } catch (e) {
            console.error(e);
            this.notification.showNotification({
                status: 'error',
                message: 'Sao chép tài liệu thất bại',
            });
        }
    }

    get isEditorSupportZoomFeature(): boolean {
        return this.coord.editorByType(this.edType).isSupportFeature(FEATURE_ZOOM);
    }
}
