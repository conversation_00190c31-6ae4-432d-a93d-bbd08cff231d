@font-face {
    font-family: 'viclass-common-floating-menu';
    src:
        url('fonts/viclass-common-floating-menu.ttf?1xvwku') format('truetype'),
        url('fonts/viclass-common-floating-menu.woff?1xvwku') format('woff'),
        url('fonts/viclass-common-floating-menu.svg?1xvwku#viclass-common-floating-menu') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vcon-common {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'viclass-common-floating-menu' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vcon_zoom-to-fit:before {
    content: '\e967d';
}
.vcon_fixed-dimention:before {
    content: '\e518';
}
.vcon_mini-spinner:before {
    content: '\e521';
}
.vcon_refresh:before {
    content: '\e519';
}
.vcon_page-bar_share:before {
    content: '\e939e';
}
.vcon_embed:before {
    content: '\e032';
}
.vcon_emty:before {
    content: '\e901';
}
.vcon_error:before {
    content: '\e907b';
    color: #ff002e;
}
.vcon_align_bottom:before {
    content: '\e931a';
}
.vcon_align_hor-center:before {
    content: '\e931b';
}
.vcon_align_left:before {
    content: '\e931c';
}
.vcon_align_right:before {
    content: '\e931d';
}
.vcon_align_top:before {
    content: '\e931e';
}
.vcon_align_ver-center:before {
    content: '\e931f';
}
.vcon_logo-icon:before {
    content: '\e941';
}
.vcon_vertical-scrolling:before {
    content: '\e001';
}
.vcon_move:before {
    content: '\e943d';
}
.vcon_hand-tool:before {
    content: '\e943c';
}
.vcon_undo:before {
    content: '\e943a';
}
.vcon_redo:before {
    content: '\e943b';
}
.vcon_document_freedrawing:before {
    content: '\e910a';
}
.vcon_document_geometry:before {
    content: '\e910d';
}
.vcon_document_word:before {
    content: '\e910b';
}
.vcon_document_mathtype:before {
    content: '\e910e';
}
.vcon_document_magh:before {
    content: '\e568';
}
.vcon_page-bar_zoom-in-area:before {
    content: '\e967a';
}
.vcon_page-bar_zoom-out-area:before {
    content: '\e967c';
}
.vcon_page-bar_ad:before {
    content: '\e938';
}
.vcon_page-bar_zoom-out:before {
    content: '\e967b';
}
