import { HttpClientModule } from '@angular/common/http';
import { enableProdMode, EnvironmentProviders, importProvidersFrom, Provider } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import {
    AUTH_FLOW_CONFIG,
    CommonErrorHandlerModule,
    ENVIRONMENT,
    FileStoreServiceModule,
    UserServiceModule,
} from '@viclass/portal.common';
import { AppRoutingModule } from './app/app-routing.module';
import { AppComponent } from './app/app.component';
import { environment } from './environments/environment';

export const commonProviders: (Provider | EnvironmentProviders)[] = [
    importProvidersFrom(AppRoutingModule),
    importProvidersFrom(HttpClientModule),
    importProvidersFrom(NoopAnimationsModule),
    importProvidersFrom(UserServiceModule),
    importProvidersFrom(FileStoreServiceModule),
    importProvidersFrom(CommonErrorHandlerModule),
    { provide: AUTH_FLOW_CONFIG, useValue: environment.authflowConfig },
    { provide: ENVIRONMENT, useValue: environment },
];

export const bootstrapCommon = () => {
    if (environment.production) enableProdMode();

    return bootstrapApplication(AppComponent, {
        providers: commonProviders,
    }).catch(err => console.error(err));
};
