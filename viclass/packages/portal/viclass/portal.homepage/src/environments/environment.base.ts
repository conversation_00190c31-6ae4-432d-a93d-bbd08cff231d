export type EnvironmentType = {
    production: boolean;
    confEnv: string; // the environment key to use with configuration service
    apiUrl: string;
    authflowConfig: {
        defaultReturnUrl: string;
    };
    verificationEmailResendCooldownSecs: number;
    domain: string;
    enableBeta: boolean;
};

export function envBase(domain: string): EnvironmentType {
    const base = {
        production: false,
        confEnv: 'dev', // the environment key to use with configuration service
        apiUrl: '/api/jsRoutes',
        authflowConfig: {
            defaultReturnUrl: '/',
        },
        verificationEmailResendCooldownSecs: 180,
        domain: domain,
        enableBeta: true,
    };

    return base;
}
