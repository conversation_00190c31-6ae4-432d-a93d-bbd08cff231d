import { enableProdMode, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { bootstrapApplication, BrowserModule, provideClientHydration } from '@angular/platform-browser';
import { provideServerRendering, ServerModule } from '@angular/platform-server';
import '@viclass/ww/mfeRT';
import 'zone.js';
import { AppComponent } from '../app/app.component';
import { commonProviders } from '../bootstrap.common';
import { environment } from '../environments/environment';

const bootstrap = () => {
    if (environment.production) {
        enableProdMode();
    }

    return bootstrapApplication(AppComponent, {
        providers: [
            provideZoneChangeDetection({ eventCoalescing: true }),
            importProvidersFrom(BrowserModule),
            importProvidersFrom(ServerModule),
            provideServerRendering(),
            provideClientHydration(),
            ...commonProviders,
        ],
    }).catch(err => console.error(err));
};

export default bootstrap;
