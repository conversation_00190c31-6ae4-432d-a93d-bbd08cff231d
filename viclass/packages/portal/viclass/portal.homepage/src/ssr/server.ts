import { HTTP_INTERCEPTORS, <PERSON>ttpE<PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { enableProdMode } from '@angular/core';
import { ngExpressEngine } from '@nguniversal/express-engine';
import { SSR_REQUEST_COOKIE_TOKEN, SSR_REQUEST_FORWARDED_HOST } from '@viclass/portal.common';
import express from 'express';
import { existsSync } from 'node:fs';
import { dirname, join, resolve } from 'node:path';
import { Observable } from 'rxjs';
import XMLHttpRequest from 'xhr2';
import 'zone.js/node';
import { environment } from '../environments/environment';
import { bootstrap } from '../main.server';

// XMLHttpRequest is used by the Universal Express Engine to make HTTP requests.
(global as any).XMLHttpRequest = XMLHttpRequest;

// allow cookies and host headers to be sent in the HttpRequest
XMLHttpRequest.prototype._restrictedHeaders = {
    ...XMLHttpRequest.prototype._restrictedHeaders,
    cookie: false,
    host: false,
};

// This interceptor is used to add the request cookies and forwarded host to the HTTP requests
class CommonSSRHttpInterceptor implements HttpInterceptor {
    constructor(
        private readonly requestCookie: string,
        private readonly requestForwardedHost: string
    ) {}

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        let modifiedReq = req;
        if (this.requestCookie) modifiedReq = modifiedReq.clone({ setHeaders: { Cookie: this.requestCookie } });

        // If the request URL starts with '/' and requestForwardedHost is available,
        // clone the request and update the URL to include the forwarded host
        if (req.url.startsWith('/') && this.requestForwardedHost)
            modifiedReq = modifiedReq.clone({ url: `https://${this.requestForwardedHost}${req.url}` });

        return next.handle(modifiedReq);
    }
}

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
    if (environment.production) enableProdMode();

    const server = express();
    const serverDistFolder = dirname(__filename);
    const browserDistFolder = resolve(serverDistFolder, '../portal.homepage');
    const indexHtml = existsSync(join(browserDistFolder, 'index.original.html'))
        ? join(browserDistFolder, 'index.original.html')
        : join(browserDistFolder, 'index.html');

    // Our Universal express-engine (found @ https://github.com/angular/universal/tree/main/modules/express-engine)
    server.engine(
        'html',
        ngExpressEngine({
            bootstrap: bootstrap as any,
        })
    );

    server.set('view engine', 'html');
    server.set('views', browserDistFolder);

    // Example Express Rest API endpoints
    // server.get('/api/**', (req, res) => { });
    // Serve static files from /browser
    server.get(
        '*.*',
        express.static(browserDistFolder, {
            maxAge: '1d',
        })
    );

    // Serve the index.html for all other routes to support Angular's client-side routing.
    server.get('*', (req, res) => {
        // Extract the forwarded host and cookies from the request headers.

        let forwardedHost: string = '';
        if (req.headers['x-forwarded-host'] instanceof Array) forwardedHost = req.headers['x-forwarded-host'][0];
        else if (req.headers['x-forwarded-host']) forwardedHost = req.headers['x-forwarded-host'];

        const cookie = req.headers.cookie || '';

        // Render the index.html with the request and response objects,
        // and provide the forwarded host and cookies as providers.
        res.render(indexHtml, {
            req,
            res,
            providers: [
                { provide: SSR_REQUEST_FORWARDED_HOST, useValue: forwardedHost },
                { provide: SSR_REQUEST_COOKIE_TOKEN, useValue: cookie },
                {
                    provide: HTTP_INTERCEPTORS,
                    useValue: new CommonSSRHttpInterceptor(cookie, forwardedHost),
                    multi: true,
                },
            ],
        });
    });

    return server;
}

function run(): void {
    const port = process.env['PORT'] || 4000;

    // Start up the Node server
    const server = app();
    server.listen(port, () => {
        console.log(`Node Express server listening...`);
    });
}

// Webpack will replace 'require' with '__webpack_require__'
// '__non_webpack_require__' is a proxy to Node 'require'
// The below code is to ensure that the server is run only when not requiring the bundle.
declare const __non_webpack_require__: NodeRequire;
const mainModule = __non_webpack_require__.main;
const moduleFilename = (mainModule && mainModule.filename) || '';
if (moduleFilename === __filename || moduleFilename.includes('iisnode')) {
    run();
}

export default bootstrap;
