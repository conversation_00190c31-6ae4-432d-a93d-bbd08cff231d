/* You can add global styles to this file, and also import other style files */
@use '@viclass/portal.common/common_v3';

@use '@viclass/themes/common';
@use '@viclass/themes/button';
@use '@viclass/themes/form';

@import 'assets/icons/style.css';
@import '@angular/material/prebuilt-themes/indigo-pink.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
    @apply text-[16px] leading-[1.5];
}

.active-page {
    @apply border-b-P1 border-b-[5px] text-BW7;

    a {
        @apply mt-[5px];
    }
}

.section-margin {
    @apply px-[15px] md:px-[27px] lg:px-[55px] xl:px-[80px] 2xl:px-[200px];
}

.section-content {
    @apply lg:w-[1170px] md:w-[970px] mx-auto;
}

/* menu icon */

.hamburger-menu {
    .hamburger-icon {
        @apply bg-BW1;
        display: block;
        height: 2px;
        position: relative;
        width: 18px;
    }

    .hamburger-icon:before,
    .hamburger-icon:after {
        @apply bg-BW1;
        content: '';
        display: block;
        height: 100%;
        position: absolute;
        width: 100%;
        transition: all 0.2s ease-out;
    }

    .hamburger-icon:before {
        top: 5px;
    }

    .hamburger-icon:after {
        top: -5px;
    }

    /* menu btn */

    .hamburger-btn {
        display: none;
    }

    .hamburger-btn:checked {
        ~ label .hamburger-icon {
            background: transparent;

            &:before {
                transform: rotate(-45deg);
                top: 0;
            }

            &:after {
                transform: rotate(45deg);
                top: 0;
            }
        }
    }
}

[app-ed-switch] {
    .active-switch {
        .switch-icon {
            @apply bg-P1 text-BW1;
        }
    }

    .switch-icon {
        @apply bg-none text-BW7;

        &::before {
            font-size: 30px;
        }
    }
}

.scrolled {
    .active-page {
        @apply text-BW1;
    }
}

.board-viewport {
    position: relative;
    background-color: #fff;
    z-index: 1;
}

.viewport-container {
    overflow: hidden;

    &.show {
        display: block;
    }

    &.hide {
        display: none;
    }

    @keyframes viewport {
        0% {
            transform: translateX(0px);
            visibility: visible;
            opacity: 1;
        }

        99% {
            transform: translateX(100%);
            visibility: visible;
            opacity: 0;
        }

        100% {
            visibility: hidden;
            transform: translateX(100%);
        }
    }

    @keyframes viewport1 {
        0% {
            transform: translateX(0px);
            visibility: visible;
            opacity: 1;
        }

        99% {
            transform: translateX(100%);
            visibility: visible;
            opacity: 0;
        }

        100% {
            visibility: hidden;
            transform: translateX(100%);
        }
    }

    editor-ui-group {
        height: fit-content;
        max-height: 100%;
        width: fit-content;
        max-width: 100%;
        overflow: auto;
        scrollbar-width: none;
        pointer-events: none;
    }

    editor-ui-group::-webkit-scrollbar {
        display: none;
    }
}

.menu-overlay-pane {
    @apply w-[290px] md:w-[400px];
}

#feature-table > .grid {
    .feature-check {
        @apply bg-[url('assets/img/rainbow-stick.svg')] bg-[center] bg-[length:40px_40px] bg-no-repeat;
    }

    .header-shape {
        @apply flex p-[28px_5px] w-full justify-center text-center items-center h-[190px] rounded-tl-[50%] rounded-br-[50%] shadow-[3px_3px_0_0_rgb(var(--SC2))];
    }

    & > :nth-child(6n + 1) {
        @apply text-P2 p-[20px];
    }

    & > :nth-child(6n + 1):not(:nth-child(n + 1):nth-child(-n + 6)),
    & > :nth-child(6n + 2):not(:nth-child(n + 1):nth-child(-n + 6)),
    & > :nth-child(6n + 3):not(:nth-child(n + 1):nth-child(-n + 6)),
    & > :nth-child(6n + 4):not(:nth-child(n + 1):nth-child(-n + 6)),
    & > :nth-child(6n + 5):not(:nth-child(n + 1):nth-child(-n + 6)) {
        @apply border-r border-r-BW3 border-dashed;
    }

    & > :nth-child(n + 13):nth-child(-n + 42) {
        @apply border-t border-t-BW3 border-dashed;
    }
}

.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container {
    border-radius: inherit;
}

.mat-mdc-dialog-container .mdc-dialog__surface {
    border-radius: inherit !important;
}

.vi-popup-container {
    @apply bg-BW7;
    display: flex;
    margin-left: auto;
    margin-right: auto;
    width: 350px;
    height: 250px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    -webkit-flex-shrink: 0;
    border: solid 1px rgb(var(--BW3));
    border-radius: inherit;
    padding: 0 25px;

    .vi-popup-message {
        @apply text-BW2;
        font-size: 14px;
        font-family: Montserrat;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
    }
}

.mat-mdc-dialog-container {
    border-radius: 15px !important;
    padding: 0 !important;
    overflow: visible !important;
}

.vi-arrow-bottom {
    &:after {
        content: '';
        background: black;
        height: 5px;
        width: 10px;
        display: flex;
        clip-path: polygon(50% 100%, 0% 0%, 100% 0%);
    }
}

.vi-arrow-top {
    &:before {
        content: '';
        background: black;
        height: 5px;
        width: 10px;
        display: flex;
        clip-path: polygon(100% 100%, 0 100%, 50% 0%);
    }
}

.vi-arrow-bottom,
.vi-arrow-top {
    &.vi-arrow-pos-center {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

.vi-arrow-right {
    &:before {
        content: '';
        background: black;
        height: 10px;
        width: 5px;
        display: flex;
        clip-path: polygon(100% 50%, 0% 100%, 0% 0%);
    }
}

.vi-arrow-left {
    &:after {
        content: '';
        background: black;
        height: 10px;
        width: 5px;
        display: flex;
        clip-path: polygon(100% 100%, 0% 50%, 100% 0%);
    }
}

.vi-arrow-right,
.vi-arrow-left {
    &.vi-arrow-pos-center {
        display: flex;
        flex-direction: row;
        align-items: center;
    }
}

.homepage-viewport {
    .viewport-root {
        @apply absolute top-0 left-[3rem] w-[calc(100%-3rem)] h-[calc(100%-3rem)] rounded-[20px] overflow-hidden z-0;
    }

    .top-left-side-tools {
        @apply max-h-[calc(100%-3rem)];
    }

    .zoom-bar {
        @apply right-[12.25rem] #{!important};
    }

    .v-tool-group {
        @apply bg-white;
    }

    &[edType='WordEditor'] .viewport-root .board-viewport,
    &[edType='MathEditor'] .viewport-root .board-viewport {
        @apply px-[10px] py-[30px];
    }
}

.profilepage-viewport {
    .viewport-root {
        @apply absolute top-0 left-[3rem] w-[calc(100%-3rem)] h-[calc(100%-3rem)] rounded-[20px] overflow-hidden z-0 border border-BW4;

        &.no-ed-toolbar {
            @apply left-[0] w-full;
        }

        &.no-zoom-toolbar {
            @apply h-full;
        }
    }

    .top-left-side-tools {
        @apply max-h-[calc(100%-3rem)];
    }

    .v-tool-group {
        @apply bg-white shadow-none border border-BW4 #{!important};
    }

    .vptools-bar {
        @apply px-[20px] py-[10px];
    }

    [edType='WordEditor'] .viewport-root .board-viewport,
    [edType='MathEditor'] .viewport-root .board-viewport {
        @apply px-[30px] py-[30px];
    }
}

.documentpage-viewport {
    @apply bg-BW2;

    .viewport-container {
        @apply xs:mt-[60px] md:mt-[80px] xs:h-[calc(100%-60px)] md:h-[calc(100%-80px)];
    }

    .viewport-root {
        @apply w-full mx-auto xs:h-[calc(100%-60px)] md:h-[calc(100%-80px)] rounded-[20px] overflow-hidden z-0 xs:max-w-[300px] sm:max-w-[620px] md:max-w-[920px] lg:max-w-[1120px] xl:max-w-[1350px];
    }

    .top-left-side-tools {
        @apply max-h-[calc(100%-3rem)] h-full pl-[10px];
    }

    .v-tool-group {
        @apply bg-white;
    }

    .zoom-bar {
        @apply px-[20px] py-[10px];
    }

    .vptools-bar {
        @apply px-[20px] py-[10px];
    }

    &.is-own {
        .zoom-bar {
            @apply right-[6.25rem] #{!important};
        }
    }

    [edType='WordEditor'] .viewport-root .board-viewport,
    [edType='MathEditor'] .viewport-root .board-viewport {
        @apply px-[30px] py-[30px];
    }
}

// IOS is a bit shitty and interpret long press pen as selection
// so have to disable it.
.viewport-root,
.board-viewport,
canvas,
.viclass-float-layer,
.vcon,
app-ed-switch,
app-viewport-manager {
    user-select: none;
}
