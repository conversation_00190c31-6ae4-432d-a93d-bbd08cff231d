/** @type {import('tailwindcss').Config} */
const baseTail = require('../../../../tailwind.config');
const path = require('path');
const theme = baseTail.theme;

theme.extend.spacing = {
    ...theme.extend.spacing,
    // more extension for homepage project
    largePageMargin: '80px',
    smallPageMargin: '40px',
};

module.exports = {
    content: [path.join(__dirname, 'src/**/*.{html,js,ts,scss}')],
    theme: {
        ...theme,
        screens: {
            xs: '320px',
            sm: '640px',
            md: '1024px',
            lg: '1200px',
            xl1: '1280px',
            xl: '1440px',
            '2xl': '1920px',
            '3xl': '2560px',
        },
    },
    plugins: [],
    darkMode: 'selector',
};
