const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const { merge } = require('webpack-merge');
const commonBase = require('../../../webpack/webpack.common.base');
const wpshare = require('../../../webpack/webpack.mf.wpshare');
const share = wpshare.share;

var merged = merge(commonBase, {
    plugins: [
        new ModuleFederationPlugin({
            name: '@viclass/classrooms',
            shared: {
                ...wpshare['shareAngular'],
                ...wpshare['shareCommon'],
                ...wpshare['shareProto'],
                ...wpshare['shareMathlive'],
                ...share('@viclass/editor.core', { requiredVersion: '*' }),
                ...share('@viclass/editorui.loader', { requiredVersion: '*', strictVersion: false }),
                ...share('@viclass/portal.common/common_v3', { requiredVersion: '*' }),
                ...share('@viclass/editor.coordinator/common', { requiredVersion: '*' }),
            },
        }),
    ],
    resolve: {
        fallback: { url: false },
    },
});

module.exports = merged;
