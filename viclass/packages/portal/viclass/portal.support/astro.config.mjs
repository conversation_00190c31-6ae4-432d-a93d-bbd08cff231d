import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';
import tailwind from '@astrojs/tailwind';
import path from 'path';

// https://astro.build/config
export default defineConfig({
    base: '/support',
    outDir: path.resolve('../../../../dist/viclass/portal.support'),

    integrations: [
        starlight({
            favicon: '/support/favicon.ico',
            title: {
                vn: 'Trung Tâm Hỗ Trợ',
                en: 'Support Center',
            },
            defaultLocale: 'root',
            locales: {
                en: {
                    label: 'English',
                    lang: 'en',
                },
                root: {
                    label: 'Tiếng Việt',
                    lang: 'vn',
                },
            },
            sidebar: [
                {
                    label: 'Giới thiệu chung',
                    translations: {
                        en: 'General introduction',
                    },
                    autogenerate: {
                        directory: '01-intro',
                    },
                    collapsed: true,
                    icon: 'vcon vcon-general vcon_user',
                },
                {
                    label: '<PERSON>ông cụ trên viclass',
                    translations: {
                        en: 'The viclass toolbox',
                    },
                    items: [
                        {
                            label: 'Soạn thảo văn bản',
                            autogenerate: {
                                directory: '02-toolbox/01-word',
                            },
                            translations: {
                                en: 'Word editor',
                            },
                            collapsed: true,
                        },
                        {
                            label: 'Dựng hình học',
                            autogenerate: {
                                directory: '02-toolbox/02-geo',
                            },
                            translations: {
                                en: 'Geometry editor',
                            },
                            collapsed: true,
                        },
                        {
                            label: 'Vẽ tự do',
                            autogenerate: {
                                directory: '02-toolbox/03-freedrawing',
                            },
                            translations: {
                                en: 'Freedrawing editor',
                            },
                            collapsed: true,
                        },
                    ],
                    collapsed: true,
                    icon: 'vcon vcon-general vcon_edit',
                },
                {
                    label: 'Tài liệu trên viclass',
                    translations: {
                        en: 'Documents on viclass',
                    },
                    autogenerate: {
                        directory: '03-documents',
                    },
                    collapsed: true,
                    icon: 'vcon vcon-general vcon_sidebar-document',
                },
            ],
            customCss: ['@viclass/themes/vi-theme', './src/tailwind.scss'],
            components: {
                Header: './src/components/Header.astro',
                SiteTitle: './src/components/SiteTitle.astro',
                Sidebar: './src/components/Sidebar.astro',
            },
            pagefind: true,
        }),
        tailwind({
            applyBaseStyles: false,
        }),
        {
            name: 'viclass-integration',
            hooks: {
                'astro:config:setup': options => {
                    options.injectScript('page', `import '/src/viclass/integration.js'`);
                },
            },
        },
    ],
    vite: {
        plugins: [
            // myPlugin()
        ],
        resolve: {
            alias: [
                {
                    find: '@starlight-internal',
                    replacement: path.resolve('../../../../node_modules/@astrojs/starlight'),
                },
            ],
        },
    },
});

// const fileRegex = /\.scss$/

// function myPlugin() {
//   return {
//     name: 'transform-file',

//     transform(src, id) {
//       if (fileRegex.test(id)) {
//         console.log("\n\n ---------------------------- \n\n")
//         console.log(id)
//         console.log(src)
//       }
//     },
//   }
// }
