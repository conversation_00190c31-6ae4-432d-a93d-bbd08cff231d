---
import { logos } from 'virtual:starlight/user-images';
import config from 'virtual:starlight/user-config';
import type { Props } from '@astrojs/starlight/props';
const { siteTitle, siteTitleHref } = Astro.props;
---

<a href={siteTitleHref} class="site-title sl-flex">
	{
		config.logo && logos.dark && (
			<>
				<img
					class:list={{ 'light:sl-hidden': !('src' in config.logo) }}
					alt={config.logo.alt}
					src={logos.dark.src}
					width={logos.dark.width}
					height={logos.dark.height}
				/>
				{/* Show light alternate if a user configure both light and dark logos. */}
				{!('src' in config.logo) && (
					<img
						class="dark:sl-hidden"
						alt={config.logo.alt}
						src={logos.light?.src}
						width={logos.light?.width}
						height={logos.light?.height}
					/>
				)}
			</>
		)
	}
	<div class="flex align-items-center gap-[10px]">
		<span class="vcon vcon-general !leading-[27px] vcon_logo-icon !text-[25px] text-[#00AEEF]"></span>
		<span class="text-[18px] !leading-[27px] font-[700]" class:list={{ 'sr-only': config.logo?.replacesTitle }}>
			{siteTitle}
		</span>
	</div>
</a>

<style>
	.site-title {
		align-items: center;
		gap: var(--sl-nav-gap);
		font-size: var(--sl-text-h4);
		font-weight: 600;
		color: var(--sl-color-white);
		text-decoration: none;
		white-space: nowrap;
	}
	img {
		height: calc(var(--sl-nav-height) - 2 * var(--sl-nav-pad-y));
		width: auto;
		max-width: 100%;
		object-fit: contain;
		object-position: 0 50%;
	}
</style>
