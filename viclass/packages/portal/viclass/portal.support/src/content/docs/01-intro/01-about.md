---
title: V<PERSON> chúng tôi
---

<PERSON><PERSON><PERSON> một cách ngắn gọn, VIClass được tạo ra để mang đến một trải nghiệm dạy và học online hoàn hảo!

Nếu như bạn tò mò về sự ra đời của VIClass, chúng tôi trình bày một số nội dung chi tiết dưới đây, với hi vọng bạn có thể hiểu rõ hơn về vấn đề chúng tôi giải quyết, và qua đó, có thể quyết định liệu VIClass có mang lại lợi ích gì cho bạn.

<PERSON><PERSON><PERSON><PERSON> lại, bạn hoàn toàn có thể bỏ qua phần này và tiếp tục đọc về những ứng dụng tuyệt vời của VIClass trong mục - [**<u>Ứng dụng VICLASS</u>**](/01-intro/02-usecases)

## Tại sao lại có VIClass

Với sự phát triển phổ cập của mạng Internet, từ lâu, chúng ta đã có những công cụ giúp tương tác trực tuyến và chia sẻ nội dung hữu hiệu ví dụ như Mạng Xã Hội, công cụ họp trực tuyến, websites, v..v.. Điều này giúp cho việc giáo dục trực tuyến trở nên khả thi và thông dụng hơn.

So với việc phải đến lớp học trực tiếp trước đây, học sinh ngày này đã có thể tiếp cận cận những hình thức học tập trực tuyến đa dạng hơn rất nhiều bao gồm:

- Nghe giảng trực tiếp trong các buổi học trực tuyến trên các nền tảng live stream hoặc conference call như Facebook, Youtube, Zoom, v..v..
- Thực hiện các buổi học kèm 1-1 trên mạng cũng thông qua video call
- Tham ra các khóa tự học trả phí online trên các nền tảng kết nối như Cousera, Khanacademy
- Mua các bài giảng tự học bằng videos trên các trang web bán khóa học như Học Mãi
- Tham gia thảo luận, hỏi đáp, trên các nền tảng mạng xã hội hoặc diễn đàn
- Tự học miễn phí bằng các video trực tuyến miễn phí trên Youtube, Facebook
- Tự tìm kiếm học liệu trên các websites thông qua công cụ tìm kiếm như Google

Mặc dù không thể hoàn toàn thay thế việc học tập trực tiếp, việc học online mang lại nhiều lợi thế không thể phủ nhận trên rất nhiều khía cạnh. Thứ nhất, người học có khả năng học ở mọi nơi mọi lúc, thay vì phải ở gần lớp học hoặc phải đến lớp vào thời gian cố định. Thứ hai, với mức độ tiếp cận người học rộng rãi hơn, chi phí của các khóa học online thường rẻ hơn. Thứ ba, với tính tức thì của môi trường mạng, một số hình thức học chỉ có thể thực hiện trực tuyến, ví dụ như người học có thể đặt câu hỏi và nhận câu trả lời cho vấn đề mình chưa biết ngay khi phát sinh, thay vì phải chờ đến khi gặp thầy cô giáo. Cuối cùng, vì không bị cản trở bởi yếu tố địa lý, việc học online sẽ tiết kiệm rất nhiều chi phí di chuyển, đồng thời giúp học sinh tiếp cận được giáo viên chất lượng, dù ở cách xa hàng nghìn kilomet, thậm chí ở những nền văn hóa khác.

Việc học online vì vậy đang trở thành một xu hướng bổ trợ với nhiều lợi ích thiết thực cho học sinh. Tuy nhiên, ta có thể liệt kê một số nhược điểm lớn đối với việc học online như dưới đây

**<u>Khả năng tương tác hai chiều hạn chế</u>**

Phần lớn các buổi học online cung cấp phương thức trình bày ý tưởng thông qua việc chia sẻ màn hình bên cạnh việc sử dụng video. Yếu điểm lớn nhất của phương thức này là người xem không thể tương tác trực tiếp với nội dung được trình bày trên màn hình của người chia sẻ.

Bên cạnh đó, sự khác biệt về độ phân giải màn hình hay tốc độ đường truyền làm ảnh hưởng lớn đến chất lượng hình ảnh được chia sẻ. Ví dụ như khi màn hình của người trình bày ở độ phân giải 4K và người xem có màn hình ở độ phân giải Full HD, thì hình ảnh người xem nhận được sẽ có các chi tiết nhỏ hơn đáng kể so với những gì người trình bày nhìn thấy.

Một số ứng dụng bảng trắng đã được sinh ra để phần nào khắc phục những yếu điểm này. Người dùng ở những nơi khác nhau có thể cùng tương tác chỉnh sửa trên một tài liệu (collaborative editing). Tuy nhiên, các ứng dụng bảng trắng trên thị trường thường dừng lại ở việc viết / vẽ những nội dung phổ quát đơn giản, thay vì tập trung vào các công cụ thiết kế đặc biệt cho việc học tập.

Một ví dụ đơn giản là hiện tại chưa có ứng dụng bảng trắng nào cho phép vẽ đường tròn nội tiếp hoặc ngoại tiếp một tam giác hay đa giác trong môi trường tương tác đa người dùng.

```
Ở đây có lẽ có thể đính kèm một video demo việc vẽ hình học và update thời gian thực giữa nhiều học sinh được thực hiện trên viclass
```

**<u>Thiếu khả năng soạn thảo trên diễn đàn, mạng xã hội</u>**

Ngoài việc học trực tiếp, thảo luận trên các diễn đàn hay mạng xã hội cũng là một cách học online tương đối hiệu quả. Việc thảo luận trên các diễn đàn mang lại cơ hội thể hiện bản thân, giải quyết các vấn đề mới, phản biện và nhận phản biện người khác.

Tuy nhiên việc thiếu những công cụ soạn thảo cần thiết, việc thảo luận này bị hạn chế chủ yếu ở các nội dung ít tính tương tác như chữ hoặc ảnh tĩnh. Ví dụ khi soạn thảo hoặc trả lời một chủ đề trên các diễn đàn, vì thiếu công cụ sinh công thức toán trực quan, người dùng có thể phải gõ lệnh bằng TEX / LATEX hoặc sử dụng công cụ bên ngoài để tạo ra công thức toán, upload ảnh, và chèn ảnh tĩnh vào bài viết. Tương tự, khi vẽ các hàm số, hoặc hình học, việc soạn thảo sẽ rất bất tiện. Thậm chí, một số người dùng đã viết ra giấy và chụp hình bằng điện thoại và upload lên các diễn đàn hoặc mạng xã hội cho mục đích thảo luận (Hình dưới).

```
Hình ví dụ
01-intro-hinh-01.png
01-intro-hinh-02.png
```

Cách làm này không chỉ bất tiện trong thao tác, mà còn dẫn đến việc dễ thất lạc hoặc khó khăn trong tìm kiếm sau này. Một công cụ soạn thảo theo kiểu WYSIWYG (what you see is what you get), được nhúng trực tiếp vào diễn đàn cho người dùng một trải nghiệm soạn thảo và trả lời liền mạch sẽ thân thiện hơn rất nhiều.

**<u>Thiếu sự tích hợp giữa các công cụ</u>**

Các công cụ hiện có trên mạng thường thiếu sự tương thích nhất định, dẫn đến việc sử dụng chúng thường khó khăn. Ví dụ như khi vẽ hình học, giải tích phân, hay viết phương trình vật lý, hóa học, người dùng thường sẽ phải sử dụng các công cụ khác nhau, và cuối cùng là xuất ra định dạng ảnh tĩnh để đưa vào các văn bản hoặc trình bày trong lớp học.

Việc sử dụng nhiều công cụ từ nhiều nhà cung cấp sẽ làm tăng chi phí, và thời gian học các sử dụng công cụ, đồng thời việc quản lý tài liệu trở nên phức tạp hơn rất nhiều, và người dùng thường sẽ chỉ lưu trữ các ảnh tĩnh cuối cùng đã xuất bản ra. Hơn nữa, khả năng truyền đạt của bức ảnh sẽ không thể đa dạng bằng những tài liệu tương tác được nhúng trực tiếp vào nội dung trình bày hoặc văn bản được soạn thảo.

Ví dụ, có thể kể đến là đối với những người học chơi cờ vua, việc nhìn hình ảnh một thế cờ sẽ không cho người học một cái nhìn đầy đủ về những nước đi cờ dẫn đến thế cờ đó. Hoặc người học sẽ không thử nghiệm được các nước cờ mình cần di chuyển để giải thế cờ ngay trên hình ảnh.

Ví dụ thứ hai, khi xem ảnh một đồ thị hàm số, người dùng sẽ không thể khảo sát được các giá trị, hay vẽ được tiếp tuyến của hàm số đó. Thay vào đó, nếu hàm số đó được vẽ dưới dạng interactive, người dùng sẽ có nhiều phương tiện để tìm hiểu về dạng hàm số hay khảo sát các kiến thức liên quan đến hàm số (điểm cực đại, cực tiểu, v..v..) đó một cách trực quan hơn.

**<u>Khác biệt về công cụ giữa người dùng</u>**

Vì sự đa dạng của các công cụ có sẵn, cả web app và ứng dụng truyền thống dẫn đến khả năng không tương thích giữa các người dùng với nhau. Ví dụ như giáo viên có thể dùng Power point để vẽ hình hoặc sơ đồ, nhưng người dùng lại không cài đặt Powerpoint.

Đặc biệt, với những học sinh ở vùng sâu, vùng xa hoặc ít có khả năng tiếp cận với máy tính, điện thoại hay máy tính bảng thì sẽ rất ít khả năng những học sinh này có thể cài đặt hoặc chi trả cho các ứng dụng chuyên nghiệp cho từng môn học.

Việc sử dụng công cụ không đồng nhất trong cộng đồng người dùng dẫn đến việc truyền đạt ý tưởng hoặc trả lời câu hỏi của giáo viên và học sinh trở nên khó khăn hơn, khi mọi nội dung đều phải chuyển đổi sang hình thức định dạng hình ảnh, hoặc text.

Chính vì vậy, một ứng dụng Web App không đòi hỏi sự cài đặt đặc biệt và miễn phí, với các công cụ tích hợp chặt chẽ phục vụ việc dạy và học, đồng thời cho một trải nghiệm soạn thảo liền mạch thuận tiện trong nhiều tình huống sử dụng (cả trong và ngoài buổi học trực tuyến) sẽ giảm bớt các rào cản với việc học online.

## VIClass là gì?

VIClass được sinh ra để giải quyết các vấn đề đã liệt kê ở trên. VIClass là một bộ công cụ dạy và học với các tiêu chí

- **Toàn diện** - phủ sóng tất cả các bộ môn học tập, tạo ra được tất cả các học liệu cần thiết phục vụ cho việc dạy và học không chỉ các môn học phổ thông truyền thống, mà cả những môn học mang tính sở thích, ví dụ như các môn cờ, nhạc, họa, v..v.
- **Chuyên sâu** - công cụ dành cho mỗi môn học hoặc lĩnh vực được thiết kế đặc biệt cho lĩnh vực đó,
- **Tích hợp chặt chẽ** - các công cụ có thể sử dụng thuận tiện và tương thích với nhau trong toàn bộ hệ sinh thái VIClass, đồng thời có thể được tích hợp dễ dàng với các nền tảng khác hoặc các trang web đơn lẻ của bên thứ ba.

VIClass hướng tới mục tiêu đặt ra một nền tảng, và chuẩn mực mới giúp giáo viên và học sinh tạo ra tài liệu học tập, thực hiện việc dạy và học ngay trên trình duyệt, một cách dễ dàng, thuận tiện, và với mức độ tương tác cao nhất, và chi phí thấp nhất. VIClass cũng muốn khuyến khích những hình thức học tập, trao đổi mới đa dạng, sáng tạo và gần gũi hơn với học sinh và giáo viên thông qua việc sử dụng những công cụ của mình.
