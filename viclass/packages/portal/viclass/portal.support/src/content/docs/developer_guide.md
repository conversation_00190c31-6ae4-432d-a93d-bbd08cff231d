---
title: <PERSON><PERSON><PERSON><PERSON> dẫn dành cho nhà phát triển
---

<PERSON><PERSON> sử dụng embed trên trang html tĩ<PERSON>, bắt buộc phải thêm thư viện này vào trang của bạn:

```js
<script src="https://viclass.vn/modules/ww/vi.docloader.js" type="text/javascript"></script>
```

Bước đầu tiên cần phải khởi tạo docloader:

```js
await viclass.ViDocLoader.initialize();
```

<PERSON><PERSON> tải tất cả tài liệu có trên trang, hãy dùng lệnh sau, mặc định sẽ tải tất cả các tài liệu trên thẻ có class vi-embed-viewport:

```js
await viclass.ViDocLoader.renderAll;
```

<PERSON><PERSON>ài ra có thể truyền option vào cho hàm renderAll, ví dụ sau đây tải tất cả tài liệu trên selector .vi-embed-viewport-2:

```js
await viclass.ViDocLoader.renderAll({ vpSelector: '.vi-embed-viewport-2' });
```

Có thể tham khảo các option khác trong phần tham khảo thêm. Ngoài ra cũng có thể tải tài liệu lên một thẻ chỉ định thông qua API sau:

```js
const el = document.getElementsByClassName('vi-embed-viewport-3')[0];
await viclass.ViDocLoader.renderEl(el);
```

Và tất nhiên cũng có thể truyền option vào API này:

```js
await viclass.ViDocLoader.renderEl(el5, { showTool: true });
```

Tài liệu có thể hiển thị trên trang web, cần phải cung cấp data configuration cho thư viện.

## Có hai hình thức cung cấp data

### 1. Thông qua html data-attribute

```html
<div
    class="vi-embed-viewport-2"
    data-vi-show-tool="true"
    data-vi-vp-id="vpid02"
    data-vi-vp-type="board"
    data-vi-tools="zoom,pan"
    data-vi-tool-v-align="bottom"
    data-vi-tool-h-align="left"
    data-vi-tool-direction="ttb"
    style="width: 900px; height: 500px; position: relative">
    <div class="vi-embed-doc" data-vi-doc-id="660e53f04326eb4d1e45c0c0" data-vi-ed-type="GeometryEditor"></div>
    <div class="vi-embed-doc" data-vi-doc-id="6616bd3a92839853c4edf380" data-vi-ed-type="FreeDrawingEditor"></div>
</div>
```

Tài liệu có thể được config bên trong element chính như ví dụ trên, hoặc có thể config trực tiếp ngay trên element chính như sau:

```html
<div
    class="vi-embed-viewport-5"
    data-vi-show-tool="true"
    data-vi-vp-id="vpid05"
    data-vi-vp-type="board"
    data-vi-doc-id="660e53f04326eb4d1e45c0c0"
    data-vi-ed-type="GeometryEditor"
    data-vi-doc-id-2="6616bd3a92839853c4edf380"
    data-vi-ed-type-2="FreeDrawingEditor"
    style="width: 900px; height: 500px; position: relative"></div>
```

### 2. Thông qua option truyền vào lúc gọi hàm

```js
await viclass.ViDocLoader.renderEl({ vpSelector: '.vi-embed-viewport-2' });
```

Xem thêm phần tham khảo để biết các loại data configuration mà thư viện hỗ trợ.

## THAM KHẢO

```js
BoardType = 'board' | 'inline';

EditorType = |
    'FreeDrawingEditor' |
    'GeometryEditor' |
    'WordEditor' |
    'PhotoEditor' |
    'PdfEditor' |
    'MathEditor';

RenderOption {
    vpType ? : 'board' | 'inline';
    lookAtX ? : number;
    lookAtY ? : number;
    zoom ? : number;
    width ? : number;
    height ? : number;
    /**
     * The element selector. All element with this selector will be
     * checked for potential documents data to be rendered.
     */
    docSelector ? : string;
    /**
     * The element selector. All element with this selector will be
     * checked for potential documents to be rendered.
     */
    vpSelector ? : string;
    edType ? : "FreeDrawingEditor" | "GeometryEditor" | "WordEditor" | "PhotoEditor" | "PdfEditor" | "MathEditor";
    showTool ? : boolean;
    tools ? : ("pan" | "zoom")[];
    toolVAlign ? : string; // v-align toolbar
    toolHAlign ? : string; // h-align toolbar
    toolDirection ? : string; // toolbar direction
}
```

Tất cả các configuration trên, ngoại trừ các config selector đều có thể define bằng html data attribute.

Ví dụ:

- vpType -> data-vi-vp-type
- showTool -> data-vi-show-tool
- tools -> data-vi-tools
