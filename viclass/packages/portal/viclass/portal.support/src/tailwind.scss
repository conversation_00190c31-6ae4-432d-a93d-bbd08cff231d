@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,500;0,600;0,700;1,500;1,600;1,700&display=swap');

.vi-theme {
    font-family: 'Montserrat';

    .vi-btn {
        font-family: Montserrat;
        padding-left: 10px;
        padding-right: 10px;
        font-weight: 600;
        font-size: 0.85rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        text-decoration: none;
    }

    .vi-btn-normal {
        height: 40px;
        border-radius: 15px;
        border-width: 0px;
        padding-left: 10px;
        padding-right: 10px;
        line-height: 40px;
    }

    .vi-btn-focus:not([disabled]) {
        background-image: linear-gradient(270deg, #f3a9ff 2.08%, #bbedff 67.71%);
        color: #121414;
    }
}

:root[data-theme='light'].vi-theme {
    --sl-color-bg-nav: var(--sl-color-black) !important;

    header {
        @apply shadow-[0px_1px_30px_0_rgb(var(--BW1)/0.25)];
    }

    @apply lg:[--sl-sidebar-width:20rem] xl:[--sl-sidebar-width:25rem];

    .sidebar-content {
        .large {
            --sl-text-base: var(--sl-text-sm);
            --sl-text-lg: var(
                --sl-text-sm
            ); /* when screen small, sidebar item use large text size for the collapsing sidebar */
            font-weight: bold;
        }

        ul {
            --sl-sidebar-item-padding-inline: 0.6rem;
            @apply mb-[0.5rem] text-[13px];

            li:first-child {
                @apply pt-[0.6rem];
            }

            li:not(:first-child) {
                @apply pt-[0.3rem];
            }
        }

        @apply pt-7 px-7;

        a[aria-current='page'] {
            --sl-color-text-accent: transparent;
            --sl-color-text-invert: rgb(var(--SC1));
        }

        .group-label {
            @apply flex items-center;
        }

        .vcon {
            @apply ml-[-7px] text-[15px];
        }
    }

    starlight-toc,
    mobile-starlight-toc {
        a[aria-current='true'] {
            --sl-color-text-accent: rgb(var(--SC1));
            color: var(--sl-color-text-accent);
        }
    }

    /* .sl-container {
        
    } */

    .right-sidebar-container {
        @apply w-[calc(100vw-var(--sl-sidebar-width)-var(--sl-content-width))] lg:max-w-[calc(0.67*var(--sl-sidebar-width))];
    }

    .main-pane {
        @apply xl:[--sl-content-width:60rem] 2xl:[--sl-content-width:75rem];
        @apply w-[var(--sl-content-width)];
    }
}
