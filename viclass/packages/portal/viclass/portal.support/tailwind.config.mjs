import starlightPlugin from '@astrojs/starlight-tailwind';

const baseTail = require('../../../../tailwind.config');
const baseTheme = baseTail.theme;

// Generated color palettes
const accent = {
    200: 'rgb(0 113 153)',
    600: 'rgb(var(--P1))',
    900: 'rgb(var(--P2))',
    950: 'rgb(var(--P3))',
};

const gray = {
    100: 'rgb(var(--BW6))',
    200: 'rgb(var(--BW5))',
    300: 'rgb(var(--BW4))',
    400: 'rgb(var(--BW3))',
    500: 'rgb(var(--BW3))',
    700: 'rgb(var(--BW3))',
    800: 'rgb(var(--BW2))',
    900: 'rgb(var(--BW1))',
};

/** @type {import('tailwindcss').Config} */

const tailwindConfig = {
    content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
    theme: {
        ...baseTheme,
        fontFamily: {
            sans: ['Montserrat'],
        },
    },
    plugins: [
        starlightPlugin(),
        function ({ addBase, config }) {
            addBase({
                ':root': {
                    '--breakpoint-sm': config('theme.screens.sm'),
                    '--breakpoint-md': '51rem',
                    '--breakpoint-lg': config('theme.screens.lg'),
                    '--breakpoint-xl': config('theme.screens.xl'),
                    '--breakpoint-2xl': config('theme.screens.2xl'),
                    '--breakpoint-3xl': config('theme.screens.3xl'),
                },
            });
        },
    ],
};

tailwindConfig.theme.extend.colors = {
    ...tailwindConfig.theme.extend.colors,
    accent,
    gray,
};

export default tailwindConfig;
