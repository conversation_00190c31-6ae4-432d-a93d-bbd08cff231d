// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

import { DocLoaderCoordinatorConfig } from '@viclass/editor.coordinator/docloader';

let editorTypeMapping = {
    FreeDrawingEditor: { id: 1, channelCode: 1 },
    GeometryEditor: { id: 2, channelCode: 2 },
};

let viclassRoot = 'https://devlocal.viclass.vn';

let docLoaderConf: DocLoaderCoordinatorConfig = {
    id: 'docloader',
    editorTypeMapping: editorTypeMapping,
    edLookups: [
        {
            editorType: 'FreeDrawingEditor',
            implContainerName: 'editor.freedrawing',
            implUri: `${viclassRoot}/modules/editor.freedrawing/editor.freedrawing.js`,
            implModuleName: './editor.freedrawing',
            settings: {
                apiUri: `${viclassRoot}/frd`,
            },
        },
        {
            editorType: 'GeometryEditor',
            implContainerName: 'editor.geo',
            implUri: `${viclassRoot}/modules/editor.geo/editor.geo.js`,
            implModuleName: './editor.geo',
            settings: {
                apiUri: `${viclassRoot}/geo`,
                numDim: 2,
                unit: 10,
            },
        },
        {
            editorType: 'WordEditor',
            implContainerName: 'editor.word',
            implUri: `${viclassRoot}/modules/editor.word/editor.word.js`,
            implModuleName: './editor.word',
            settings: {
                apiUri: `${viclassRoot}/word`,
                numDim: 2,
                unit: 10,
            },
        },
    ],
};

export const environment = {
    production: false,
    docLoader: docLoaderConf,
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
