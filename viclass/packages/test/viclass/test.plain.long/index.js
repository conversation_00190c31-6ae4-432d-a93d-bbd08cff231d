var manager;
window['ngDevMode'] = true;

class UIManager {
    constructor() {}

    async initialize() {
        let docLoaderCreator = viclass.DocLoader.createLoader;

        let viclassRoot = 'https://devlocal.viclass.vn';

        let editorTypeMapping = {
            FreeDrawingEditor: { id: 1, channelCode: 1 },
            GeometryEditor: { id: 2, channelCode: 2 },
        };

        let config = {
            id: 'docloader',
            editorTypeMapping: editorTypeMapping,
            edLookups: [
                {
                    editorType: 'FreeDrawingEditor',
                    implContainerName: 'editor.freedrawing',
                    implUri: `${viclassRoot}/modules/editor.freedrawing/editor.freedrawing.js`,
                    implModuleName: './editor.freedrawing',
                    settings: {
                        apiUri: `${viclassRoot}/frd`,
                    },
                },
                {
                    editorType: 'GeometryEditor',
                    implContainerName: 'editor.geo',
                    implUri: `${viclassRoot}/modules/editor.geo/editor.geo.js`,
                    implModuleName: './editor.geo',
                    settings: {
                        apiUri: `${viclassRoot}/geo`,
                        numDim: 2,
                        unit: 10,
                    },
                },
            ],
        };

        // get an instance of the coordinator
        this.coordinator = await docLoaderCreator(config);

        await this.coordinator.initialize();
        await this.coordinator.start();

        let vp01El = document.getElementById('freedrawingDoc01');
        let v01 = this.coordinator.createViewport(vp01El, {
            bType: 'board',
            viewportBoundingEl: vp01El.parentElement,
        });

        setTimeout(() => {
            this.coordinator.loadDocOnViewport(v01, {
                bType: 'board',
                edType: 'FreeDrawingEditor',
                gId: '638ef30a0d06b9634bf10a71',
            });
        });

        let ui01 = document.getElementById('ui');
        this.initializeUI(ui01, 'freedrawingDoc01');

        let zoomUI = document.getElementById('uiZoom');
        this.loadZoomUI(zoomUI, 'freedrawingDoc01');

        viclass.mfeRT
            .loadRemoteModule({
                type: 'module',
                remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.loader.webcomp.js',
                exposedModule: './editorui.loader.webcomp',
            })
            .then(() => {
                console.log('Editor ui loader module loaded!');
            });
    }

    initializeUI(ui, viewportId) {
        let compRef = undefined;

        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;

            if (ed.eventType == 'all-ui-loaded') {
                // compRef.switchTo("FreeDrawingEditor")
            } else if (ed.eventType == 'ui-loaded') {
                let uiComp = compRef.getUI(ed.state);

                if (ed.state == 'CommonToolsBar') {
                    uiComp.connectToolbar(this.coordinator.getCommonToolbar(viewportId));
                } else {
                    let toolbar = this.coordinator.getEditorToolbar(viewportId, ed.state);
                    uiComp.connectToolbar(toolbar);
                }
            } else if (ed.eventType == 'loader-initialized') {
                compRef = ed.source;

                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }
        });

        ui.setAttribute(
            'lookups',
            JSON.stringify([
                {
                    editorType: 'CommonToolsBar',
                    uiImpl: {
                        remoteName: 'editorui.commontools',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.commontools.js',
                        exposedModule: './editorui.commontools',
                    },
                    style: {
                        remoteName: 'editorui.commontools.style',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.commontools.style.js',
                        exposedModule: './editorui.commontools.style',
                    },
                    settings: {
                        availableEditors: ['FreeDrawingEditor', 'GeometryEditor'],
                        iconClasses: {
                            FreeDrawingEditor: 'vcon_document_freedrawing',
                            GeometryEditor: 'vcon_document_geometry',
                        },
                    },
                },
                {
                    editorType: 'FreeDrawingEditor',
                    uiImpl: {
                        remoteName: 'editorui.freedrawing',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/editorui.freedrawing/editorui.freedrawing.js',
                        exposedModule: './editorui.freedrawing',
                    },
                },
            ])
        );
    }

    loadZoomUI(ui, viewportId) {
        let compRef = undefined;

        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;

            if (ed.eventType == 'all-ui-loaded') {
                // compRef.switchTo("FreeDrawingEditor")
            } else if (ed.eventType == 'ui-loaded') {
                compRef.getUI(ed.state).connectToolbar(this.coordinator.getCommonToolbar(viewportId));
            } else if (ed.eventType == 'loader-initialized') {
                compRef = ed.source;

                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }
        });

        ui.setAttribute(
            'lookups',
            JSON.stringify([
                {
                    editorType: 'CommonToolsBar',
                    uiImpl: {
                        remoteName: 'editorui.commontools',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.commontools.js',
                        exposedModule: './editorui.zoomtools',
                    },
                    style: {
                        remoteName: 'editorui.commontools.style',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.commontools.style.js',
                        exposedModule: './editorui.commontools.style',
                    },
                },
            ])
        );
    }
}
