var manager;
window['ngDevMode'] = true;

class UIManager {
    constructor() {}

    async initialize() {
        let viDocLoader = viclass.ViDocLoader;
        // // get an instance of the coordinator
        this.coordinator = await viDocLoader.initialize();

        let vp01El = document.getElementById('freedrawingDoc01');
        let v01 = this.coordinator.createViewport(vp01El, {
            bType: 'board',
            viewportBoundingEl: vp01El.parentElement,
        });

        let vp02El = document.getElementById('freedrawingDoc02');
        let v02 = this.coordinator.createViewport(vp02El, {
            bType: 'board',
            viewportBoundingEl: vp02El.parentElement,
        });

        let vp03El = document.getElementById('geoDoc01');
        let v03 = this.coordinator.createViewport(vp03El, {
            bType: 'board',
            viewportBoundingEl: vp03El.parentElement,
        });

        let vp04El = document.getElementById('geoDoc02');
        let v04 = this.coordinator.createViewport(vp04El, {
            bType: 'board',
            viewportBoundingEl: vp04El.parentElement,
        });

        // let ui01 = document.getElementById("ui01")
        // this.initializeUI(ui01)

        // let ui02 = document.getElementById("ui02")
        // this.initializeUI(ui02)

        let commonUi = document.getElementById('uiCommon');
        // this.loadCommonUi(commonUi);

        let zoomUI = document.getElementById('uiZoom');
        // this.loadZoomUI(zoomUI);

        let frdUI01 = document.getElementById('uiFrd01');
        this.loadFrdUi(frdUI01, 'freedrawingDoc01');

        // let frdUI02 = document.getElementById('uiFrd02');
        // this.loadFrdUi(frdUI02, 'freedrawingDoc02');

        let geoUI01 = document.getElementById('uiGeo01');
        this.loadGeoUi(geoUI01, 'geoDoc01');

        // let geoUI02 = document.getElementById('uiGeo02');
        // this.loadGeoUi(geoUI02, 'geoDoc02');

        // let frdUI3 = document.getElementById("uiFrd3")
        // this.loadFrdUi(frdUI3, "freedrawingDoc03")
        //
        // let frdUI4 = document.getElementById("uiFrd4")
        // this.loadFrdUi(frdUI4, "freedrawingDoc04")

        await viDocLoader.loadWebcomp();
    }

    initializeUI(ui) {
        let compRef = undefined;

        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;

            if (ed.eventType == 'all-ui-loaded') {
                compRef.switchTo('FreeDrawingEditor');
            } else if (ed.eventType == 'ui-loaded') {
                let editor = this.coordinator.editorByType(ed.state);
                if (editor) compRef.getUI(ed.state).connectToolbar(editor.toolBar);
            } else if (ed.eventType == 'loader-initialized') {
                if (ed.eventType === 'all-ui-loaded') {
                    // compRef.switchTo("FreeDrawingEditor")
                } else if (ed.eventType === 'ui-loaded') {
                    // let editor = this.coordinator.editorByType(ed.state)
                    // compRef.getUI(ed.state).connectToolbar(editor.toolBar)
                } else if (ed.eventType === 'loader-initialized') {
                    compRef = ed.source;

                    compRef.loadBaseTheme({
                        remoteName: 'editorui.theme',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                        exposedModule: './editorui.theme',
                    });
                }
            }
        });
    }

    loadCommonUi(ui) {
        let compRef = undefined;

        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;

            if (ed.eventType === 'all-ui-loaded') {
                // compRef.switchTo("FreeDrawingEditor")
            } else if (ed.eventType === 'ui-loaded') {
                // let editor = this.coordinator.editorByType(ed.state)
                // compRef.getUI(ed.state).connectToolbar(editor.toolBar)
            } else if (ed.eventType === 'loader-initialized') {
                compRef = ed.source;

                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }
        });

        ui.setAttribute(
            'lookups',
            JSON.stringify([
                {
                    editorType: 'CommonToolsBar',
                    uiImpl: {
                        remoteName: 'editorui.commontools',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.commontools.js',
                        exposedModule: './editorui.commontools',
                    },
                    style: {
                        remoteName: 'editorui.commontools.style',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.commontools.style.js',
                        exposedModule: './editorui.commontools.style',
                    },
                },
                // {
                //     editorType: "GeometryEditor",
                //     uiImpl: {
                //         remoteName: "editorui.geo",
                //         remoteEntry: "https://devlocal.viclass.vn/modules/editorui.geo/editorui.geo.js",
                //         exposedModule: "./editorui.geo"
                //     }
                // },
                // {
                //     editorType: "FreeDrawingEditor",
                //     uiImpl: {
                //         remoteName: "editorui.freedrawing",
                //         remoteEntry: "https://devlocal.viclass.vn/modules/editorui.freedrawing/editorui.freedrawing.js",
                //         exposedModule: "./editorui.freedrawing"
                //     }
                // }
            ])
        );
    }

    loadFrdUi(ui, viewportId) {
        let compRef = undefined;
        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;
            if (ed.eventType === 'all-ui-loaded') {
                compRef.switchTo('FreeDrawingEditor');
            } else if (ed.eventType === 'ui-loaded') {
                let toolbar = this.coordinator.getEditorToolbar(viewportId, 'FreeDrawingEditor');
                compRef.getUI(ed.state).connectToolbar(toolbar);
                compRef.getUI(ed.state).showUI();
            } else if (ed.eventType === 'loader-initialized') {
                compRef = ed.source;
                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }

            ui.setAttribute(
                'lookups',
                JSON.stringify([
                    {
                        editorType: 'FreedrawingToolsBar',
                        uiImpl: {
                            remoteName: 'editorui.freedrawing',
                            remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.freedrawing.js',
                            exposedModule: './editorui.freedrawing',
                        },
                        style: {
                            remoteName: 'editorui.commontools.style',
                            remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.freedrawing.style.js',
                            exposedModule: './editorui.freedrawing.style',
                        },
                    },
                ])
            );
        });
    }

    loadGeoUi(ui, viewportId) {
        let compRef = undefined;
        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;
            if (ed.eventType === 'all-ui-loaded') {
                compRef.switchTo('GeometryEditor');
            } else if (ed.eventType === 'ui-loaded') {
                let toolbar = this.coordinator.getEditorToolbar(viewportId, 'GeometryEditor');
                compRef.getUI(ed.state).connectToolbar(toolbar);
                compRef.getUI(ed.state).showUI();
            } else if (ed.eventType === 'loader-initialized') {
                compRef = ed.source;
                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }

            ui.setAttribute(
                'lookups',
                JSON.stringify([
                    {
                        editorType: 'GeometryToolsBar',
                        uiImpl: {
                            remoteName: 'editorui.geo',
                            remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.geo.js',
                            exposedModule: './editorui.geo',
                        },
                        style: {
                            remoteName: 'editorui.commontools.style',
                            remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.geo.style.js',
                            exposedModule: './editorui.geo.style',
                        },
                    },
                ])
            );
        });
    }

    loadZoomUI(ui) {
        let compRef = undefined;

        ui.addEventListener('loaderEvent', event => {
            let ed = event.detail;
            if (ed.eventType === 'all-ui-loaded') {
                // compRef.switchTo("FreeDrawingEditor")
            } else if (ed.eventType === 'ui-loaded') {
                // let editor = this.coordinator.editorByType(ed.state)
                // compRef.getUI(ed.state).connectToolbar(editor.toolBar)
            } else if (ed.eventType === 'loader-initialized') {
                compRef = ed.source;

                compRef.loadBaseTheme({
                    remoteName: 'editorui.theme',
                    remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.theme.js',
                    exposedModule: './editorui.theme',
                });
            }
        });

        ui.setAttribute(
            'lookups',
            JSON.stringify([
                {
                    editorType: 'CommonToolsBar',
                    uiImpl: {
                        remoteName: 'editorui.commontools',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/mfe/editorui.commontools.js',
                        exposedModule: './editorui.zoomtools',
                    },
                    style: {
                        remoteName: 'editorui.commontools.style',
                        remoteEntry: 'https://devlocal.viclass.vn/modules/themes/editorui.commontools.style.js',
                        exposedModule: './editorui.commontools.style',
                    },
                },
            ])
        );
    }

    createNewUI(parent) {
        parent = parent || document.body;
        let ui = document.createElement('editor-ui');

        this.initializeUI(ui);
        parent.appendChild(ui);
    }
}
