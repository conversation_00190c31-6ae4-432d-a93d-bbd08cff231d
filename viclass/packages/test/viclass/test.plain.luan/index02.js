var manager;
window['ngDevMode'] = true;

class UIManager {
    constructor() {}

    async initialize() {
        let viDocLoader = viclass.ViDocLoader;
        // // get an instance of the coordinator
        this.coordinator = await viDocLoader.initialize();

        // let vp01El = document.getElementById('freedrawingDoc01');
        // let v01 = this.coordinator.createViewport(vp01El, {
        //     bType: 'board',
        //     viewportBoundingEl: vp01El.parentElement,
        // });
        //
        // let frdUI01 = document.getElementById('uiFrd01');
        // this.loadFrdUi(frdUI01, 'freedrawingDoc01');

        await viDocLoader.renderAll();

        await viDocLoader.loadWebcomp();
    }
}
