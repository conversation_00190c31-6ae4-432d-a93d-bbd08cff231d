<!doctype html>
<html lang="en">
    <head>
        <meta charset="utf-8" />
        <title>@viclass/testEmbed</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/x-icon" href="favicon.ico" />
        <script src="https://devlocal.viclass.vn/modules/ww/mfe.webrt.js" type="text/javascript"></script>
        <script src="https://devlocal.viclass.vn/modules/ww/docloader.coordinator.js" type="text/javascript"></script>
        <script src="./index.js" type="text/javascript"></script>

        <style>
            .board-viewport {
                height: 400px;
                width: 100%;
                position: relative;
                background: linear-gradient(-90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                    linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                    linear-gradient(-90deg, rgba(0, 0, 0, 0.08) 1px, transparent 1px),
                    linear-gradient(rgba(0, 0, 0, 0.08) 1px, transparent 1px), #fff;
                background-size:
                    20px 20px,
                    20px 20px,
                    100px 100px,
                    100px 100px;
            }

            .viewport-container {
                border: 1px solid red;
                width: 60%;
                position: relative;
                overflow: hidden;
                margin-left: auto;
                margin-right: auto;
            }

            .viewport-container editor-ui {
                position: absolute;
                height: fit-content;
                max-height: 100%;
                width: fit-content;
                max-width: 100%;
                overflow: auto;
                scrollbar-width: none;
            }

            .viewport-container editor-ui::-webkit-scrollbar {
                display: none;
            }
        </style>
    </head>
    <body onload="performLoad()">
        <h1>Example of loading document</h1>
        <ul>
            <li>
                <h3>A freedrawing document, drawn inside classroom</h3>
                <div class="viewport-container">
                    <div id="freedrawingDoc01" class="board-viewport"></div>
                    <editor-ui id="ui01" v-align="top" h-align="center" direction="ltr"></editor-ui>
                </div>
            </li>
            <li>
                <h3>A geo document, drawn inside classroom</h3>
                <div class="viewport-container">
                    <div id="geoDoc01" class="board-viewport"></div>
                    <editor-ui id="ui02" v-align="top" h-align="left" direction="ttb"></editor-ui>
                    <editor-ui id="uiZoom" v-align="right" h-align="bottom" direction="ltr"></editor-ui>
                </div>
            </li>
        </ul>

        <hr />
        <button onclick="javascript:manager.createNewUI()">Create</button>

        <script type="text/javascript">
            function performLoad() {
                manager = new UIManager();
                manager.initialize();
            }
        </script>
    </body>
</html>
