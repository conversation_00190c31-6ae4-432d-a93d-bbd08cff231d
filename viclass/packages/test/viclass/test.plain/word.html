<!doctype html>
<html>
    <head>
        <style>
            table {
                font-family: arial, sans-serif;
                border-collapse: collapse;
                width: 100%;
            }

            td,
            th {
                border: 1px solid #dddddd;
                text-align: left;
                padding: 8px;
            }

            tr:nth-child(even) {
                background-color: #dddddd;
            }
        </style>
    </head>
    <body onload="onload()" contenteditable="false">
        <div>
            Paragraph 1
            <ul>
                <li>
                    ABsfowiefjowi
                    <p>iusdhfiuweifuhwfuie wieuf</p>
                </li>
                <li>
                    Con mèo
                    <div>
                        <div>
                            <div>
                                mà
                                <div>chèo</div>
                                cây
                                <div>cau</div>
                                hỏi thăm chú chuột
                            </div>
                            đi đâu vắng nhà
                        </div>
                    </div>
                    dfg
                </li>
            </ul>
        </div>
        <a href="google.com"><span>Con meo</span></a>
        <p>Paragraph 2</p>

        <div id="ed" contenteditable style="min-height: 100px; padding: 5px; border: 1px solid black">
            <ul>
                <li>
                    Công hòa
                    <p>công gô việt nam</p>
                </li>
                <li>
                    <div>là của chúng mình</div>
                    <p>Thế giới này</p>
                </li>
            </ul>
        </div>
        <script type="text/javascript">
            function onload() {
                document.designMode = 'on';
                let ed = document.getElementById('ed');
                let selection = document.getSelection();

                const observer = new MutationObserver(mutationList => {
                    console.log('Mutation', mutationList);
                });

                observer.observe(ed, {
                    childList: true,
                    subtree: true,
                });

                ed.addEventListener('paste', e => {
                    let data = e.clipboardData;
                });

                ed.addEventListener('beforeinput', e => {
                    let r = e.getTargetRanges()[0];
                });

                ed.addEventListener('input', e => {
                    let r = e.getTargetRanges()[0];
                    if (!r) {
                        r = selection.getRangeAt(0);
                    }
                });

                // Add an event listener for the keydown event
                ed.addEventListener('keydown', function (event) {
                    // Check if the Enter key is pressed (key code 13)
                    if (event.keyCode === 13) {
                        event.preventDefault(); // Prevent the default behavior of the Enter key

                        // Get the current selection range
                        const selection = window.getSelection();
                        const range = selection.getRangeAt(0);

                        // Create a new paragraph element
                        const newParagraph = document.createElement('p');

                        // Check if the range is collapsed (no selection)
                        if (range.collapsed) {
                            // Insert the new paragraph element at the current position
                            range.insertNode(newParagraph);
                        } else {
                            // Split the current range into two separate ranges
                            const afterRange = range.cloneRange();
                            afterRange.setStartAfter(range.endContainer, range.endOffset);

                            // Extract the selected content into a document fragment
                            const fragment = range.extractContents();

                            // Append the fragment to the new paragraph element
                            newParagraph.appendChild(fragment);

                            // Insert the new paragraph element after the current range
                            afterRange.insertNode(newParagraph);

                            // Collapse the range after the new paragraph element
                            afterRange.setStartAfter(newParagraph);
                            afterRange.collapse(true);

                            // Update the selection to the collapsed range after the new paragraph
                            selection.removeAllRanges();
                            selection.addRange(afterRange);
                        }
                    }
                });
            }
        </script>
    </body>
</html>
