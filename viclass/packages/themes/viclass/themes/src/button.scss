.vi-theme {
    .vi-btn {
        @apply px-SP2 font-semibold flex items-center gap-[5px] justify-center;
    }

    --icon-btn-border-width: 0px;
    --icon-btn-radius: var(--BR2);

    --btn-small-border-width: 0px;
    --btn-small-radius: var(--BR2);

    --btn-normal-border-width: 0px;
    --btn-normal-radius: var(--BR2);

    .vi-icon-btn {
        --icon-btn-height: 24px;
        --icon-btn-font-size: 20px;
        @apply h-icon-btn px-SP1 leading-icon-btn text-icon-btn rounded-icon-btn border-icon-btn;
    }

    .vi-btn-normal {
        --btn-normal-height: 40px;
        @apply h-btn-normal px-SP4 leading-btn-normal rounded-btn-normal border-btn-normal;

        .vcon {
            @apply leading-btn-normal;
        }
    }

    .vi-btn-small {
        --btn-small-height: 30px;
        @apply h-btn-small px-SP2 leading-btn-small rounded-btn-small border-btn-small;

        .vcon {
            @apply leading-btn-small;
        }
    }

    .vi-btn-active {
        background-color: var(--P2);
    }

    .vi-btn-focus:not([disabled]) {
        background-image: var(--GR1);
        @apply text-BW1;
    }

    .vi-btn-focus:not([disabled]):hover {
        background-image: var(--GR2);
    }

    .vi-btn-disable,
    .vi-btn[disabled] {
        --btn-normal-border-width: 2px !important;
        --icon-btn-border-width: 2px !important;
        @apply text-BW4 bg-BW5 border-BW4;
    }

    .vi-btn-outline {
        border: solid;
        --btn-normal-border-width: 2px !important;
        --icon-btn-border-width: 2px !important;
        @apply border-P1;

        &:hover {
            @apply bg-P3;
        }
    }

    .vi-btn-gradient {
        background-image: var(--GR1) !important;
        @apply text-BW1;
    }

    // for the menu items inside mat-menu
    .vi-btn-menu-item {
        font-family: Montserrat !important;
        border: solid;
        width: inherit;
        --btn-normal-border-width: 1px !important;
        --icon-btn-border-width: 1px !important;
        --btn-normal-radius: var(--BR1) !important;
        --icon-btn-radius: var(--BR1) !important;
        @apply border-BW1 border-btn-normal font-normal;
    }

    // vi-btn but with the style of an input
    .vi-btn-input {
        @apply text-BW1 rounded-[12px] border border-BW4;

        &:focus {
            @apply outline-none;
        }
    }
}
