:root {
    --cursor-size: 16px;
    --cursor-transparent: 0;
    --cursor-color: black;
    --marker-icon-url: url('../assets/cursor-image/cursor_marker.svg');
}

.vi-theme {
    .vi-cursor-root {
        background-size: cover;
        position: fixed;
        pointer-events: none;
        justify-content: center;
        align-items: center;
        display: flex;
        top: 0;
        left: 0;

        .vi-cursor-avatar {
            translate: calc(var(--cursor-size) - 5px) calc(var(--cursor-size) - 5px);
            width: calc(var(--cursor-size) + 5px);
            height: calc(var(--cursor-size) + 5px);
            border-radius: 100%;
            background-position: center;
            background-image: url(../assets/avatar-man.png);
            background-size: cover;
            background-repeat: no-repeat;
        }
    }

    .vi-cursor {
        background-size: cover;
        position: absolute;
        pointer-events: none;
        justify-content: center;
        align-items: center;
        display: flex;

        height: calc(var(--cursor-size) * 1px);
        width: calc(var(--cursor-size) * 1px);
        top: calc(var(--cursor-size) / -2 * 1px);
        left: calc(var(--cursor-size) / -2 * 1px);
        opacity: calc(1 - var(--cursor-transparent));
        font-size: calc(var(--cursor-size) * 1px) !important;
        line-height: calc(var(--cursor-size) * 1px) !important;
        color: var(--cursor-color);

        .path1::before,
        .path2::before,
        .path3::before {
            margin-top: calc(var(--cursor-size) / -2 * 1px);
            margin-left: calc(var(--cursor-size) / -2 * 1px);
            position: absolute;
            color: var(--cursor-color);
        }
        .path2:before {
            color: var(--cursor-color);
        }
    }

    .vi-cursor_presenter {
        background: url('../assets/cursor-image/cursor_presenter.svg') !important;
    }

    .vi-cursor_default {
        background: url('../assets/cursor-image/cursor_default.svg') !important;
    }

    .vi-cursor_brush-default {
        background: url('../assets/cursor-image/cursor_brush-default.svg') !important;
    }

    .vi-cursor_hand {
        background: url('../assets/cursor-image/cursor_hand.svg') !important;
    }

    .vi-cursor_marker {
        background: url('../assets/cursor-image/cursor_marker.svg') !important;
    }

    .vi-cursor_eraser {
        background: url('../assets/cursor-image/cursor_eraser.svg') !important;
    }

    .vi-cursor_add {
        background: url('../assets/cursor-image/cursor_add.svg') !important;
    }

    .vi-cursor_text {
        background: url('../assets/cursor-image/cursor_text.svg') !important;
    }

    .vi-cursor_zoom-in {
        background: url('../assets/cursor-image/cursor_zoom-in.svg') !important;
    }

    .vi-cursor_zoom-out {
        background: url('../assets/cursor-image/cursor_zoom-out.svg') !important;
    }
}
