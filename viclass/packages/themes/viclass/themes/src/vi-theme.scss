/**
Common styles for all viclass.
Since this style might be loaded inside a third party website,
it make sense to scoped everything so that we can turn on / off vi-theme easily
*/
.vi-theme {
    /******** THEME VARIABLE DEFINITION *******************/
    --P1: 0 174 239;
    ---P1: rgb(0, 174, 239);
    --P2: 141 228 255;
    ---P2: rgb(141, 228, 255);
    --P3: 216 248 255;
    ---P3: rgb(216, 248, 255);
    //  Secondary
    --SC1: 219 0 255;
    ---SC1: rgb(219, 0, 255);
    --SC2: 49 227 124;
    ---SC2: rgb(49, 227, 124);
    --SC3: 255 214 0;
    ---SC3: rgb(255, 214, 0);
    --SC4: 255 122 0;
    ---SC4: rgb(255, 122, 0);
    --SC5: 255 0 46;
    ---SC5: rgb(255, 0, 46);
    //  Pastel
    --PAS1: 255 246 202;
    ---PAS1: rgb(255, 246, 202);
    --PAS2: 237 255 130;
    ---PAS2: rgb(237, 255, 130);
    --PAS3: 255 221 182;
    ---PAS3: rgb(255, 221, 182);
    --PAS4: 189 255 208;
    ---PAS4: rgb(189, 255, 208);
    --PAS5: 193 223 255;
    ---PAS5: rgb(193, 223, 255);
    --PAS7: 255 219 225;
    ---PAS6: rgb(255, 219, 225);

    //  Grayscale
    --BW1: 18 20 20;
    ---BW1: rgb(18, 20, 20);
    --BW2: 54 58 62;
    ---BW2: rgb(54, 58, 62);
    --BW3: 98 103 107;
    ---BW3: rgb(98, 103, 107);
    --BW4: 164 173 180;
    ---BW4: rgb(164, 173, 180);
    --BW5: 240 246 248;
    ---BW5: rgb(240, 246, 248);
    --BW6: 251 251 251;
    ---BW6: rgb(251, 251, 251);
    --BW7: 255 255 255;
    ---BW7: rgb(255, 255, 255);

    //  gradient
    --GR1: linear-gradient(270deg, #f3a9ff 2.08%, #bbedff 67.71%);
    --GR2: linear-gradient(264deg, #b0eaff 15.07%, #f2a0ff 100%);

    // transparent
    --TP1: rgb(0, 66, 75, 0.2);
    --TP2: rgb(0, 0, 0, 0.2);
    --TP3: rgb(255, 255, 255, 0.66);

    //  shadow
    --SH1: 0px 5px 20px var(--TP1);

    // shadow inner
    --SHI1: inset 1px 1px 3px 0 rgba(0, 0, 0, 0.25);
    --SHI2: inset 20px 0px 20px -20px rgba(102, 213, 255, 0.4), inset -20px 0px 20px -20px rgba(102, 213, 255, 0.4);

    // shadow outer
    --SHOL1: 20px 0px 20px -20px rgba(102, 213, 255, 0.4);

    // spacing
    --SP1: 5px;
    --SP2: 10px;
    --SP3: 3px;
    --SP4: 15px;

    --BR1: 10px;
    --BR2: 15px;
    --BR3: 12px;

    /***************************************/

    a {
        @apply cursor-pointer;
    }

    /******* VICLASS ICON DEFINITION *********************/
    .vcon,
    .vcon-general {
        font-size: 20px;
        line-height: 20px;
    }

    .vcon-link {
        display: block;
        height: 20px;
        line-height: 20px;
        color: rgb(var(--BW1));

        &:hover {
            text-decoration: none;
            color: rgb(var(--BW1));
        }

        i .vcon {
            line-height: inherit;
        }
    }

    @keyframes loaderSpinning {
        0% {
            transform: scale(1) rotate(0deg);
        }

        50% {
            transform: scale(0.5) rotate(180deg);
        }

        100% {
            transform: scale(1) rotate(360deg);
        }
    }

    .vcon_mini-spinner {
        animation: loaderSpinning 0.8s linear 0s infinite normal;
    }

    .hide-browser-cursor {
        --cursor-type: none !important;

        * {
            cursor: var(--cursor-type);
        }
    }

    .show-browser-cursor {
        --cursor-type: auto; // reset to default

        .vi-doc-boundary {
            .vi-b-resize-handle {
                pointer-events: visible;

                &.h-0-0 {
                    cursor: nw-resize;
                }

                &.h-1-0 {
                    cursor: n-resize;
                }

                &.h-2-0 {
                    cursor: ne-resize;
                }

                &.h-0-1,
                &.h-2-1 {
                    cursor: ew-resize;
                }

                &.h-0-2 {
                    cursor: sw-resize;
                }

                &.h-1-2 {
                    cursor: s-resize;
                }

                &.h-2-2 {
                    cursor: se-resize;
                }
            }
        }
    }

    /******* BOUNDARY DEFINITION *************************/
    // viclass editors use boundary to define working areas of documents, hence it is considered something
    // quite fundamental for viclass, hence its style is defined here
    .vi-doc-boundary {
        .vi-b-border {
            @apply stroke-1 stroke-[rgb(var(--P1))] fill-none pointer-events-none;
            vector-effect: non-scaling-stroke;
        }

        .vi-b-handle {
            @apply stroke-[10px] invisible fill-none;
            cursor: move;
        }

        [hidden] {
            display: none !important;
        }

        .vi-b-resize-handle {
            @apply stroke-1 stroke-[var(--G0)] fill-[rgb(var(--P1))];
            vector-effect: non-scaling-stroke;
        }
    }

    .vi-wrapping-unbounded-svg-layer::-webkit-scrollbar {
        display: none;
    }

    .vi-select-tool-align-line {
        @apply stroke-1 stroke-[rgb(var(--P1))] pointer-events-none;
    }

    .cdk-overlay-container {
        z-index: 1000;
    }
}

.disable-user-select {
    * {
        user-select: none;
        -webkit-touch-callout: none; /* iOS-specific: disables long-press menu */
    }
}

html.html-vi-word-doc {
    ::-webkit-scrollbar {
        width: 6px;
    }

    ::-webkit-scrollbar-track {
        background-color: transparent;
        -webkit-border-radius: 6px;
        border-radius: 5px;
    }

    ::-webkit-scrollbar-thumb {
        -webkit-border-radius: 6px;
        border-radius: 5px;
        @apply bg-BW4;
    }
}

button.v-tool-btn,
button.vi-btn,
.click-indicator {
    transition: background-color 0.1s ease;

    &:active {
        background-color: var(--click-indicator-bg, rgb(var(--PAS5))) !important;
    }
}
