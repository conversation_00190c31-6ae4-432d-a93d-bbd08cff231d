{"registerPojoPackages": ["vinet.ccs.pojo", "portal.lsession.pojo.activity"], "dbConf": {"connectionString": "mongodb://localhost:27017", "dbName": "viclass"}, "userPeerAlivePeriod": 180000, "classroomAliveMs": 1800000, "syncerPeerAlivePeriod": 86400000, "editorBackends": {"1": "http://localhost:18010", "2": "http://localhost:18011", "3": "http://localhost:18012", "4": "http://localhost:18013", "5": "http://localhost:18014"}, "suggestionNamesFile": "conf/suggestion-names.txt", "kafkaConfig": {"processPoolSize": 10, "consumerConfigs": [{"consumerName": "notifications-consumer", "topics": ["notifications"], "propertiesPath": "conf/kafka.consumer.properties"}], "reactorConfigs": [{"reactorName": "notification-event-reactor", "eventToTopicMap": {"NotificationEvent": ["notifications"]}}]}, "seConf": {"userService": {"serviceId": "US01", "serviceType": "US", "host": "localhost", "port": 1122}, "lSessionService": {"serviceId": "LS01", "serviceType": "LS", "host": "localhost", "port": 1133}, "notificationService": {"serviceId": "NS01", "serviceType": "NS", "host": "localhost", "port": 1155}, "metadataService": {"serviceId": "MDS01", "serviceType": "MDS", "host": "localhost", "port": 1166}}}