package vinet.ccs.actor

import kotlinx.coroutines.CompletableDeferred

enum class ActorTaskType(val internal: Boolean = false) {
    CheckReport(true),
    ReloadRegistration(true),
    KickOutPeer(true),
    Report,
    LeaveClass,
    NewQuestion,
    StopQuestion,
    AcceptPresentation,
    StopPresentation,
    RaiseHand,
    ReqShareScreen,
    AcceptRaiseHand,
    AcceptShareScreen,
    CancelRaiseHand,
    RejectRaiseHand,
    RejectShareScreen,
    UserOffline,
    UserOnline,
    UserAway,
    UserIdle,
    CancelShareScreen,
    RequestPinTab,
    CancelRequestPinTab,
}

class ActorTask(val taskType: ActorTaskType, val action: suspend () -> Any) {
    val completable = CompletableDeferred<Any>()
}
