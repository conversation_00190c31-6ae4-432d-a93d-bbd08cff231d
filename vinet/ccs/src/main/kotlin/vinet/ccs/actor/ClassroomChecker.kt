package vinet.ccs.actor

import com.google.common.util.concurrent.ThreadFactoryBuilder
import common.libs.logger.Logging
import kotlinx.coroutines.*
import org.koin.core.component.KoinComponent
import org.koin.core.component.get
import org.koin.core.component.inject
import portal.datastructures.lsession.LSessionStatus
import portal.datastructures.lsession.UserAvailableStatus
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.config.ConfigBean
import vinet.ccs.db.DatabaseService
import vinet.ccs.gateways.ClassroomServiceGateway
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.pojo.ClassroomStatus
import java.util.concurrent.Executors

/**
 * Manages checks for a specific classroom (`roomId`) to detect sustained inactivity
 * (no actors remaining for `classroomAliveMs`) and trigger a shutdown.
 * It uses a combination of direct notifications from [ActorManager] and a periodic
 * fallback check, ensuring thread-safe state evaluation via a dedicated single-threaded context.
 */
class ClassroomChecker constructor(
    val roomId: String,
) : KoinComponent, Logging {
    override val loggerName: String = "${javaClass.name}[$roomId]"

    // --- Dependencies ---
    private val configBean: ConfigBean by inject()
    private val actorManager: ActorManager by inject()
    private val databaseService: DatabaseService by inject()
    private val classroomInfoCache: ClassroomInfoCache by inject()
    private val lSessionServiceGateway: LSessionServiceGateway by inject()
    private val classroomServiceGateway: ClassroomServiceGateway by inject()
    private val userAvailableStatusHandler: UserAvailableStatusHandler = get()

    // --- Coroutine Scopes and Contexts ---

    // Main scope for launching the checker loop and related asynchronous tasks.
    // SupervisorJob ensures that failures in child coroutines (like a single checkRoom execution)
    // don't cancel the entire scope, allowing the loop to potentially continue.
    private val scope = CoroutineScope(
        Dispatchers.Default + SupervisorJob() + CoroutineName("ClassroomCheckerScope-$roomId")
    )

    // Holds the active Job for the main periodic check loop (returned by scope.launch).
    private var checkerJob: Job? = null

    // Dedicated single-threaded context for this specific room instance.
    // Used with `withContext` to ensure that checking actor presence and deciding
    // to shut down is serialized, preventing race conditions. Lazily initialized.
    private val singleContext: ExecutorCoroutineDispatcher by lazy {
        Executors.newSingleThreadExecutor(
            ThreadFactoryBuilder().setNameFormat("ClassroomCheckerLockCtx-$roomId-%d").build()
        ).asCoroutineDispatcher()
    }

    // --- State for sustained emptiness check ---
    @Volatile // Ensure visibility for firstEmptyTimestamp if accessed outside singleContext (though primarily inside)
    private var firstEmptyTimestamp: Long? = null
    private var shutdownJob: Job? = null // Job for the delayed final shutdown check

    init {
        // Automatically start the check loop when an instance is created.
        startPeriodicChecks()
    }

    /**
     * Launches the main coroutine that periodically executes [checkRoom].
     * Ensures only one check loop runs per instance.
     */
    private fun startPeriodicChecks() {
        // Prevent starting multiple concurrent loops for the same room.
        if (checkerJob?.isActive == true) {
            logger.warn("Periodic checks already running.")
            return
        }

        logger.info("Starting periodic room checks every ${configBean.classroomAliveMs}ms.")
        checkerJob = scope.launch(CoroutineName("ClassroomCheckerLoop-$roomId")) {
            try {
                // Loop continues as long as the coroutine scope is active.
                while (isActive) {
                    // Wait for the configured interval before the next check.
                    delay(configBean.classroomAliveMs)
                    logger.debug("Initiating periodic room check.")

                    // Perform the check within a try-catch to handle potential errors
                    // without terminating the entire loop.
                    try {
                        checkRoom()
                        // If checkRoom initiated shutdown, the loop will be cancelled shortly
                        // when cancel() is called after removeActorChecker.
                    } catch (e: CancellationException) {
                        logger.info("Periodic checkRoom iteration cancelled.")
                        throw e
                    } catch (t: Throwable) {
                        logger.error("Error during periodic checkRoom, continuing loop.", t)
                    }
                }
            } catch (e: CancellationException) {
                logger.info("Periodic check loop cancelled.")
            } catch (t: Throwable) {
                logger.error("Fatal error in periodic check loop!", t)
            } finally {
                logger.info("Periodic check loop finished.")
            }
        }
    }

    /**
     * Periodic check acting as a fallback/synchronizer.
     * Delegates to internal handlers for consistent state management.
     */
    private suspend fun checkRoom() {
        logger.debug("Periodic room check evaluation...")
        try {
            withContext(singleContext) {
                logger.debug("Acquired lock for periodic check. Current firstEmptyTimestamp: $firstEmptyTimestamp, shutdownJob active: ${shutdownJob?.isActive}")
                val remainingActors = actorManager.getActors(roomId)
                logger.debug("Periodic check: ${remainingActors.size} actors in room.")

                if (remainingActors.isEmpty()) {
                    onRoomBecameEmptyInternal()
                } else {
                    onActorJoinedInternal()
                }
                logger.debug("Released lock after periodic check. New firstEmptyTimestamp: $firstEmptyTimestamp, shutdownJob active: ${shutdownJob?.isActive}")
            }
        } catch (e: CancellationException) {
            logger.info("Periodic checkRoom iteration cancelled.")
            throw e // Propagate to stop the loop if checker itself is cancelled
        } catch (t: Throwable) {
            // Log non-cancellation errors from withContext block but don't let them kill the periodic loop.
            logger.error("Error during periodic checkRoom's synchronized block.", t)
        }
    }

    /**
     * PUBLIC: Called by ActorManager when an actor is added to this room.
     * Ensures operations run on the [singleContext].
     */
    suspend fun onActorJoined() {
        logger.debug("Notification received: Actor joined.")
        withContext(singleContext) {
            logger.debug("Acquired lock for onActorJoined. Current firstEmptyTimestamp: $firstEmptyTimestamp, shutdownJob active: ${shutdownJob?.isActive}")
            onActorJoinedInternal()
            logger.debug("Released lock after onActorJoined. New firstEmptyTimestamp: $firstEmptyTimestamp, shutdownJob active: ${shutdownJob?.isActive}")
        }
    }

    /**
     * PUBLIC: Called by ActorManager when the room might have become empty.
     * Ensures operations run on the [singleContext] and re-verifies emptiness.
     */
    suspend fun onRoomBecameEmpty() {
        logger.debug("Notification received: Room became potentially empty.")
        withContext(singleContext) {
            logger.debug("Acquired lock for onRoomBecameEmpty. Current firstEmptyTimestamp: $firstEmptyTimestamp, shutdownJob active: ${shutdownJob?.isActive}")
            val remainingActors = actorManager.getActors(roomId) // Re-verify
            if (remainingActors.isEmpty()) {
                onRoomBecameEmptyInternal()
            } else {
                logger.debug("onRoomBecameEmpty notification, but room still has ${remainingActors.size} actors. Resetting any prior empty state.")
                onActorJoinedInternal()
            }
            logger.debug("Released lock after onRoomBecameEmpty. New firstEmptyTimestamp: $firstEmptyTimestamp, shutdownJob active: ${shutdownJob?.isActive}")
        }
    }

    /**
     * Internal: Handles room becoming empty. MUST be called within [singleContext].
     * Sets timestamp, schedules delayed final check. Idempotent.
     */
    private fun onRoomBecameEmptyInternal() {
        if (firstEmptyTimestamp == null) {
            logger.info("Room is empty. Setting empty timestamp and scheduling final shutdown check in ${configBean.classroomAliveMs}ms.")
            firstEmptyTimestamp = System.currentTimeMillis()

            shutdownJob?.cancel(CancellationException("Superseded by new empty detection"))
            shutdownJob = scope.launch(CoroutineName("FinalShutdownCheckLauncher-$roomId")) {
                val selfJob = coroutineContext[Job]!!
                delay(configBean.classroomAliveMs)
                logger.debug("Shutdown job ${selfJob.hashCode()} finished delay, attempting final check.")
                try {
                    logger.debug("Shutdown job ${selfJob.hashCode()} entering singleContext.")
                    withContext(singleContext) { // Critical section for final check
                        logger.debug("Shutdown job ${selfJob.hashCode()} inside singleContext. Current this.shutdownJob: ${<EMAIL>?.hashCode()}, current this.firstEmptyTimestamp: ${<EMAIL>}")

                        val isCurrentJob = <EMAIL> == selfJob
                        val isTimestampSet = <EMAIL> != null
                        logger.debug("Shutdown job ${selfJob.hashCode()} - isCurrentJob: $isCurrentJob, isTimestampSet: $isTimestampSet")

                        if (isCurrentJob && isTimestampSet) {
                            logger.debug("Executing final shutdown check for job ${selfJob.hashCode()}.")
                            finalShutdownCheckInternal(selfJob)
                        } else {
                            logger.info("Final shutdown check for job ${selfJob.hashCode()} aborted: state changed or job superseded. isCurrentJob: $isCurrentJob, isTimestampSet: $isTimestampSet. Current actual shutdownJob: ${<EMAIL>?.hashCode()}.")
                        }
                        logger.debug("Shutdown job ${selfJob.hashCode()} inside singleContext, after state check.")
                    }
                    logger.debug("Shutdown job ${selfJob.hashCode()} exited singleContext successfully.")
                } catch (e: CancellationException) {
                    logger.info("Shutdown job (hash: $selfJob) was cancelled after delay.", e)
                    throw e // Re-throw cancellation
                } catch (t: Throwable) {
                    logger.error("Error executing shutdown job (hash: ${selfJob.hashCode()}) after delay.", t)
                    if (<EMAIL> == selfJob) {
                        <EMAIL> = null
                    }
                }
                logger.debug("Shutdown job ${selfJob.hashCode()} finished try/catch block.")
            }
            logger.info("assign shutdownJob ${shutdownJob.hashCode()}")
            shutdownJob?.invokeOnCompletion { ex ->
                if (ex is CancellationException) {
                    logger.debug("Shutdown job (hash: ${shutdownJob.hashCode()}) was cancelled: ${ex.message}")
                } else if (ex != null) {
                    logger.error("Shutdown job (hash: ${shutdownJob.hashCode()}) completed with error", ex)
                } else {
                    logger.debug("Shutdown job (hash: ${shutdownJob.hashCode()}) completed successfully (no error).")
                }
            }
        } else {
            logger.debug("Room already marked as empty (timestamp: $firstEmptyTimestamp). Shutdown check (job: ${shutdownJob?.hashCode()}, active: ${shutdownJob?.isActive}) is likely pending.")
        }
    }

    /**
     * Internal: Handles actor joining or room found not empty. MUST be called within [singleContext].
     * Clears empty timestamp, cancels pending shutdown. Idempotent.
     */
    private fun onActorJoinedInternal() {
        if (firstEmptyTimestamp != null || shutdownJob?.isActive == true) {
            logger.info("Room no longer considered empty. Cancelling pending shutdown check (job: ${shutdownJob?.hashCode()}). Resetting empty timer.")
            firstEmptyTimestamp = null
            shutdownJob?.cancel(CancellationException("Actor joined or room no longer empty, shutdown aborted."))
            shutdownJob = null
        } else {
            logger.debug("Room active, and no shutdown was pending. No action needed.")
        }
    }

    /**
     * Internal: Performs final verification if room is still empty after delay. MUST be called within [singleContext].
     */
    private suspend fun finalShutdownCheckInternal(selfJob: Job) {
        logger.info("Performing final shutdown check. Expected empty since: $firstEmptyTimestamp.")

        if (firstEmptyTimestamp == null) {
            logger.warn("Final shutdown check: Aborted as room no longer marked continuously empty (firstEmptyTimestamp is null).")
            // Consider if shutdownJob should be nulled more carefully if it's the current context's job
            if (this.shutdownJob?.isActive == false || this.shutdownJob == selfJob) { // Check if it's this job or already completed
                this.shutdownJob = null
            }
            return
        }

        try {
            val remainingActors = actorManager.getActors(roomId)
            if (remainingActors.isEmpty()) {
                val emptyDuration = System.currentTimeMillis() - firstEmptyTimestamp!!
                if (emptyDuration >= (configBean.classroomAliveMs - 500L)) { // 500ms tolerance
                    logger.info("Room confirmed empty for ${emptyDuration}ms. Initiating shutdown.")
                    this.firstEmptyTimestamp = null
                    this.shutdownJob = null // Nulled as part of successful path or if shutdownRoom throws
                    shutdownRoom()
                } else {
                    logger.warn("Room confirmed empty, but sustained period ($emptyDuration ms) < required (${configBean.classroomAliveMs}ms). Resetting timer.")
                	this.firstEmptyTimestamp = null // To allow onRoomBecameEmptyInternal to set a fresh one
                    this.shutdownJob = null
                	onRoomBecameEmptyInternal() // Restart the process with current conditions
                }
            } else {
                logger.info("Final shutdown check for: Actors (${remainingActors.size}) found. Aborting shutdown. Resetting empty timer.")
                this.firstEmptyTimestamp = null
                this.shutdownJob = null
            }
        } catch (e: CancellationException) {
            logger.info("finalShutdownCheckInternal cancelled.", e)
            throw e // Re-throw cancellation
        } catch (t: Throwable) {
            logger.error("Error during finalShutdownCheckInternal's main logic. Room will likely not shutdown via this attempt.", t)
            // Critical: Decide recovery. Resetting state might be risky without knowing the error.
            // For now, let the job fail. The periodic checker will eventually try again if the room is still empty.
            // If we null firstEmptyTimestamp here, the next periodic check will restart the timer.
            // this.firstEmptyTimestamp = null
            // this.shutdownJob = null
            // onRoomBecameEmptyInternal() // This could lead to rapid retries if getActors always fails.
            // It's safer to let the current shutdownJob fail and rely on the periodic check.
            // Ensure this job is no longer considered the active shutdownJob if it failed.
            if (this.shutdownJob == selfJob) {
                this.shutdownJob = null
            }
        }
    }


    suspend fun shutdownRoom() {
        logger.info("Executing shutdownRoom sequence...")
        try {
            val shutdownTasks = listOf(
                scope.async { classroomInfoCache.get(roomId)?.let { it.status = ClassroomStatus.STOPPED } },
                scope.async { databaseService.updateRoomStatus(roomId, ClassroomStatus.STOPPED) },
                scope.async { classroomServiceGateway.endClassroomAsync(roomId).await() },
                scope.async { lSessionServiceGateway.updateSessionStatusAsync(roomId, LSessionStatus.ENDED).await() }
            )
            awaitAll(*shutdownTasks.toTypedArray())
            logger.info("Room shutdown operations completed successfully.")
        } catch (t: Throwable) {
            logger.error("Error during shutdown process! Proceeding with checker removal.", t)
        } finally {
            logger.info("Requesting removal of ClassroomChecker from manager due to room shutdown.")
            actorManager.removeClassroomChecker(roomId) // This will trigger cancel()
        }
    }

    suspend fun cancel() {
        logger.info("Cancelling ClassroomChecker...")

        checkerJob?.cancel(CancellationException("ClassroomChecker explicitly cancelled (periodicJob)"))
        checkerJob = null
        logger.debug("Periodic check loop job cancelled.")

        // Volatile read for shutdownJob then cancel.
        // The modifications to this.shutdownJob are done within singleContext or here.
        val currentShutdownJob = this.shutdownJob
        this.shutdownJob = null // Prevent further use
        currentShutdownJob?.cancel(CancellationException("ClassroomChecker explicitly cancelled (shutdownJob)"))
        logger.debug("Pending shutdown job cancelled (if any).")

        val context = try { singleContext } catch (e: Throwable) {
            logger.error("Error accessing/initializing singleContext during cancel", e); null
        }
        if (context != null) {
            if (context.isActive) {
                try {
                    context.close()
                    logger.debug("Single-threaded context closed.")
                } catch (e: Throwable) { logger.error("Error closing singleContext", e) }
            } else { logger.debug("Single-threaded context was already inactive.") }
        } else { logger.debug("Single-threaded context could not be accessed/initialized.") }

        try {
            scope.cancel(CancellationException("ClassroomChecker explicitly cancelled (scope)"))
            logger.debug("Main checker scope cancelled.")
        } catch (e: Throwable) { logger.error("Error cancelling main checker scope", e) }

        logger.info("ClassroomChecker cancelled successfully.")

        // This loop for UserAvailableStatus.OFFLINE might be redundant if shutdownRoom was due to emptiness.
        // However, if cancel() is called for other reasons (e.g. app shutdown), it's relevant.
        val actorsInRoom = actorManager.getActors(roomId) // Re-fetch, might be empty
        if (actorsInRoom.isNotEmpty()) {
            logger.warn("Processing UserAvailableStatus.OFFLINE for ${actorsInRoom.size} actors during checker cancellation.")
        }
        for (userClassroomActor in actorsInRoom) {
            userAvailableStatusHandler.process(userClassroomActor, UserAvailableStatus.OFFLINE)
        }
    }
}