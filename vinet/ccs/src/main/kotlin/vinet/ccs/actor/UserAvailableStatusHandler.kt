package vinet.ccs.actor

import common.libs.logger.Logging
import jayeson.utility.concurrent.worker.batch.SharedExecutorBatchFutureWorker
import kotlinx.coroutines.Job
import org.bson.codecs.configuration.CodecRegistry
import org.koin.core.annotation.Singleton
import portal.datastructures.lsession.ShareScreenStatus
import portal.datastructures.lsession.UserAvailableStatus
import portal.lsession.pojo.toPojo
import portal.notification.pojo.ClassroomTarget
import portal.notification.pojo.Notification
import portal.notification.pojo.notidata.ShareScreenRemovedND
import portal.notification.pojo.notidata.UserAvailableStatusChangedND
import vinet.ccs.cache.CoordinatorStateCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.gateways.NotificationServiceGateway
import vinet.ccs.model.SignalMessage
import vinet.ccs.model.SignalType
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerStatus
import vinet.ccs.pojo.PeerType
import vinet.ccs.utility.getLSessionRegistrationIfExist
import vinet.ccs.utility.json
import vinet.ccs.utility.serializeNotificationToJsonNode
import java.time.Instant

/**
 * Handles updating the available status of a user within a classroom. This class processes changes
 * in user availability (online, offline, away, idle) and performs necessary actions such as
 * updating the LSession service, notifying other users, and managing peer connections.
 *
 * @property codecRegistry Codec registry for serializing and deserializing notifications.
 * @property batchWorker Shared executor batch worker for handling asynchronous tasks.
 * @property peerManager Manages peer connections within the classroom.
 * @property db Database service for data persistence.
 * @property coordStateCache Cache for coordinator state.
 * @property notificationSG Notification service gateway for sending notifications.
 * @property lsessionSG LSession service gateway for updating user availability.
 */
@Singleton
class UserAvailableStatusHandler(
    private val codecRegistry: CodecRegistry,
    private val batchWorker: SharedExecutorBatchFutureWorker<String, Unit>,
    private val peerManager: PeerManager,
    private val db: DatabaseService,
    private val coordStateCache: CoordinatorStateCache,
    private val notificationSG: NotificationServiceGateway,
    private val lsessionSG: LSessionServiceGateway,
) : Logging {

    private val notificationCodec = codecRegistry.get(Notification::class.java)

    /**
     * Processes a user's available status change, triggering appropriate actions.
     *
     * @param actor The [UserClassroomActor] representing the user.
     * @param userAvailableStatus The new [UserAvailableStatus].
     *
     * @return A [Job] representing the asynchronous task.
     */
    suspend fun process(actor: UserClassroomActor, userAvailableStatus: UserAvailableStatus): Job {
        try {
            return when (userAvailableStatus) {
                UserAvailableStatus.OFFLINE -> {
                    actor.send(ActorTaskType.UserOffline) { handleUserOffline(actor) }
                }
                UserAvailableStatus.ONLINE -> actor.send(ActorTaskType.UserOnline) { handleUserOnline(actor) }
                UserAvailableStatus.AWAY -> actor.send(ActorTaskType.UserAway) { handleUpdateUserStatus(actor, userAvailableStatus, "${actor.userProto.username} đang tạm vắng") }
                UserAvailableStatus.IDLE -> actor.send(ActorTaskType.UserIdle) { handleUpdateUserStatus(actor, userAvailableStatus, "${actor.userProto.username} đang tạm vắng") }
            }
        } catch (t: Throwable) {
            logger.error(
                "Error updating user {} status to {} in room {}: {}", actor.userId, userAvailableStatus, actor.roomId, t.message, t
            )
            throw t
        }
    }

    /**
     * Updates user status in LSession and sends notifications.
     *
     * @param actor The [UserClassroomActor] representing the user.
     * @param userAvailableStatus The new [UserAvailableStatus].
     * @param message An optional notification message.
     */
    private suspend fun handleUpdateUserStatus(
        actor: UserClassroomActor, userAvailableStatus: UserAvailableStatus, message: String = ""
    ) {
        actor.lsRegistration.userState.availableStatus = userAvailableStatus

        runCatching {
            lsessionSG.updateUserAvailableStatusAsync(actor.lsRegistration.id, actor.roomId, userAvailableStatus).await()
        }.onFailure {
            logger.error(
                "Failed to update LSession status for user ${actor.userId} in room ${actor.roomId}: ${it.message}", it
            )
        }

        runCatching {
            notifyUpdateUserAvailableStatus(actor, userAvailableStatus, message)
        }.onFailure {
            logger.error(
                "Failed to send status notification for user ${actor.userId} in room ${actor.roomId}: ${it.message}", it
            )
        }
    }

    /**
     * Handles user online event.
     *
     * @param actor The [UserClassroomActor] representing the user.
     */
    private suspend fun handleUserOnline(actor: UserClassroomActor) {
        handleUpdateUserStatus(
            actor, UserAvailableStatus.ONLINE, "${actor.userProto.username} đã trực tuyến"
        )
    }

    /**
     * Handles backend cleanup and status update when a user goes offline in a classroom session.
     *
     * - Skips processing if the user is already marked as OFFLINE.
     * - If the user was sharing their screen, stops the screen share and notifies others.
     * - Updates the user's availability status to OFFLINE and broadcasts the change.
     * - Attempts to remove any shared screen documents, logging errors if failed.
     * - Marks all of the user's active peer connections as INACTIVE and queues them for batch processing.
     *
     * This ensures consistent session state and proper resource cleanup when a user disconnects.
     */
    private suspend fun handleUserOffline(actor: UserClassroomActor) {
        if (actor.peer.info.status == PeerStatus.ACTIVE) {
            actor.peer.info.status = PeerStatus.INACTIVE
            batchWorker.submit(actor.peer.info.id)
        }

        val lsReg = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(actor.roomId, actor.userId).await().registration.toPojo()

        if (lsReg.userState.availableStatus == UserAvailableStatus.OFFLINE) {
            return
        }

        if (lsReg.userState.shareScreenStatus != ShareScreenStatus.NONE) {
            lsessionSG.updateShareScreenStatusAsync(actor.lsRegistration.id, ShareScreenStatus.NONE).await()

            val notification = Notification(
                actor.lsRegistration.userId,
                ClassroomTarget(actor.lsRegistration.lsId),
                "Học viên ${actor.userProto.username} đã rời khỏi lớp và tự tắt share screen",
                Instant.now().plusSeconds(300),
                ShareScreenRemovedND(
                    actor.lsRegistration.lsId, actor.lsRegistration.userId
                )
            )
            notificationSG.saveNotificationAsync(notification).await()
        }

        handleUpdateUserStatus(
            actor, UserAvailableStatus.OFFLINE, "${actor.userProto.username} đã ngoại tuyến"
        )

        runCatching { removeShareScreenDoc(actor) }.onFailure {
            logger.error(
                "Failed to remove shared screen doc for user ${actor.userId} in room ${actor.roomId}: ${it.message}", it
            )
        }
    }

    /**
     * Sends user available status change notification to all active browsers in the room.
     *
     * @param actor The [UserClassroomActor] representing the user.
     * @param userAvailableStatus The new [UserAvailableStatus].
     * @param message An optional notification message.
     */
    private fun notifyUpdateUserAvailableStatus(
        actor: UserClassroomActor, userAvailableStatus: UserAvailableStatus, message: String = ""
    ) {
        val notification = Notification(
            actor.userId, ClassroomTarget(actor.roomId), message, Instant.now().plusSeconds(300), UserAvailableStatusChangedND(
                actor.roomId, actor.userId, userAvailableStatus
            )
        )

        val msgData = json.objectNode().putPOJO(
            "notiData", serializeNotificationToJsonNode(notification, notificationCodec)
        )
        peerManager.filterAndSendAsync({
            SignalMessage("", it.info.id, SignalType.UserAvailableStatusChangedND, msgData, null)
        }) {
            it.info.roomId == actor.roomId && it.info.peerType == PeerType.BROWSER && it.info.status == PeerStatus.ACTIVE
        }
    }

    /**
     * Removes user's shared screen document and sends a notification.
     *
     * @param actor The [UserClassroomActor] representing the user.
     */
    private suspend fun removeShareScreenDoc(actor: UserClassroomActor) {
        val coords = db.removeByGlobalIdInRoom(actor.roomId, actor.lsRegistration.userId)
        if (coords.isNotEmpty()) {
            coords.forEach { coordStateCache.add(it) }
            val notification = Notification(
                actor.userId,
                ClassroomTarget(actor.roomId),
                "Học viên ${actor.userProto.username} đã rời khỏi lớp và tự tắt share screen",
                Instant.now().plusSeconds(300),
                ShareScreenRemovedND(actor.roomId, actor.userId)
            )
            notificationSG.saveNotificationAsync(notification).await()
        }
    }
}