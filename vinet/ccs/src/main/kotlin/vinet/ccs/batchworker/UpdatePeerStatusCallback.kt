package vinet.ccs.batchworker

import jayeson.utility.concurrent.worker.batch.SharedExecutorBatchFutureWorker
import org.koin.core.annotation.Factory
import org.koin.core.annotation.Singleton

@Singleton
class UpdatePeerStatusCallback constructor(

) : SharedExecutorBatchFutureWorker.BatchWorkerCallback<String, Unit> {

    override fun batchProcessed(
        worker: SharedExecutorBatchFutureWorker<String, Unit>,
        dataList: MutableList<String>,
        result: Unit?,
        error: Throwable?
    ) {
        // do nothing
    }

}