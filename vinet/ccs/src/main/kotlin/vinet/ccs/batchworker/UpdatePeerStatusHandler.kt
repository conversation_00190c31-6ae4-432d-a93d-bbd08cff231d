package vinet.ccs.batchworker

import common.libs.logger.Logging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.asCompletableFuture
import kotlinx.coroutines.launch
import org.koin.core.annotation.Singleton
import vinet.ccs.db.DatabaseService
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerStatus
import java.util.*
import java.util.concurrent.CompletableFuture
import java.util.function.Function

@Singleton
class UpdatePeerStatusHandler constructor(
    private val databaseService: DatabaseService,
    private val peerMan: PeerManager
) : Function<List<String>, CompletableFuture<Unit>>, Logging {

    private val scope = CoroutineScope(Dispatchers.IO)

    override fun apply(dataList: List<String>): CompletableFuture<Unit> {
        val grouped = dataList.map { Optional.ofNullable(peerMan.getPeer(it)) }
            .filter { it.isPresent }.map { it.get() }
            .groupBy { it.info.status }

        val job = scope.launch {
            grouped.forEach { (status, peers) ->
                val ids = peers.map { it.info.id }
                databaseService.updatePeerStatus(ids, status)
                if (status != PeerStatus.ACTIVE) {
                    /**
                     * Remove the  peer from the peer manager.
                     * This is done in a try-catch block to prevent the actor from failing to destroy if the peer removal fails.
                     * The peer removal is not critical to the actor's destruction.
                     */
                    try {
                        peerMan.removePeers(ids)
                    } catch (e: Throwable) {
                        logger.error("Error remove peer [{}] during update database", ids, e)
                    }
                }
            }
        }

        return job.asCompletableFuture()
    }

}
