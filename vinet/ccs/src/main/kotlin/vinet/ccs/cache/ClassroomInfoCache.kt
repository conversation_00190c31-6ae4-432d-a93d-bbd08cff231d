package vinet.ccs.cache

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Singleton
import vinet.ccs.db.DatabaseService
import vinet.ccs.pojo.ClassroomInfo
import java.util.*
import java.util.concurrent.TimeUnit


/**
 *
 * <AUTHOR>
 */
@Singleton
class ClassroomInfoCache constructor(
    private val db: DatabaseService
) : Logging {
    private val loader: CacheLoader<String, Optional<ClassroomInfo>> = object : CacheLoader<String, Optional<ClassroomInfo>>() {
        override fun load(key: String): Optional<ClassroomInfo> {
            return Optional.ofNullable(runBlocking(Dispatchers.IO) { db.findRoom(key) })
        }
    }

    private val cache: LoadingCache<String, Optional<ClassroomInfo>> = CacheBuilder.newBuilder()
        .expireAfterAccess(60, TimeUnit.MINUTES)
        .maximumSize(100_000)
        .build(loader)

    suspend fun add(classroomInfo: ClassroomInfo) = withContext(Dispatchers.IO) {
        cache.put(classroomInfo.id, Optional.of(classroomInfo))
    }

    suspend fun get(roomId: String): ClassroomInfo? = withContext(Dispatchers.IO) {
        cache.get(roomId).orElseGet { null }
    }

    fun invalidate(roomId: String) {
        cache.invalidate(roomId)
    }
}
