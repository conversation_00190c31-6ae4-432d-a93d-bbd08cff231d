package vinet.ccs.cache

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Singleton
import vinet.ccs.db.DatabaseService
import vinet.ccs.pojo.CoordinatorState
import java.util.*
import java.util.concurrent.TimeUnit


/**
 *
 * <AUTHOR>
 */
@Singleton
class CoordinatorStateCache constructor(
    private val db: DatabaseService
) : Logging {
    private val loader: CacheLoader<String, Optional<CoordinatorState>> =
        object : CacheLoader<String, Optional<CoordinatorState>>() {
            override fun load(key: String): Optional<CoordinatorState> {
                return Optional.ofNullable(runBlocking(Dispatchers.IO) { db.getCoordinatorState(key) })
            }
        }

    private val cache: LoadingCache<String, Optional<CoordinatorState>> = CacheBuilder.newBuilder()
        .expireAfterAccess(60, TimeUnit.MINUTES)
        .maximumSize(100_000)
        .build(loader)

    suspend fun add(coordinatorState: CoordinatorState) = withContext(Dispatchers.IO) {
        cache.put(coordinatorState.id, Optional.of(coordinatorState))
    }

    suspend fun get(id: String): CoordinatorState? = withContext(Dispatchers.IO) {
        cache.get(id).orElseGet { null }
    }

    suspend fun invalidate(id: String) = withContext(Dispatchers.IO) {
        cache.invalidate(id)
    }
}
