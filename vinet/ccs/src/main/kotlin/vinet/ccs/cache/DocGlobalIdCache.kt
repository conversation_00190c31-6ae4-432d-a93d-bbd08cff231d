package vinet.ccs.cache

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Singleton
import vinet.ccs.db.DatabaseService
import java.util.*
import java.util.concurrent.TimeUnit


/**
 *
 * <AUTHOR>
 */
@Singleton
class DocGlobalIdCache constructor(
    private val db: DatabaseService
) : Logging {
    private val loader: CacheLoader<DocGlobalIdKey, Optional<String>> =
        object : CacheLoader<DocGlobalIdKey, Optional<String>>() {
            override fun load(key: DocGlobalIdKey): Optional<String> {
                return Optional.ofNullable(
                    runBlocking(Dispatchers.IO) { db.getDocGlobalId(key.coordinatorId, key.channelCode, key.localId) }
                )
            }
        }

    private val cache: LoadingCache<DocGlobalIdKey, Optional<String>> = CacheBuilder.newBuilder()
        .expireAfterAccess(30, TimeUnit.MINUTES)
        .maximumSize(10_000)
        .build(loader)

    suspend fun get(key: DocGlobalIdKey): String? = withContext(Dispatchers.IO) {
        cache.get(key).orElseGet { null }
    }

    suspend fun add(key: DocGlobalIdKey, globalId: String) = withContext(Dispatchers.IO) {
        cache.put(key, Optional.of(globalId))
    }

    fun invalidate(key: DocGlobalIdKey) {
        cache.invalidate(key)
    }
}
