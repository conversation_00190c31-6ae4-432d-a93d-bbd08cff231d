package vinet.ccs.cache

import com.google.common.cache.CacheBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import common.libs.logger.Logging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Singleton
import vinet.ccs.db.LocalContentDatabaseService
import vinet.ccs.pojo.LocalContentState
import java.util.*
import java.util.concurrent.TimeUnit


/**
 *
 * <AUTHOR>
 */
@Singleton
class LocalContentStateCache constructor(
    private val db: LocalContentDatabaseService
) : Logging {
    private val loader: CacheLoader<String, Optional<LocalContentState>> =
        object : CacheLoader<String, Optional<LocalContentState>>() {
            override fun load(key: String): Optional<LocalContentState> {
                return Optional.ofNullable(runBlocking(Dispatchers.IO) { db.getOrCreateLocalContentState(key) })
            }
        }

    private val cache: LoadingCache<String, Optional<LocalContentState>> = CacheBuilder.newBuilder()
        .expireAfterAccess(60, TimeUnit.MINUTES)
        .maximumSize(100_000)
        .build(loader)

    suspend fun add(contentState: LocalContentState) = withContext(Dispatchers.IO) {
        cache.put(contentState.id, Optional.of(contentState))
    }

    suspend fun get(id: String): LocalContentState? = withContext(Dispatchers.IO) {
        cache.get(id).orElseGet { null }
    }

    suspend fun invalidate(id: String) = withContext(Dispatchers.IO) {
        cache.invalidate(id)
    }
}
