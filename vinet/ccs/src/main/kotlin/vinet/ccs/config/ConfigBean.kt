package vinet.ccs.config

import portal.kafka.api.configs.KafkaConfig

data class ConfigBean(
    var seConf: ServiceExplorerConfig,
    var registerPojoPackages: List<String> = emptyList(),
    var dbConf: DBConfig,

    var userPeerAlivePeriod: Long = 180000,      // number of millisecond since last seen that a peer considered to be alive
    var syncerPeerAlivePeriod: Long = 86400000,  // number of millisecond since last seen that a synchronizers peer considered to be alive

    // map channel code with url, each editor has a fixed channel code, and predefined, therefore
    // we can have this config, where the url represent the endpoint for calling backend for saving data
    var editorBackends: Map<Int, String>,

    // reference from https://www.famousscientists.org/list/
    var suggestionNamesFile: String,
    var kafkaConfig: KafkaConfig,

    var classroomAliveMs: Long = 1800000,

    var signalMessageBufferSize: Int = 10
)
