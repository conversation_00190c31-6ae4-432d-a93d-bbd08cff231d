package vinet.ccs.config

data class ServiceExplorerConfig(
    @JvmField var userService: ServicePeer,
    @JvmField var lSessionService: ServicePeer,
    @JvmField var notificationService: ServicePeer,
    @JvmField var metadataService: ServicePeer,
) {
    data class ServicePeer(
        @JvmField var serviceId: String? = null,
        @JvmField var serviceType: String? = null,
        @JvmField var host: String,
        @JvmField var port: Int = -1
    )
}
