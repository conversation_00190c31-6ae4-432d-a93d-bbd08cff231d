package vinet.ccs.controllers

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.koin.core.annotation.Singleton
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.gateways.UserServiceGateway
import vinet.ccs.model.CreateJitsiJwtTokenRequest
import vinet.ccs.model.ErrorResponse
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.ClassroomStatus
import vinet.ccs.utility.*

/**
 * Controller class for managing conference-related operations, specifically generating Jitsi JWT tokens.
 *
 * This class handles requests to generate JWT tokens for users to join Jitsi conferences within a classroom environment.
 * It validates user permissions, room states, and learning session registrations before issuing the token.
 */
@Singleton
class ConferenceCtrl constructor(
    private val db: DatabaseService,
    private val peerMan: PeerManager,
    private val classroomInfoCache: ClassroomInfoCache,
    private val userSG: UserServiceGateway,
    private val lsessionSG: LSessionServiceGateway,
    private val jitsiJwt: JitsiJwtUtil
) : Logging {

    /**
     * Generates a Jitsi JWT token for a user to join a conference in a classroom.
     *
     * This function validates the user's request, checks their permissions, room state,
     * and learning session registration status before generating and returning a JWT token.
     *
     * @param call The ApplicationCall representing the incoming request.
     */
    suspend fun generateJitsiJwtToken(call: ApplicationCall) {
        // Receive the request containing peerId, userId, and roomId.
        val request = call.receive<CreateJitsiJwtTokenRequest>()

        // Retrieve peer information based on peerId.
        val peer = getPeerIfExist(logger, call, peerMan, request.peerId)?.info ?: return

        // Extract roomId from the peer information.
        val roomId: String = peer.roomId

        // Validate if the user associated with the peer matches the requested userId.
        if (peer.userId != request.userId) {
            // Respond with a BadRequest status and an error message if the user does not match.
            return call.respond(HttpStatusCode.Forbidden, "Peer doesn't match user.")
        }

        // Validate if the roomId associated with the peer matches the requested roomId.
        if (roomId != request.roomId) {
            // Respond with a BadRequest status and an error message if the room does not match.
            return call.respond(HttpStatusCode.Forbidden, "Peer's room is not matched.")
        }

        // Retrieve room information from cache based on roomId.
        val classroomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, roomId) ?: return
        // Retrieve user profile.
        val userProfile = getUserProfileIfExist(logger, call, userSG, request.userId) ?: return

        // Validate the room state to ensure it is opened or created by the room owner.
        // RoomState.CREATED is for when the user just created the classroom and enter it, the online report is not yet available
        if (classroomInfo.status != ClassroomStatus.OPENED && (classroomInfo.status == ClassroomStatus.CREATED && classroomInfo.owner != peer.userId)) {
            // Log an error if the room is not in an appropriate state.
            logger.error("Unable to generate JWT Token. Room {} not started. Room status {} room owner {} peer user id {}", roomId, classroomInfo.status, classroomInfo.owner, peer.userId)
            // Respond with a BadRequest status and an error message.
            return call.respond(HttpStatusCode.Forbidden, ErrorResponse(classroomInfo.status.toResponseError().name, "Room not started"))
        }

        /**
         * TODO: determine the role of the user. Owner of the room will have admin role.
         */
        // Generate and respond with the Jitsi JWT token.
        call.respond(HttpStatusCode.OK, mapOf("token" to jitsiJwt.tokenForRoom(classroomInfo, userProfile)))
    }
}