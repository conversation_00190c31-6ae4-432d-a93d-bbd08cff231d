package vinet.ccs.controllers

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.koin.core.annotation.Singleton
import vinet.ccs.cache.CoordinatorStateCache
import vinet.ccs.cache.LocalContentStateCache
import vinet.ccs.db.LocalContentDatabaseService
import vinet.ccs.model.UpdateLocalContentRequest
import vinet.ccs.utility.getCoordIfExist
import vinet.ccs.utility.requireParams

@Singleton
class LocalContentCtrl constructor(
    private val coordStateCache: CoordinatorStateCache,
    private val localContentCache: LocalContentStateCache,
    private val db: LocalContentDatabaseService,
) : Logging {
    suspend fun getLocalContentStateById(call: ApplicationCall) {
        if (!call.requireParams(listOf("id"))) return

        val id: String = call.parameters["id"]!!
        try {
            getCoordIfExist(logger, call, coordStateCache, id) ?: throw NoSuchElementException("Not found coordinator with id: $id")
            val contentState = localContentCache.get(id) ?: throw NoSuchElementException("Not found local content state with id: $id")
            return call.respond(HttpStatusCode.OK, contentState)
        } catch (e: NoSuchElementException) {
            logger.error("failed to fetch local content state {}, not found ... ", id, e)
            return call.respond(HttpStatusCode.NotFound, e.message ?: "not found")
        } catch (t: Throwable) {
            logger.error("failed to fetch local content state {} ... ", id, t)
            return call.respond(HttpStatusCode.InternalServerError, "failed to load content")
        }
    }

    suspend fun updateLocalContentState(call: ApplicationCall) {
        val request = try {
            call.receive<UpdateLocalContentRequest>()
        } catch (e: Throwable) {
            logger.error("failed to parse request: ", e)
            call.respond(HttpStatusCode.BadRequest, "failed to parse request")
            return
        }

        getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return

        try {
            val result = db.updateLocalContentState(request.coordStateId, request.changes)
            localContentCache.add(result)
            return call.respond(HttpStatusCode.OK)
        } catch (t: Throwable) {
            logger.error("update local content failed for coord {}... ", request.coordStateId, t)
            return call.respond(HttpStatusCode.InternalServerError, "update local content failed")
        }

    }
}