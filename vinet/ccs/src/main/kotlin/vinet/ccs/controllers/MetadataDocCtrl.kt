package vinet.ccs.controllers

import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import org.bson.BsonType
import org.koin.core.annotation.Singleton
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.cache.CoordinatorStateCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.gateways.MetadataServiceGateway
import vinet.ccs.metatadata.model.*
import vinet.ccs.model.DocumentInfoResponse
import vinet.ccs.model.SignalMessage
import vinet.ccs.model.SignalType
import vinet.ccs.model.UpdateDocInfoMessage
import vinet.ccs.peer.Peer
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerStatus
import vinet.ccs.pojo.PeerType
import vinet.ccs.utility.*


/**
 *
 * <AUTHOR>
 */
@Singleton
class MetadataDocCtrl constructor(
    private val peerMan: PeerManager,
    private val coordStateCache: CoordinatorStateCache,
    private val classroomInfoCache: ClassroomInfoCache,
    private val metadataServiceGateway: MetadataServiceGateway,
    private val db: DatabaseService,
) : Logging {

    suspend fun createMetadataDocs(call: ApplicationCall) {
        val request = call.receive<CreateDocumentInfoRequest>()

        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return

        // Ensure the details list matches the lengths of coordStateIds, docGlobalIds, and editorTypes

        try {

            val responses = mutableListOf<DocumentInfoResponse>()

            for (doc in request.docs) {
                val coordStateId = request.coordStateId
                val docGlobalId = doc.docGlobalId
                val editorType = doc.editorType
                val detail = doc.details

                // Validate owner information
                if (detail.ownerUserId != peerInfo.userId || detail.ownerRegId != peerInfo.lsRegId) {
                    logger.warn("Owner information does not match for peerId: ${request.peerId}")
                    continue
                }

                val coordState = getCoordIfExist(logger, call, coordStateCache, coordStateId) ?: continue
                val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: continue

                if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) continue

                val grpcRes = metadataServiceGateway.createMetadataDoc(
                    docGlobalId,
                    editorType,
                    CLASSROOM_METADATA_DOC_TYPE,
                    detail
                ).await()

                val docInfo = DocumentInfoResponse(
                    docGlobalId = grpcRes.metadata.docId,
                    editorType = grpcRes.metadata.editorType,
                    docType = grpcRes.metadata.metadataType,
                    details = defaultMapper.readValue(grpcRes.metadata.metadataDetails, DocumentInfoDetail::class.java)
                )

                logger.info("Metadata doc {} is created {}", docInfo.docGlobalId, docInfo)
                responses.add(docInfo)
            }

            call.respond(HttpStatusCode.OK, responses)
        } catch (e: Throwable) {
            logger.error("failed to create metadata doc {} ...", request, e)
            call.respond(HttpStatusCode.InternalServerError, e.message ?: "Unknown error")
        }
    }

suspend fun updateMetadataDoc(call: ApplicationCall) {
    val request = call.receive<UpdateDocumentInfoRequest>()

    val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
    if (request.details.ownerUserId != peerInfo.userId || request.details.ownerRegId != peerInfo.lsRegId) return

    val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
    val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

    if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) return

    val grpcRes = metadataServiceGateway.updateMetadataDoc(request.docGlobalId, request.details).await()
    val docInfo = DocumentInfoResponse(
        docGlobalId = grpcRes.metadata.docId,
        editorType = grpcRes.metadata.editorType,
        docType = grpcRes.metadata.metadataType,
        details = defaultMapper.readValue(grpcRes.metadata.metadataDetails, DocumentInfoDetail::class.java)
    )

    // request msg to syncer
    val aliveSyncer = db.findAliveSynchronizers()    // try finding alive peers from PeerManager instead
    aliveSyncer.sortedByDescending { pInfo -> pInfo.lastSeen }

    val syncerPeer = aliveSyncer.find { pInfo -> peerMan.hasPeer(pInfo.id) }   // find the first alive peer inside peer man
    if (syncerPeer == null) {
        logger.error("Unable to find a syncer to serve the room with id {}", roomInfo.id)
        return call.respond(HttpStatusCode.ServiceUnavailable, "Unable to find a syncer")
    }

    // send msg to peers
    val notify = UpdateDocInfoMessage(roomInfo.id, coordState.id, docInfo.docGlobalId, docInfo)
    val msgBuilder: (Peer) -> SignalMessage = {
        SignalMessage(peerInfo.id, it.info.id, SignalType.DocInfoUpdated, json.pojoNode(notify))
    }

    peerMan.filterAndSendAsync(msgBuilder) {
        it.info.roomId == roomInfo.id
                && it.info.peerType == PeerType.BROWSER
                && it.info.status == PeerStatus.ACTIVE
    }

    logger.info("metadata doc {} is updated {}", docInfo.docGlobalId, docInfo)
    call.respond(HttpStatusCode.OK, docInfo)
}

suspend fun loadMetadataDoc(call: ApplicationCall) {
    val request = call.receive<LoadDocumentInfoRequest>()

    val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
    val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
    val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

    val grpcRes = metadataServiceGateway.loadMetadataDoc(
        CLASSROOM_METADATA_DOC_TYPE, listOfNotNull(
            Triple("classroomId", BsonType.STRING, roomInfo.id),
            Triple("coordStateId", BsonType.STRING, coordState.id),
        ), request.docGlobalId, request.editorType
    ).await()

    val res = grpcRes.metadataList.map {
        DocumentInfoResponse(
            docGlobalId = it.docId,
            editorType = it.editorType,
            docType = it.metadataType,
            details = defaultMapper.readValue(it.metadataDetails, DocumentInfoDetail::class.java)
        )
    }
    call.respond(HttpStatusCode.OK, res)
}

suspend fun loadMetadataDocs(call: ApplicationCall) {
    val requests = call.receive<List<LoadDocumentInfoRequest>>()

    val responses = requests.mapNotNull { request ->
        val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return@mapNotNull null
        val coordState =
            getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return@mapNotNull null
        val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return@mapNotNull null

        val grpcRes = metadataServiceGateway.loadMetadataDoc(
            CLASSROOM_METADATA_DOC_TYPE, listOfNotNull(
                Triple("classroomId", BsonType.STRING, roomInfo.id),
                Triple("coordStateId", BsonType.STRING, coordState.id),
            ), request.docGlobalId, request.editorType
        ).await()

        grpcRes.metadataList.map {
            DocumentInfoResponse(
                docGlobalId = it.docId,
                editorType = it.editorType,
                docType = it.metadataType,
                details = defaultMapper.readValue(it.metadataDetails, DocumentInfoDetail::class.java)
            )
        }
    }.flatten()

    call.respond(HttpStatusCode.OK, responses)
}

suspend fun markValidMetadataDoc(call: ApplicationCall) {
    val request = call.receive<MarkDocumentValidRequest>()

    val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
    val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return
    val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

    if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) return

    val grpcRes = metadataServiceGateway.markValidMetadataDoc(
        CLASSROOM_METADATA_DOC_TYPE,
        request.docGlobalId,
        request.editorType,
        request.isValid
    ).await()
    val docInfo = DocumentInfoResponse(
        docGlobalId = grpcRes.metadata.docId,
        editorType = grpcRes.metadata.editorType,
        docType = grpcRes.metadata.metadataType,
        details = defaultMapper.readValue(grpcRes.metadata.metadataDetails, DocumentInfoDetail::class.java)
    )

    logger.info("metadata doc {} is updated {}", docInfo.docGlobalId, docInfo)
    call.respond(HttpStatusCode.OK, docInfo)
}

suspend fun markValidMetadataDocs(call: ApplicationCall) {
    val request = call.receive<MarkMultiDocumentValidRequests>()

    val peerInfo = getPeerInfoIfExist(logger, call, peerMan, request.peerId) ?: return
    val roomInfo = getRoomInfoIfExist(logger, call, classroomInfoCache, peerInfo.roomId) ?: return

    val docInfos = mutableListOf<DocumentInfoResponse>()

    request.docs.forEachIndexed { index, doc ->
        val coordState = getCoordIfExist(logger, call, coordStateCache, request.coordStateId) ?: return@forEachIndexed

        if (!validateModifyCoordStageContent(logger, call, peerInfo, roomInfo, coordState)) return@forEachIndexed

        val grpcRes = metadataServiceGateway.markValidMetadataDoc(
            CLASSROOM_METADATA_DOC_TYPE,
            doc.docGlobalId,
            doc.editorType,
            doc.isValid
        ).await()

        val docInfo = DocumentInfoResponse(
            docGlobalId = grpcRes.metadata.docId,
            editorType = grpcRes.metadata.editorType,
            docType = grpcRes.metadata.metadataType,
            details = defaultMapper.readValue(grpcRes.metadata.metadataDetails, DocumentInfoDetail::class.java)
        )

        docInfos.add(docInfo)
    }

        logger.info("Metadata doc is updated {}", docInfos)
    call.respond(HttpStatusCode.OK, docInfos)
}
}
