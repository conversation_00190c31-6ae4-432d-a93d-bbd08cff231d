package vinet.ccs.db

import com.mongodb.client.model.Filters
import com.mongodb.client.model.Filters.*
import com.mongodb.client.model.FindOneAndUpdateOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.client.model.Sorts.descending
import com.mongodb.client.model.Updates
import com.mongodb.client.model.Updates.*
import com.mongodb.client.result.DeleteResult
import com.mongodb.client.result.InsertOneResult
import com.mongodb.client.result.UpdateResult
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.reactive.awaitFirst
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.rx3.await
import kotlinx.coroutines.withContext
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import vinet.ccs.config.ConfigBean
import vinet.ccs.koin.DatabaseModule
import vinet.ccs.model.*
import vinet.ccs.pojo.*


@Singleton
class DatabaseService(
    @Named(DatabaseModule.MONGO_COLLECTION_PEER_INFO) private val peerCol: MongoCollection<PeerInfo>,
    @Named(DatabaseModule.MONGO_COLLECTION_ROOM_INFO) private val roomCol: MongoCollection<ClassroomInfo>,
    @Named(DatabaseModule.MONGO_COLLECTION_COORDINATOR_STATE) private val coordinatorCol: MongoCollection<CoordinatorState>,
    private val config: ConfigBean,
) : Logging {

    suspend fun insertPeer(peerInfo: PeerInfo): InsertOneResult? {
        return peerCol.insertOne(peerInfo).awaitFirstOrNull()
    }

    suspend fun removePeer(peerId: String?, userId: String?, roomId: String?): DeleteResult? {
        val filter: Bson = if (peerId != null) {
            eq("_id", ObjectId(peerId))
        } else {
            and(eq("userId", ObjectId(userId)), eq("roomId", ObjectId(roomId)))
        }
        return peerCol.deleteMany(filter).awaitFirstOrNull()
    }

    suspend fun insertRoom(classroomInfo: ClassroomInfo): InsertOneResult {
        return Flowable.defer {
            roomCol.insertOne(classroomInfo)
        }.firstOrError().doOnError {
            logger.error("Unable to insert peer info {} ...", classroomInfo, it)
        }.await()
    }

    suspend fun findRoom(roomId: String): ClassroomInfo? {
        val filter = eq("_id", ObjectId(roomId))
        return Flowable.defer { roomCol.find(filter) }.awaitFirstOrNull()
    }

    suspend fun findOpenRoom(): List<ClassroomInfo> {
        val filters = eq(ClassroomInfo::status.name, ClassroomStatus.OPENED)
        return Flowable.defer { roomCol.find(filters) }.toList().await()
    }

    /**
     * Find all synchronizers
     */
    suspend fun findAliveSynchronizers(): List<PeerInfo> {
        val filter = and(
            eq(PeerInfo::peerType.name, PeerType.SYNCER),
            eq(PeerInfo::status.name, PeerStatus.ACTIVE),
            gt(PeerInfo::lastSeen.name, System.currentTimeMillis() - config.syncerPeerAlivePeriod)
        )

        return Flowable.defer {
            peerCol.find(filter)
        }.toList().await()
    }

    suspend fun findFirstAliveSynchronizer(): PeerInfo? {
        val filter = and(
            eq(PeerInfo::peerType.name, PeerType.SYNCER),
            eq(PeerInfo::status.name, PeerStatus.ACTIVE),
            gt(PeerInfo::lastSeen.name, System.currentTimeMillis() - config.syncerPeerAlivePeriod)
        )

        return peerCol.find(filter).sort(descending(PeerInfo::lastSeen.name)).first().awaitFirstOrNull()
    }

    suspend fun findAliveUserPeers(): List<PeerInfo> {
        logger.info("Finding all alive user peers")
        val filter = and(
            gt(PeerInfo::lastSeen.name, System.currentTimeMillis() - config.userPeerAlivePeriod),
            eq(PeerInfo::status.name, PeerStatus.ACTIVE),
            eq(PeerInfo::peerType.name, PeerType.BROWSER)
        )

        return Flowable.defer {
            peerCol.find(filter)
        }.toList().await()
    }

    suspend fun updatePeerLastSeen(peerId: String, lastSeen: Long): UpdateResult? {
        val filter = eq("_id", ObjectId(peerId))
        return withContext(Dispatchers.IO) {
            peerCol.updateOne(filter, set(PeerInfo::lastSeen.name, lastSeen)).awaitFirstOrNull()
        }
    }

    suspend fun updatePeerStatus(peerIds: List<String>, status: PeerStatus): UpdateResult {
        val filter = `in`("_id", peerIds.map { ObjectId(it) })
        val result = Flowable.defer {
            peerCol.updateOne(filter, set(PeerInfo::status.name, status))
        }

        return result.awaitFirst()
    }

    suspend fun getPeerById(peerId: String): PeerInfo? {
        val filter = eq("_id", ObjectId(peerId))
        val result = Flowable.defer {
            peerCol.find(filter)
        }

        return result.awaitFirstOrNull()
    }

    suspend fun getPeerByUserIdAndRoomId(userId: String, roomId: String): PeerInfo? {
        val filter = and(eq("userId", ObjectId(userId)), eq("roomId", ObjectId(roomId)))
        val result = Flowable.defer {
            peerCol.find(filter)
        }

        return result.awaitFirstOrNull()
    }

    suspend fun updatePresenting(roomId: String, userId: String?, peerId: String?): UpdateResult {
        logger.info("update presenting room {}, user {}, peer {}", roomId, userId, peerId)
        val filter = eq("_id", ObjectId(roomId))
        val updates: ArrayList<Bson> = ArrayList<Bson>()
        userId?.let { updates.add(set(ClassroomInfo::presentingUser.name, ObjectId(userId))) }
        peerId?.let { updates.add(set(ClassroomInfo::presentingPeer.name, ObjectId(peerId))) }
        return Flowable.defer {
            roomCol.updateOne(filter, updates)
        }.doOnNext {
            logger.info("Updated presenting {} for room {}", updates, roomId)
        }.doOnError {
            logger.error("Unable to update presenting {} for room {} ...", updates, roomId)
        }.awaitFirst()
    }

    suspend fun updateRoomStatus(roomId: String, status: ClassroomStatus): UpdateResult {
        val filter = eq("_id", ObjectId(roomId))
        val update = set(ClassroomInfo::status.name, status)
        return Flowable.defer {
            roomCol.updateOne(filter, update)
        }.doOnNext {
            logger.info("Updated status {} for room {}: {}", status, roomId, it)
        }.doOnError {
            logger.error("Unable to update status {} for room {} ...", status, roomId)
        }.awaitFirst()
    }

    suspend fun addPinCoordinatorState(roomId: String, coordStateId: String): UpdateResult {
        val filter = eq("_id", ObjectId(roomId))
        return Flowable.defer {
            roomCol.updateOne(filter, push(ClassroomInfo::pinnedCoordStates.name, coordStateId))
        }.doOnNext {
            logger.info("Updated pin coordinator {}", coordStateId)
        }.doOnError {
            logger.error("Unable to update pin coordinator {} ...", coordStateId, it)
        }.awaitFirst()
    }

    suspend fun removePinCoordinatorState(roomId: String, coordStateId: String): UpdateResult {
        val filter = eq("_id", ObjectId(roomId))
        return Flowable.defer {
            roomCol.updateOne(filter, pull(ClassroomInfo::pinnedCoordStates.name, coordStateId))
        }.doOnNext {
            logger.info("Removed pin coordinator {}", coordStateId)
        }.doOnError {
            logger.error("Unable to remove pin coordinator {} ...", coordStateId, it)
        }.awaitFirst()
    }

    suspend fun updatePresentingCoordinatorState(roomId: String, coordinatorId: String? = null): UpdateResult {
        val filter = eq("_id", ObjectId(roomId))
        return Flowable.defer {
            roomCol.updateOne(
                filter,
                set(ClassroomInfo::presentingCoordState.name, coordinatorId?.let { ObjectId(coordinatorId) })
            )
        }.doOnNext {
            logger.info("Updated presenting coordinator {} for room {}", coordinatorId, roomId)
        }.doOnError {
            logger.error("Unable to update presenting coordinator {} for room {} ...", coordinatorId, roomId)
        }.awaitFirst()
    }

    suspend fun insertCoordinatorState(state: CoordinatorState): InsertOneResult {
        return Flowable.defer {
            coordinatorCol.insertOne(state)
        }.doOnError {
            logger.error("Unable to insert coordinator state {} ..", state, it)
        }.firstOrError().await()
    }

    suspend fun deleteCoordinator(coordStateId: String): DeleteResult {
        val filter = eq("_id", ObjectId(coordStateId))
        return Flowable.defer {
            coordinatorCol.deleteOne(filter)
        }.doOnError {
            logger.error("Unable to delete coordinator state {} ..", coordStateId, it)
        }.firstOrError().await()
    }

    suspend fun getCoordStateByRoom(roomId: String): List<CoordinatorState> {
        val filter = eq("room", ObjectId(roomId))
        return Flowable.defer {
            coordinatorCol.find(filter)
        }.doOnError {
            logger.error("Unable to get coordinator state by room {} ..", roomId, it)
        }.toList().await()
    }

    suspend fun updateCoordinatorStateTitle(coordStateId: String, title: String): UpdateResult {
        val filter = eq("_id", ObjectId(coordStateId))
        return Flowable.defer {
            coordinatorCol.updateOne(filter, set(CoordinatorState::title.name, title))
        }.doOnNext {
            logger.info("Updated title coordinator {}", coordStateId)
        }.doOnError {
            logger.error("Unable to update title coordinator {} ...", coordStateId, it)
        }.awaitFirst()
    }

    suspend fun getCoordinatorState(id: String): CoordinatorState? {
        val filter = eq("_id", ObjectId(id))
        return Flowable.defer {
            coordinatorCol.find(filter)
        }.awaitFirstOrNull()
    }

    suspend fun getCoordinatorStates(ids: List<String>): List<CoordinatorState> {
        val filter = `in`("_id", ids.map { ObjectId(it) })
        return Flowable.defer {
            coordinatorCol.find(filter)
        }.toList().await()
    }

    suspend fun getCoordinatorStateByUser(room: String, owner: String): List<CoordinatorState> {
        val filter =
            and(eq(CoordinatorState::room.name, ObjectId(room)), eq(CoordinatorState::owner.name, ObjectId(owner)))
        return Flowable.defer {
            coordinatorCol.find(filter)
        }.toList().await()
    }

    suspend fun addNewDocMapping(
        coordinatorId: String,
        version: Int,
        docs: List<DocInfo>,
        defaultSetting: DefaultSetting,
    ): CoordinatorState {
        require(docs.isNotEmpty()) {
            "Docs is not empty."
        }

        val filter = and(eq("_id", ObjectId(coordinatorId)), lt(CoordinatorState::version.name, version))
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

        val updateList = mutableListOf<Bson>()
        var updatedState: CoordinatorState? = null

        updateList.add(set(CoordinatorState::version.name, version))

        for (i in docs.indices) {
            val key = "${docs[i].channelCode}_${docs[i].docLocalId}"
            updateList.add(
                set("${CoordinatorState::docMapping.name}.$key", docs[i].docGlobalId),
            )

            updateList.add(
                set(
                    "${CoordinatorState::docSettings.name}.$key", CoordinatorState.DocSetting(
                        docLocalId = docs[i].docLocalId,
                        channelCode = docs[i].channelCode,
                        background = defaultSetting.background,
                        backgroundColor = defaultSetting.backgroundColor,
                        shadow = defaultSetting.shadow,
                        shadowType = defaultSetting.shadowType,
                        border = defaultSetting.border,
                        borderColor = defaultSetting.borderColor,
                        borderType = defaultSetting.borderType
                    )
                )
            )
        }
        updatedState = Flowable.defer { coordinatorCol.findOneAndUpdate(filter, updateList, ops) }.doOnError {
            logger.error("Failed to update multiple layer positions for coordinator {}: ", coordinatorId, it)
        }.firstOrError().await()
        return updatedState ?: throw IllegalStateException("Failed to update any documents")
    }

    /**
     * Removes document references by global ID within a specified room.
     *
     * This function retrieves affected documents, dynamically constructs update operations
     * to remove relevant document mappings, settings, and layers, then updates the database
     * accordingly. If successful, it returns the updated coordinator states.
     * Any errors encountered during execution are logged.
     *
     * @param roomId The ID of the room to search within.
     * @param globalId The global ID of the document to be removed.
     * @return An array of updated CoordinatorState objects.
     */
    suspend fun removeByGlobalIdInRoom(roomId: String, globalId: String): Array<CoordinatorState> {
        val filter = eq("room", ObjectId(roomId))

        // Step 1: Fetch affected documents using RxJava Flowable
        val affectedDocs = try {
            Flowable.defer { coordinatorCol.find(filter) }.toList().await() // Collects all documents as a List

        } catch (e: Exception) {
            logger.error("Error fetching documents for room: $roomId", e)
            return emptyArray()
        }

        if (affectedDocs.isEmpty()) {
            logger.warn("No documents found in room: $roomId with globalId: $globalId")
            return emptyArray()
        }

        val updatedStates = mutableListOf<CoordinatorState>()

        for (doc in affectedDocs) {
            val updateList = mutableListOf<Bson>()
            val filterDoc = eq("_id", ObjectId(doc.id))

            try {
                // Step 2: Construct updates dynamically
                val docKeys = doc.docMapping.entries.filter { entry ->
                    val parts = entry.value.split("_")
                    parts.size > 3 && parts[3] == globalId
                }.map { it.key }

                if (docKeys.isEmpty()) continue

                docKeys.forEach { docKey ->
                    updateList.add(unset("docMapping.$docKey"))
                    updateList.add(unset("docSettings.$docKey"))

                    doc.layers.keys.filter { it.startsWith("${docKey}_") }
                        .forEach { layerKey -> updateList.add(unset("layers.$layerKey")) }
                }

                if (updateList.isNotEmpty()) {
                    // Step 3: Perform update and fetch updated document AFTER applying changes
                    val options = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

                    val updatedDoc =
                        Flowable.defer { coordinatorCol.findOneAndUpdate(filterDoc, combine(updateList), options) }
                            .awaitFirstOrNull() // Await execution and get updated document

                    updatedDoc?.let { updatedStates.add(it) }
                }
            } catch (e: Exception) {
                logger.error("Error updating document ${doc.id} in room: $roomId", e)
            }
        }

        // Step 4: Return the updated documents as an array
        return updatedStates.toTypedArray()
    }


    suspend fun removeDocMappingAndLayers(
        coordinatorId: String, version: Int, docLocalId: Int,
        layers: List<LayerInfo>, channelCode: Int,
        increaseVersion: Boolean = true,
    ): CoordinatorState? {
        val filter = and(
            eq("_id", ObjectId(coordinatorId)), lt(CoordinatorState::version.name, version)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        val docKey = "${channelCode}_${docLocalId}"

        val bsonUpdates =
            layers.map { unset("${CoordinatorState::layers.name}.${docKey}_${it.layerId}") }.toMutableList()
        bsonUpdates.add(unset("${CoordinatorState::docMapping.name}.$docKey"))
        bsonUpdates.add(unset("${CoordinatorState::docSettings.name}.$docKey"))
        if (increaseVersion) bsonUpdates.add(set(CoordinatorState::version.name, version))

        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, combine(bsonUpdates), ops) }
            .doOnError { error ->
                logger.error(
                    "Failed to update coordinator {} with filter {} and update {}: {}",
                    coordinatorId,
                    filter,
                    bsonUpdates,
                    error.message,
                    error
                )
            }.awaitFirstOrNull()
    }

    suspend fun getDocGlobalId(coordinatorId: String, channelCode: Int, localId: Int): String? {
        val filter = and(
            eq("_id", ObjectId(coordinatorId)), exists("${CoordinatorState::docMapping.name}.${channelCode}_$localId")
        )
        return Flowable.defer {
            coordinatorCol.find(filter)
        }.awaitFirstOrNull()?.docMapping?.get("${channelCode}_$localId")
    }

    suspend fun addNewLayer(
        coordinatorId: String,
        version: Int,
        docLocalId: Int,
        channelCode: Int,
        layers: List<CoordinatorState.LayerInfo>,
        increaseVersion: Boolean = true,
    ): CoordinatorState {
        val filter = and(eq("_id", ObjectId(coordinatorId)), lt(CoordinatorState::version.name, version))
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

        val updateList = mutableListOf<Bson>()
        if (increaseVersion) updateList.add(set(CoordinatorState::version.name, version))
        for (l in layers) {
            val key = "${channelCode}_${docLocalId}_${l.id}"
            updateList.add(
                set("${CoordinatorState::layers.name}.$key", l)
            )
        }

        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, updateList, ops) }.doOnError {
            logger.error("failed to add new layer {} to coord {}", layers, coordinatorId, it)
        }.firstOrError().await()
    }

    suspend fun updateLayerPosition(
        coordinatorId: String, channelCode: Int, docLocalId: Int,
        layerId: Int, positionStart: Position?, positionEnd: Position?, version: Int,
    ): CoordinatorState {
        val key = "${channelCode}_${docLocalId}_${layerId}"
        val filter = and(
            eq("_id", ObjectId(coordinatorId)),
            lt(CoordinatorState::version.name, version),
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER).upsert(false)

        val position: MutableList<Double> = mutableListOf()

        if (positionStart != null) position.addAll(listOf(positionStart.x, positionStart.y))
        if (positionEnd != null) position.addAll(listOf(positionEnd.x, positionEnd.y))

        val updates = combine(
            set("${CoordinatorState::layers.name}.${key}.${CoordinatorState.LayerInfo::position.name}", position),
            set(CoordinatorState::version.name, version)
        )
        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, updates, ops) }.doOnError {
            logger.error("failed to update layer position {}: ", key, it)
        }.firstOrError().await()
    }

    suspend fun updateMutilLayerPosition(
        coordinatorId: String, version: Int, layers: List<Layer>,
    ): CoordinatorState {
        val filter = and(
            eq("_id", ObjectId(coordinatorId)),
            lt(CoordinatorState::version.name, version),
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER).upsert(false)

        val updateList = mutableListOf<Bson>()

        updateList.add(set(CoordinatorState::version.name, version))

        layers.forEach { (channelCode, layerId, docLocalId, positionStart, positionEnd) ->
            val key = "${channelCode}_${docLocalId}_${layerId}"
            val position: MutableList<Double> = mutableListOf()

            if (positionStart != null) position.addAll(listOf(positionStart.x, positionStart.y))
            if (positionEnd != null) position.addAll(listOf(positionEnd.x, positionEnd.y))

            updateList.add(
                set(
                    "${CoordinatorState::layers.name}.${key}.${CoordinatorState.LayerInfo::position.name}", position
                )
            )
        }

        val updates = combine(updateList)

        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, updates, ops) }.doOnError {
            logger.error("failed to update mutil layers position: ", it)
        }.firstOrError().await()
    }

    suspend fun updateLayerInfoIndex(
        coordinatorId: String,
        channelCode: Int,
        docLocalId: Int,
        layerId: Int,
        index: Int,
        version: Int,
    ): CoordinatorState {
        val key = "${channelCode}_${docLocalId}_${layerId}"
        val filter = Filters.and(
            eq("_id", ObjectId(coordinatorId)),
            lt(CoordinatorState::version.name, version),
//            exists("${CoordinatorState::layers.name}.$key")
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER).upsert(false)

        val updates = combine(
            set("${CoordinatorState::layers.name}.${key}.${CoordinatorState.LayerInfo::zIndex}", index),
            set(CoordinatorState::version.name, version)
        )
        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, updates, ops) }.doOnError {
            logger.error("failed to update layer index {}: ", key, it)
        }.firstOrError().await()
    }

    suspend fun removeLayer(coordinatorId: String, version: Int, layerId: Int): CoordinatorState {
        val filter = Filters.and(
            eq("_id", ObjectId(coordinatorId)), lt(CoordinatorState::version.name, version)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)
        val updates = Updates.combine(
            unset("${CoordinatorState::layers.name}.$layerId"), set(CoordinatorState::version.name, version)
        )
        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, updates, ops) }.doOnError {
            logger.error("failed to remove layer {} from coord {}: ", layerId, coordinatorId, it)
        }.firstOrError().await()
    }

    suspend fun updateDocsSettings(
        version: Int,
        coordId: String,
        docSettings: List<CoordinatorState.DocSetting>,
    ): CoordinatorState {
        val filter = and(
            eq("_id", ObjectId(coordId)), lt(CoordinatorState::version.name, version)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

        val bsonUpdates = docSettings.map { setting ->
            val docKey = "${setting.channelCode}_${setting.docLocalId}"
            set("${CoordinatorState::docSettings.name}.$docKey", setting)
        }.toMutableList()

        bsonUpdates.add(set(CoordinatorState::version.name, version))

        return Flowable.defer { coordinatorCol.findOneAndUpdate(filter, combine(bsonUpdates), ops) }.doOnError {
            logger.error("failed to update doc settings for coord {}: ", coordId, it)
        }.firstOrError().await()
    }

    suspend fun updatePresenterState(
        version: Int,
        coordId: String,
        presenterState: PresenterState,
    ): CoordinatorState {
        val filter = and(
            eq("_id", ObjectId(coordId)), lt(CoordinatorState::version.name, version)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

        return Flowable.defer {
            coordinatorCol.findOneAndUpdate(
                filter, combine(
                    set(CoordinatorState::presenterState.name, presenterState),
                    set(CoordinatorState::version.name, version)
                ), ops
            )
        }.doOnError {
            logger.error("failed to update presenter State for coord {}: ", coordId, it)
        }.firstOrError().await()
    }

    suspend fun updateDefaultDocSetting(
        version: Int,
        coordId: String,
        defaultSettings: DefaultSetting,
    ): CoordinatorState {
        val filter = and(
            eq("_id", ObjectId(coordId)), lt(CoordinatorState::version.name, version)
        )
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER)

        return Flowable.defer {
            coordinatorCol.findOneAndUpdate(
                filter, combine(
                    set(CoordinatorState::defaultSetting.name, defaultSettings),
                    set(CoordinatorState::version.name, version)
                ), ops
            )
        }.doOnError {
            logger.error("failed to update default settings for coord {}: ", coordId, it)
        }.firstOrError().await()
    }
}
