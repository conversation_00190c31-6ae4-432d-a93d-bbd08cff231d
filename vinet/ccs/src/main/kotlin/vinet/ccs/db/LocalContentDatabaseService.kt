package vinet.ccs.db

import com.mongodb.client.model.Updates.set
import com.mongodb.client.model.Filters.eq
import com.mongodb.client.model.FindOneAndUpdateOptions
import com.mongodb.client.model.ReturnDocument
import com.mongodb.reactivestreams.client.MongoCollection
import common.libs.logger.Logging
import io.reactivex.rxjava3.core.Flowable
import kotlinx.coroutines.reactive.awaitFirstOrNull
import kotlinx.coroutines.rx3.await
import org.bson.conversions.Bson
import org.bson.types.ObjectId
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import vinet.ccs.config.ConfigBean
import vinet.ccs.koin.DatabaseModule
import vinet.ccs.model.LocalContentChange
import vinet.ccs.pojo.LocalContentState

@Singleton
class LocalContentDatabaseService(
    @Named(DatabaseModule.MONGO_COLLECTION_LOCAL_CONTENT_STATE) private val localContentCol: MongoCollection<LocalContentState>,
    private val config: ConfigBean,
) : Logging {

    // Retrieves an existing LocalContentState or creates a new LocalContentState with the given id if none exists.
    suspend fun getOrCreateLocalContentState(coordId: String): LocalContentState {
        val filter = eq("_id", ObjectId(coordId))
        var result = Flowable.defer { localContentCol.find(filter) }.awaitFirstOrNull()
        if (result == null) {
            result = LocalContentState(coordId)
            Flowable.defer { localContentCol.insertOne(result) }.awaitFirstOrNull()
        }
        return result;
    }

    suspend fun updateLocalContentState(coordId: String, changes: List<LocalContentChange>):
            LocalContentState {
        val contentState = getOrCreateLocalContentState(coordId);

        val filter = eq("_id", ObjectId(coordId))
        val ops = FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER).upsert(false)

        val updateList = mutableListOf<Bson>()

        for (change in changes) {
            val docKey = "${change.channelCode}_${change.docLocalId}"
            // skip this change if current content is newer than the version
            if (contentState.contentMapping.containsKey(docKey)) {
                val currContent = contentState.contentMapping[docKey]!!
                if (currContent.version >= change.version) continue
            }
            updateList.add(
                set("${LocalContentState::contentMapping.name}.${docKey}", LocalContentState.LocalContent(change.version, change.content))
            )
        }

        if (updateList.isEmpty()) return contentState;

        val newLocalContent = Flowable.defer { localContentCol.findOneAndUpdate(filter, updateList, ops) }.doOnError {
            logger.error("failed to update local content: ", it)
        }.firstOrError().await()

        return newLocalContent;
    }
}