package vinet.ccs.exception

/**
 * Base class for exceptions related to peer interactions within the system.
 * Provides a common type for catching various peer-related errors.
 *
 * @param message A descriptive message explaining the reason for the exception.
 * @param cause The underlying cause of the exception, if any.
 */
open class PeerException(message: String, cause: Throwable? = null) : Exception(message, cause)

/**
 * Exception thrown when a peer has been actively kicked out or disconnected from the system
 * by an administrative action or system rule.
 *
 * @param message A descriptive message explaining the reason for the kick-out.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerKickedOutException(message: String, cause: Throwable? = null) : PeerException(message, cause)

/**
 * Exception thrown when an operation is attempted on a peer that is currently marked as inactive
 * or unresponsive, but not necessarily closed or kicked.
 *
 * @param message A descriptive message explaining why the peer is considered inactive.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerInactiveException(message: String, cause: Throwable? = null) : PeerException(message, cause)

/**
 * Exception thrown when an operation is attempted on a peer connection that has already been closed,
 * either gracefully or unexpectedly.
 *
 * @param message A descriptive message indicating the closed state.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerClosedException(message: String, cause: Throwable? = null) : PeerException(message, cause)

/**
 * Exception thrown when an attempt is made to interact with a peer that cannot be found
 * or identified within the system (e.g., using an invalid ID).
 *
 * @param message A descriptive message, often including the identifier of the peer that was not found.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerNotFound(message: String, cause: Throwable? = null) : PeerException(message, cause)

/**
 * Base class for exceptions indicating that an operation involving a peer timed out.
 * Specific timeout types should extend this class.
 *
 * @param message A descriptive message explaining the timeout situation.
 * @param cause The underlying cause of the exception, if any.
 */
open class PeerTimeoutException(message: String, cause: Throwable? = null) : PeerException(message, cause)

/**
 * Exception thrown specifically when the operation of sending data *to* a peer times out.
 *
 * @param message A descriptive message explaining the send timeout.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerSendTimeoutException(message: String, cause: Throwable? = null) : PeerTimeoutException(message, cause)

/**
 * Exception thrown specifically when waiting to receive data *from* a peer times out.
 *
 * @param message A descriptive message explaining the receive timeout.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerReceiveTimeoutException(message: String, cause: Throwable? = null) : PeerTimeoutException(message, cause)

/**
 * Exception thrown when a request sent to a peer times out while waiting for a response or acknowledgment.
 * Often used in request-response communication patterns.
 *
 * @param message A descriptive message explaining the request timeout.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerRequestTimeoutException(message: String, cause: Throwable? = null) : PeerTimeoutException(message, cause)

/**
 * Exception thrown specifically when waiting for a *reply* from a peer (to a previous request) times out.
 * This might be used to distinguish timeout waiting for the reply payload vs. timeout waiting for the initial request acknowledgment,
 * depending on the protocol.
 *
 * @param message A descriptive message explaining the reply timeout.
 * @param cause The underlying cause of the exception, if any.
 */
class PeerReplyTimeoutException(message: String, cause: Throwable? = null) : PeerTimeoutException(message, cause)