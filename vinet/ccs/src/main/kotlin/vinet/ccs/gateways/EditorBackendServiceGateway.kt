package vinet.ccs.gateways

import common.libs.logger.Logging
import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.http.content.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import io.ktor.util.*
import io.ktor.utils.io.*
import org.koin.core.annotation.Singleton
import vinet.ccs.config.ConfigBean
import vinet.ccs.model.DuplicateDocumentsResponse

/**
 *
 * <AUTHOR>
 */
@Singleton
class EditorBackendServiceGateway constructor(
    private val cf: ConfigBean,
    private val client: HttpClient,
) : Logging {

    suspend fun submitDuplicateDocumentRequest(channelCode: Int, globalIds: List<String>): DuplicateDocumentsResponse {
        val url: String = cf.editorBackends.get(channelCode) ?: run {
            logger.error("not found editor service for channel {}", channelCode)
            throw RuntimeException("not found editor service")
        }
        val requestBuilder: (HttpRequestBuilder.() -> Unit) = {
            url("${url}/documents/duplicate")
            contentType(ContentType.Application.Json)
            method = HttpMethod.Post
            setBody(globalIds)
        }

        return client.request(requestBuilder).body()
    }

    suspend fun submitDeleteDocumentsRequest(channelCode: Int, globalIds: List<String>): HttpResponse {
        val url: String = cf.editorBackends.get(channelCode) ?: run {
            logger.error("not found editor service for channel {}", channelCode)
            throw RuntimeException("not found editor service")
        }
        val requestBuilder: (HttpRequestBuilder.() -> Unit) = {
            url("${url}/documents/delete")
            contentType(ContentType.Application.Json)
            method = HttpMethod.Post
            setBody(globalIds)
        }

        return client.request(requestBuilder).body()
    }

    suspend fun submitCmdRequest(channelCode: Int, globalId: String, data: ByteArray): HttpResponse {
        val url: String = cf.editorBackends.get(channelCode) ?: run {
            logger.error("not found editor service for channel {}", channelCode)
            throw RuntimeException("not found editor service")
        }
        val requestBuilder: (HttpRequestBuilder.() -> Unit) = {
            url("${url}/cmd")
            contentType(ContentType.Application.OctetStream)
            method = HttpMethod.Post
            parameter("globalId", globalId)
            setBody(data)
        }

        return client.request(requestBuilder).body()
    }

    suspend fun submitGetDocByGlobalIdRequest(channelCode: Int, globalId: String): HttpResponse {
        val url: String = cf.editorBackends.get(channelCode) ?: run {
            logger.error("not found editor service for channel {}", channelCode)
            throw RuntimeException("not found editor service")
        }
        val requestBuilder: (HttpRequestBuilder.() -> Unit) = {
            url("${url}/document/fetch")
            contentType(ContentType.Application.Json)
            method = HttpMethod.Get
            parameter("globalId", globalId)
        }

        return client.request(requestBuilder)
    }

    /**
     * proxy editor backend response back to calling request without parsing data
     */
    suspend fun proxyEditorBackendResponse(call: ApplicationCall, response: HttpResponse) {
        val proxiedHeaders = response.headers
        val contentType = proxiedHeaders[HttpHeaders.ContentType]
        val contentLength = proxiedHeaders[HttpHeaders.ContentLength]

        // whatever the backend return, don't do anything, just return it to the data saver
        call.respond(object : OutgoingContent.WriteChannelContent() {
            override val contentLength: Long? = contentLength?.toLong()
            override val contentType: ContentType? = contentType?.let { ContentType.parse(it) }
            override val headers: Headers = Headers.build {
                appendAll(proxiedHeaders.filter { key, _ ->
                    !key.equals(HttpHeaders.ContentType, ignoreCase = true) &&
                            !key.equals(HttpHeaders.ContentLength, ignoreCase = true) &&
                            !key.equals(HttpHeaders.TransferEncoding, ignoreCase = true)
                })
            }
            override val status: HttpStatusCode = response.status
            override suspend fun writeTo(channel: ByteWriteChannel) {
                response.bodyAsChannel().copyAndClose(channel)
            }
        })
    }
}
