package vinet.ccs.gateways

import com.google.protobuf.StringValue
import common.libs.logger.Logging
import io.grpc.ManagedChannel
import kotlinx.coroutines.*
import org.bson.BsonType
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import vi.metadata.doc.proto.MetadataDocMessage.*
import vi.metadata.doc.proto.MetadataDocMessage.DuplicateMetadataDocRequest.DataReplace
import vi.metadata.doc.proto.MetadataDocMessage.LoadMetadataDocRequest.DataFilter
import vi.metadata.doc.proto.MetadataDocMessage.UpdateMetadataDocFieldRequest.DataUpdate
import vi.metadata.doc.proto.MetadataDocServiceGrpcKt
import vinet.ccs.koin.METADATA_SERVICE_CHANNEL
import vinet.ccs.metatadata.model.DocumentInfoDetail
import vinet.ccs.utility.defaultMapper
import java.util.*


/**
 *
 * <AUTHOR>
 */
@Singleton
class MetadataServiceGateway constructor(
    @Named(METADATA_SERVICE_CHANNEL) private val channel: ManagedChannel,
) : Logging {
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    suspend fun createMetadataDoc(
        docId: String, editorType: String, metadataType: String, details: DocumentInfoDetail
    ): Deferred<MetadataDocResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val req = CreateMetadataDocRequest.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setMetadata(
                MetadataDocProto.newBuilder()
                    .setDocId(docId)
                    .setEditorType(editorType)
                    .setMetadataType(metadataType)
                    .setMetadataDetails(defaultMapper.writeValueAsString(details))
            )
            .build()

        return scope.async { stub.createMetadataDoc(req) }
    }

    suspend fun updateMetadataDoc(
        docId: String, details: DocumentInfoDetail
    ): Deferred<MetadataDocResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val req = UpdateMetadataDocRequest.newBuilder()
            .setRequestId(UUID.randomUUID().toString())
            .setDocId(docId)
            .setMetadataDetails(defaultMapper.writeValueAsString(details))
            .build()

        return scope.async { stub.updateMetadataDoc(req) }
    }

    suspend fun loadMetadataDoc(
        metadataType: String,
        filterFields: List<Triple<String, BsonType, String>>,
        docGlobalId: String? = null,
        editorType: String? = null,
    ): Deferred<MetadataDocsResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val reqBuilder = LoadMetadataDocRequest.newBuilder()
            .setMetadataType(StringValue.of(metadataType))
            .addAllDataFilter(filterFields.map { (f, t, v) ->
                DataFilter.newBuilder()
                    .setFieldName(f)
                    .setDataType(t.name)
                    .setValue(v)
                    .build()
            })

        docGlobalId?.let { reqBuilder.setDocId(StringValue.of(it)) }
        editorType?.let { reqBuilder.setEditorType(StringValue.of(it)) }

        return scope.async { stub.loadMetadataDoc(reqBuilder.build()) }
    }

    suspend fun duplicateMetadataDoc(
        replaceFields: List<Triple<String, BsonType, String>>,
        docGlobalId: String,
        newDocGlobalId: String,
        metadataType: String
    ): Deferred<MetadataDocResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val reqBuilder = DuplicateMetadataDocRequest.newBuilder()
            .setMetadataType(metadataType)
            .setDocId(docGlobalId)
            .setNewDocId(newDocGlobalId)
            .addAllMetadataDetails(replaceFields.map { (f, t, v) ->
                DataReplace.newBuilder()
                    .setFieldName(f)
                    .setDataType(t.name)
                    .setValue(v)
                    .build()
            })
        return scope.async { stub.duplicateMetadataDoc(reqBuilder.build()) }
    }

    suspend fun markValidMetadataDoc(
        metadataType: String,
        docGlobalId: String,
        editorType: String,
        isValid: Boolean
    ): Deferred<MetadataDocResponse> {
        val stub = MetadataDocServiceGrpcKt.MetadataDocServiceCoroutineStub(channel)
        val reqBuilder = UpdateMetadataDocFieldRequest.newBuilder()
            .setMetadataType(metadataType)
            .setDocId(docGlobalId)
            .setEditorType(editorType)
            .addAllMetadataDetails(listOf(
                DataUpdate.newBuilder()
                    .setFieldName("isValid")
                    .setDataType(BsonType.BOOLEAN.name)
                    .setValue(isValid.toString())
                    .build()
            ))

        return scope.async { stub.updateMetadataDocField(reqBuilder.build()) }
    }
}
