package vinet.ccs.gateways

import common.libs.logger.Logging
import io.grpc.ManagedChannel
import kotlinx.coroutines.*
import org.koin.core.annotation.Factory
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import proto.portal.user.UserMessage.*
import proto.portal.user.UserServiceGrpcKt
import vinet.ccs.koin.USER_SERVICE_CHANNEL

@Singleton
class UserServiceGateway constructor(
    @Named(USER_SERVICE_CHANNEL) private val channel: ManagedChannel
) : Logging {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    suspend fun getUserByUsernameAsync(username: String): Deferred<GetUserResponse> {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val req = GetUserByUsernameRequest.newBuilder().setUsername(username).build()

        return scope.async {
            try {
                logger.debug("getUserByUsername request: {}", req)
                val res = stub.getUserByUsername(req)

                logger.debug("getUserByUsername response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("getUserByUsername failed: ", t)
                throw t
            }
        }
    }

    suspend fun getUserByIdAsync(userId: String): Deferred<GetUserResponse> {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val req = GetUserByIdRequest.newBuilder().setUserId(userId).build()

        return scope.async {
            try {
                logger.debug("getUserById request: {}", req)
                val res = stub.getUserById(req)

                logger.debug("getUserById response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("getUserById failed: ", t)
                throw t
            }
        }
    }

    suspend fun getUserProfileByIdAsync(userId : String) : Deferred<GetUserProfileResponse> {
        val stub = UserServiceGrpcKt.UserServiceCoroutineStub(channel)
        val req = GetUserProfileByIdRequest.newBuilder().setUserId(userId).build()

        return scope.async {
            try {
                logger.debug("getUserProfileById request: {}", req)
                val res = stub.getUserProfileById(req)

                logger.debug("getUserProfileById response: {}", res)
                res
            } catch (t: Throwable) {
                logger.error("getUserProfileById failed: ", t)
                throw t
            }
        }
    }
}
