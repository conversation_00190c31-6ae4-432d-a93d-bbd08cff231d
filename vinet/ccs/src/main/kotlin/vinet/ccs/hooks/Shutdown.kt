package vinet.ccs.hooks

import common.libs.logger.Logging
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import portal.kafka.api.IKafkaConsumerManager
import vinet.ccs.peer.PeerManager
import vinet.ccs.processor.ClassroomProcessorManager

@Singleton
class Shutdown : KoinComponent, Logging {
    private val consumerManager by inject<IKafkaConsumerManager>()
    private val peerManager by inject<PeerManager>()
    private val classroomProcessorManager by inject<ClassroomProcessorManager>()

    fun registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(StopThread())
    }

    fun stop() = runBlocking {
        classroomProcessorManager.close()
        peerManager.shutdown()
        consumerManager.stop().await()
    }

    private inner class StopThread : Thread() {
        override fun run() {
            logger.info("*** shutting down CCS server")
            try {
                <EMAIL>()
            } catch (e: Throwable) {
                logger.error("*** shutting down CCS server interrupted", e)
            }
            logger.info("*** CCS server shut down")
        }
    }

}