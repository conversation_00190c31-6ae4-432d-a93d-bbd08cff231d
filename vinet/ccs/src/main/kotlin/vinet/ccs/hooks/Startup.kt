package vinet.ccs.hooks

import common.libs.logger.Logging
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import org.koin.core.component.KoinComponent
import portal.datastructures.lsession.LSRegStatus
import portal.kafka.api.IKafkaConsumerManager
import portal.lsession.pojo.toPojo
import vinet.ccs.actor.ActorManager
import vinet.ccs.actor.ClassroomChecker
import vinet.ccs.actor.UserClassroomActor
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.db.DatabaseService
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.gateways.UserServiceGateway
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.PeerInfo
import kotlin.system.exitProcess

/**
 * Handles the application startup sequence.
 *
 * This class is responsible for initializing the core components and restoring necessary state
 * when the application starts.
 * 1. Reconstructing the state for each user in their respective rooms by:
 *    - Identifying the latest active peer for each user based on `lastSeen`.
 *    - Fetching user details and LSession registration status.
 *    - Creating or retrieving the corresponding `UserClassroomActor`.
 *    - Updating the actor and `PeerManager` with the latest peer information via an actor task.
 *    - Marking any previously tracked, older peers for the same user as inactive and submitting them for batch processing.
 * 2. Ensuring all user state restoration completes *before* proceeding using `coroutineScope`.
 * 3. Starting the Kafka consumer (`IKafkaConsumerManager`) only *after* the user state reload
 *    process is finished. This ensures the system is ready with the restored context before
 *    processing incoming Kafka messages.
 */
@Singleton
class Startup constructor(
    private val databaseService: DatabaseService,
    private val actorManager: ActorManager,
    private val peerManager: PeerManager,
    private val lsessionSG: LSessionServiceGateway,
    private val classroomInfoCache: ClassroomInfoCache,
    private val userSG: UserServiceGateway,
    private val consumerManager: IKafkaConsumerManager,
) : Logging, KoinComponent {

    /**
     * Executes the startup sequence.
     *
     * This function blocks the calling thread until the entire startup process completes
     * (or fails critically). It orchestrates fetching initial data, restoring user/peer states
     * concurrently, and finally starting the Kafka consumers.
     */
    fun start() = runBlocking { // runBlocking is suitable here as startup needs to complete before app is ready
        logger.info("Application startup sequence initiated...")

        // --- 1. Fetch Initial State from Database ---
        val rooms = runCatching {
            logger.info("Fetching open rooms from database...")
            databaseService.findOpenRoom()
        }.getOrElse { e ->
            logger.error("CRITICAL: Failed to fetch open rooms during startup. Aborting.", e)
            // Depending on requirements, you might exit or throw to halt startup
            exitProcess(1) // Example: Exit if rooms can't be loaded
        }
        logger.info("Found ${rooms.size} open rooms.")

        val syncerPeers = runCatching {
            logger.info("Fetching alive synchronizer peers...")
            databaseService.findAliveSynchronizers()
        }.getOrElse { e ->
            logger.error("CRITICAL: Failed to fetch synchronizer peers during startup. Aborting.", e)
            exitProcess(1)
        }
        logger.info("Found ${syncerPeers.size} alive synchronizer peers.")

        val userPeers = runCatching {
            logger.info("Fetching alive user peers...")
            databaseService.findAliveUserPeers()
        }.getOrElse { e ->
            logger.error("CRITICAL: Failed to fetch user peers during startup. Aborting.", e)
            exitProcess(1)
        }
        logger.info("Found ${userPeers.size} alive user peers across all rooms.")

        // --- 2. Initialize Syncer Peers ---
        logger.info("Initializing synchronizer peers in PeerManager...")
        syncerPeers.forEach {
            peerManager.addPeer(it)
        }
        logger.info("Synchronizer peers initialized.")

        // --- 3. Restore User State Concurrently ---
        logger.info("Starting user state reload process for ${rooms.size} open rooms...")
        // Use coroutineScope to launch user reloads concurrently and wait for all to complete.
        coroutineScope {
            rooms.forEach { room ->
                // Filter peers for the current room and group by user ID
                val usersInRoom = userPeers.filter { it.roomId == room.id }.groupBy { it.userId }

                if (usersInRoom.isNotEmpty()) {
                    // Cache room info if there are active users/peers in it
                    classroomInfoCache.add(room)
                    logger.debug("Processing room [${room.id}]: Found ${usersInRoom.size} users with active peers.")
                    actorManager.addClassroomChecker(ClassroomChecker(room.id))

                    usersInRoom.forEach { (userId, peersInRoomForUser) ->
                        // Find the single most recent peer for this user in this room based on lastSeen
                        peersInRoomForUser.maxByOrNull { it.lastSeen }?.let { latestPeerInfo ->
                            // Launch a separate coroutine for each user's reload process
                            launch { // Launch within coroutineScope for structured concurrency
                                // Use runCatching to isolate failures per user reload
                                kotlin.runCatching {
                                    logger.debug("Reloading state for user [{}] in room [{}] using peer [{}] (lastSeen: {})", userId, room.id, latestPeerInfo.id, latestPeerInfo.lastSeen)
                                    reloadUser(room.id, userId, latestPeerInfo)
                                    logger.debug("Successfully submitted reload task for user [{}] in room [{}]", userId, room.id)
                                }.onFailure { e ->
                                    // Log failure but allow other users/rooms to continue processing
                                    logger.error("Failed to reload state for user [{}] in room [{}]. Peer [{}].", userId, room.id, latestPeerInfo.id, e)
                                }
                            }
                        }
                    }
                } else {
                    logger.trace("Skipping room [${room.id}]: No active user peers found.")
                }
            }
        } // coroutineScope ensures all launched reloadUser jobs are complete (or have failed) here

        logger.info("User state reload process finished.")

        // --- 4. Start Kafka Consumers (Only After User State is Restored) ---
        logger.info("Starting Kafka consumer manager...")
        runCatching {
            consumerManager.start().await() // Wait for Kafka consumers to be ready
            logger.info("Kafka consumer manager started successfully.")
        }.onFailure { e ->
            // Decide if the application can run without Kafka or if this is critical
            logger.error("CRITICAL: Failed to start Kafka consumer manager. Application might not function correctly.", e)
             exitProcess(1)
        }

        logger.info("Application startup sequence completed.")
    }

    /**
     * Reloads the state for a specific user in a specific room using their latest known peer.
     * Fetches necessary information, validates registration, gets/creates the actor,
     * and dispatches a task to the actor to handle the peer update logic.
     * This function is designed to be called concurrently for different users.
     *
     * @param roomId The ID of the room/classroom.
     * @param userId The ID of the user.
     * @param latestPeer The most recent `PeerInfo` found for this user in this room during startup.
     */
    private suspend fun reloadUser(roomId: String, userId: String, latestPeer: PeerInfo) {
        // Fetch necessary user and session info asynchronously
        val userProto = userSG.getUserByIdAsync(userId).await().user // Potential failure caught by runCatching in start()
        val lsRegistrationRes = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(roomId, userId).await() // Potential failure caught by runCatching in start()
        val lsRegistration = lsRegistrationRes.registration.toPojo()

        // --- Validation ---
        // Only restore state for users actively registered in the LSession
        if (lsRegistration.regStatus != LSRegStatus.REGISTERED) {
            logger.warn("Skipping reload for user [{}] in room [{}]: LSession status is [{}], not REGISTERED.", userId, roomId, lsRegistration.regStatus)
            return
        }

        // Add/Update the peer in the central manager. Assumes addPeer handles existing peers correctly (e.g., updates lastSeen).
        val reloadedPeer = peerManager.addPeer(latestPeer)
        logger.debug("Peer [{}] added/updated in PeerManager for user [{}] in room [{}]", reloadedPeer.info.id, userId, roomId)
        // --- Actor Interaction ---
        val actor = UserClassroomActor(roomId, userId, lsRegistration, userProto, reloadedPeer)
        actorManager.addActor(actor)
        logger.debug("Obtained actor for user [{}] in room [{}]", userId, roomId)
    }
}