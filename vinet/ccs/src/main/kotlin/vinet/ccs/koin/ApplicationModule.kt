package vinet.ccs.koin

import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import io.ktor.client.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.serialization.jackson.*
import jayeson.utility.concurrent.worker.batch.SharedExecutorBatchFutureWorker
import org.koin.core.annotation.Module
import org.koin.core.annotation.Singleton
import vinet.ccs.batchworker.UpdatePeerStatusCallback
import vinet.ccs.batchworker.UpdatePeerStatusHandler
import java.util.concurrent.Executors

/**
 * Certain components needed by our application (e.g. FormFactory) will be
 * provided from here.
 */
@Module
internal class ApplicationModule {
    @Singleton
    fun provideUpdateStatusBatchWorker(
        handler: UpdatePeerStatusHand<PERSON>,
        callback: UpdatePeerStatusCallback,
    ): SharedExecutorBatchFutureWorker<String, Unit> {
        return SharedExecutorBatchFutureWorker(
            Executors.newSingleThreadScheduledExecutor(),
            handler, 10, callback
        )
    }

    @Singleton
    fun provideHttpClient(): HttpClient {
        return HttpClient(CIO) {
            expectSuccess = false
            install(ContentNegotiation) {
                jackson {
                    setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                        indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                        indentObjectsWith(DefaultIndenter("  ", "\n"))
                    })
                    registerModule(JavaTimeModule())  // support java.time.* types
                    registerModule(KotlinModule.Builder().build())  // support java.time.* types
                }
            }
        }
    }
}
