package vinet.ccs.koin

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import jayeson.utility.JacksonConfig
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import org.slf4j.LoggerFactory
import portal.kafka.api.configs.KafkaConfig
import vinet.ccs.config.ConfigBean
import vinet.ccs.config.DBConfig
import vinet.ccs.config.ServiceExplorerConfig
import java.io.File

@Module
class ConfigurationModule {
    private val config: ConfigBean
    private val suggestionNames: List<String>

    private fun loadConfig(): ConfigBean {
        return try {
            val config =
                JacksonConfig.readConfig(CONFIG_PATH, CONFIG_VAR, ConfigBean::class.java, jacksonObjectMapper())
            if (config == null) {
                logger.error("Failed to load configurations")
                throw RuntimeException("Failed to load configurations")
            }



            config
        } catch (e: Throwable) {
            logger.error("Failed to load configurations ", e)
            throw e
        }
    }

    @Singleton
    fun provideConfigBean(): ConfigBean {
        return config
    }

    @Singleton
    @Named(SUGGESTION_NAMES)
    fun provideSuggestionNames(): List<String> {
        return this.suggestionNames
    }

    @Singleton
    fun provideDBConfig(config: ConfigBean): DBConfig {
        return config.dbConf
    }

    @Singleton
    fun provideKafkaConfig(config: ConfigBean): KafkaConfig {
        return config.kafkaConfig
    }

    @Singleton
    fun provideServiceExplorerConfig(): ServiceExplorerConfig {
        return config.seConf
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ConfigurationModule::class.java)
        private const val CONFIG_PATH = "config.json"
        private const val CONFIG_VAR = "vinet.ccs.conf"
    }

    init {
        config = loadConfig()

        // load suggestion names from file
        this.suggestionNames = File(config.suggestionNamesFile).readLines()
    }
}
