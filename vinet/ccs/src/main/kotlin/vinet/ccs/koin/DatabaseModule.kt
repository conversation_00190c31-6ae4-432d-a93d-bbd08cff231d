package vinet.ccs.koin

import com.google.common.reflect.ClassPath
import com.mongodb.reactivestreams.client.MongoClients
import com.mongodb.reactivestreams.client.MongoCollection
import com.mongodb.reactivestreams.client.MongoDatabase
import common.libs.codec.EnumCodecProvider
import org.bson.codecs.configuration.CodecRegistries
import org.bson.codecs.configuration.CodecRegistry
import org.bson.codecs.pojo.PojoCodecProvider
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import vinet.ccs.config.ConfigBean
import vinet.ccs.config.DBConfig
import vinet.ccs.pojo.ClassroomInfo
import vinet.ccs.pojo.CoordinatorState
import vinet.ccs.pojo.LocalContentState
import vinet.ccs.pojo.PeerInfo
import java.util.stream.Collectors

@Module
@ComponentScan("vinet.ccs.db")
class DatabaseModule {
    companion object {
        const val MONGO_COLLECTION_PEER_INFO = "MongoCollectionPeerInfo"
        const val MONGO_COLLECTION_ROOM_INFO = "MongoCollectionRoomInfo"
        const val MONGO_COLLECTION_COORDINATOR_STATE = "MongoCollectionCoordinatorState"
        const val MONGO_COLLECTION_LOCAL_CONTENT_STATE = "MongoCollectionLocalContentState"
    }

    @Singleton
    fun provideMongoDatabase(dbConf: DBConfig, codecRegistry: CodecRegistry): MongoDatabase {
        val mongoClient = MongoClients.create(dbConf.connectionString)
        return mongoClient.getDatabase(dbConf.dbName).withCodecRegistry(codecRegistry)
    }

    @Singleton
    @Named(MONGO_COLLECTION_PEER_INFO)
    fun providePeerInfoCollection(db: MongoDatabase): MongoCollection<PeerInfo> {
        return db.getCollection("vinet-peer-info", PeerInfo::class.java)
    }

    @Singleton
    @Named(MONGO_COLLECTION_ROOM_INFO)
    fun provideRoomInfoCollection(db: MongoDatabase): MongoCollection<ClassroomInfo> {
        return db.getCollection("vinet-room-info", ClassroomInfo::class.java)
    }

    @Singleton
    @Named(MONGO_COLLECTION_COORDINATOR_STATE)
    fun provideCoordinatorStateCollection(db: MongoDatabase): MongoCollection<CoordinatorState> {
        return db.getCollection("vinet-coordinator-state", CoordinatorState::class.java)
    }

    @Singleton
    @Named(MONGO_COLLECTION_LOCAL_CONTENT_STATE)
    fun provideLocalContentStateCollection(db: MongoDatabase): MongoCollection<LocalContentState> {
        return db.getCollection("vinet-local-content-state", LocalContentState::class.java)
    }

    @Singleton
    fun provideCodecRegistry(configBean: ConfigBean): CodecRegistry {
        return CodecRegistries.fromRegistries(
            MongoClients.getDefaultCodecRegistry(),
            CodecRegistries.fromProviders(
                EnumCodecProvider(),
                PojoCodecProvider.builder()
                    .automatic(true)
                    .register(*configBean.registerPojoPackages.toTypedArray())
                    .register(*loadAllBsonDiscriminatorClasses(configBean.registerPojoPackages).toTypedArray())
                    .build()
            )
        )
    }

    private fun loadAllBsonDiscriminatorClasses(packages: List<String>): List<Class<*>> {
        return try {
            ClassPath.from(javaClass.classLoader)
                .allClasses.stream()
                .filter { c: ClassPath.ClassInfo -> packages.contains(c.packageName) }
                .map { c: ClassPath.ClassInfo -> c.load() }
                .filter { it.isAnnotationPresent(BsonDiscriminator::class.java) }
                .collect(Collectors.toList())
        } catch (t: Throwable) {
            emptyList()
        }
    }
}