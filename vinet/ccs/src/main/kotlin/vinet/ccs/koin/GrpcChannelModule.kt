package vinet.ccs.koin;

import io.grpc.ManagedChannel
import io.grpc.ManagedChannelBuilder
import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Singleton
import vinet.ccs.config.ServiceExplorerConfig

@Module
class GrpcChannelModule {

    @Singleton
    @Named(USER_SERVICE_CHANNEL)
    fun provideUserServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.userService.host, seConf.userService.port).usePlaintext()
            .build();
    }

    @Singleton
    @Named(LSESSION_SERVICE_CHANNEL)
    fun provideLSessionServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.lSessionService.host, seConf.lSessionService.port).usePlaintext()
            .build();
    }

    @Singleton
    @Named(NOTIFICATION_SERVICE_CHANNEL)
    fun provideNotificationServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.notificationService.host, seConf.notificationService.port)
            .usePlaintext().build();
    }

    @Singleton
    @Named(METADATA_SERVICE_CHANNEL)
    fun provideMetadataServiceChannel(seConf: ServiceExplorerConfig): ManagedChannel {
        return ManagedChannelBuilder.forAddress(seConf.metadataService.host, seConf.metadataService.port).usePlaintext()
            .build();
    }
}
