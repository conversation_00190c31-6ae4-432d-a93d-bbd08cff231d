package vinet.ccs.koin

import org.koin.core.annotation.Module
import org.koin.core.annotation.Named
import org.koin.core.annotation.Single
import org.koin.core.component.KoinComponent
import portal.kafka.api.IKafkaEventReactor
import portal.kafka.koin.KAFKA_EVENT_REACTOR_LIST
import portal.kafka.koin.KafkaModule
import vinet.ccs.reactor.ClassroomNotificationEventReactor

/**
 * Entry koin component
 */
@Module(
    includes = [
        KafkaModule::class,
        ActorModule::class,
        ApplicationModule::class,
        BatchWorker::class,
        CacheModule::class,
        ConfigurationModule::class,
        ControllerModule::class,
        DatabaseModule::class,
        GatewayModule::class,
        GrpcChannelModule::class,
        PeerModule::class,
        ReactorModule::class,
        UtilityModule::class,
        HookModule::class,
        ProcessorModule::class,
    ]
)
class KoinApplicationComponent() : KoinComponent {
    @Single
    @Named(KAFKA_EVENT_REACTOR_LIST)
    fun provideKafkaReactors(notificationReactor: ClassroomNotificationEventReactor): Set<IKafkaEventReactor> {
        return setOf(notificationReactor)
    }

}