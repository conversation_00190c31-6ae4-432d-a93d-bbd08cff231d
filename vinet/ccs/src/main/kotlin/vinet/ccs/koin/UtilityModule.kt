package vinet.ccs.koin

import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.server.application.*
import org.koin.core.annotation.ComponentScan
import org.koin.core.annotation.Module
import org.koin.core.annotation.Single
import vinet.ccs.utility.JitsiJwtUtil
import vinet.ccs.utility.defaultMapper

@Module
@ComponentScan("vinet.ccs.utility")
class UtilityModule {

    @Single
    fun provideJitsiJwtUtil(env: ApplicationEnvironment) : JitsiJwtUtil {
        return JitsiJwtUtil(
            env.config.property("jitsi.jwtAppSecret").getString(),
            env.config.property("jitsi.jwtAppId").getString(),
            env.config.property("jitsi.jwtAcceptedIssuer").getList(),
            env.config.property("jitsi.jwtAcceptedAudiences").getList(),
            defaultMapper
        )
    }

}