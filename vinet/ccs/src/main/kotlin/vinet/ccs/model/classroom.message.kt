package vinet.ccs.model

data class StartClassRequest(
    val lsId: String,
    val callingUserId: String
)

data class StopClassRequest(
    val lsId: String,
    val callingUserId: String
)

data class LeaveClassRequest(
    val lsId: String,
    val callingPeerId: String,
)

data class NewQuestionRequest(
    val lsId: String,
    val callingPeerId: String,
    val question: String
)

data class StopQuestionRequest(
    val lsId: String,
    val callingPeerId: String,
    val activityId: String
)

data class RequestPresentationRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String
)

data class CancelRequestPresentationRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String,
    val activityId: String
)

data class AcceptRequestPresentationRequest(
    val lsId: String,
    val callingPeerId: String,
    val activityId: String
)

data class RejectRequestPresentationRequest(
    val lsId: String,
    val callingPeerId: String,
    val activityId: String,
)

data class StopPresentationRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String
)

data class RaiseHandRequest(
    val lsId: String,
    val callingPeerId: String,
)

data class CancelShareScreenRequest(
    val lsId: String,
    val callingPeerId: String,
)


data class ReqShareScreenRequest(
    val lsId: String,
    val callingPeerId: String,
)

data class CancelRaiseHandRequest(
    val lsId: String,
    val callingPeerId: String,
)

data class RejectRaiseHandRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String
)

data class RejectShareScreenRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String
)

data class AcceptRaiseHandRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String
)

data class AcceptShareScreenRequest(
    val lsId: String,
    val callingPeerId: String,
    val targetUserId: String
)

