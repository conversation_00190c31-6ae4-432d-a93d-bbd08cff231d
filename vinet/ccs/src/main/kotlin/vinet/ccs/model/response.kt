package vinet.ccs.model

import vinet.ccs.metatadata.model.DocumentInfoDetail
import vinet.ccs.pojo.ClassroomStatus


/**
 *
 * <AUTHOR>
 */
enum class ResponseErrorCode {
    CLASS_ENDED,
    CLASS_NOT_OPENED,
    PEER_KICKED_OUT,
    PEER_INVALID,
    OFFLINE_USER
}

data class ErrorResponse(
    val errorCode: String,
    val message: String? = null
)

data class CoordResponse(
    val code: String,
    val coordinatorStateId: String,
    val message: String? = null
)

data class DocumentInfoResponse constructor(
    val docGlobalId: String,
    val editorType: String,
    val docType: String,
    val details: DocumentInfoDetail
)

/**
 * This response used for syncer, just needs some fields
 */
data class SyncerCoordStateResponse constructor(
    val id: String,
    val ownerId: String,
    val roomId: String,
    val version: Int,
)

/**
 * This response used for syncer, just needs some fields
 */
data class SyncerRoomInfoResponse constructor(
    val id: String,
    val owner: String,
    val defaultCoordState: String,
    val presentingUser: String,
    val presentingCoordState: String?,
    val presentingPeer: String?,
    val pinnedCoordStates: List<String> = emptyList(),
    val coordStates: List<SyncerCoordStateResponse> = emptyList(),
)

/**
 * This response used for api to get classroom details, most all information.
 */
data class ClassroomInfoResponse constructor(
    val id: String,
    val owner: String,
    val status: ClassroomStatus,
    val defaultCoordState: String,
    val presentingUser: String,
    val presentingCoordState: String?,
    val presentingPeer: String?,
    val pinnedCoordStates: List<String> = emptyList(),
)