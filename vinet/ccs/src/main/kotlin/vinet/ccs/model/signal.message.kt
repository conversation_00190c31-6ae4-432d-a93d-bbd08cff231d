package vinet.ccs.model

import com.fasterxml.jackson.databind.JsonNode
import portal.datastructures.lsession.UserAvailableStatus

enum class SignalType {
    requestOffer,
    offer,
    iceCandidate,
    answer,
    roomUpdate,
    InsertDocResponse,
    InsertLayerResponse,
    UpdateLayerResponse,
    presentCoordState,
    renamedCoordState,
    kickOut,
    JoinClassND, LeaveClassND,
    RequestPinTabND, CancelRequestPinTabND, ApproveRequestPinTabND, RejectRequestPinTabND, UpdateRequestPinTabND,
    AcceptRaiseHandND, RaiseHandND,AcceptShareScreenND, RejectRaiseHandND,RejectShareScreenND, CancelRaiseHandND,ShareScreenRemovedND ,ReqShareScreenND, CancelShareScreenND,
    RegisterND, RegistrationCancelledND, AcceptRegistrationND, RejectRegistrationND,
    RequestPresentationND, StopPresentationND, AcceptPresentationRequestND, RejectPresentationRequestND, CancelPresentationRequestND,
    NewQuestionND, StopQuestionND,
    StartClassND, StopClassND,
    PinnedCoordStateND, UnpinnedCoordStateND,
    UserAvailableStatusChangedND,
    DocInfoUpdated, DocInfoDeleted
}

data class SignalMessage(
    var fromPeer : String,
    var toPeer : String,
    var signalType : SignalType,
    var data : JsonNode,
    val requestId: String? = null,
)

data class ReportRequest constructor(
    val roomId: String,
    val peerId: String,
    var availableStatus: UserAvailableStatus,
    var rtcConn: Any? = null,
    var reportTime: Long = System.currentTimeMillis()
)

data class CoordStateMessage(
    val roomId: String,
    val coordStateId: String,
)

data class RenameCoordStateMessage(
    val coordStateId: String,
    val title: String,
)

data class CreateCoordStateMessage(
    val roomId: String,
    val createdCoordState: SyncerCoordStateResponse,
)

data class RemoveCoordStateMessage(
    val roomId: String,
    val removedCoordStateId: String,
)

data class PinCoordStateMessage(
    val roomId: String,
    val pinCoordStateId: String,
)

data class UnpinCoordStateMessage(
    val roomId: String,
    val unpinCoordStateId: String,
)

data class PresentCoordStateMessage(
    val roomId: String,
    val presentingCoordStateId: String,
)

data class UpdatePresentingPeerMessage(
    val roomId: String,
    val presentingPeerId: String,
)

data class UpdateDocInfoMessage(
    val roomId: String,
    val coordStateId: String,
    val docGlobalId: String,
    val docInfo: DocumentInfoResponse
)

