package vinet.ccs.peer

import common.libs.logger.Logging
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ClosedReceiveChannelException
import kotlinx.coroutines.channels.ClosedSendChannelException
import kotlinx.coroutines.withTimeout
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import vinet.ccs.config.ConfigBean
import vinet.ccs.exception.*
import vinet.ccs.model.SignalMessage
import vinet.ccs.pojo.PeerInfo
import vinet.ccs.pojo.PeerStatus
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.cancellation.CancellationException

/**
 * Represents a connected peer instance within the signaling system (CCS).
 *
 * This class encapsulates the communication channel (mailbox) and logic for sending
 * and receiving [SignalMessage] objects to/from a specific remote peer identified by [PeerInfo].
 * It handles request-reply patterns with timeouts, manages the peer's lifecycle (closing),
 * and enforces status checks (e.g., preventing communication with inactive or kicked-out peers).
 *
 * @param info Contains essential identification and status information about the peer
 *
 * @see SignalMessage
 * @see PeerInfo
 * @see PeerStatus
 */
class Peer constructor(
    val info: PeerInfo,
) : KoinComponent, Logging {
    //  @property idFull A unique identifier string combining room, user, and peer IDs for logging/debugging.
    private val idFull = "${info.roomId}.${info.userId}.${info.id}"
    // @property loggerName The specific logger name used for this peer instance.
    override val loggerName: String = "${javaClass.name}[$idFull]"
    private val config: ConfigBean by inject()
    private val mailBox: Channel<SignalMessage> = Channel(config.signalMessageBufferSize)
    private val replyObservables: ConcurrentHashMap<String, CompletableDeferred<SignalMessage>> = ConcurrentHashMap()

    private val closing = AtomicBoolean(false)

    /**
     * Sends a request message and suspends waiting for a reply.
     * Throws an exception if the peer is closed, inactive, kicked out,
     * if the send operation times out, or if the reply is not received within the specified timeout.
     *
     * @param msg The request message (must have a requestId).
     * @param replyTimeoutMillis The maximum time to wait for the reply. Must be positive.
     * @param sendTimeoutMillis Optional timeout for the internal send operation (defaults to 5000ms).
     * @return The SignalMessage containing the reply.
     * @throws IllegalArgumentException if msg.requestId is null or replyTimeoutMillis is not positive.
     * @throws PeerKickedOutException if the peer status is KICKED_OUT.
     * @throws PeerInactiveException if the peer status is INACTIVE.
     * @throws PeerClosedException if the peer is closing, closed, or the channel closes during operation.
     * @throws PeerRequestTimeoutException if the *send* operation times out.
     * @throws PeerReplyTimeoutException if the *reply* is not received within replyTimeoutMillis.
     * @throws Throwable for other underlying errors during send or await.
     */
    suspend fun request(
        msg: SignalMessage,
        replyTimeoutMillis: Long?, // Timeout for the reply
        sendTimeoutMillis: Long? // Optional timeout for to send itself
    ): SignalMessage {
        val requestId = msg.requestId ?: throw IllegalArgumentException("Request message must have a requestId")

        // --- Initial Status and Closing Checks ---
        if (info.status == PeerStatus.KICKED_OUT) {
            logger.warn("Cannot request message [$requestId], Peer is kicked out")
            throw PeerKickedOutException("Peer is kicked out")
        }
        if (info.status == PeerStatus.INACTIVE) {
            logger.warn("Cannot request message [$requestId], Peer is inactive")
            throw PeerInactiveException("Peer is inactive")
        }
        if (closing.get()) {
            logger.warn("Cannot request message [$requestId], Peer is closing or closed")
            throw PeerClosedException("Peer is closing or closed")
        }

        // --- Prepare Deferred for Reply ---
        val deferred = CompletableDeferred<SignalMessage>()
        // Atomically insert the deferred. If another request with the same ID is somehow
        // already pending (shouldn't happen with unique IDs), this will return the existing one.
        // We handle potential overwrites or race conditions below. Using putIfAbsent is safer.
        val existing = replyObservables.putIfAbsent(requestId, deferred)

        if (existing != null) {
            // This scenario is unlikely if requestIds are truly unique per request.
            // If it happens, it means another request coroutine with the same ID is active.
            // We should probably fail this current request attempt.
            logger.error("Duplicate request ID [$requestId] detected while preparing request. Failing current attempt.")
            // Complete our *newly created* deferred exceptionally, so we don't leak it.
            deferred.completeExceptionally(IllegalStateException("Duplicate request ID [$requestId] detected"))
            // Throw exception for the current caller
            throw IllegalStateException("Duplicate request ID [$requestId] already in progress")
        }

        // --- Check Closing Status *Again* After Inserting Deferred ---
        // This handles the race condition where close() might start after the initial check
        // but before or during the putIfAbsent operation.
        if (closing.get()) {
            // Attempt to remove the deferred we just added
            replyObservables.remove(requestId, deferred)
            // Complete our deferred exceptionally
            val closeException = PeerClosedException("Peer closed during request setup for [$requestId]")
            deferred.completeExceptionally(closeException)
            logger.warn("Cannot request message [$requestId], Peer closed during setup")
            throw closeException // Throw for the caller
        }

        // --- Send the Request and Wait for Reply ---
        try {
            // 1. Send the message with its own timeout
            try {
                logger.debug("Sending request [{}] with sendTimeout {}...", requestId, sendTimeoutMillis ?: "none")
                if (sendTimeoutMillis != null && sendTimeoutMillis > 0) {
                    withTimeout(sendTimeoutMillis) { mailBox.send(msg) }
                } else {
                    mailBox.send(msg)
                }
                logger.debug("Request [$requestId] sent successfully.")

            } catch (e: Throwable) {
                // Handle send-specific errors (Timeout, Closed Channel)
                val cause = when (e) {
                    is ClosedSendChannelException -> PeerClosedException("Failed to send request [$requestId]: Channel closed", e)
                    is TimeoutCancellationException -> PeerRequestTimeoutException("Failed to send request [$requestId]: Send timeout ($sendTimeoutMillis ms)", e)
                    else -> Exception("Failed to send request [$requestId]: ${e.message}", e) // Other unexpected send errors
                }
                logger.warn("Failed to send request [$requestId]", cause)
                // Clean up the deferred before rethrowing, as await won't be reached
                replyObservables.remove(requestId, deferred)
                deferred.completeExceptionally(cause)
                throw cause // Rethrow the specific send error
            }

            // 2. Wait for the reply with the reply timeout
            logger.debug("Waiting for reply to request [$requestId] (max ${replyTimeoutMillis}ms)...")
            return if (replyTimeoutMillis != null && replyTimeoutMillis > 0) {
                withTimeout(replyTimeoutMillis) {
                    deferred.await() // Suspend until deferred is completed or timeout occurs
                }
            } else {
                deferred.await() // Suspend until deferred is completed (no timeout)
            }

        } catch (e: Throwable) {
            // --- Handle Exceptions During Send or Await ---
            when (e) {
                is TimeoutCancellationException -> {
                    // This specifically catches the timeout from withTimeout(replyTimeoutMillis)
                    val replyTimeoutEx = PeerReplyTimeoutException("Reply timeout ($replyTimeoutMillis ms) for request [$requestId]", e)
                    logger.warn(replyTimeoutEx.message)
                    // Important: Cancel the underlying deferred as well, although close() would eventually clean it.
                    // Use a specific cancellation cause.
                    deferred.completeExceptionally(replyTimeoutEx)
                    return deferred.await()
                }
                is PeerClosedException, is PeerRequestTimeoutException, is PeerKickedOutException, is PeerInactiveException -> {
                    // Re-throw exceptions that might have been set on the deferred by close()
                    // or exceptions thrown directly by the send block.
                    logger.warn("Request [$requestId] failed: ${e.message}")
                    // Ensure deferred is completed exceptionally if not already (e.g., if send failed)
                    deferred.completeExceptionally(e)
                    return deferred.await()
                }
                is CancellationException -> {
                    // Catch potential cancellation of the coroutine running this request
                    logger.warn("Request [$requestId] cancelled externally: ${e.message}")
                    // Ensure deferred is cancelled
                    deferred.cancel(e)
                    return deferred.await()
                }
                else -> {
                    // Catch any other unexpected exceptions during await()
                    logger.error("Request [$requestId] failed unexpectedly while waiting for reply", e)
                    // Complete the deferred exceptionally if it wasn't already
                    deferred.completeExceptionally(e)
                    return deferred.await()
                }
            }
        } finally {
            // --- Cleanup: Always remove the deferred from the map ---
            // This runs regardless of whether send/await succeeded or failed.
            // It prevents memory leaks if a reply arrives after a timeout/error,
            // or if the request failed before awaiting.
            // Use remove(key, value) for slightly safer removal in concurrent scenarios,
            // although remove(key) is usually sufficient here as we own the deferred.
            val removed = replyObservables.remove(requestId, deferred)
            if (removed) {
                logger.debug("Cleaned up reply observable for request [$requestId]")
            } else {
                // This might happen if close() ran concurrently and already removed it, or if timeout occurred.
                logger.debug("Reply observable for request [$requestId] was already removed (or never added successfully).")
            }
        }
    }

    fun reply(msg: SignalMessage) {
        val requestId = msg.requestId ?: run {
            logger.warn("Reply message lacks requestId: $msg")
             throw IllegalArgumentException("Reply message lacks requestId")
        }

        // Use computeIfPresent which is atomic and removes if the lambda returns null
        replyObservables.computeIfPresent(requestId) { _, deferred ->
            if (deferred.isActive) { // Only complete if not already completed/cancelled
                logger.debug("Completing reply observable for request [$requestId]")
                deferred.complete(msg) // Complete successfully
                null // Returning null removes the entry from the map
            } else {
                logger.warn("Attempted to complete reply for request [$requestId], but deferred was already inactive (completed/cancelled).")
                // Actually, we still want to remove it even if inactive, so return null.
                null
            }
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    suspend fun receive(timout: Long? = null): SignalMessage {
        // --- Status and Closing Checks ---
        if (info.status == PeerStatus.KICKED_OUT) {
            logger.warn("Cannot receive message, Peer is kicked out")
            throw PeerKickedOutException("Peer is kicked out")
        }
        if (info.status == PeerStatus.INACTIVE) {
            logger.warn("Cannot receive message, Peer is inactive")
            throw PeerInactiveException("Peer is inactive")
        }
        // Check combined closing state and mailbox emptiness *before* trying to receive
        // If closing is true AND mailbox is empty, it's definitely closed for receiving.
        if (closing.get() && mailBox.isEmpty) {
            logger.warn("Cannot receive message, Peer is closed and mailbox is empty")
            throw PeerClosedException("Peer is closed and mailbox is empty")
        }
        // If closing is true but mailbox is NOT empty, allow receiving remaining messages.

        // --- Attempt to Receive ---
        try {
            return if (timout != null && timout > 0) {
//                logger.debug("Attempting to receive message with timeout {}ms...", timout)
                withTimeout(timout) {
                    mailBox.receive() // Suspends until a message arrives or timeout
                }
            } else {
                logger.debug("Attempting to receive message indefinitely...")
                // Allow indefinite receive if no timeout, but check for closed channel
                mailBox.receive() // Suspends until a message arrives or channel closes
            }
        } catch (e: ClosedReceiveChannelException) {
            // This happens when receive() is called after mailBox.close() and the buffer is empty.
            logger.warn("Cannot receive message: Peer channel is closed and empty.")
            throw PeerClosedException("Cannot receive message: Peer channel is closed and empty", e)
        } catch (e: TimeoutCancellationException) {
//            logger.warn("Cannot receive message: Receive timeout ($timout ms)")
            // Use a more specific timeout exception if available, otherwise wrap
            throw PeerReceiveTimeoutException("Cannot receive message: Receive timeout ($timout ms)", e)
        } catch (e: CancellationException) {
            logger.warn("Receive operation cancelled externally: ${e.message}")
            throw e // Re-throw cancellation
        } catch (e: Throwable) {
            logger.error("Unexpected error during receive", e)
            throw e // Re-throw other unexpected errors
        }
    }

    /**
     * Sends a message ("fire-and-forget") to the peer's mailbox.
     * Suspends only if the mailbox buffer is full, potentially timing out.
     *
     * @param msg The message to send.
     * @param timeoutMillis Optional timeout in milliseconds for the send operation
     *                      if the mailbox buffer is full. If null or non-positive,
     *                      it waits indefinitely for space (or until the channel closes).
     * @throws PeerKickedOutException if the peer status is KICKED_OUT.
     * @throws PeerInactiveException if the peer status is INACTIVE.
     * @throws PeerClosedException if the peer is closing, closed, or the channel closes during send.
     * @throws PeerSendTimeoutException if the send operation times out due to a full buffer.
     * @throws Throwable for other underlying errors during send.
     */
    suspend fun send(msg: SignalMessage, timeoutMillis: Long? = null) { // Renamed parameter
        // --- Status and Closing Checks (Essential before trying to send) ---
        if (info.status == PeerStatus.KICKED_OUT) {
            logger.warn("Cannot send message [${msg.signalType}], Peer is kicked out") // Log message type for context
            throw PeerKickedOutException("Cannot send message, Peer is kicked out")
        }
        if (info.status == PeerStatus.INACTIVE) {
            logger.warn("Cannot send message [${msg.signalType}], Peer is inactive")
            throw PeerInactiveException("Cannot send message, Peer is inactive") // Use PeerInactiveException
        }
        // Check closing status *before* attempting send. A closed channel will also throw ClosedSendChannelException.
        if (closing.get()) {
            // It's possible close() was called but the channel isn't fully closed yet.
            // This check provides an earlier exit. The channel send will also fail if closed.
            logger.warn("Cannot send message [${msg.signalType}], Peer is closing or closed (atomic check)")
            throw PeerClosedException("Cannot send message, Peer is closing or closed")
        }

        // --- Attempt to Send ---
        try {
            logger.debug("Sending message [{}] type [{}] with timeout {}...", msg.requestId ?: "no-id", msg.signalType, timeoutMillis ?: "none")
            if (timeoutMillis != null && timeoutMillis > 0) {
                // Use withTimeout only if a positive timeout is provided
                withTimeout(timeoutMillis) {
                    mailBox.send(msg) // Suspends only if buffer is full
                }
                logger.debug("Message [{}] type [{}] sent successfully (with timeout {}).", msg.requestId ?: "no-id", msg.signalType, timeoutMillis)
            } else {
                // No timeout specified, send will suspend indefinitely if buffer is full,
                // or throw ClosedSendChannelException if closed.
                mailBox.send(msg)
                logger.debug("Message [{}] type [{}] sent successfully (no timeout).", msg.requestId ?: "no-id", msg.signalType)
            }
        } catch (e: Throwable) {
            // --- Handle Send Errors ---
            when (e) {
                is ClosedSendChannelException -> {
                    logger.warn("Cannot send message [${msg.signalType}]: Peer channel is closed", e)
                    // Throw specific exception indicating the peer is closed
                    throw PeerClosedException("Cannot send message: Peer channel is closed", e)
                }
                is TimeoutCancellationException -> {
                    logger.warn("Cannot send message [${msg.signalType}]: Send timeout ($timeoutMillis ms) due to full buffer", e)
                    // Throw specific exception for send timeout
                    throw PeerSendTimeoutException("Cannot send message: Send timeout ($timeoutMillis ms)", e)
                }
                is CancellationException -> {
                    // Catch potential cancellation of the coroutine running this send
                    logger.warn("Send message [${msg.signalType}] cancelled externally: ${e.message}", e)
                    throw e // Re-throw cancellation
                }
                // Re-check status exceptions in case the status changed concurrently, although less likely here
                is PeerKickedOutException, is PeerInactiveException, is PeerClosedException -> {
                    logger.warn("Send message [${msg.signalType}] failed due to peer state: ${e.message}")
                    throw e
                }
                else -> {
                    // Catch any other unexpected errors during the send operation
                    logger.error("Cannot send message [${msg.signalType}]: Unexpected error", e)
                    throw e // Re-throw unexpected errors
                }
            }
        }
    }

    /**
     * Initiates the closing process for the peer.
     *
     * This method is idempotent; subsequent calls after the first will have no effect.
     * It performs the following actions:
     * 1. Sets an atomic flag to indicate the closing state.
     * 2. Closes the underlying communication [Channel] (`mailBox`), preventing further sends
     *    and causing subsequent receives on an empty channel to throw [PeerClosedException].
     *    Existing messages in the channel buffer can still be received.
     * 3. Cancels all pending requests stored in `replyObservables` by completing their
     *    [CompletableDeferred] instances exceptionally with a [PeerClosedException].
     * 4. Clears the `replyObservables` map.
     */
    fun close() {
        if (!closing.compareAndSet(false, true)) {
            logger.debug("Peer [$idFull] already closing or closed.")
            return // Already closing or closed
        }
        logger.info("Closing Peer [$idFull]...")

        // 1. Define the exception to use for closing operations
        val closeException = PeerClosedException("Peer [$idFull] closed normally")

        // 2. Close the mailbox first - this prevents new sends and signals receivers about closure.
        //    Messages already buffered can still be received.
        //    Pass the exception cause for clarity if receive/send fails due to closure.
        mailBox.close(closeException)
        logger.debug("Mailbox channel closed for Peer [$idFull].")


        // 3. Cancel all pending requests by completing their deferreds exceptionally
        //    Use keys.toList() to avoid ConcurrentModificationException if map changes during iteration (unlikely here but safer)
        val pendingRequestIds = replyObservables.keys.toList()
        if (pendingRequestIds.isNotEmpty()) {
            logger.debug("Cancelling ${pendingRequestIds.size} pending request(s) for Peer [$idFull]...")
            pendingRequestIds.forEach { requestId ->
                // Atomically remove and complete exceptionally if present
                replyObservables.remove(requestId)?.let { deferred ->
                    // Create a specific exception for each request cancellation
                    val requestCloseEx = PeerClosedException("Peer [$idFull] closed, request [$requestId] cancelled")
                    // Use completeExceptionally for clarity over cancel
                    val completed = deferred.completeExceptionally(requestCloseEx)
                    if (completed) {
                        logger.debug("Cancelled pending request [$requestId] for Peer [$idFull].")
                    } else {
                        // This might happen if the deferred was already completed/cancelled (e.g., by a concurrent timeout)
                        logger.debug("Pending request [$requestId] for Peer [$idFull] was already completed/cancelled when closing.")
                    }
                }
            }
        } else {
            logger.debug("No pending requests to cancel for Peer [$idFull].")
        }

        // 4. Ensure the map is clear (belt-and-suspenders, remove() in the loop should suffice)
        replyObservables.clear()

        logger.info("Peer [$idFull] closed.")
    }
}