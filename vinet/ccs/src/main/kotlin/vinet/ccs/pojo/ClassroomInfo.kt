package vinet.ccs.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonId
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation
import java.util.*

data class ClassroomInfo @BsonCreator constructor(
        @BsonId
        @BsonRepresentation(BsonType.OBJECT_ID)
        var id: String,

        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("owner") var owner: String,

        @BsonProperty("status") var status: ClassroomStatus,

        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("defaultCoordState") var defaultCoordState: String,

        /**
         * Current user that is presenting
         */
        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("presentingUser") var presentingUser: String,

        /**
         * Current coordinator that is presenting
         */
        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("presentingCoordState") var presentingCoordState: String? = null,

        /**
         * Current peer that is presenting
         */
        @BsonRepresentation(BsonType.OBJECT_ID)
        @BsonProperty("presentingPeer") var presentingPeer: String? = null,

        @BsonProperty("pinnedCoordStates") var pinnedCoordStates: MutableList<String> = mutableListOf(),

        /**
         * List of all recorders
         */
        @BsonProperty("recorders") var recorders : MutableList<String> = mutableListOf(),

        @BsonProperty("createdAt") var createdAt : Date = Date(),
)
