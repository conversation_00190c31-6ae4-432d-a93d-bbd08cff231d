package vinet.ccs.pojo

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.*

data class LocalContentState @BsonCreator constructor(
    @BsonId
    @BsonRepresentation(BsonType.OBJECT_ID)
    val id: String,
    // mapped by channelCode_docLocalId
    @BsonProperty("contentMapping")
    val contentMapping: Map<String, LocalContent> = emptyMap(),
) {

    data class LocalContent @BsonCreator constructor(
        @BsonProperty("version") val version: Int,
        // content in JSON format
        @BsonProperty("content") val content: String
    )
}


