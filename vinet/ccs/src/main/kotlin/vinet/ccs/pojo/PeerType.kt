package vinet.ccs.pojo

enum class PeerType {

    SYNC<PERSON>,         // peer that forward data messages, media stream to other peers joining the same synchronization rooms
    BACKEND,        // backend peer, peers that receiving messages to perform certain operations
    RECORDER,       // a special backend peer that recording message of a synchronization room
    BROWSER         // user peer are users connecting to <PERSON><PERSON> from the browser

}
