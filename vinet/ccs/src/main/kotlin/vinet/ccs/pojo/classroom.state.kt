package vinet.ccs.pojo

/**
 * Đ<PERSON>y là trạng thái liên quan đến một phòng học ảo cụ thể.
 * <PERSON><PERSON> bao gồm các thông tin về: Trạng thái hoạt động của phòng học (đang mở, đang đóng, đang tạm dừng).
 * <PERSON><PERSON> tập trung vào "môi trường" diễn ra buổi học.
 *
 * Classroom Status khác với Learning sesion status, Một "Learning Session" (khóa học) có thể ở trạng thái "STARTED" (đã bắt đầu).
 * <PERSON>u khi "Learning Session" bắt đầu, một "Classroom" (phòng học) sẽ được tạo ra và có thể ở trạng thái
 * "OPENED" (đang mở) hoặc "PAUSED" (tạm dừng) trong suốt quá trình diễn ra buổi học.
 *
 * <AUTHOR>
 */
enum class ClassroomStatus {
    /**
     * Phòng học đã đượ<PERSON> tạ<PERSON>, nhưng chưa bắt đầu hoạt động.
     */
    CREATED,

    /**
     * Phòng học đang hoạt động và người tham gia có thể tương tác.
     */
    OPENED,

    /**
     * Phòng học tạm thời bị dừng lại.
     */
    PAUSED,

    /**
     * Phòng học đã kết thúc và không còn hoạt động.
     */
    STOPPED,
}