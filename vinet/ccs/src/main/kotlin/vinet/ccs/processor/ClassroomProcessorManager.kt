package vinet.ccs.processor

import common.libs.logger.Logging
import kotlinx.coroutines.*
import org.koin.core.annotation.Singleton
import java.io.Closeable
import java.util.concurrent.ConcurrentHashMap

@Singleton
class ClassroomProcessorManager constructor(
    applicationScope: CoroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
) : Logging, Closeable {

    // This scope is for the manager itself and serves as the parent for individual processor scopes.
    // The SupervisorJob ensures that if one processor's context has an unhandled issue (rare),
    // it doesn't bring down the entire manager or other processors.
    private val managerScope = CoroutineScope(
        applicationScope.coroutineContext +
                SupervisorJob() +
                CoroutineName("ClassroomProcessorManagerScope")
    )

    private val processors: ConcurrentHashMap<String, AwaitableSequentialTaskProcessor> = ConcurrentHashMap()

    /**
     * Retrieves an existing processor for the given classroomId or creates a new one.
     * Each processor gets its own CoroutineScope, which is a child of the managerScope,
     * ensuring its lifecycle is managed and it operates independently.
     */
    fun getOrCreateProcessor(classroomId: String): AwaitableSequentialTaskProcessor {
        return processors.computeIfAbsent(classroomId) {
            logger.info("Creating new AwaitableSequentialTaskProcessor for classroom: $classroomId")
            // Each processor runs in a child coroutine scope of managerScope.
            // It gets its own SupervisorJob for robustness.
            // CoroutineName helps in debugging (e.g., in thread dumps or logs).
            val processorScope = CoroutineScope(
                managerScope.coroutineContext +
                        SupervisorJob() +
                        CoroutineName("AwaitableProcessor-$classroomId")
            )
            AwaitableSequentialTaskProcessor(scope = processorScope)
        }
    }

    /**
     * Removes and shuts down the processor for a given classroomId.
     * @param classroomId The ID of the classroom whose processor is to be removed.
     * @param graceful If true, the processor will attempt to finish queued tasks before shutting down.
     *                 If false, it will cancel all tasks immediately.
     */
    fun removeAndShutdownProcessor(classroomId: String, graceful: Boolean = true) {
        processors.remove(classroomId)?.let { processor ->
            logger.info("Removing and shutting down processor for classroom: $classroomId. Graceful: $graceful")
            if (graceful) {
                processor.shutdownGracefully()
            } else {
                processor.cancelAllImmediately("Processor for classroom $classroomId explicitly removed.")
            }
            // The AwaitableSequentialTaskProcessor's internal scope (processorScope)
            // will be cancelled by its own shutdownGracefully or cancelAllImmediately methods
            // because its main processingJob is a child of that scope.
        } ?: logger.warn("Attempted to remove processor for classroom $classroomId, but it was not found.")
    }

    /**
     * Shuts down all managed processors and cancels the manager's own scope.
     * This should be called when the application or this manager is being shut down.
     * @param graceful If true, processors will attempt a graceful shutdown.
     */
    fun shutdownManager(graceful: Boolean = true) {
        logger.info("Shutting down ClassroomProcessorManager. Graceful: $graceful")
        // Iterate over a copy of keys to avoid ConcurrentModificationException if processors are removed during iteration.
        val classroomIds = processors.keys.toList()
        classroomIds.forEach { classroomId ->
            removeAndShutdownProcessor(classroomId, graceful)
        }
        processors.clear() // Ensure the map is empty.
        managerScope.cancel("ClassroomProcessorManager is shutting down.") // Cancel the manager's own scope.
        logger.info("ClassroomProcessorManager shutdown complete.")
    }

    /**
     * Implements Closeable for integration with dependency injection frameworks like Koin
     * that can manage resource lifecycles (e.g., calling close() when a scope is closed).
     */
    override fun close() {
        shutdownManager(graceful = true) // Default to graceful shutdown.
    }
}