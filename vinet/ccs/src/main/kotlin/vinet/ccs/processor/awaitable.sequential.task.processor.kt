package vinet.ccs.processor

import common.libs.logger.Logging
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.consumeEach

// Define a task type that can return a result T
typealias AwaitableTask<T> = suspend () -> T

// A wrapper class to hold the task and its corresponding CompletableDeferred
private data class PackagedTask<T>(
    val task: AwaitableTask<T>,
    val deferred: CompletableDeferred<T>
)

/**
 * A processor to execute tasks sequentially.
 * Tasks are added to a queue and processed one after another.
 * It's possible to await the result or completion of each task.
 */
class AwaitableSequentialTaskProcessor constructor(
    // CoroutineScope to run the task processing coroutine.
    // It's recommended to pass a managed scope.
    // SupervisorJob() ensures that if a child task fails, it doesn't cancel the entire processor's scope.
    private val scope: CoroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())
): Logging {

    // Channel now holds PackagedTask<*> to support different result types
    private val taskChannel = Channel<PackagedTask<*>>(Channel.UNLIMITED)
    private var processingJob: Job? = null

    init {
        startProcessing()
    }

    private fun startProcessing() {
        processingJob = scope.launch {
            taskChannel.consumeEach { packagedTaskGeneric ->
                // Type casting is needed because taskChannel holds PackagedTask<*>
                // Assuming type T is handled correctly when adding and retrieving.
                @Suppress("UNCHECKED_CAST")
                val packagedTask = packagedTaskGeneric as PackagedTask<Any?>

                try {
                    if (!isActive) {
                        logger.info("Processing coroutine is no longer active, skipping task: ${packagedTask.task.hashCode()}")
                        packagedTask.deferred.cancel(CancellationException("Processor is not active"))
                        return@consumeEach
                    }
                    logger.info("Starting execution of task: ${packagedTask.task.hashCode()}")
                    val result = packagedTask.task() // Execute the task
                    logger.info("Completed task: ${packagedTask.task.hashCode()} with result: $result")
                    packagedTask.deferred.complete(result)
                } catch (e: CancellationException) {
                    logger.error("Task ${packagedTask.task.hashCode()} was canceled during execution.")
                    packagedTask.deferred.cancel(e) // Propagate cancellation
                } catch (e: Exception) {
                    logger.error("Error executing task ${packagedTask.task.hashCode()}: ${e.message}")
                    packagedTask.deferred.completeExceptionally(e) // Complete with error
                }
            }
            logger.info("Task processor has stopped accepting new tasks (channel closed and all processed).")
        }
        processingJob?.invokeOnCompletion { cause ->
            val reason = when (cause) {
                null -> "completed normally."
                is CancellationException -> "was canceled."
                else -> "finished with an error: $cause"
            }
            logger.warn("Task processing coroutine $reason")
            if (cause != null) {
                taskChannel.close(cause)
                // Optionally, iterate over remaining tasks in the channel (if any after close)
                // and cancel their deferreds. However, consumeEach stops on close,
                // and new sends will fail. Deferreds might also be cancelled by their caller's scope.
            }
        }
    }

    /**
     * Submits a new task to the queue and returns a Deferred to await its result.
     * @param task The task to execute, a suspend lambda returning type T.
     * @return A Deferred<T> representing the result or error of the task.
     */
    @OptIn(DelicateCoroutinesApi::class)
    suspend fun <T> submitAwaitableTask(task: AwaitableTask<T>): Deferred<T> {
        if (taskChannel.isClosedForSend) {
            logger.warn("Cannot add new task, processor is closed.")
            return CompletableDeferred<T>().apply {
                completeExceptionally(IllegalStateException("Processor is closed"))
            }
        }
        val deferred = CompletableDeferred<T>()
        val packagedTask = PackagedTask(task, deferred)
        logger.info("Added new task to the queue: ${task.hashCode()}")
        taskChannel.send(packagedTask)
        return deferred
    }

    /**
     * Submits a new task (that doesn't return an explicit result) to the queue
     * and returns a Job to await its completion.
     * @param task The task to execute, a suspend lambda returning Unit.
     * @return A Job representing the completion or error of the task.
     */
    suspend fun submitTaskAsync(task: suspend () -> Unit): Job {
        // Reuse the generic submitTask<T> with T as Unit
        val deferredResult: Deferred<Unit> = submitAwaitableTask { task() }
        return deferredResult // Deferred<T> is also a Job
    }

    /**
     * Shuts down the processor gracefully:
     * 1. Prevents new tasks from being added.
     * 2. Tasks already in the queue will be processed.
     * 3. The processing coroutine will terminate after the queue is empty.
     */
    fun shutdownGracefully() {
        logger.info("Requesting graceful shutdown of the task processor...")
        taskChannel.close() // Close the channel. consumeEach will terminate when the channel is empty.
    }

    /**
     * Immediately cancels the processing coroutine and all pending tasks in the channel.
     * The currently running task might be interrupted if it's cooperative with cancellation.
     * @param message The reason for cancellation.
     */
    fun cancelAllImmediately(message: String = "Processor was requested to cancel all tasks.") {
        logger.info("Requesting immediate cancellation of all tasks and shutdown of the processor...")
        processingJob?.cancel(CancellationException(message))
        // Cancelling the channel will also cause consumeEach to stop and clear pending items.
        // Deferreds for tasks already sent might get cancelled by the processing loop
        // or by their caller's scope.
        taskChannel.cancel(CancellationException("Task channel canceled due to immediate shutdown request."))
    }
}