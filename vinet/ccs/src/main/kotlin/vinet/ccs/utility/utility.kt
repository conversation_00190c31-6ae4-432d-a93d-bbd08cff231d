package vinet.ccs.utility

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.bson.BsonDocument
import org.bson.BsonDocumentWriter
import org.bson.codecs.Codec
import org.bson.codecs.EncoderContext
import org.bson.json.JsonWriterSettings
import portal.datastructures.lsession.LSessionStatus
import portal.notification.pojo.Notification
import vinet.ccs.logger
import vinet.ccs.model.ResponseErrorCode
import vinet.ccs.pojo.ClassroomStatus
import vinet.ccs.pojo.PeerStatus


/**
 *
 * <AUTHOR>
 */

val json: JsonNodeFactory = JsonNodeFactory.instance
val defaultMapper: ObjectMapper = jacksonObjectMapper().apply {
    this.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false)
    this.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
}

/**
 * Check require parameters then response as Bad Request status code 400 if missing any parameters in the list
 */
suspend fun ApplicationCall.requireParams(params: List<String>, block: (suspend () -> Unit)? = null ): Boolean {
    params.forEach { p ->
        if (this.parameters[p].isNullOrBlank()) {
            if (block == null) this.respond(HttpStatusCode.BadRequest,"Missing parameters")
            return false
        }
    }
    return true
}

fun serializeNotificationToJsonNode(notification: Notification, notificationCodec: Codec<Notification>): JsonNode {
    val document = BsonDocument()
    notificationCodec.encode(
        BsonDocumentWriter(document),
        notification,
        EncoderContext.builder().build()
    )

    // replace _id by id
    val id = document.getObjectId("_id")
    document.remove("_id")
    document["id"] = id

    val jsonNotification = document.toJson(JsonWriterSettings.builder().objectIdConverter(ObjectIdConverter()).build())
    return defaultMapper.readTree(jsonNotification)
}

/**
 * Log the execution time of a block of code when it takes longer than [timeLimitMs].
 *
 * ! For investigate the 504 error that sometime disconnect the classroom
 */
suspend fun <T> logLongExecution(
    name: String = "operation",
    timeLimitMs: Long = 5_000,
    block: suspend () -> T
): T {
    val scope = CoroutineScope(Dispatchers.Default)
    val job = scope.launch {
        delay(timeLimitMs)
        logger.warn("[LONG_EXECUTION] Action '$name' is taking longer than ${timeLimitMs}ms")
    }

    val result = block() // This might take forever
    job.cancel() // Stop the warning if the operation completes

    return result
}

fun ClassroomStatus.toResponseError(): ResponseErrorCode {
    if (this == ClassroomStatus.STOPPED) return ResponseErrorCode.CLASS_ENDED
    return ResponseErrorCode.CLASS_NOT_OPENED
}

fun LSessionStatus.toResponseError(): ResponseErrorCode {
    if (this == LSessionStatus.ENDED || this == LSessionStatus.CLOSED) return ResponseErrorCode.CLASS_ENDED
    return ResponseErrorCode.CLASS_NOT_OPENED
}

fun PeerStatus.toResponseError(): ResponseErrorCode {
    if (this == PeerStatus.KICKED_OUT) return ResponseErrorCode.PEER_KICKED_OUT
    if (this == PeerStatus.INACTIVE) return ResponseErrorCode.PEER_INVALID
    return ResponseErrorCode.PEER_INVALID
}