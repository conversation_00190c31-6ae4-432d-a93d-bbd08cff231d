package vinet.ccs.utility

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import org.slf4j.Logger
import portal.datastructures.lsession.LSRegStatus
import portal.lsession.pojo.LSessionRegistration
import portal.lsession.pojo.toPojo
import proto.portal.lsession.LsessionMessage
import proto.portal.user.UserMessage.UserProfileProto
import vinet.ccs.actor.ActorManager
import vinet.ccs.actor.UserClassroomActor
import vinet.ccs.cache.ClassroomInfoCache
import vinet.ccs.cache.CoordinatorStateCache
import vinet.ccs.gateways.LSessionServiceGateway
import vinet.ccs.gateways.UserServiceGateway
import vinet.ccs.model.ErrorResponse
import vinet.ccs.model.ResponseErrorCode
import vinet.ccs.peer.Peer
import vinet.ccs.peer.PeerManager
import vinet.ccs.pojo.*

/**
 * <AUTHOR>
 */


suspend fun getPeerIfExist(logger: Logger, call: Application<PERSON>all, peerMan: PeerManager, peerId: String): Peer? {
    val peer = peerMan.getPeer(peerId) ?: run {
        logger.error("not found peer {}", peerId)
        call.respond(HttpStatusCode.Forbidden, ErrorResponse(ResponseErrorCode.PEER_INVALID.name, "Phiên kết nối không tìm thấy"))
        return null
    }
    if (peer.info.status != PeerStatus.ACTIVE) {
        logger.error("peer {} is not active", peerId)
        call.respond(HttpStatusCode.Forbidden, ErrorResponse(peer.info.status.toResponseError().name, "Phiên kết nối không hoạt động"))
        return null
    }
    return peer
}

suspend fun getPeerInfoIfExist(logger: Logger, call: ApplicationCall, peerMan : PeerManager, peerId: String): PeerInfo? {
    val peer = peerMan.getPeer(peerId) ?: run {
        logger.error("not found peer {}", peerId)
        call.respond(HttpStatusCode.Forbidden, ErrorResponse(ResponseErrorCode.PEER_INVALID.name, "Phiên kết nối không tìm thấy"))
        return null
    }
    if (peer.info.status != PeerStatus.ACTIVE) {
        logger.error("peer {} is not active", peerId)
        call.respond(HttpStatusCode.Forbidden, ErrorResponse(peer.info.status.toResponseError().name, "Phiên kết nối không hoạt động"))
        return null
    }
    return peer.info
}

suspend fun getRoomInfoIfExist(logger: Logger, call: ApplicationCall, classroomInfoCache: ClassroomInfoCache, roomId: String): ClassroomInfo? {
    val room = classroomInfoCache.get(roomId) ?: run {
        logger.error("not found room {}", roomId)
        call.respond(HttpStatusCode.Forbidden, "Không tìm thấy lớp học")
        return null
    }
    if (room.status != ClassroomStatus.OPENED) {
        logger.error("room {} is not opened", roomId)
        call.respond(HttpStatusCode.Forbidden, ErrorResponse(room.status.toResponseError().name, "Lớp học đang không mở"))
        return null
    }
    return room
}

suspend fun getLsessionIfExist(logger: Logger, call: ApplicationCall, lsessionSG: LSessionServiceGateway, roomId: String): LsessionMessage.LSessionDetailsProto? {
    val resp = try {
        lsessionSG.getLSessionAsync(roomId).await().lsessionDetails
    } catch (t: Throwable) {
        logger.error("load lsession {} failed", roomId)
        call.respond(HttpStatusCode.InternalServerError,"Lỗi hệ thống, vui lòng tải lại trang")
        return null
    }
    return resp ?: run {
        logger.error("lsession not found room {}", roomId)
        call.respond(HttpStatusCode.Forbidden,"Không tìm thấy khóa học")
        return null
    }
}

suspend fun getCoordIfExist(logger: Logger, call: ApplicationCall, coordStateCache: CoordinatorStateCache, coordId: String): CoordinatorState? {
    return coordStateCache.get(coordId) ?: run {
        logger.error("not found coordinator state {}", coordId)
        call.respond(HttpStatusCode.Forbidden,"Không tìm thấy bảng trắng")
        return null
    }
}

suspend fun getActorIfExist(logger: Logger, call: ApplicationCall, actorManager: ActorManager, roomId: String, userId: String): UserClassroomActor? {
    return actorManager.getActor(roomId, userId) ?: run {
        logger.error("not found actor room {} user {}", roomId, userId)
        call.respond(HttpStatusCode.Forbidden,"Phiên kết nối không tìm thấy")
        return null
    }
}

suspend fun getUserProfileIfExist(logger: Logger, call: ApplicationCall, userSG: UserServiceGateway, userId: String): UserProfileProto? {
    return try {
        userSG.getUserProfileByIdAsync(userId).await().userProfile
    } catch (t: Throwable) {
        logger.error("load user profile {} failed", userId)
        call.respond(HttpStatusCode.InternalServerError,"Lỗi hệ thống, vui long tải lại trang")
        null
    }
}

suspend fun getLSessionRegistrationIfExist(logger: Logger, call: ApplicationCall, lsessionSG: LSessionServiceGateway, roomId: String, userId: String): LSessionRegistration? {
    return try {
        val lsReg = lsessionSG.getLSessionRegistrationByLsIdAndUserIdAsync(roomId, userId).await().registration.toPojo()
        if (lsReg.regStatus != LSRegStatus.REGISTERED) {
            logger.error("user {} not registered yet in room {}", lsReg.userId, lsReg.lsId)
            call.respond(HttpStatusCode.Forbidden,"Chưa đăng ký vào lớp học")
            return null
        }
        lsReg
    } catch (t: Throwable) {
        logger.error("load lsession registration failed room {} user {}", roomId, userId)
        call.respond(HttpStatusCode.InternalServerError,"Lỗi hệ thống, vụi lòng tải lại trang")
        null
    }
}

suspend fun validateRemoveCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    if (coordState.owner != peerInfo.userId) {
        logger.error("user {} is not owner when removing coord {}", peerInfo.userId, coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền xóa bảng này")
        return false
    }

    if (coordState.id == room.defaultCoordState) {
        logger.error("cannot remove default coord {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền xóa bảng mặc định")
        return false
    }

    if (coordState.id == room.presentingCoordState) {
        logger.error("cannot remove presenting coord {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền xóa bảng đang trình bày")
        return false
    }

    if (coordState.id in room.pinnedCoordStates) {
        logger.error("cannot remove pined coord {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền xóa bảng đang ghim")
        return false
    }

    return true
}

suspend fun validateRenameCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    if (coordState.owner != peerInfo.userId) {
        logger.error("user {} is not owner when rename coord {}", peerInfo.userId, coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền đổi tên bảng này")
        return false
    }

    return true
}

suspend fun validatePinCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    if (peerInfo.userId != room.owner || room.defaultCoordState == coordState.id) {
        logger.error("user {} is not owner of classroom when pin coord {}", peerInfo.userId, coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền ghim bảng này")
        return false
    }

    if (room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("coord state {} have being pinned already", coordState.id)
        call.respond(HttpStatusCode.OK, "Bảng này đã được ghim sẵn rồi")
        return false
    }

    return true
}

suspend fun validateUnpinCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    if (peerInfo.userId != room.owner) {
        logger.error("user {} is not owner of classroom when unpin coord {}", peerInfo.userId, coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền bỏ ghim bảng này")
        return false
    }

    if (room.defaultCoordState == coordState.id) {
        logger.error("cannot unpin default coord {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền bỏ ghim bảng này")
        return false
    }

    if (!room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("coord state {} have not being pinned", coordState.id)
        call.respond(HttpStatusCode.OK, "Bẳng này chưa được ghim")
        return false
    }

    return true
}

suspend fun validateModifyCoordStageContent(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val userIsPresenting = peerInfo.id == room.presentingPeer
    val isPresentingCoord = coordState.id == room.presentingCoordState
    val isOwnedCoord = peerInfo.userId == coordState.owner

    // case 1: user can edit NONE-PRESENTING-OWNED coords
    if (isOwnedCoord && !isPresentingCoord) return true;

    // case 2: user is presenting -> can edit presenting coords
    if (userIsPresenting && isPresentingCoord) return true;

    logger.error("[{}] don't have permission to cannot update content", coordState.id)

    call.respond(HttpStatusCode.Forbidden,"Không có quyền chỉnh sửa bảng này")

    return false
}

suspend fun validateRequestPinCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val isOwnedCoord = peerInfo.userId == coordState.owner
    val isDefaultCoord = coordState.id == room.defaultCoordState

    if (isDefaultCoord || !isOwnedCoord) {
        logger.error("don't have permission to request pin coord state {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden, "Không có quyền yêu cầu ghim bảng này")
        return false
    }

    if (room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("coord state {} have being pinned already", coordState.id)
        call.respond(HttpStatusCode.OK,"Bảng này đã được ghim sẵn rồi")
        return false
    }

    return true
}

suspend fun validateCancelRequestPinCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val isOwnedCoord = peerInfo.userId == coordState.owner
    val isDefaultCoord = coordState.id == room.defaultCoordState

    if (isDefaultCoord || !isOwnedCoord) {
        logger.error("don't have permission to cancel request pin coord state {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden, "Không có quyền hủy yêu cầu ghim bảng này")
        return false
    }

    if (room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("coord state {} have being pinned already", coordState.id)
        call.respond(HttpStatusCode.OK, "Bảng này đang được ghim")
        return false
    }

    return true
}

suspend fun validateRejectRequestPinCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val isOwnedRoom = peerInfo.userId == room.owner
    val isDefaultCoord = coordState.id == room.defaultCoordState

    if (isDefaultCoord || !isOwnedRoom) {
        logger.error("don't have permission to reject request pin coord state {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden,"Không có quyền từ chối ghim bảng này")
        return false
    }

    if (room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("coord state {} have being unpinned already", coordState.id)
        call.respond(HttpStatusCode.PreconditionFailed, "Bảng này đã được ghim")
        return false
    }

    return true
}

suspend fun validateApproveRequestPinCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val isOwnedRoom = peerInfo.userId == room.owner
    val isDefaultCoord = coordState.id == room.defaultCoordState

    if (isDefaultCoord || !isOwnedRoom) {
        logger.error("don't have permission to approve request pin coord state {}", coordState.id)
        call.respond(HttpStatusCode.Forbidden, "Không có quyền ghim bảng này")
        return false
    }

    if (room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("coord state {} have being pinned already", coordState.id)
        call.respond(HttpStatusCode.OK, "Bảng này đã được ghim sẵn rồi")
        return false
    }

    return true
}

suspend fun validatePresentCoordState(logger: Logger, call: ApplicationCall, peerInfo: PeerInfo, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val isPresentingCoord = coordState.id == room.presentingCoordState
    val isDefaultCoord = coordState.id == room.defaultCoordState
    val userIsPresenting = peerInfo.userId == room.presentingUser
    val peerIsPresenting = peerInfo.id == room.presentingPeer
    val isOwnedCoord = peerInfo.userId == coordState.owner

    if (!userIsPresenting || !peerIsPresenting) {
        logger.error("user {} not presenting, current is {}", peerInfo.userId, room.presentingUser)
        call.respond(HttpStatusCode.Forbidden, "Chỉ người thuyết trình mới được phép trình bày bảng này")
        return false
    }

    if (!isOwnedCoord && !isDefaultCoord && !room.pinnedCoordStates.contains(coordState.id)) {
        logger.error("user {} not owner of classroom, current is {}", peerInfo.userId, coordState.owner)
        call.respond(HttpStatusCode.Forbidden, "Không có quyền trình bày bảng này")
        return false
    }

    if (isPresentingCoord) {
        logger.error("Coordinator ${coordState.id} have being presented already")
        call.respond(HttpStatusCode.OK, "Bảng này đã được trình bày sẵn rồi")
        return false
    }

    return true
}

fun validateAccessToCoordState(userId: String, room: ClassroomInfo, coordState: CoordinatorState): Boolean {
    val isOwnedCoord = userId == coordState.owner
    val isDefaultCoord = coordState.id == room.defaultCoordState
    val isPinnedCoord = room.pinnedCoordStates.contains(coordState.id)

    return isDefaultCoord || isOwnedCoord || isPinnedCoord
}

suspend fun validateCoordVersion(logger: Logger, call: ApplicationCall, coordId: String, coordStateVs: Int, requestVs: Int): Boolean {
    if (coordStateVs >= requestVs) {
        logger.error("coord [{}] invalid coord version {} - {}", coordId, coordStateVs, requestVs)
        call.respond(HttpStatusCode.PreconditionFailed,"Dữ liệu chưa đồng bộ, vui lòng tải lại trang")
        return false
    }

    return true
}
