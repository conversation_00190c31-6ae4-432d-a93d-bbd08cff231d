syntax = 'proto3';

package CoordinatorStateCmds;


/**
 *
 * <AUTHOR>
 */

enum CmdTypeProto {
  ADD_NEW_DOC_MAPPING = 0;
  REMOVE_DOC_MAPPING_AND_LAYERS = 1;
  REMAP_DOC_MAPPING_AND_LAYERS = 2;
  ADD_LAYER = 3;
  REMOVE_LAYER = 4;
  UPDATE_LAYER_Z_INDEX = 5;
  UPDATE_LAYER_POSITION = 6;
  UPDATE_MUTIL_LAYER_POSITION = 7;
  SYNC_DOC_SETTINGS = 8;
  SYNC_DEFAULT_DOC_SETTING = 9;
  SYNC_PRESENTER = 10;
  SYNC_MARKER_UPDATE = 11;
  SYNC_MARKER_DELETE = 12;
  SYNC_UPDATE_PARTIAL_MARKER = 13;
  SYNC_MARKER_UPDATE_END = 14;
}

message AddNewDocMappingProto {
  string coordinator_id = 1;
  repeated NewDocMappingProto docs = 2;
  DefaultDocSettingProto default_settings = 3;
}

message NewDocMappingProto{
   string doc_global_id = 1;
   uint32 doc_local_id = 2;
   uint32 channel_code = 3;
}

message DefaultDocSettingProto {
  bool background = 1;
  string background_color = 2;

  bool shadow = 3;
  string shadow_type = 4;
  
  bool border = 5;
  string border_type = 6;
  string border_color = 7;
}

message RemapDocMappingAndLayersProto {

  message LayerProto {
    uint32 layer_id = 1;
    int32 index = 2;
    optional PositionProto position_start = 3;
    optional PositionProto position_end = 4;
  }

  string coordinator_id = 1;
  uint32 doc_local_id = 2;
  string doc_global_id = 3;
  uint32 channel_code = 4;
  repeated LayerProto layers = 5;
}

message RemapMultipleDocMappingAndLayersProto {
  repeated RemapDocMappingAndLayersProto documents = 1;
}

message UpdateMutilLayerPositionProto {
  
  message LayerProto {
    uint32 channel_code = 1;
    uint32 doc_local_id = 2;
    uint32 layer_id = 3;
    bool end_point = 4;
    optional PositionProto position_start = 5;
    optional PositionProto position_end = 6;
  }

  string coordinator_id = 1;
  repeated LayerProto layers = 2;
}
message RemoveDocMappingAndLayersProto {
  string coordinator_id = 1;
  uint32 doc_local_id = 2;
  uint32 channel_code = 3;
  repeated uint32 layer_ids = 4;
}

message LayerProto {
  string coordinator_id = 1;
  uint32 channel_code = 2;
  uint32 doc_local_id = 3;
  uint32 layer_id = 4;
  int32 index = 5;
  optional PositionProto position_start = 6;
  optional PositionProto position_end = 7;
}

message AddMultiLayerProto {
  repeated LayerProto layers = 1;
}


message RemoveLayerProto {
  string coordinator_id = 1;
  uint32 channel_code = 2;
  uint32 doc_local_id = 3;
  uint32 layer_id = 4;
}

message UpdateLayerZIndexProto {
  string coordinator_id = 1;
  uint32 channel_code = 2;
  uint32 doc_local_id = 3;
  map<int32, int32> z_indexes = 4;
}

message UpdateLayerPositionProto {
  string coordinator_id = 1;
  uint32 channel_code = 2;
  uint32 doc_local_id = 3;
  uint32 layer_id = 4;
  bool end_point = 5;
  optional PositionProto position_start = 6;
  optional PositionProto position_end = 7;
}

message PositionProto {
  double x = 1;
  double y = 2;
}

message SizeProto {
  double width = 1;
  double height = 2;
}


message DocSettingProto {
  uint32 doc_local_id = 1;
  uint32 channel_code = 2;

  bool background = 3;
  string background_color = 4;

  bool shadow = 5;
  string shadow_type = 6;

  bool border = 7;
  string border_type = 8;
  string border_color = 9;
}

message MarkerUpdateProto {
  string user_id = 1;
  string line_id = 2;
  string color = 3;
  double size = 4;
  bool end_point = 5;
  optional PositionProto point = 6;
}
message SyncMarkerUpdateProto {
  string coordinator_id = 1;
  optional MarkerUpdateProto marker = 2 ;
}

message UpdatePartialMarkerProto {
  string user_id = 1;
  string line_id = 2;
  string color = 3;
  double size = 4;
  repeated PositionProto point = 5;
}

message SyncUpdatePartialMarkerProto {
  string coordinator_id = 1;
  optional UpdatePartialMarkerProto marker = 2 ;
}

message SyncMarkerDeleteProto {
  string coordinator_id = 1;
  string user_id = 2;
}

message PresenterProto {
  double vp_zoom = 1;
  optional PositionProto vp_pos = 2;
  optional SizeProto vp_size = 3;
}

message SyncDocSettingsProto {
  string coordinator_id = 1;
  repeated DocSettingProto doc_settings = 2 ;
}

message SyncPresenterProto {
  string coordinator_id = 1;
  optional PresenterProto presenter = 2 ;
}

message SyncDefaultDocSettingProto {
  string coordinator_id = 1;
  DefaultDocSettingProto default_setting = 2;
}