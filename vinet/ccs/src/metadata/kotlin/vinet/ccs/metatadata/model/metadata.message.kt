package vinet.ccs.metatadata.model


/**
 *
 * <AUTHOR>
 */

const val CLASSROOM_METADATA_DOC_TYPE = "classroom-doc"

data class DocumentInfoDetail(
    val isValid: Boolean,
    val classroomId: String,
    val coordStateId: String,
    val docGlobalId: String,
    val docLocalId: Int,
    val docName: String? = null,
    val ownerUserId: String,
    val ownerUserName: String,
    val ownerRegId: String,
)

data class DocumentInfo(
    val docGlobalId: String,
    val editorType: String,
    val details: DocumentInfoDetail
)

data class CreateDocumentInfoRequest(
    val peerId: String,
    val coordStateId: String,
    val docs: List<DocumentInfo>
)

data class UpdateDocumentInfoRequest(
    val peerId: String,
    val coordStateId: String,
    val docGlobalId: String,
    val editorType: String,
    val details: DocumentInfoDetail
)

data class LoadDocumentInfoRequest(
    val peerId: String,
    val coordStateId: String,
    val editorType: String?,
    val docGlobalId: String? = null
)

data class MarkDocumentValidRequest(
    val peerId: String,
    val coordStateId: String,
    val editorType: String,
    val docGlobalId: String,
    val isValid: Boolean
)

data class MarkMultiDocumentValidRequests(
    val peerId: String,
    val coordStateId: String,
    val docs: List<MarkValidDocument>
)

data class MarkValidDocument(
    val editorType: String,
    val docGlobalId: String,
    val isValid: Boolean
)
